/* eslint-disable react/no-array-index-key */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-lonely-if */
/* eslint-disable jsx-a11y/media-has-caption */
/* eslint-disable no-unused-vars */
import InfoIcon from 'components/icon/InfoIcon';
import IntlMessages from 'helpers/IntlMessages';
import React, { useEffect, useRef, useState } from 'react';
import {
  Button,
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  UncontrolledDropdown
} from 'reactstrap';
import usePermission from 'hooks/usePermission';
import { CAMERA_POSITIONS } from 'constants/recording';

const ScreenRecording = ({
  isCameraStarted,
  isStarted,
  stopCamera,
  startCamera,
  audio,
  setAudio,
  startRecording,
  stopRecording,
  setSelectedCamera,
  setSelectedMicrophone,
  selectedCamera,
  selectedMicrophone,
  cameraPos,
  setCameraPos,
  isRecordingStopped
}) => {
  const [showPreview, setShowPreview] = useState(true);
  const [cameraName, setCameraName] = useState('');
  const {
    audioDevices: microphones,
    videoDevices: cameras,
    isError,
    isCameraPermissionGranted,
    isMicrophonePermissionGranted,
    isFetching
  } = usePermission({
    condition: true
  });

  const handleScreenShareCancel = () => {
    setShowPreview(true);
  };

  return (
    <div className="d-flex justify-content-center align-items-center flex-column mb-4  w-full">
      <div className="d-flex flex-column mt-4 mb-3 align-items-center">
        {!isFetching && !isCameraPermissionGranted && (
          <div
            className="mb-3"
            style={{
              fontSize: '14px',
              padding: '20px',
              color: 'red',
              border: '1px solid red',
              borderRadius: '10px',
              fontWeight: 'bold'
            }}
          >
            <i className="simple-icon-info font-weight-bold "> </i>
            <IntlMessages id="no-camera-permission" />
          </div>
        )}
        <div className="d-flex flex-row justify-content-end">
          <div className=" d-flex align-items-center justify-content-center mr-3">
            <span className="media-label-title">
              <InfoIcon
                message="enable camera while recording your screen"
                name=""
              />
            </span>
          </div>
          <div
            style={{ width: '250px' }}
            aria-disabled={!isCameraPermissionGranted}
          >
            <UncontrolledDropdown style={{ width: '250px' }}>
              <DropdownToggle
                caret
                color="primary"
                className="btn-sm w-full overflow-hidden"
                outline
              >
                {selectedCamera && !isError ? cameraName : 'Select Camera'}
              </DropdownToggle>
              <DropdownMenu center>
                <DropdownItem
                  onClick={() => {
                    stopCamera();
                    setSelectedCamera('');
                  }}
                >
                  Select Camera
                </DropdownItem>
                {cameras?.map((camera) => (
                  <DropdownItem
                    key={camera.deviceId}
                    onClick={() => {
                      startCamera();
                      setSelectedCamera(camera.deviceId);
                      setCameraName(camera.label);
                    }}
                  >
                    {camera.label || `Camera ${camera.deviceId}`}
                  </DropdownItem>
                ))}
              </DropdownMenu>
            </UncontrolledDropdown>
          </div>
        </div>
        <div>
          {!isFetching && !isMicrophonePermissionGranted && (
            <div
              className="mt-4"
              style={{
                fontSize: '14px',
                padding: '20px',
                color: 'red',
                border: '1px solid red',
                borderRadius: '10px',
                fontWeight: 'bold'
              }}
            >
              <i className="simple-icon-info font-weight-bold"> </i>
              <IntlMessages id="no-mic-permission" />
            </div>
          )}
          <div className="d-flex flex-row mt-4 align-items-center justify-content-center">
            <div className=" d-flex align-items-center justify-content-center mr-3">
              <span className="media-label-title">
                <InfoIcon
                  name="mic"
                  message="enable your microphone while recording the screen"
                />
              </span>
            </div>
            <UncontrolledDropdown
              style={{ width: '250px' }}
              aria-disabled={!isMicrophonePermissionGranted}
            >
              <DropdownToggle
                caret
                color="primary"
                className="btn-sm w-full overflow-hidden"
                outline
              >
                {selectedMicrophone && !isError
                  ? selectedMicrophone
                  : 'Select Microphone'}
              </DropdownToggle>
              <DropdownMenu center>
                <DropdownItem
                  onClick={() => {
                    setAudio(false);
                    setSelectedMicrophone('');
                  }}
                >
                  Select Microphone
                </DropdownItem>
                {microphones?.map((microphone) => (
                  <DropdownItem
                    key={microphone.deviceId}
                    onClick={() => {
                      setAudio(true);
                      setSelectedMicrophone(microphone.deviceId);
                    }}
                  >
                    {microphone.label || `Microphone ${microphone.deviceId}`}
                  </DropdownItem>
                ))}
              </DropdownMenu>
            </UncontrolledDropdown>
          </div>
        </div>
      </div>
      {selectedCamera && showPreview && !isRecordingStopped && (
        <>
          <CameraPreview
            selectedCamera={selectedCamera}
            cameraPos={cameraPos}
          />
          <p className="font-bolder">Camera Position</p>
          <CameraPosition cameraPos={cameraPos} setCameraPos={setCameraPos} />
        </>
      )}
      <Button
        type="button"
        color="primary"
        onClick={() => {
          if (selectedCamera) {
            setShowPreview(false);
          }
          if (!isStarted) {
            startRecording(handleScreenShareCancel);
          } else {
            stopRecording();
          }
        }}
      >
        <img
          alt="screen recording"
          height={20}
          width={20}
          src="/assets/icons/screen.svg"
          style={{ marginRight: '10px' }}
        />
        {!isStarted ? (
          <IntlMessages id="rec.start-rec" />
        ) : (
          <IntlMessages id="rec.stop-rec" />
        )}
      </Button>
    </div>
  );
};

const CameraPosition = ({ setCameraPos, cameraPos }) => {
  return (
    <div className="cam-position">
      {CAMERA_POSITIONS.map((m, i) => (
        <span key={i}>
          <div
            style={{ border: cameraPos === m.id && '2px solid #992288' }}
            className={`cp-box-common ${m.className}`}
            onClick={() => setCameraPos(m.id)}
          >
            <Circle isActive={cameraPos === m.id} />
          </div>
          <p style={{ fontSize: 12 }} className="text-center">
            {m.name}
          </p>
        </span>
      ))}
    </div>
  );
};

const Circle = ({ isActive }) => {
  return (
    <span
      style={{
        height: 20,
        width: 20,
        borderRadius: 10,
        background: isActive ? '#992288' : 'silver'
      }}
    />
  );
};

const CameraPreview = ({ selectedCamera, cameraPos }) => {
  const videoRef = useRef(null);
  const streamRef = useRef(null);

  useEffect(() => {
    const getVideoStream = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: selectedCamera ? { deviceId: selectedCamera } : true
        });

        if (videoRef.current) {
          videoRef.current.srcObject = stream;
        }
        streamRef.current = stream;
      } catch (error) {
        console.error('Error accessing media devices.', error);
      }
    };
    getVideoStream();
    return () => {
      if (streamRef.current) {
        const tracks = streamRef.current.getTracks();
        tracks.forEach((track) => track.stop());
      }
    };
  }, [selectedCamera]);

  return (
    <div className="w-100 d-flex flex-column align-items-center justify-content-center my-4">
      <h5 className="font-bolder">Preview of your camera on the screen</h5>
      <div
        className={`share-screen-preview ${
          CAMERA_POSITIONS.find((f) => f.id === +cameraPos).className
        }`}
      >
        <div className="preview">
          <video ref={videoRef} autoPlay height="100%" />
        </div>
      </div>
    </div>
  );
};

export default ScreenRecording;
