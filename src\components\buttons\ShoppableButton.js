import React, { useState } from 'react';
import { Button } from 'reactstrap';

const ShoppableButton = ({ isActive, onClick }) => {
  const [hovered, setHovered] = useState(false);

  return (
    <span
      style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center'
      }}
    >
      <Button
        color={isActive && 'primary'}
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
        style={{
          height: '3rem',
          width: '3rem',
          marginRight: '1rem'
        }}
        onClick={onClick}
        className={!isActive && 'badge-outline-primary btn btn-outline-primary'}
      >
        <span style={{ position: 'relative', left: '-5px' }}>
          <ShoppableIcon active={isActive} h={16} w={16} hovered={hovered} />
        </span>
      </Button>
      <span
        style={{
          left: '-10px',
          position: 'relative',
          color: '#992288',
          textAlign: 'center'
        }}
      >
        Shopping
        {/* Add Shopping */}
      </span>
    </span>
  );
};

const ShoppableIcon = ({ active, h = 30, w = 30, hovered }) => {
  const fill = active || hovered ? '#fff' : '#992288';
  return (
    <svg
      width={w}
      height={h}
      viewBox="0 0 23 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.5 8V17C2.5 17.5304 2.71071 18.0391 3.08579 18.4142C3.46086 18.7893 3.96957 19 4.5 19H18.5C19.0304 19 19.5391 18.7893 19.9142 18.4142C20.2893 18.0391 20.5 17.5304 20.5 17V8"
        stroke={fill}
        strokeWidth="1.5"
      />
      <path
        d="M14.333 19V13C14.333 12.4696 14.1223 11.9609 13.7472 11.5858C13.3721 11.2107 12.8634 11 12.333 11H10.333C9.80257 11 9.29387 11.2107 8.91879 11.5858C8.54372 11.9609 8.33301 12.4696 8.33301 13V19"
        stroke={fill}
        strokeWidth="1.5"
        strokeMiterlimit="16"
      />
      <path
        d="M21.318 7.364L19.624 1.435C19.5881 1.30965 19.5124 1.19939 19.4083 1.1209C19.3042 1.04241 19.1774 0.999971 19.047 1H15L15.475 6.704C15.4823 6.79568 15.5114 6.88429 15.5597 6.96254C15.6081 7.04078 15.6743 7.10641 15.753 7.154C16.143 7.387 16.905 7.817 17.5 8C18.516 8.313 20 8.2 20.846 8.096C20.9282 8.08537 21.0072 8.05692 21.0773 8.01263C21.1474 7.96835 21.207 7.90929 21.252 7.8396C21.2969 7.7699 21.3261 7.69123 21.3375 7.60909C21.3489 7.52695 21.3423 7.44331 21.318 7.364Z"
        stroke={fill}
        strokeWidth="1.5"
      />
      <path
        d="M13.5 8C14.068 7.825 14.788 7.426 15.19 7.188C15.2835 7.13205 15.3594 7.05087 15.409 6.95377C15.4585 6.85667 15.4796 6.74757 15.47 6.639L15 1H8L7.53 6.639C7.52018 6.74774 7.54124 6.85704 7.59077 6.95433C7.64031 7.05163 7.7163 7.13297 7.81 7.189C8.212 7.426 8.932 7.825 9.5 8C10.993 8.46 12.007 8.46 13.5 8Z"
        stroke={fill}
        strokeWidth="1.5"
      />
      <path
        d="M3.37601 1.435L1.68201 7.365C1.6581 7.44418 1.6517 7.52762 1.66327 7.60951C1.67484 7.69141 1.7041 7.76981 1.74901 7.83927C1.79392 7.90873 1.85341 7.96758 1.92334 8.01174C1.99328 8.0559 2.07199 8.08431 2.15401 8.095C2.99901 8.2 4.48401 8.312 5.50001 8C6.09501 7.817 6.85801 7.387 7.24701 7.155C7.32583 7.10731 7.39217 7.04153 7.44051 6.9631C7.48885 6.88467 7.51782 6.79585 7.52501 6.704L8.00001 1H3.95301C3.82263 0.999971 3.69579 1.04241 3.59168 1.1209C3.48757 1.19939 3.41186 1.30965 3.37601 1.435Z"
        stroke={fill}
        strokeWidth="1.5"
      />
    </svg>
  );
};

export default ShoppableButton;
