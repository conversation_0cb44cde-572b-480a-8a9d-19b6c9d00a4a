import IntlMessages from 'helpers/IntlMessages';
import React from 'react';
import Dropzone from 'components/content/upload/components/DropZone';

export default function ImagesUpload({ dropzone, handleContinue }) {
  return (
    <div className="mb-3">
      <p className="dashboard-center-this-item pr-3 pl-3  text-center text-primary mb-2">
        <strong>
          <i className="simple-icon-info font-weight-bold"> </i>
          <IntlMessages id="up.imgdesc" />
        </strong>
      </p>
      <Dropzone
        ref={dropzone}
        handleContinue={(props) => handleContinue(props)}
      />
    </div>
  );
}
