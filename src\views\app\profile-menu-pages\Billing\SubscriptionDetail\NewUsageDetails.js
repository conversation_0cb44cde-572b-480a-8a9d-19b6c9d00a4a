import React, { useEffect, useState } from 'react';
import UsageDetailsCard from './UsageDetailsCard';
import { Card, CardHeader, CardBody } from 'reactstrap';
import IntlMessages from 'helpers/IntlMessages';
import { secondsToTimeString } from 'helpers/Utils';

const NewUsageDetails = ({ data }) => {
  const [details, setDetails] = useState({
    left: [],
    right: []
  });

  useEffect(() => {
    if (data) {
      const contentSize = data?.contentSize ?? 0;
      const contentSizeLimit = data?.contentSizeLimit ?? 0;
      const spaceCount = data?.spaceCount ?? 0;
      const spaceCountLimit = data?.spaceCountLimit ?? 0;
      const playerCount = data?.playerCount ?? 0;
      const playerCountLimit = data?.playerCountLimit ?? 0;
      const storiesCount = data?.storiesCount ?? 0;
      const storiesCountLimit = data?.storiesCountLimit ?? 0;
      const contentDuration = data?.contentDuration ?? 0;
      const totalDurationLimit = data?.totalDurationLimit ?? 0;
      const sharingCount = data?.sharingCount ?? 0;
      const sharingLimit = data?.sharingLimit ?? 0;
      const showcaseCount = data?.showcaseCount ?? 0;
      const showcasesLimit = data?.showcasesLimit ?? 0;
      const contentPercent = ((contentSize / contentSizeLimit) * 100).toFixed(
        1
      );

      setDetails({
        left: [
          {
            id: 1,
            left: 'subs.pc.Storage',
            right: `${(contentSize / (1024 * 1024 * 1024)).toFixed(1)} /
                ${(contentSizeLimit / (1024 * 1024 * 1024)).toFixed(1)}
                GB Used.`,
            percent: contentPercent
          },
          {
            id: 2,
            left: 'No. of Spaces',
            right: `${spaceCount} / ${spaceCountLimit} Spaces
                Created.`,
            percent: ((spaceCount / spaceCountLimit) * 100).toFixed(1)
          },
          {
            id: 3,
            left: ' Audio/Video Storage Hours (per month)',
            right: `${secondsToTimeString(
              contentDuration
            )} / ${totalDurationLimit} Hours`,
            percent: (
              (contentDuration / (totalDurationLimit * 60 * 60)) *
              100
            ).toFixed(1)
          },
          {
            id: 4,
            left: 'No. of Stories',
            right: `${storiesCount} / ${storiesCountLimit} Stories
                Created.`,
            percent: ((storiesCount / storiesCountLimit) * 100).toFixed(1)
          }
        ],
        right: [
          {
            id: 1,
            left: 'No. of Media Players',
            percent: ((playerCount / playerCountLimit) * 100).toFixed(1),
            right: `${playerCount} / ${playerCountLimit} Media Player
                Created.`
          },
          {
            id: 2,
            left: 'Sharing',
            percent: ((sharingCount / sharingLimit) * 100).toFixed(1),
            right: `${sharingCount} / ${sharingLimit} Shared`
          },
          {
            id: 3,
            left: 'Showcase',
            percent: ((showcaseCount / showcasesLimit) * 100).toFixed(1),
            right: `${showcaseCount} / ${showcasesLimit} Showcased`
          }
        ]
      });
    }
  }, [data]);

  return (
    <Card className="mt-3 p-2" style={{ borderRadius: '10px' }}>
      <CardHeader className="p-3 headerBorder">
        <p className="m-0" style={{ fontSize: '18px' }}>
          <b>
            <IntlMessages id="Usage Details" />
          </b>
        </p>
      </CardHeader>
      <CardBody className="subscriptionDetailsCards">
        <div className="AccCard p-2">
          {details?.left?.map((d) => (
            <UsageDetailsCard key={d.id} data={d} />
          ))}
        </div>
        <div className="AccCard p-2">
          {details?.right?.map((d) => (
            <UsageDetailsCard key={d.id} data={d} />
          ))}
        </div>
      </CardBody>
    </Card>
  );
};

export default NewUsageDetails;
