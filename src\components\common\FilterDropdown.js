import IntlMessages from 'helpers/IntlMessages';
import React, { useEffect, useState } from 'react';
import {
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  UncontrolledDropdown
} from 'reactstrap';

const workflowFilters = [
  { id: 6, status: 11, name: 'review.approved' },
  { id: 7, status: 22, name: 'review.commented' },
  { id: 8, status: 33, name: 'review.assigned' },
  { id: 9, status: 44, name: 'review.rejected' }
];
const storiesListFilters = [
  { id: 1, status: true, name: 'dd-filter-enabled' },
  { id: 2, status: false, name: 'dd-filter-disabled' },
  ...workflowFilters,
  { id: 3, status: null, name: 'ins.all' }
];

const contentFilters = [
  { id: 1, status: 1, name: 'ins.processing' },
  { id: 2, status: 2, name: 'ins.ready' },
  { id: 3, status: 3, name: 'ins.draft' },
  { id: 4, status: 4, name: 'ins.failed' },
  ...workflowFilters,
  { id: 5, status: null, name: 'ins.all' }
];

function FilterDropdown({ setFilter, className, isStoriesList, filter }) {
  const [filterName, setFilterName] = useState('ins.all');

  useEffect(() => {
    if (!filter) {
      setFilterName('ins.all');
    }
  }, [filter]);

  const filters = isStoriesList ? storiesListFilters : contentFilters;

  return (
    <UncontrolledDropdown
      style={{ width: '170px' }}
      className={`dropdown-menu-right ${className}`}
    >
      <DropdownToggle
        caret
        color="empty"
        className="btn-sm w-full bt-trans border-0 header-icon"
        outline
      >
        <FilterIcon className="mr-2" />
        <span>
          <IntlMessages id={filterName} />
        </span>
      </DropdownToggle>
      <DropdownMenu center className="dropmenuheightcont overflow-auto">
        {filters.map((f) => (
          <DropdownItem
            key={f.id}
            onClick={() => {
              setFilter(f.status);
              setFilterName(f.name);
            }}
          >
            <IntlMessages id={f.name} />
          </DropdownItem>
        ))}
      </DropdownMenu>
    </UncontrolledDropdown>
  );
}

const FilterIcon = ({ className }) => {
  return (
    <span className={className}>
      <svg
        width="14"
        height="10"
        viewBox="0 0 14 10"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M6.25 9.5H7.75C8.1625 9.5 8.5 9.1625 8.5 8.75C8.5 8.3375 8.1625 8 7.75 8H6.25C5.8375 8 5.5 8.3375 5.5 8.75C5.5 9.1625 5.8375 9.5 6.25 9.5ZM0.25 1.25C0.25 1.6625 0.5875 2 1 2H13C13.4125 2 13.75 1.6625 13.75 1.25C13.75 0.8375 13.4125 0.5 13 0.5H1C0.5875 0.5 0.25 0.8375 0.25 1.25ZM3.25 5.75H10.75C11.1625 5.75 11.5 5.4125 11.5 5C11.5 4.5875 11.1625 4.25 10.75 4.25H3.25C2.8375 4.25 2.5 4.5875 2.5 5C2.5 5.4125 2.8375 5.75 3.25 5.75Z"
          fill="#922C88"
        />
      </svg>
    </span>
  );
};

export default FilterDropdown;
