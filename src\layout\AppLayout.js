/* eslint-disable jsx-a11y/media-has-caption */
/* eslint-disable react/no-unescaped-entities */
/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import { connect, useSelector } from 'react-redux';
import { withRouter } from 'react-router-dom';

import TopNav from 'containers/navs/Topnav';
import Sidebar from 'containers/navs/Sidebar/index';
import { checkifLoggedIn } from 'helpers/Utils';
import useScreenRecorder from 'hooks/useScreenRecorder';
import RecNUploadModal from 'components/content/upload/RecNUploadModal';
import { CAMERA_POSITION_TYPES } from 'constants/recording';

const AppLayout = ({
  containerClassnames,
  children,
  history,
  subscriptionDetails,
  expiredSubscriptions,
  roleType,
  ...props
}) => {
  const [isSideBarVisible, setIsSideBarVisible] = useState(false);
  const isLoggedIn = checkifLoggedIn();
  const { daysUntillExpiry, recordingEnabled } = useSelector(
    (state) => state.subscription
  );
  const weShowRibbon = daysUntillExpiry.days <= 7;
  const isEvent = true;

  useEffect(() => {
    if (window.innerWidth >= 768) {
      const IsSideBarInActive = containerClassnames.includes('main-hidden');
      setIsSideBarVisible(IsSideBarInActive);
    } else {
      const IsSideBarInActive = containerClassnames.includes(
        'main-show-temporary'
      );
      setIsSideBarVisible(!IsSideBarInActive);
    }
  }, [containerClassnames]);

  const mediaWrapperRef = useRef(null);
  const [isRecModal, setIsRecModal] = useState(false);
  const [audio, setAudio] = useState(false);
  const [type, setType] = useState(1);
  const previewCamRef = useRef(null);
  const [selectedCamera, setSelectedCamera] = useState(null);
  const [selectedMicrophone, setSelectedMicrophone] = useState(null);
  const [cameraPos, setCameraPos] = useState(CAMERA_POSITION_TYPES.BL);

  useEffect(() => {
    mediaWrapperRef.current = document.getElementById('screen-recording');
  }, []);

  const {
    blobUrl,
    startRecording,
    blob,
    duration: screenRecordingDuration,
    isStarted,
    stopRecording,
    resetScreenRecorderData = () => {},
    isStopped,
    isPaused,
    isResumed,
    pauseRecording,
    resumeRecording,
    deleteRecording,
    currentDuration
  } = useScreenRecorder({
    audio,
    selectedMicrophone,
    selectedCamera,
    onStop: () => {
      if (!isRecModal) {
        setIsRecModal(true);
      }
      // stopCamera();
      setAudio(false);
    },
    onStart: () => {
      setIsRecModal(false);
    },
    mediaWrapperRef,
    cameraPos
  });

  const toggleModal = () => {
    if (!isStarted) {
      setIsRecModal(!isRecModal);
    } else {
      setIsRecModal(true);
      stopRecording();
    }
  };

  const toggleAudioRecording = () => {
    setType(1);
    toggleModal();
  };
  const toggleVideoRecording = () => {
    setType(2);
    toggleModal();
  };
  const toggleScreenRecording = () => {
    setType(3);
    toggleModal();
    resetScreenRecorderData();
  };

  const recordingProps = {
    toggleScreenRecording,
    toggleVideoRecording,
    toggleAudioRecording,
    toggleModal,
    blobUrl,
    startRecording,
    blob,
    duration: screenRecordingDuration,
    isStarted,
    stopRecording,
    resetScreenRecorderData,
    isStopped,
    isPaused,
    isResumed,
    pauseRecording,
    resumeRecording,
    mediaWrapperRef,
    isRecModal,
    audio,
    type,
    previewCamRef,
    selectedCamera,
    selectedMicrophone,
    setIsRecModal,
    setAudio,
    setType,
    setSelectedCamera,
    setSelectedMicrophone,
    deleteRecording,
    currentDuration
  };

  return (
    <>
      <div id="app-container" className={containerClassnames}>
        <TopNav
          history={history}
          recordingProps={recordingProps}
          expiredSubscription={expiredSubscriptions}
          subscriptionDetails={subscriptionDetails}
          roleType={roleType}
          {...props}
        />
        {isLoggedIn && (
          <Sidebar
            {...props}
            history={history}
            roleType={roleType}
            isEvent={isEvent}
            recordingEnabled={recordingEnabled}
            recordingProps={recordingProps}
            isSideBarVisible={isSideBarVisible}
            subscriptionDetails={subscriptionDetails}
            expiredSubscription={expiredSubscriptions}
          />
        )}
        <main
          className="position-relative"
          style={{
            marginLeft: !isLoggedIn && '20px',
            paddingTop: weShowRibbon && '1%',
            marginRight: '1%'
          }}
        >
          <div className="container-fluid">{children}</div>
        </main>
      </div>
      {isRecModal && (
        <RecNUploadModal
          modalOpen={isRecModal}
          toggleModal={() => {
            setIsRecModal(!isRecModal);
          }}
          isCameraStarted={() => {}}
          stopCamera={() => {}}
          startCamera={() => {}}
          screenRecordingDuration={screenRecordingDuration}
          isStarted={isStarted}
          audio={audio}
          setAudio={setAudio}
          startRecording={startRecording}
          stopRecording={stopRecording}
          blobUrl={blobUrl}
          blob={blob}
          type={type}
          roleType={roleType}
          resetScreenRecorderData={resetScreenRecorderData}
          isRecordingStopped={isStopped}
          setType={setType}
          setSelectedCamera={setSelectedCamera}
          setSelectedMicrophone={setSelectedMicrophone}
          selectedMicrophone={selectedMicrophone}
          selectedCamera={selectedCamera}
          cameraPos={cameraPos}
          setCameraPos={setCameraPos}
        />
      )}
      <video
        ref={previewCamRef}
        height={100}
        width={100}
        muted={false}
        style={{ display: 'none' }}
        autoPlay
      />
    </>
  );
};
const mapStateToProps = ({ menu }) => {
  const { containerClassnames } = menu;
  return { containerClassnames };
};
const mapActionToProps = {};

export default withRouter(
  connect(mapStateToProps, mapActionToProps)(AppLayout)
);
