import React, { useState, useRef } from 'react';
import { Card, CardBody, Button, Popover, PopoverBody } from 'reactstrap';
import IntlMessages from 'helpers/IntlMessages';
import useOnClickOutside from 'hooks/useOnClickOutSide';
import StoryIcon from 'constants/StoryIcon';

const DashboardCard = ({
  className = 'mb-4',
  info,
  icon,
  cardTitle,
  numberValue
}) => {
  const cardRef = useRef(null);
  const [popoverOpen, setPopoverOpen] = useState(false);
  // eslint-disable-next-line no-plusplus
  const id = info.split('.')[1];

  useOnClickOutside(cardRef, () => setPopoverOpen(false));

  return (
    <div ref={cardRef}>
      <div className={`icon-row-item ${className}`}>
        <span className="info">
          <Button
            className="mr-1 mb-2"
            id={`popover_${id}`}
            onClick={() => setPopoverOpen(true)}
          >
            <i className="simple-icon-info font-weight-bold" />
          </Button>
          <Popover
            placement="top"
            isOpen={popoverOpen}
            target={`popover_${id}`}
            toggle={() => setPopoverOpen(!popoverOpen)}
          >
            <PopoverBody>
              <IntlMessages id={info} />
            </PopoverBody>
          </Popover>
        </span>
        <Card className="spaces-card h-75">
          <CardBody className="text-center d-flex flex-column align-content-center" style={{ padding: "0.8rem" }}>
            <span className='d-flex align-items-center gap-2 w-100 justify-content-center'>
              {
                // eslint-disable-next-line no-nested-ternary
                icon === 'iconsminds-film-video' ? <i> <i style={{ position: "relative", top: "-2px" }}><StoryIcon width={24} active /></i></i> : icon === 'simple-icon-earphones' ? <i className={[icon]} style={{ fontSize: "20px", marginRight: '4px', lineHeight: '42px', paddingBottom: '5px' }} /> : icon === 'iconsminds-gift-box' ? <i className={[icon]} style={{ position: "relative", top: "-3px" }} /> : <i className={[icon]} style={{ font: "22px" }} />}
              {/* <i className={[icon]} style={{ font: "22px" }} /> */}
              <p className=" font-weight-semibold font-large mb-0 mt-0">
                <IntlMessages id={cardTitle} />
              </p>
            </span>
            <p className="lead text-center font-weight-lightbold mb-0">{numberValue ?? 0}</p>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default React.memo(DashboardCard);
