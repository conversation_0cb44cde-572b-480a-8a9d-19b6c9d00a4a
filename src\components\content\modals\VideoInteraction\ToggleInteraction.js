import React, { useState } from 'react';
import Switch from 'rc-switch';
import { updateContent } from 'functions/api/spacesApi';
import { NotificationManager } from 'components/common/react-notifications';
import IntlMessages from 'helpers/IntlMessages';
import { Spinner } from 'reactstrap';

const ToggleInteraction = ({ content, getContent, handlePlayerChange }) => {
  const { spaceId, id } = content;
  const [isEnabling, setIsEnabling] = useState(false);
  const [isEnabled, setIsEnabled] = useState(content.enableInteraction);

  const toggleInteraction = async () => {
    try {
      setIsEnabling(true);
      const payload = {
        Id: id,
        enableInteraction: !isEnabled,
        spaceId
      };
      const { isError } = await updateContent(payload, spaceId);
      if (isError) {
        NotificationManager.error(
          <IntlMessages id="something-wrong" />,
          <IntlMessages id="req.error" />
        );
      } else {
        await getContent(true);
        handlePlayerChange();
        const newState = !isEnabled;
        setIsEnabled(newState);
        NotificationManager.success(
          <IntlMessages
            id={newState ? 'medya-ai-enabled' : 'medya-ai-disabled'}
          />
        );
      }
      setIsEnabling(false);
    } catch (error) {
      console.error('Something went wrong in fetchList due to ', error);
      setIsEnabling(false);
    }
  };

  return (
    <div className="enableInPlayer">
      Display Interaction markers on the player &nbsp;
      {isEnabling ? (
        <Spinner size="sm" color="info">
          Loading...
        </Spinner>
      ) : (
        <span>
          <Switch
            aria-disabled={isEnabling}
            className="custom-switch custom-switch-primary custom-switch-small ml-1"
            checked={isEnabled}
            onClick={toggleInteraction}
          />
        </span>
      )}
    </div>
  );
};

export default ToggleInteraction;
