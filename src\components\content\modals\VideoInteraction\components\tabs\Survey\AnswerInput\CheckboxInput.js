/* eslint-disable no-unused-vars */
/* eslint-disable react/no-array-index-key */
import React from 'react';
import TextBoxInput from './TextBoxInput';
import Remove from '../../../Remove';

const CheckboxInput = ({ values, index, setPolls }) => {
  const handleInputchange = (e, valIndex) => {
    const text = e?.target?.value;
    const val = text.substring(0, 30);

    setPolls((prev) => {
      const newPolls = prev.map((m, i) => {
        if (index - 1 === i) {
          return {
            ...m,
            values: values.map((mapVal, i) => {
              if (i === valIndex) {
                return {
                  value: val
                };
              }
              return mapVal;
            })
          };
        }
        return m;
      });
      return newPolls;
    });
  };

  const handleAddOption = () => {
    setPolls((prev) => {
      const newPolls = prev.map((m, i) => {
        if (index - 1 === i) {
          return {
            ...m,
            values: [
              ...values,
              {
                value: ''
              }
            ]
          };
        }
        return m;
      });
      return newPolls;
    });
  };

  const handleRemove = (valueIndex) => {
    setPolls((prev) => {
      const newPolls = prev.map((f, i) => {
        if (index - 1 === i) {
          const updatedValues = f.values.filter((v, j) => j !== valueIndex);
          return {
            ...f,
            values: updatedValues
          };
        }
        return f;
      });
      return newPolls;
    });
  };

  return (
    <div>
      <div className="checkboxInput">
        {values.map((v, i) => (
          <div
            key={i}
            className="d-flex flex-row align-items-center gap-2"
            style={{ width: '49%' }}
          >
            <TextBoxInput
              maxCharLimit={30}
              label=""
              className="w-full-imp"
              isTextArea={false}
              value={v.value}
              placeholder=""
              onChange={(e) => handleInputchange(e, i)}
            />
            {values.length > 2 && (
              <Remove
                onClick={() => {
                  handleRemove(i);
                }}
              />
            )}
          </div>
        ))}
      </div>
      {values.length < 4 && (
        <button
          type="button"
          color="primary"
          onClick={handleAddOption}
          style={{
            background: 'transparent',
            borderRadius: 10,
            color: '#992288',
            fontSize: 12,
            width: 'auto',
            padding: '4px 10px'
          }}
          className="dotted-border-primary-box mb-4 mt-3"
        >
          Add Option
        </button>
      )}
    </div>
  );
};

export default CheckboxInput;
