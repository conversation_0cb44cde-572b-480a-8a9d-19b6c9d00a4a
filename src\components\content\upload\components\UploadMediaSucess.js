/* eslint-disable no-nested-ternary */
import React, { useEffect } from 'react';
import { NavLink } from 'react-router-dom';
import { Colxx } from 'components/common/CustomBootstrap';
import { spaceHomeRoute } from 'constants/defaultValues';
import IntlMessages from 'helpers/IntlMessages';

const UploadMediaSuccess = ({
  setProgress,
  toggleModal,
  spaceId,
  MediaType,
  isRecording,
  isYoutube,
  optimize
}) => {
  useEffect(() => {
    return setProgress(0);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const to = (() => {
    if (!MediaType) {
      return `${spaceHomeRoute}/space/${spaceId}/manage-content`;
    }
    if (!isRecording) {
      return `${spaceHomeRoute}/space/${spaceId}/manage-content${
        MediaType ? `?tabIndex=${MediaType}` : ''
      }`;
    }
    return `${spaceHomeRoute}/space/${spaceId}/manage-content${
      MediaType ? `?tabIndex=${MediaType}&triggerRefresh=true` : ''
    }`;
  })();

  return (
    <div className="up-sucess-container">
      <div className="up-content-container">
        <div className="upload-success-icon-container">
          <i className="simple-icon-check upload-success-icon" />
        </div>
        <div className="px-3">
          <p style={{ fontSize: '17px' }}>
            <IntlMessages
              id={
                isYoutube
                  ? 'UploadMediaSuccess-yt-info'
                  : optimize
                  ? 'UploadMediaSuccess-info'
                  : 'UploadMediaSuccess-opt-false-info'
              }
            />
          </p>
        </div>
        <Colxx xxs="12" className="d-flex justify-content-center">
          <NavLink
            className="btn-link"
            to={to}
            location={{}}
            onClick={() => toggleModal()}
          >
            <IntlMessages id="UploadMediaSuccess-save" />
          </NavLink>
        </Colxx>
      </div>
    </div>
  );
};

export default UploadMediaSuccess;
