/* eslint-disable jsx-a11y/aria-proptypes */
/* eslint-disable react/no-array-index-key */
import React, { useMemo } from 'react';
import QuesAnsPreview from '../../components/tabs/Survey/SurveyPreview/QuesAnsPreview';

const SurveyDetails = ({ data, surveyQuestions }) => {
  const eventData = JSON.parse(data.eventdata);

  const formattedSurveys = useMemo(() => {
    return surveyQuestions.map((m) => {
      const answers = eventData.find(
        (f) => f.type === m.type && m.question === f.question
      );
      return {
        question: m.question,
        responseType: m.type,
        values: m.options.map((j) => ({
          value: j
        })),
        answer: answers.answer
      };
    });
  }, [eventData, surveyQuestions]);

  return (
    <div className="SurveyPreview">
      <h1>Survey Details</h1>
      <div className="SPCard">
        <div className="SDBody SPCardBody">
          {formattedSurveys.map((m, id) => (
            <QuesAnsPreview key={id} data={m} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default SurveyDetails;
