/* eslint-disable no-unused-vars */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import IntlMessages from 'helpers/IntlMessages';
import React, { useState, memo, useEffect } from 'react';
import { Modal, Row, Col, ModalBody, Card, Button } from 'reactstrap';
import Event from './Event';
import ShareNow from './ShareNow';
import { useHistory } from 'react-router-dom';
import { getCurrentColor } from 'helpers/Utils';
import { deleteShare, getShares } from 'functions/api/spacesApi';
import { NotificationManager } from 'components/common/react-notifications';
import BackButton from 'components/buttons/BackButton';

function ShareModal({
  modalOpen,
  toggleModal,
  isYtVimeoVideo,
  MediaType,
  spaceId,
  EntityKind,
  entity,
  setShareChanged = () => {},
  handleDelete = (entityId, shareId) => {},
  handleCreate = (entityId, shareId) => {}
}) {
  const [isShareNow, setIsShareNow] = useState(false);
  const [next, setNext] = useState('');
  const isShared = !!entity?.shareId;
  const [sharingData, setSharingData] = useState(null);
  const [isLoading, setIsLoading] = useState(isShared);
  const [isRemoving, setIsRemoving] = useState(false);
  const [isEventEditing, setIsEventEditing] = useState(false);
  const [isShareNowEditing, setIsShareNowEditing] = useState(false);
  const [isHovered, setIsHovered] = useState(null);

  const showBackButton = !sharingData && next && (!isRemoving || !isLoading);

  console.log('entity', entity);

  const handleToggle = () => {
    setIsShareNow(false);
    setNext('');
    setSharingData(null);
    setIsLoading(true);
    toggleModal();
  };

  const getShareDetails = async (shareId) => {
    try {
      console.log('fetching share details of', shareId);
      setIsLoading(true);
      const { data, isError } = await getShares(entity.spaceId, shareId);
      if (!isError && data) {
        setSharingData(data);
        if (data.shareAlways) {
          setNext('NOW');
        } else {
          setNext('EVENT');
        }
      }
      if (data?.message === 'SHARE_DOES_NOT_EXIST') {
        handleDelete(entity.id);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      console.log('error', error);
    }
  };

  const handleUpdateEntity = (id, fetchAgain = false) => {
    handleCreate(entity.id, id);
    setShareChanged({ ...entity, shareId: id });
    if (fetchAgain) {
      getShareDetails(id);
    }
  };

  useEffect(() => {
    if (entity?.spaceId && entity?.shareId) {
      getShareDetails(entity?.shareId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [entity]);

  const handleRemove = async () => {
    try {
      setIsRemoving(true);
      const remove = await deleteShare(entity.spaceId, sharingData.id);
      setIsRemoving(false);
      if (remove.isError) {
        setIsRemoving(false);
        NotificationManager.error(
          <IntlMessages id="share.error-msg" />,
          <IntlMessages id="share.remove-error" />,
          3000,
          null,
          null,
          ''
        );
      } else {
        handleDelete(entity?.id, sharingData.id);
        setIsLoading(false);
        NotificationManager.success(
          <IntlMessages id="share.sucess" />,
          <IntlMessages id="share.remove-success" />,
          3000,
          null,
          null,
          ''
        );
        handleToggle();
      }
    } catch (error) {
      setIsRemoving(false);
      console.log('error', error);
    }
  };

  const handleSchedule = () => {
    setNext('EVENT');
    setIsEventEditing(true);
  };

  const handleShareNow = () => {
    setNext('NOW');
    setIsShareNowEditing(true);
  };

  const commonProps = {
    MediaType,
    isYtVimeoVideo,
    spaceId,
    sharingData,
    isShared,
    title: entity?.title,
    handleRemove,
    entityId: entity?.id,
    entityKind: EntityKind,
    isRemoving,
    isEventEditing,
    isShareNowEditing,
    getShareDetails,
    handleUpdateEntity,
    setSharingData,
    showSelectPlayer: MediaType !== 3 && !isYtVimeoVideo,
    isDRM: entity?.isDRM
  };

  return (
    <Modal
      isOpen={modalOpen}
      toggle={handleToggle}
      contentClassName="border-radius-10"
      centered
      style={{
        minWidth: next === 'EVENT' ? '70%' : '50%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        borderRadius: 20
      }}
      backdrop="static"
    >
      <ModalBody
        className="m-3 border-radius-10 d-flex align-items-center flex-column"
        style={{ border: '1px solid #992288' }}
      >
        <ModalHeader
          toggleModal={handleToggle}
          title={entity?.title}
          showBackButton={showBackButton}
          setNext={setNext}
        />
        {isLoading ? (
          <div className="dataloading-overlay detailed-content">
            <div className="dataloading" />
          </div>
        ) : (
          <div aria-disabled={isLoading || isRemoving} className="w-full">
            {!next && (
              <Col>
                <Row className="align-items-center justify-content-around my-4">
                  <SelectCard
                    isActive={isShareNow}
                    isHovered={isHovered}
                    type="NOW"
                    setIsHovered={setIsHovered}
                    onClick={() => setNext('NOW')}
                    imgSrc="/assets/img/share/share-link.svg"
                    title="Share Now"
                    description="Share your content immediately across all selected platforms."
                  />
                  <SelectCard
                    isActive={!isShareNow}
                    isHovered={isHovered}
                    type="EVENT"
                    setIsHovered={setIsHovered}
                    onClick={() => setNext('EVENT')}
                    imgSrc="/assets/img/share/calender.svg"
                    title="Schedule an Event"
                    description="Schedule an event with start and end dates."
                  />
                </Row>
              </Col>
            )}
            {next === 'NOW' && (
              <ShareNow
                {...commonProps}
                isEditing={isShareNowEditing}
                setIsEditing={setIsShareNowEditing}
                handleSchedule={handleSchedule}
              />
            )}
            {next === 'EVENT' && (
              <Event
                {...commonProps}
                isEditing={isEventEditing}
                setIsEditing={setIsEventEditing}
                handleShareNow={handleShareNow}
              />
            )}
          </div>
        )}
      </ModalBody>
    </Modal>
  );
}

const ModalHeader = ({ toggleModal, title, showBackButton, setNext }) => {
  return (
    <Row className="d-flex align-items-start justify-content-between w-full">
      <Row className="d-flex align-items-center w-95">
        {showBackButton && <BackButton onClick={() => setNext(null)} />}
        <Col className="p-0">
          <h2 className="mb-0 font-bolder">{title}</h2>
          <p className="text-primary m-0">
            Share your content with a click. Get a unique URL and embed code to
            reach a wider audience
          </p>
        </Col>
      </Row>
      <span onClick={toggleModal} className="w-5">
        <i className="simple-icon-close close-btn" />
      </span>
    </Row>
  );
};

const SelectCard = ({
  imgSrc,
  title,
  description,
  onClick,
  isActive,
  isHovered,
  setIsHovered,
  type
}) => {
  return (
    <Card
      onClick={onClick}
      onMouseEnter={() => setIsHovered(type)}
      onMouseLeave={() => setIsHovered(null)}
      style={{
        background: isHovered === type ? '#FFDAFB' : '',
        width: 225,
        height: 300,
        color: isHovered === type ? '#000' : ''
      }}
      className="p-3 c-pointer border-radius-10 flex-column align-items-center"
    >
      <div>
        <img
          className="p-3"
          alt="Select card"
          height={180}
          width="100%"
          src={imgSrc}
        />
      </div>
      <h3 className="text-center my-1 font-bolder">
        <IntlMessages id={title} />
      </h3>
      <p
        className="text-center m-0"
        style={{ fontSize: 12, lineHeight: '14px' }}
      >
        <IntlMessages id={description} />
      </p>
    </Card>
  );
};

export default ShareModal;
