/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable jsx-a11y/label-has-for */
import React from 'react';
import { Row } from 'reactstrap';
import { Colxx } from 'components/common/CustomBootstrap';
import IntlMessages from 'helpers/IntlMessages';

import { SliderTooltip } from 'components/common/SliderTooltips';

export default function MediaQuality({ mediaScore, setMediaScore, DRMStatus }) {
  return (
    <Row className="spaces-media-processing-row">
      <Colxx xxs="12" sm="6">
        <label className="media-label-title">
          <IntlMessages id="spaces.mediaProcessingQuality" />
        </label>
      </Colxx>
      <Colxx xxs="12" sm="6">
        {!DRMStatus && <div className="slidercont">
          <span>Lowest</span>
          <span>Highest</span>
        </div>}
        {DRMStatus ? (
          'Auto'
        ) : (
          <SliderTooltip
            min={0}
            max={5}
            defaultValue={mediaScore}
            className="mb-5"
            step={1}
            dots
            dotStyle={{
              borderColor: 'purple',
              innerHeight: '3px',
              outerHeight: '3.5px',
              marginRight: '1px',
              marginBottom: '2px',
            }}
            setMediaScore={setMediaScore}
          />
        )}
      </Colxx>
    </Row>
  );
}
