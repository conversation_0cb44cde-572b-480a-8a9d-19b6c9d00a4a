/* eslint-disable react/no-array-index-key */
/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import IntlMessages from 'helpers/IntlMessages';
import { Colxx } from 'components/common/CustomBootstrap';
import { But<PERSON>, Card, Progress } from 'reactstrap';
import { NotificationManager } from 'components/common/react-notifications';
import ConfirmationModal from 'components/common/ConfirmationModal';
import { completedFirstTimeLogin, isFirstTimeLogin } from 'helpers/Utils';
import UploadMediaSuccess from './UploadMediaSucess';
import { URLTypeNameEnum } from 'data/urlTypes';

export default function ProgressBar({
  progress,
  setProgress,
  setUploading,
  AbortUpload = () => {},
  reload = () => {},
  toggleModal,
  message,
  spaceId,
  MediaType,
  isRecording,
  imagesUploadStatus,
  setImagesUploadStatus,
  isImgUploaded,
  remoteFileUrl,
  type,
  optimize
}) {
  const [showUploadSuccess, setShowUploadSuccess] = useState(false);
  const [progressMsg, setProgressMsg] = useState('progress.uploading');
  const [openConfirmationModal, setOpenConfirmationModal] = useState(false);

  const toggleConfirmationModal = () => {
    setOpenConfirmationModal(!openConfirmationModal);
  };

  useEffect(() => {
    if (
      remoteFileUrl?.length > 0 &&
      (type === URLTypeNameEnum.YOUTUBE || type === URLTypeNameEnum.VIMEO)
    ) {
      setProgressMsg('progress.processing');
    }
    if (MediaType !== 3) {
      if (progress.toFixed(0) > 99) {
        setTimeout(() => {
          if (!isFirstTimeLogin()) reload(null, 'refresh');
          else completedFirstTimeLogin();
          setProgressMsg('progress.uploadComplete');
          setShowUploadSuccess(true);
          NotificationManager.success(
            message ?? <IntlMessages id="upload-sucess" />,
            <IntlMessages id="req.success" />
          );
        }, 2000);
      } else if (optimize && progress.toFixed(0) > 89) {
        setProgressMsg('progress.encoding');
      } else if (progress.toFixed(0) < 0) {
        if (!isFirstTimeLogin()) reload(null, 'refresh');
        else completedFirstTimeLogin();
      }
    }
    if (isImgUploaded && MediaType === 3) {
      setShowUploadSuccess(true);
    }
    // eslint-disable-next-line
  }, [progress, MediaType, isImgUploaded, remoteFileUrl, type, optimize]);

  const handleConfirm = () => {
    AbortUpload();
    setProgress(0);
    setUploading(false);
  };

  const handleClose = () => {
    toggleConfirmationModal();
  };

  return showUploadSuccess ? (
    <UploadMediaSuccess
      setProgress={setProgress}
      toggleModal={toggleModal}
      spaceId={spaceId}
      isYoutube={
        remoteFileUrl?.length > 0 &&
        (type === URLTypeNameEnum.YOUTUBE || type === URLTypeNameEnum.VIMEO)
      }
      MediaType={MediaType}
      isRecording={isRecording}
      optimize={optimize}
    />
  ) : (
    <>
      <ConfirmationModal
        openConfirmationModal={openConfirmationModal}
        toggleConfirmationModal={toggleConfirmationModal}
        handleConfirm={handleConfirm}
        handleClose={handleClose}
        isPortal
      />
      <div className="progressBar-container-house">
        <div className="dashboard-media-upload-container progressBar-container">
          <Colxx xxs="12" className="progress-status mb-2">
            <IntlMessages id={progressMsg} />
          </Colxx>
          <div>
            {MediaType === 3 ? (
              <>
                {imagesUploadStatus.map((m, i) => (
                  <Card
                    key={i}
                    className="d-flex mb-2 flex-row p-2 align-items-center justify-content-center"
                  >
                    <img
                      height={50}
                      width={80}
                      alt=""
                      src={URL.createObjectURL(m.imageFile)}
                    />
                    <div className="d-flex flex-column w-full px-2 align-items-center justify-content-center">
                      <Progress
                        animated
                        striped
                        max="100"
                        value={m.progress}
                        className="innerloop-progress-bar"
                      />
                      <p
                        className="mb-0 w-full text-center"
                        style={{ fontSize: 12 }}
                      >
                        {m.FileName}
                      </p>
                    </div>
                  </Card>
                ))}
              </>
            ) : (
              <>
                <Progress
                  animated
                  striped
                  max="100"
                  value={progress}
                  className="innerloop-progress-bar"
                />
                <p className="mt-2 progress-number">{`${progress?.toFixed(
                  0
                )} %`}</p>
              </>
            )}
            <Button
              color="primary"
              outline
              onClick={() => toggleConfirmationModal()}
              className="upload-cancel-btn"
            >
              <IntlMessages id="spaces.cancel" />
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
