import ConfirmationModal from 'components/common/ConfirmationModal';
import React from 'react';

function DeleteAiDescriptionModal({ openModal, toggleModal, handleConfirm }) {
  const handleClose = () => {
    toggleModal();
  };

  return (
    <ConfirmationModal
      openConfirmationModal={openModal}
      toggleConfirmationModal={toggleModal}
      handleConfirm={handleConfirm}
      handleClose={handleClose}
      type="deleteAIdesc"
    />
  );
}

export default DeleteAiDescriptionModal;
