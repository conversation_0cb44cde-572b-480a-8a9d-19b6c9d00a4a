/* eslint-disable react/no-array-index-key */
/* eslint-disable no-unused-vars */
import { updateContent, updateStory } from 'functions/api/spacesApi';
import { NotificationManager } from 'components/common/react-notifications';
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import {
  Button,
  FormGroup,
  Label,
  Modal,
  ModalBody,
  UncontrolledDropdown,
  DropdownToggle,
  DropdownMenu,
  DropdownItem
} from 'reactstrap';
import ReactQuill from 'react-quill';
import InfiniteScroll from 'react-infinite-scroll-component';
import IntlMessages from 'helpers/IntlMessages';
import { createFlag, getFlag } from 'functions/api/flagApi';
import { nanoid } from 'nanoid';
import Avtar from 'components/common/Avtar';
import { formatDate, getTeamUserRoleType, removeHtmlTags } from 'helpers/Utils';

const Types = [
  { id: 1, name: 'Sexual Content' },
  { id: 2, name: 'Violent or repulsive content' },
  { id: 3, name: 'Hateful or abusive' },
  { id: 4, name: 'Harmful or dangerous acts' },
  { id: 5, name: 'Misinformation' },
  { id: 6, name: 'Child abuse' },
  { id: 7, name: 'Promotes terrorism' },
  { id: 8, name: 'Spam or misleading' },
  { id: 9, name: 'Infringes my rights' },
  { id: 10, name: 'Caption issue' },
  { id: 11, name: 'None of these are my issues' }
];

const FlagModal = ({
  modalOpen,
  toggleModal,
  entity,
  spaceId,
  isStory,
  setList,
  list
}) => {
  const [selectIssue, setSelectIssue] = useState('');
  const [comments, setComments] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [activities, setActivities] = useState([]);
  const { uid: userId } = JSON.parse(
    localStorage.getItem('innerloop_current_user')
  );
  const tenantDetails = JSON.parse(localStorage.getItem('tenantDetails'));
  const email = tenantDetails?.data?.selectedTenant?.ownerEmail;

  const handleReset = () => {
    setSelectIssue('');
    setComments('');
    setActivities([]);
    setIsLoading(false);
  };

  const handleToggleModal = () => {
    handleReset();
    toggleModal();
  };

  const handleCreateFlag = async () => {
    setIsLoading(true);
    const payload = {
      id: nanoid(),
      entityKind: isStory ? 2 : 1,
      entityId: entity.id,
      messages: comments,
      flagType: selectIssue
    };
    const { data, isError } = await createFlag(spaceId, payload, email);
    if (data && !isError) {
      if (list && data.id) {
        if (!isStory) {
          const newList = list.content.map((l) => {
            if (l.id === entity.id) {
              return {
                ...l,
                flagId: data.id,
                isFlagged: true
              };
            }
            return l;
          });
          setList({ ...list, content: newList });
        } else {
          const newList = list.map((l) => {
            if (l.id === entity.id) {
              return {
                ...l,
                flagId: data.id,
                isFlagged: true
              };
            }
            return l;
          });
          setList(newList);
        }
      }
      NotificationManager.success(
        'Content has been flagged Successfully',
        'Success',
        3000,
        null,
        null
      );
    }
    if (isError) {
      NotificationManager.success(
        'Failed to flag the Content',
        'Error',
        3000,
        null,
        null
      );
    }
    setIsLoading(false);
    handleToggleModal();
  };

  const handleGetFlag = async () => {
    setIsLoading(true);
    const { data, isError } = await getFlag(spaceId, entity.id);
    if (data && !isError) {
      if (data?.items) {
        const flaggedData = data?.items;
        setActivities(flaggedData);
        const myFlag = flaggedData.find((f) => f.creatorId === userId);
        if (myFlag) {
          setSelectIssue(myFlag.flagType);
          setComments(myFlag?.messages);
        }
      }
    }
    if (isError) {
      NotificationManager.warning(
        'Failed to flag the Content',
        'Error',
        3000,
        null,
        null
      );
      handleToggleModal();
    }
    setIsLoading(false);
  };

  useEffect(() => {
    if (entity?.flagId && modalOpen) {
      handleGetFlag();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [entity?.flagId, modalOpen]);

  const handleButtonDisable =
    selectIssue.length === 0 ||
    removeHtmlTags(comments).length === 0 ||
    isLoading;

  return (
    <Modal
      isOpen={modalOpen}
      toggle={handleToggleModal}
      centered
      style={{
        minWidth: entity?.flagId ? '80%' : '40%'
      }}
      backdrop="static"
    >
      {isLoading ? (
        <div className="loading"> </div>
      ) : (
        <ModalBody className="d-flex flex-column pt-0 dotted-border m-4 p-3">
          <div className="d-flex flex-row w-full align-align-items-center justify-content-between">
            <span>
              <h4>
                <b>
                  <IntlMessages id="flag-content" />
                </b>
              </h4>
            </span>
            <Button
              color="white"
              type="button"
              className="d-flex justify-content-end p-2"
              onClick={handleToggleModal}
            >
              <i className="simple-icon-close close-btn" />
            </Button>
          </div>
          <div className="d-flex flex-row w-full">
            <div
              style={{
                width: entity?.flagId ? '50%' : '100%',
                padding: '30px'
              }}
            >
              <div className="d-flex flex-column mt-4">
                <UncontrolledDropdown>
                  <DropdownToggle
                    caret
                    color="primary"
                    className={`btn-sm w-full ${
                      !!entity?.flagId && 'cursor-notallowed'
                    }`}
                    outline={selectIssue.length}
                    disabled={!!entity?.flagId}
                  >
                    {selectIssue.length > 0 ? (
                      selectIssue
                    ) : (
                      <IntlMessages id="flag-select" />
                    )}
                  </DropdownToggle>
                  <DropdownMenu center className="w-full">
                    <InfiniteScroll
                      dataLength={Types.length}
                      hasMore={false}
                      height={170}
                    >
                      {Types.map((p) => (
                        <DropdownItem
                          key={p.id}
                          onClick={() => {
                            setSelectIssue(p.name);
                          }}
                        >
                          <p
                            style={{
                              marginBottom: '-1px',
                              whiteSpace: 'normal'
                            }}
                          >
                            <b>{p.name}</b>
                          </p>
                        </DropdownItem>
                      ))}
                    </InfiniteScroll>
                  </DropdownMenu>
                </UncontrolledDropdown>
                <FormGroup
                  className="form-group has-float-label mb-3 mt-5"
                  style={{ flex: 1 }}
                  onClick={() => {
                    if (!selectIssue) {
                      NotificationManager.warning(
                        <IntlMessages id="flag-select-err" />,
                        <IntlMessages id="req.warning" />,
                        3000,
                        null,
                        null
                      );
                    }
                  }}
                >
                  <Label for="Industry">
                    <IntlMessages id="flag-add-comments" />
                    <span style={{ color: '#922c88' }}> *</span>
                  </Label>
                  <ReactQuill
                    className={`react-quill w-full-imp ${
                      !!entity?.flagId && 'cursor-notallowed'
                    }`}
                    theme="bubble"
                    readOnly={!!entity?.flagId || !selectIssue}
                    value={comments}
                    onChange={(value) => {
                      if (selectIssue) {
                        setComments(value);
                      }
                    }}
                  />
                </FormGroup>
                <p className="mt-5">
                  <IntlMessages id="flag-content-info" />
                  <Link href="guidelines" className="text-primary">
                    {' '}
                    <IntlMessages id="flag-content-usage" />
                  </Link>
                  <IntlMessages id="flag-serious" />
                </p>
                <div className="d-flex justify-content-center mt-5">
                  {!entity?.flagId && (
                    <Button
                      color="primary"
                      style={{ width: '150px' }}
                      disabled={handleButtonDisable}
                      onClick={handleCreateFlag}
                    >
                      <IntlMessages id="create-flag" />
                    </Button>
                  )}
                  <Button
                    color
                    style={{ width: '150px' }}
                    className="ml-2  badge-outline-primary"
                    onClick={handleToggleModal}
                  >
                    <IntlMessages id="flag-cancel" />
                  </Button>
                </div>
              </div>
            </div>
            {entity?.flagId && (
              <div
                style={{
                  width: '50%',
                  borderLeft: '1px solid #80808045',
                  padding: '30px'
                }}
              >
                <h5>Flagged Activities</h5>
                <div
                  className="w-full"
                  style={{ overflowY: 'scroll', height: '500px' }}
                >
                  {activities.length === 0 ? (
                    <span>No activities</span>
                  ) : (
                    activities.map((f, i) => <Card key={i} data={f} />)
                  )}
                </div>
              </div>
            )}
          </div>
        </ModalBody>
      )}
    </Modal>
  );
};

const Card = ({ data }) => {
  return (
    <div className="d-flex flex-row flagCard">
      <div className="mr-2" style={{ width: '5%', paddingTop: '5px' }}>
        <Avtar size={20} fName={data.creator.fullName} />
      </div>
      <div style={{ width: '95%' }}>
        <div className="d-flex flex-row">
          <span className="flagCardName">
            <b>{data.creator.fullName}</b>
          </span>
          <span>&nbsp; ({getTeamUserRoleType(data.creator.roleType)})</span>
        </div>
        <div className="d-flex flex-row ">
          <span className="flagCardSubname">Issue :</span>
          <span className="flagCardContent">{data.flagType}</span>
        </div>
        <div className="d-flex flex-row">
          <span className="flagCardSubname">Message :</span>
          <span className="flagCardContent">
            {removeHtmlTags(data.messages)}
          </span>
        </div>
        <div className="d-flex flex-row justify-content-end">
          <span className="flagCardDate">
            {formatDate(data.creationDateTime)}&nbsp;
            {new Date(data.creationDateTime).toLocaleTimeString()}
          </span>
        </div>
      </div>
    </div>
  );
};

export default FlagModal;
