/* eslint-disable consistent-return */
/* eslint-disable array-callback-return */
/* eslint-disable no-else-return */
/* eslint-disable no-unused-expressions */
import { NotificationManager } from 'components/common/react-notifications';
import {
  handleSubscribeTenant,
  handleUnSubscribeTenant
} from 'functions/api/showcaseApi';
import IntlMessages from 'helpers/IntlMessages';
import { checkifLoggedIn } from 'helpers/Utils';
import React, { useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';

function useSubscription({
  isAlreadySubscribed,
  onComplete,
  setShowcases,
  showcases,
  tenantId: tId,
  isSubscribedTab
}) {
  const history = useHistory();
  const [isSubscribed, setIsSubscribed] = useState(isAlreadySubscribed);
  const [subscribing, setSubscribing] = useState(false);
  const isLoggedIn = checkifLoggedIn();

  const Subscribe = async (id) => {
    setSubscribing(true);
    const { isError } = await handleSubscribeTenant(id);
    if (!isError) {
      setIsSubscribed(true);
      if (showcases?.length > 0) {
        const updatedShowcases = showcases.map((s) => {
          if (s.tenantId === tId) {
            return {
              ...s,
              isSubscribed: true
            };
          } else {
            return s;
          }
        });
        setShowcases(updatedShowcases);
      }
      NotificationManager.success(
        <IntlMessages id="sm-sub-success" />,
        <IntlMessages id="req.success" />,
        3000,
        null,
        null,
        ''
      );
      onComplete && onComplete();
    }
    setSubscribing(false);
  };

  const Unsubscribe = async (id) => {
    setSubscribing(true);
    const { isError } = await handleUnSubscribeTenant(id);
    if (!isError) {
      setIsSubscribed(false);
      if (showcases?.length > 0) {
        if (!isSubscribedTab) {
          const updatedShowcases = showcases.map((s) => {
            if (s.tenantId === tId) {
              return {
                ...s,
                isSubscribed: false
              };
            } else {
              return s;
            }
          });
          setShowcases(updatedShowcases);
        } else {
          const updatedShowcases = showcases.filter((s) => s.tenantId !== tId);
          setShowcases(updatedShowcases);
        }
      }
      NotificationManager.success(
        <IntlMessages id="sm-unsub-success" />,
        <IntlMessages id="req.success" />,
        3000,
        null,
        null,
        ''
      );
      onComplete && onComplete();
    }
    setSubscribing(false);
  };

  // eslint-disable-next-line no-unused-vars
  const handleSubscription = ({ tenantId, showcaseId, userIdentifier }) => {
    if (isLoggedIn) {
      if (isSubscribed) {
        Unsubscribe(tenantId);
      } else {
        Subscribe(tenantId);
      }
    } else if (showcaseId) {
      history.push(`/user/login?showcase=${showcaseId}`);
    } else if (userIdentifier) {
      history.push(`/user/login?tenantId=${userIdentifier}`);
    } else if (tenantId) {
      history.push(`/user/login?tenantId=${tenantId}`);
    }
  };

  useEffect(() => {
    setIsSubscribed(isAlreadySubscribed);
  }, [isAlreadySubscribed]);

  return {
    handleSubscription,
    isSubscribed,
    subscribing,
    setSubscribing,
    setIsSubscribed
  };
}

export default useSubscription;
