import { formatDate, formatTime } from 'helpers/Utils';
import useOnClickOutside from 'hooks/useOnClickOutSide';
import React, { useRef, useState } from 'react';
import { Popover, PopoverBody } from 'reactstrap';

const ShowcaseExpiryPopup = ({ id, date }) => {
  const ref = useRef();
  const [toggled, setToggled] = useState(false);
  useOnClickOutside(ref, () => setToggled(false));

  const hide = date === 1;
  date = hide ? new Date() : new Date(date);

  const key = `Popover-Expiry-${id}`;
  return (
    <span
      id={key}
      onMouseEnter={() => setToggled(true)}
      onMouseLeave={() => setToggled(false)}
    >
      <i className="iconsminds-sand-watch-2 mr-1" />
      <span ref={ref}>
        <Popover
          placement="bottom"
          isOpen={toggled}
          target={key}
          toggle={() => setToggled(!toggled)}
        >
          <PopoverBody>
            <span>{formatDate(date)}&nbsp;</span>
            <span>{formatTime(date)}</span>
          </PopoverBody>
        </Popover>
      </span>
    </span>
  );
};

export default ShowcaseExpiryPopup;
