import InfoPopover from 'components/InfoPopover';
import React, { useState } from 'react';
import { Card } from 'reactstrap';
import ChatAnalytics from '../../ChatAnalytics';

const ViewAnalytics = ({ contentId, isQna = false }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const id = `popover_view_analytics`;

  return (
    <>
      <span className="viewAnalytics">
        <InfoPopover
          infoId={id}
          iconClassName="font-weight-bold text-primary"
          message="Analytics are generated based on interactions from the shared and showcase views only"
          className="mr-2"
        />
        <Card
          className="vaCard"
          onClick={() => {
            setIsModalOpen(true);
          }}
        >
          View Analytics
          <svg
            width="10"
            height="10"
            viewBox="0 0 15 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M1.25 16.8332V6.1665H3.25V16.8332H1.25ZM6.5 16.8332V1.1665H8.5V16.8332H6.5ZM11.75 16.8332V11.1665H13.75V16.8332H11.75Z"
              stroke="#363636"
            />
          </svg>
        </Card>
      </span>
      {isModalOpen && (
        <ChatAnalytics
          contentId={contentId}
          open={isModalOpen}
          setOpen={setIsModalOpen}
          isQna={isQna}
        />
      )}
    </>
  );
};

export default ViewAnalytics;
