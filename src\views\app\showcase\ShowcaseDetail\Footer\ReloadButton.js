import React, { useState } from 'react';
import { Button } from 'reactstrap';

const ReloadButton = ({ windowWidth, onClick }) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  const handleTouchStart = () => {
    setIsHovered(true);
  };

  const handleTouchEnd = () => {
    setTimeout(() => setIsHovered(false), 300);
  };

  return (
    <Button
      color="primary"
      outline
      className="btn"
      onClick={onClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      style={{
        width: windowWidth < 510 ? '40px' : 'auto',
        height: windowWidth < 510 ? '40px' : 'auto',
        alignItems: 'center',
        justifyContent: 'center',
        display: 'flex',
        gap: '10px',
        border: 'none',
        background: isHovered ? '#992288' : 'transparent',
        color: isHovered ? '#fff' : '#992288',
        fontSize: '0.9rem'
      }}
    >
      <i className="iconsminds-repeat-6" />
      {windowWidth > 510 ? 'Reload' : ''}
    </Button>
  );
};

export default ReloadButton;
