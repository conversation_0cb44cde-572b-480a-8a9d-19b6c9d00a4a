/* eslint-disable prefer-destructuring */
/* eslint-disable no-underscore-dangle */
/* eslint-disable prefer-rest-params */
import React, { useEffect, useRef, useState } from 'react';
import { Chart } from 'chart.js';

import { centerTextPlugin } from './util';
import { doughnutChartOptions } from './config';

const Doughnut = ({ data, shadow = false, small, dimensions }) => {
  const chartContainer = useRef(null);
  const [chartInstance, setChartInstance] = useState(null);

  useEffect(() => {
    let newChartInstance = null;
    if (chartContainer && chartContainer.current) {
      if (chartInstance) {
        chartInstance.destroy();
      }
      if (shadow) {
        Chart.defaults.doughnutWithShadow = Chart.defaults.doughnut;
        Chart.controllers.doughnutWithShadow =
          Chart.controllers.doughnut.extend({
            draw(ease) {
              Chart.controllers.doughnut.prototype.draw.call(this, ease);
              const {
                chart: { ctx }
              } = this;
              ctx.save();
              ctx.shadowColor = 'rgba(0,0,0,0.15)';
              ctx.shadowBlur = 10;
              ctx.shadowOffsetX = 0;
              ctx.shadowOffsetY = 10;
              ctx.responsive = true;
              Chart.controllers.doughnut.prototype.draw.apply(this, arguments);
              ctx.restore();
            }
          });
      }
      const context = chartContainer.current.getContext('2d');
      newChartInstance = new Chart(context, {
        type: shadow ? 'doughnutWithShadow' : 'doughnut',
        options: doughnutChartOptions,
        plugins: [centerTextPlugin(small)],
        data
      });
      newChartInstance.canvas.addEventListener('click', (event) => {
        const activePoints = newChartInstance.getElementsAtEventForMode(
          event,
          'nearest',
          { intersect: true }
        );
        if (activePoints.length) {
          const firstPoint = activePoints[0];
          newChartInstance.pointIndex = firstPoint.index;
          newChartInstance.pointDataIndex = firstPoint.datasetIndex;
          newChartInstance.pointAvailable = true;
          newChartInstance.update(); // Update the chart to trigger beforeDraw()
        }
      });
      newChartInstance.canvas.addEventListener('mousemove', (event) => {
        const activePoints = newChartInstance.getElementsAtEventForMode(
          event,
          'nearest',
          { intersect: true }
        );
        if (activePoints.length) {
          const firstPoint = activePoints[0];
          newChartInstance.pointIndex = firstPoint.index;
          newChartInstance.pointDataIndex = firstPoint.datasetIndex;
          newChartInstance.pointAvailable = true;
          newChartInstance.update(); // Update the chart to trigger beforeDraw()
        }
      });
      setChartInstance(newChartInstance);
    }
    return () => {
      if (newChartInstance) {
        newChartInstance.destroy();
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [chartContainer, data, shadow]);

  return (
    <canvas
      ref={chartContainer}
      height={dimensions?.height ? dimensions.height : small && '300px'}
      width={dimensions?.width ? dimensions.width : small && '300px'}
    />
  );
};

export default Doughnut;
