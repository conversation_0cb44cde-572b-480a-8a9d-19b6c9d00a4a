/* eslint-disable no-unused-expressions */
/* eslint-disable no-unused-vars */
/* eslint-disable react/button-has-type */
/* eslint-disable react/no-array-index-key */
import React, { useState, useRef } from 'react';
import { Row, Button, Collapse } from 'reactstrap';
import { injectIntl } from 'react-intl';

import { Colxx } from 'components/common/CustomBootstrap';
import IntlMessages from 'helpers/IntlMessages';

import { ThumbListIcon, ImageListIcon } from 'components/svg';
import FilterDropdown from 'components/common/FilterDropdown';
import FilterReview from 'components/review/Filter';
import { RoleTypes, getCurrentColor } from 'helpers/Utils';
import RefreshButton from 'components/buttons/RefreshButton';
import useOnClickOutside from 'hooks/useOnClickOutSide';
import { useSelector } from 'react-redux';
import useWindowDimensions from 'hooks/useWindowDimensions';
import { SearchInput } from 'components/input';

const ListPageHeading = ({
  intl,
  displayMode,
  changeDisplayMode,
  search,
  setSearch,
  createFun,
  createText,
  createIcon,
  loading,
  setSkipCount,
  isContentManagement,
  setFilter,
  isStoriesList,
  inReview,
  roleType,
  searchDisabled,
  handleRefresh,
  MediaType,
  StreamingTypes,
  UploadTypes,
  setIsUpload,
  isUpload,
  isStream,
  setIsStream,
  handleUpload,
  filter,
  isPlayer
}) => {
  const [displayOptionsIsOpen, setDisplayOptionsIsOpen] = useState(false);
  const { adaptiveStreaming: streamEnabled, nonStreamingEnabled } = useSelector(
    (state) => state.subscription
  );
  const [tempSearch, setTempSearch] = useState(search);
  const streamRef = useRef(null);
  const uploadRef = useRef(null);
  useOnClickOutside(streamRef, () => setIsStream(false));
  useOnClickOutside(uploadRef, () => setIsUpload(false));
  const { width } = useWindowDimensions();

  const { messages } = intl;
  const currentColor = getCurrentColor().includes('dark');

  const handleTempSearch = (e) => {
    setTempSearch(e.target.value);
  };

  const handleClearClick = () => {
    setTempSearch('');
    setSkipCount && setSkipCount(0);
    if (search) setSearch(' ');
  };

  const handleSearch = () => {
    setSkipCount && setSkipCount(0);
    // console.log(tempSearch);
    setSearch(tempSearch);
  };

  const showDisplayMode = width > 750;

  return (
    <Row>
      <Colxx xxs="12 mb-3">
        <div className="mb-2">
          <Button
            color="empty"
            className="pt-0 pl-0 d-inline-block d-md-none "
            onClick={() => setDisplayOptionsIsOpen(!displayOptionsIsOpen)}
          >
            <IntlMessages id="pages.display-options" />
            <i className="simple-icon-arrow-down align-middle" />
          </Button>
          <Collapse
            isOpen={displayOptionsIsOpen}
            // className="d-flex flex-row"
            className="d-md-block"
            id="displayOptions"
          >
            <div className="d-flex flex-column w-full">
              {!isContentManagement && !inReview && <div className="mt-4" />}
              <div className="top-content-management">
                {/* <div className="d-block d-md-inline-block"> */}
                <div className=" d-flex flex-row">
                  {displayMode && (
                    <div className=" d-flex flex-row">
                      <span className="mr-3 ml-3 d-inline-block float-md-left mb-2 filter-container ">
                        {!isContentManagement && (
                          <ThumblistVideToggle
                            changeDisplayMode={changeDisplayMode}
                            displayMode={displayMode}
                          />
                        )}
                        {!isContentManagement && (
                          <ImageListVideoToggle
                            changeDisplayMode={changeDisplayMode}
                            displayMode={displayMode}
                          />
                        )}

                        {isStoriesList && (
                          <FilterDropdown
                            setFilter={setFilter}
                            filter={filter}
                            isStoriesList={isStoriesList}
                            className="filterShow"
                          />
                        )}
                      </span>
                      {isContentManagement && (
                        <span className=" d-flex flex-column">
                          {/* <div className="search-sm d-inline-block float-md-left mr-4 mb-1 align-top content-mng-input">
                            <button
                              type="button"
                              className="search-sm-btn"
                              onClick={() => {
                                if (!loading) handleSearch();
                              }}
                            >
                              <i className="simple-icon-magnifier search-sm-icon" />
                            </button>
                            <input
                              type="text"
                              name="keyword"
                              id="search"
                              placeholder={messages['menu.searchmore']}
                              value={tempSearch}
                              onChange={handleTempSearch}
                              onKeyPress={(event) => {
                                if (event.key === 'Enter') {
                                  if (!loading) handleSearch();
                                }
                              }}
                              disabled={searchDisabled}
                            />
                            {tempSearch && (
                              <button
                                type="button"
                                className="search-sm-btn-2"
                                onClick={handleClearClick}
                              >
                                <span
                                  style={{ color: '#131313' }}
                                  aria-hidden="true"
                                >
                                  ×
                                </span>
                              </button>
                            )}
                          </div> */}
                          <SearchInput
                            handleClick={() => {
                              if (!loading) handleSearch();
                            }}
                            handleClear={handleClearClick}
                            needClearIcon
                            value={tempSearch}
                            placeholder={messages['menu.searchmore']}
                            onChange={handleTempSearch}
                            onPressEnter={() => {
                              if (!loading) handleSearch();
                            }}
                            disabled={false}
                            className=""
                            width="100%"
                            minWidth="99%"
                          />
                          <span className=" d-flex flex-row">
                            {showDisplayMode && (
                              <>
                                <ThumblistVideToggle
                                  changeDisplayMode={changeDisplayMode}
                                  displayMode={displayMode}
                                />
                                <ImageListVideoToggle
                                  changeDisplayMode={changeDisplayMode}
                                  displayMode={displayMode}
                                />
                              </>
                            )}
                            <FilterDropdown
                              setFilter={setFilter}
                              filter={filter}
                              isStoriesList={isStoriesList}
                              className="filterShow"
                            />
                            <span className=" mt-0 ml-2">
                              <FilterDropdown
                                isStoriesList={isStoriesList}
                                setFilter={setFilter}
                                filter={filter}
                                className="filterHide"
                              />
                            </span>
                          </span>
                        </span>
                      )}
                    </div>
                  )}
                  {!isContentManagement && (
                    <span className="w-full d-flex flex-column">
                      <SearchInput
                        handleClick={() => {
                          if (!loading) handleSearch();
                        }}
                        handleClear={handleClearClick}
                        needClearIcon
                        value={tempSearch}
                        placeholder={messages['menu.searchmore']}
                        onChange={handleTempSearch}
                        onPressEnter={() => {
                          if (!loading) handleSearch();
                        }}
                        disabled={false}
                        className=""
                        width="100%"
                        minWidth="99%"
                      />
                      {/* <div className="search-sm d-inline-block float-md-left mr-4 mb-1 align-top content-mng-input">
                        <button
                          type="button"
                          className="search-sm-btn"
                          onClick={() => {
                            if (!loading) handleSearch();
                          }}
                        >
                          <i className="simple-icon-magnifier search-sm-icon" />
                        </button>
                        <input
                          type="text"
                          name="keyword"
                          id="search"
                          placeholder={messages['menu.searchmore']}
                          value={tempSearch}
                          onChange={handleTempSearch}
                          onKeyPress={(event) => {
                            if (event.key === 'Enter') {
                              if (!loading) handleSearch();
                            }
                          }}
                          disabled={searchDisabled}
                        />
                        {tempSearch && (
                          <button
                            type="button"
                            className="search-sm-btn-2"
                            onClick={handleClearClick}
                          >
                            <span
                              style={{ color: '#131313' }}
                              aria-hidden="true"
                            >
                              ×
                            </span>
                          </button>
                        )}
                      </div> */}
                    </span>
                  )}
                </div>
                <div className="d-flex flex-row-reverse flex-grow-1 align-items-center justify-content-between">
                  {isContentManagement && (
                    <ContentManagementButtons
                      isContentManagement={isContentManagement}
                      roleType={roleType}
                      uploadRef={uploadRef}
                      nonStreamingEnabled={nonStreamingEnabled}
                      MediaType={MediaType}
                      handleUpload={handleUpload}
                      setIsUpload={setIsUpload}
                      isUpload={isUpload}
                      setIsStream={setIsStream}
                      UploadTypes={UploadTypes}
                      currentColor={currentColor}
                      streamRef={streamRef}
                      streamEnabled={streamEnabled}
                      StreamingTypes={StreamingTypes}
                      isStream={isStream}
                      handleRefresh={handleRefresh}
                    />
                  )}

                  {isStoriesList && (
                    <FilterDropdown
                      isStoriesList={isStoriesList}
                      setFilter={setFilter}
                      filter={filter}
                      className="filterHide"
                    />
                  )}
                  {inReview && (
                    <FilterReview
                      setFilter={setFilter}
                      className="filterHide"
                    />
                  )}
                </div>

                <StoryRightButtons
                  roleType={roleType}
                  createIcon={createIcon}
                  createText={createText}
                  handleRefresh={handleRefresh}
                  isContentManagement={isContentManagement}
                  inReview={inReview}
                  createFun={createFun}
                />
              </div>
            </div>
          </Collapse>
        </div>
      </Colxx>
    </Row>
  );
};

const ContentManagementButtons = ({
  isContentManagement,
  roleType,
  uploadRef,
  nonStreamingEnabled,
  MediaType,
  handleUpload,
  setIsUpload,
  isUpload,
  setIsStream,
  UploadTypes,
  currentColor,
  streamRef,
  streamEnabled,
  StreamingTypes,
  isStream,
  handleRefresh
}) => {
  return (
    <span className=" d-flex flex-column align-items-end">
      {isContentManagement && roleType <= RoleTypes.CONTRIBUTOR && (
        <div className="upload-btn-cont">
          <CreateNonStreaming
            uploadRef={uploadRef}
            nonStreamingEnabled={nonStreamingEnabled}
            MediaType={MediaType}
            handleUpload={handleUpload}
            setIsUpload={setIsUpload}
            isUpload={isUpload}
            setIsStream={setIsStream}
            currentColor={currentColor}
            UploadTypes={UploadTypes}
          />

          {MediaType !== 3 && (
            <CreateStreaming
              streamRef={streamRef}
              streamEnabled={streamEnabled}
              StreamingTypes={StreamingTypes}
              isStream={isStream}
              currentColor={currentColor}
              setIsStream={setIsStream}
              setIsUpload={setIsUpload}
            />
          )}
        </div>
      )}
      {isContentManagement && (
        <span className="d-flex mt-1 flex-row text-primary align-items-center">
          <RefreshButton handleRefresh={handleRefresh} isText />
        </span>
      )}
    </span>
  );
};

const StoryRightButtons = ({
  roleType,
  createIcon,
  createText,
  handleRefresh,
  isContentManagement,
  inReview,
  createFun
}) => {
  return (
    <div className="d-flex flex-row justify-content-between align-items-center">
      {!isContentManagement && !inReview && (
        <>
          {roleType <= RoleTypes.CONTRIBUTOR && (
            <Button
              type="button"
              color="primary"
              className="mb-0 mr-2 btn btn-md btn-primary"
              id="space-upload-button"
              onClick={() => createFun()}
            >
              {/* liveStream ? "iconsminds-satelite-2" */}
              {createIcon && (
                <i
                  className={`${createIcon} mr-2`}
                  style={{ fontSize: '1rem' }}
                />
              )}
              <IntlMessages id={createText} />
            </Button>
          )}
          <RefreshButton handleRefresh={handleRefresh} />
        </>
      )}
    </div>
  );
};

const ThumblistVideToggle = ({ changeDisplayMode, displayMode }) => {
  return (
    <a
      href="#/"
      className={`mr-2 view-icon ${
        displayMode === 'thumblist' ? 'active' : ''
      }`}
      onClick={() => {
        sessionStorage.setItem('displayMode', 'thumblist');
        changeDisplayMode('thumblist');
      }}
    >
      <ThumbListIcon />
    </a>
  );
};

const ImageListVideoToggle = ({ changeDisplayMode, displayMode }) => {
  return (
    <a
      href="#/"
      className={`mr-2 view-icon ${
        displayMode === 'imagelist' ? 'active' : ''
      }`}
      onClick={() => {
        sessionStorage.setItem('displayMode', 'imagelist');
        changeDisplayMode('imagelist');
      }}
    >
      <ImageListIcon />
    </a>
  );
};

const CreateNonStreaming = ({
  uploadRef,
  nonStreamingEnabled,
  MediaType,
  handleUpload,
  setIsUpload,
  isUpload,
  setIsStream,
  currentColor,
  UploadTypes
}) => {
  return (
    <span ref={uploadRef} className=" position-relative">
      {(nonStreamingEnabled || MediaType === 3) && (
        <span className=" d-flex flex-row">
          <Button
            type="button"
            color="white"
            onClick={() => {
              handleUpload();
            }}
            className={`border-0 d-flex flex-row align-items-center p-2 px-3 btn-primary`}
            style={
              MediaType === 3
                ? {
                    color: 'white',
                    fontSize: '12px',
                    marginRight: '1px'
                  }
                : {
                    color: 'white',
                    borderTopRightRadius: 0,
                    borderBottomRightRadius: 0,
                    fontSize: '12px',
                    marginRight: '1px'
                  }
            }
          >
            <div className="d-flex align-items-center justify-content-center mr-2">
              <img
                src="/assets/img/ins/upload.svg"
                alt="streaming"
                height={20}
                width={20}
              />
            </div>
            <span>
              <IntlMessages id="ins-upload" />
            </span>
          </Button>
          {MediaType !== 3 && (
            <Button
              type="button"
              color="white"
              onClick={() => {
                if (MediaType === 3) {
                  handleUpload();
                } else {
                  setIsUpload(!isUpload);
                  setIsStream(false);
                }
              }}
              className={`mr-3 border-0 d-flex flex-row align-items-center p-2 px-1 btn-primary`}
              style={{
                color: 'white',
                borderTopLeftRadius: 0,
                borderBottomLeftRadius: 0,
                fontSize: '12px'
              }}
            >
              <div className="d-flex align-items-center justify-content-center mr-2">
                <i
                  className={
                    isUpload ? 'simple-icon-arrow-down' : 'simple-icon-arrow-up'
                  }
                />
              </div>
            </Button>
          )}
        </span>
      )}
      <div
        className="upload-all d-flex flex-column "
        style={{
          boxShadow: '5px 5px 4px #3333331f',
          borderRadius: '6px'
        }}
      >
        {isUpload &&
          UploadTypes.map((m) => (
            <button
              key={m.id}
              style={{
                fontSize: '13px',
                backgroundColor: currentColor ? '#242224' : 'white',
                display: 'inline-block'
              }}
              onClick={m.onClick}
              className=" p-2 border-0 text-primary upload-all-bg-hover text-white-hover"
            >
              {m.name}
            </button>
          ))}
      </div>
    </span>
  );
};

const CreateStreaming = ({
  streamRef,
  streamEnabled,
  StreamingTypes,
  isStream,
  currentColor,
  setIsStream,
  setIsUpload
}) => {
  return (
    <span ref={streamRef} className="position-relative">
      {streamEnabled && (
        <span className=" d-flex flex-row">
          <Button
            type="button"
            color="white"
            // onClick={() => {
            //   setIsStream(true);
            //   setIsUpload(false);
            // }}
            onClick={() => {
              StreamingTypes[0].onClick();
            }}
            className={`primary border-0 d-flex flex-row align-items-center p-2 px-3 btn-primary`}
            style={{
              color: 'white',
              borderTopRightRadius: 0,
              borderBottomRightRadius: 0,
              fontSize: '12px',
              marginRight: '1px'
            }}
          >
            <div className="d-flex align-items-center justify-content-center mr-2">
              <img
                src="/assets/img/ins/stream2.svg"
                alt="streaming"
                height={20}
                width={20}
              />
            </div>
            <span>
              <IntlMessages id="ins-streaming" />
            </span>
          </Button>
          <Button
            type="button"
            color="white"
            onClick={() => {
              setIsStream(!isStream);
              setIsUpload(false);
            }}
            className={`primary border-0 d-flex flex-row align-items-center p-2 px-1 btn-primary`}
            style={{
              color: 'white',
              borderTopLeftRadius: 0,
              borderBottomLeftRadius: 0,
              fontSize: '12px'
            }}
          >
            <div className="d-flex align-items-center justify-content-center mr-2">
              <i
                className={
                  isStream ? 'simple-icon-arrow-down' : 'simple-icon-arrow-up'
                }
              />
            </div>
          </Button>
        </span>
      )}
      <div
        className="upload-all d-flex flex-column"
        style={{
          boxShadow: '5px 5px 4px #3333331f',
          borderRadius: '6px'
        }}
      >
        {isStream &&
          StreamingTypes.map((m) => (
            <button
              key={m.id}
              style={{
                fontSize: '13px',
                backgroundColor: currentColor ? '#242224' : 'white',
                display: 'inline-block'
              }}
              onClick={m.onClick}
              className="p-2  border-0 text-primary upload-all-bg-hover text-white-hover"
            >
              {m.name}
            </button>
          ))}
      </div>
    </span>
  );
};

export default injectIntl(ListPageHeading);
