import IntlMessages from 'helpers/IntlMessages';
import React from 'react';
import { Button } from 'reactstrap';

function LoadingButton({
  outline = false,
  width,
  loading,
  onClick,
  text,
  className = '',
  iconClassName,
  disabled,
  type = 'Button',
  shadow = false,
  height,
  style = {}
}) {
  const newstyle = {
    ...style,
    width: typeof width === 'number' ? `${width}px` : width,
    height: `${height}px`,
    border: outline ? '' : 'none'
  };
  if (!width) {
    delete newstyle.width;
  }
  if (!height) {
    delete newstyle.height;
  }

  return (
    <Button
      color="primary"
      outline={outline}
      size="md"
      type={type}
      onClick={onClick}
      disabled={disabled || loading}
      style={newstyle}
      className={`${className} ${shadow && 'btn-shadow'} auth-btn ${
        loading ? 'btn-multiple-state show-spinner' : ''
      }`}
    >
      <span className="spinner d-inline-block">
        <span className="bounce1" />
        <span className="bounce2" />
        <span className="bounce3" />
      </span>
      <span className="label">
        {iconClassName && <i className={iconClassName} />}
        <span className="label px-2">
          <IntlMessages id={text} />
        </span>
      </span>
    </Button>
  );
}

export default LoadingButton;
