.custom-txtarea {
  border: 0.15px solid #FFE7FD;
  box-sizing: border-box;
  padding-right: 40px;
  padding-top: 10px;
  background: #FFE7FD;
}

.EventRow {
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100%;
  gap: 10px;
}
@media (max-width: 900px) {
  .EventRow {
    flex-direction: column;
  }
}
.EventRowdetails {
  width: 50%;
}
@media (max-width: 900px) {
  .EventRowdetails {
    width: 100%;
  }
}
.EventRowduration {
  width: 50%;
}
@media (max-width: 900px) {
  .EventRowduration {
    width: 100%;
  }
}/*# sourceMappingURL=share.css.map */