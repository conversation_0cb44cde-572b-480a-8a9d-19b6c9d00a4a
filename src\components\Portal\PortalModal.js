/* eslint-disable no-unused-vars */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import React from 'react';
import Portal from '.';

function PortalModal({
  isOpen,
  toggle,
  children,
  isConfirmationModal = false,
  audioSmallWidth = false,
  width = '325px'
}) {
  const style = isConfirmationModal
    ? {
        width
      }
    : {
        minWidth: '60%',
        maxWidth: audioSmallWidth ? '60%' : '80%'
      };

  if (isOpen) {
    return (
      <Portal>
        <div
          className={isOpen ? 'show' : 'hide'}
          style={{
            position: 'fixed',
            height: '100%',
            width: '100%',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 999998,
            display: 'flex',
            top: 0,
            backgroundColor: '#0000008a',
            transition: 'opacity 0.3s ease-in-out'
          }}
          onClick={toggle}
        >
          <div
            className={`modal-content ${isOpen ? 'fadeIn' : 'fadeOut'}`}
            style={style}
            onClick={(e) => e.stopPropagation()}
          >
            {children}
          </div>
        </div>
      </Portal>
    );
  }
  return null;
}

export default PortalModal;
