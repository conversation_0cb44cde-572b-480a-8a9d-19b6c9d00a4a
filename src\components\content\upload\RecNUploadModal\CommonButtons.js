import PauseIcon from 'constants/PauseIcon';
import PlayIcon from 'constants/PlayIcon';
import StopIcon from 'constants/StopIcon';
import React from 'react';

const { Button } = require('reactstrap');

const StopRecordingButton = ({ disabled, onClick, ...props }) => {
  return (
    <Button
      {...props}
      className="mt-4 align-self-center"
      type="button"
      outline
      style={{
        width: '200px',
        background: 'transparent',
        color: 'red'
      }}
      disabled={disabled}
      color="danger"
      onClick={onClick}
    >
      <StopIcon className="mr-2" />
      Stop Recording
    </Button>
  );
};

const PauseRecordingButton = ({ onClick, ...props }) => {
  return (
    <Button
      {...props}
      className="mt-4 align-self-center"
      type="button"
      style={{
        width: '200px',
        outlineColor: '#27A3E9',
        borderColor: '#27A3E9',
        background: 'transparent',
        color: '#27A3E9'
      }}
      onClick={onClick}
    >
      <PauseIcon color="#27A3E9" className="mr-2" />
      Pause Recording
    </Button>
  );
};

const ResumeRecordingButton = ({ onClick, ...props }) => {
  return (
    <Button
      {...props}
      outline
      style={{ width: '200px' }}
      className="mt-4 align-self-center"
      type="button"
      onClick={onClick}
    >
      <PlayIcon className="mr-2" />
      Resume Recording
    </Button>
  );
};

export { StopRecordingButton, PauseRecordingButton, ResumeRecordingButton };
