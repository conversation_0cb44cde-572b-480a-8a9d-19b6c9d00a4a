/* eslint-disable no-else-return */
/* eslint-disable no-unused-vars */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable react/self-closing-comp */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import React, { useEffect, useState } from 'react';
import {
  Badge,
  Card,
  CardBody,
  CardImg,
  CardText,
  Input,
  Label,
  Row,
  Tooltip
} from 'reactstrap';
import InfiniteScroll from 'react-infinite-scroll-component';
import classNames from 'classnames';
import {
  formatDate,
  formatTime,
  getCurrentUserDetails,
  getStatusColor,
  getStatusText,
  getUrlFromHistory,
  handleContentApprovedMessage,
  ifProcessingReturnContentOrElseNull,
  isDarkModeActive,
  isTimeDifferenceGreaterThanOneHour,
  isYoutubeORVimeoSource,
  returnFileDuration,
  RoleTypes
} from 'helpers/Utils';
import {
  getContentById,
  handleContentEncoding,
  handleUpdateContentStatus,
  refreshCDNSynching,
  updateContentUploadStatus
} from 'functions/api/spacesApi';
import { NotificationManager } from 'components/common/react-notifications';
import IntlMessages from 'helpers/IntlMessages';
import { NavLink, useHistory, Link } from 'react-router-dom';
import { Colxx } from 'components/common/CustomBootstrap';
import Pill from 'components/Pill';
import WorkflowPill from 'components/WorkflowPill';
import usePreview from 'hooks/usePreview';
import DeleteContentModal from './modals/deleteContentModal';
import ReadyToDraftConfirmation from './modals/ReadyToDraftConfirmation';
import ListDropdown from './ListDropdown';
import { useSelector } from 'react-redux';
import CDNsynching from 'components/CDNSynching';
import InfoIcon from 'components/icon/InfoIcon';
import { adminRoot, spaceHomeRoute } from 'constants/defaultValues';
import ConfirmationModal from 'components/common/ConfirmationModal';
import CopyId from 'components/copyId';
import ListThumbnail from './ListThumbnail';
import Aifeatures from 'components/Indicators/Aifeatures';
import { FormGroup } from 'react-bootstrap';
import ListTitle from './ListTitle';
import ShowcaseModal from './modals/ShowcaseModal';

function collect(props) {
  return { data: props.data };
}

const ListPageListing = ({
  items,
  setItems,
  displayMode,
  setContentId,
  icon,
  hasMore,
  fetchContents,
  handleWatermarkChange,
  reload,
  contentsLoading,
  isImageModal,
  MediaType,
  setShareModalOpen,
  shareModalOpen,
  setEntity,
  spaceId,
  setReviewModalOpen,
  roleType,
  setFlagModalOpen,
  subscriptionDetails,
  previewCache,

  selectedStatus,
  setSelectedStatus,
  isShowcaseSelected,
  setIsShowcaseSelected,
  isSharedSelected,
  setIsSharedSelected,
  isFlaggedSelected,
  setIsFlaggedSelected,
  selectedReview,
  setSelectedReview,
  isStreamingSelected,
  setIsStreamingSelected,
  singleContentLoading,

  allContent,
  setAllContent,
  setIsProcessing,
  isProcessing
}) => {
  const { workFlowEnabled, optimizationEnabled } = useSelector(
    (state) => state.subscription
  );

  const tenantDetails = getCurrentUserDetails();
  return contentsLoading ? (
    <div className="dataloading-overlay">
      <div className="dataloading" />
    </div>
  ) : (
    <InfiniteScroll
      dataLength={items.length}
      next={() => fetchContents()}
      hasMore={hasMore}
      style={{ minHeight: 300 }}
      loader={
        <p
          style={{
            textAlign: 'center',
            fontSize: '14px',
            padding: '10px 0px',
            margin: '0',
            fontWeight: 'bold',
            color: '#94308A'
          }}
        >
          Loading...
        </p>
      }
      endMessage={
        <p
          style={{
            textAlign: 'center',
            fontSize: '14px',
            padding: '10px 0px',
            margin: '0',
            fontWeight: 'bold',
            color: '#94308A'
          }}
        >
          <IntlMessages id="seen-all" />
        </p>
      }
    >
      <Row className="m-0 pr-1" style={{ justifyContent: 'initial' }}>
        {displayMode === 'thumblist' && items.length > 0 && (
          <Header MediaType={MediaType} />
        )}
        {items.map((p) => (
          <Item
            key={p.id}
            productData={p}
            setContentId={setContentId}
            handleWatermarkChange={handleWatermarkChange}
            icon={icon}
            collect={collect}
            reload={reload}
            isImageModal={isImageModal}
            setShareModalOpen={setShareModalOpen}
            shareModalOpen={shareModalOpen}
            setEntity={setEntity}
            displayMode={displayMode}
            tenantDetails={tenantDetails}
            spaceId={spaceId}
            setReviewModalOpen={setReviewModalOpen}
            roleType={roleType}
            setFlagModalOpen={setFlagModalOpen}
            subscriptionDetails={subscriptionDetails}
            workFlowEnabled={workFlowEnabled}
            items={items}
            setItems={setItems}
            previewCache={previewCache}
            selectedStatus={selectedStatus}
            setSelectedStatus={setSelectedStatus}
            isShowcaseSelected={isShowcaseSelected}
            setIsShowcaseSelected={setIsShowcaseSelected}
            isSharedSelected={isSharedSelected}
            setIsSharedSelected={setIsSharedSelected}
            isFlaggedSelected={isFlaggedSelected}
            setIsFlaggedSelected={setIsFlaggedSelected}
            selectedReview={selectedReview}
            setSelectedReview={setSelectedReview}
            isStreamingSelected={isStreamingSelected}
            setIsStreamingSelected={setIsStreamingSelected}
            singleContentLoading={singleContentLoading}
            allContent={allContent}
            setAllContent={setAllContent}
            setIsProcessing={setIsProcessing}
            isProcessing={isProcessing}
            optimizationEnabled={optimizationEnabled}
            someThingWentWrong={(() => {
              return ifProcessingReturnContentOrElseNull(p, true, true);
            })()}
          />
        ))}
      </Row>
    </InfiniteScroll>
  );
};

export default React.memo(ListPageListing);

const Header = ({ MediaType }) => {
  return (
    <div className="d-flex flex-column w-full px-4 text-small mb-2">
      <div className="d-flex flex-row">
        <div style={{ width: '15%' }}></div>
        <div style={{ width: '10%' }} className="centered">
          <IntlMessages id="cl-title" />
        </div>
        <div style={{ width: '1.25%' }}></div>
        <div style={{ width: '10%' }} className="centered">
          <IntlMessages id="cl-creation-time" />
        </div>
        <div style={{ width: '8%' }} className="centered">
          {MediaType !== 3 && <IntlMessages id="cl-duration" />}
        </div>
        <div style={{ width: '8%' }} className="centered">
          {MediaType !== 2 && MediaType !== 3 && (
            <IntlMessages id="cl-resolution" />
          )}
        </div>
        <div style={{ width: '10%' }}></div>
        <div style={{ width: '14%' }}></div>
        <div style={{ width: '10%' }}></div>
        <div style={{ width: '1.25%' }}></div>
        <div style={{ width: '10%' }} className="centered">
          <IntlMessages id="cl-status" />
        </div>
        <div style={{ width: '2.5%' }}></div>
      </div>
    </div>
  );
};

const Item = ({
  productData,
  isSelect,
  setContentId,
  handleWatermarkChange,
  reload,
  icon,
  isImageModal,
  setShareModalOpen,
  shareModalOpen,
  setEntity,
  displayMode,
  tenantDetails,
  setReviewModalOpen,
  roleType,
  setFlagModalOpen,
  workFlowEnabled,
  setItems,
  items,
  previewCache,
  someThingWentWrong,
  optimizationEnabled,

  selectedStatus,
  setSelectedStatus,
  isShowcaseSelected,
  setIsShowcaseSelected,
  isSharedSelected,
  setIsSharedSelected,
  isFlaggedSelected,
  setIsFlaggedSelected,
  setSelectedReview,
  selectedReview,
  isStreamingSelected,
  setIsStreamingSelected,
  singleContentLoading,
  allContent,
  setAllContent,
  setIsProcessing,
  isProcessing
}) => {
  const history = useHistory();
  const [product, setProduct] = useState(productData);
  const [openModal, setOpenModal] = useState(false);
  const [draftWarning, setDraftWarning] = useState(false);
  const [updatingContent, setUpdatingContent] = useState(false);
  const spaceId = history.location.pathname.split('/')[4];
  const [isShowcased, setIsShowcased] = useState(productData?.showcaseId);
  const [buttonIsHovered, setButtonHovered] = useState(false);

  const [associatedStoryCount, setAssociatedStoryCount] = useState(0);
  const [openAssociatedConfirmation, setAssociatedConfirmation] =
    useState(false);
  const toggleAssociatedConfirmation = () =>
    setAssociatedConfirmation(!openAssociatedConfirmation);

  // eslint-disable-next-line consistent-return
  const startProcessing = async (product, initial = false) => {
    const isProductNotProcessing = !isProcessing.some(
      (s) => s.id === product.id
    );
    if (product && isProductNotProcessing) {
      const shouldBeRefreshed = ifProcessingReturnContentOrElseNull(
        product,
        true
      );
      if (shouldBeRefreshed) {
        setIsProcessing((prev) => [...prev, product.id]);
        const processed = await getContentById({
          spaceId: product.spaceId,
          contentId: product.id,
          statusOnly: true
        });
        if (processed.data && !processed.isError) {
          const processedData = processed?.data;
          if (processedData) {
            const item = allContent.content.find((m) => m.id === product.id);
            if (item) {
              item.status = processedData?.status;
              item.isCdnCopyInProgress = processedData?.isCdnCopyInProgress;
              item.workflowStatus = processedData?.workflowStatus;
              item.duration = processedData?.duration;
              item.assignedUser = processedData?.assignedUser;
              item.assignedUserId = processedData?.assignedUserId;
            }
          }
        }
        setIsProcessing((prev) => prev.filter((f) => f.id !== product.id));
      }
      if (initial) {
        return true;
      }
    }
    if (initial) {
      return true;
    }
  };

  useEffect(() => {
    const timer = setTimeout(async () => {
      const isDone = await startProcessing(product, true);
      if (isDone) {
        const interval = setInterval(() => {
          startProcessing(product);
        }, 10000);
        return () => clearInterval(interval);
      }
      return null;
    }, 50000);
    return () => clearTimeout(timer);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [product]);

  const { previewSrc, previewValid: isPreviewValid } = usePreview({
    spaceId: product.spaceId,
    contentId: product.id,
    isMulti: false,
    MediaType: product.mediaType,
    waitFor: (() => {
      if (product?.status === 2 || product?.status === 3) {
        return true;
      }
      return false;
    })(),
    previewCache
  });

  const [isOpenShowCaseModal, setIsOpenShowCaseModal] = useState(false);

  const toggleShowCaseModal = () => {
    if (+product.workflowStatus === 1 || !workFlowEnabled) {
      setIsOpenShowCaseModal(!isOpenShowCaseModal);
    } else {
      handleContentApprovedMessage(roleType);
    }
  };

  const toggleModal = () => {
    setOpenModal(!openModal);
  };

  const isVideo = product.mediaType === 1;
  const isAudio = product.mediaType === 2;

  const handleEdit = () => {
    if (product?.status === 2 || product?.status === 3) {
      const currentUrl = getUrlFromHistory(history);
      setContentId(product.id);
      localStorage.setItem('mediaType', product?.mediaType);
      history.push(`${currentUrl}/${product?.id}/edit`);
    }
  };

  const handleDraft = async (force, ignore = false) => {
    if (!ignore) {
      setDraftWarning(!draftWarning);
    }
    setUpdatingContent(true);
    const { isError, data } = await handleUpdateContentStatus(
      {
        contentId: product.id,
        ContentStatus: 3
      },
      product.spaceId,
      force,
      ignore,
      true
    );
    if (data?.associatedStoryCount > 0) {
      setAssociatedStoryCount(data?.associatedStoryCount);
      toggleAssociatedConfirmation();
      return;
    }
    console.log('data', data);
    if (!isError) {
      await reload(product.id, 'update');
      NotificationManager.success(
        <IntlMessages id="content-draft-suc" />,
        <IntlMessages id="req.success" />,
        3000,
        null,
        null,
        ''
      );
    } else {
      NotificationManager.error(
        <IntlMessages id="content-draft-error" />,
        <IntlMessages id="req.failed" />,
        3000,
        null,
        null,
        ''
      );
    }
    setUpdatingContent(false);
  };

  useEffect(() => {
    setIsShowcased(productData?.showcaseId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [product]);

  const handleReady = async () => {
    if (product?.workflowStatus === 4) {
      NotificationManager.error(
        <IntlMessages id="rej-reeady" />,
        <IntlMessages id="req.failed" />,
        3000,
        null,
        null,
        ''
      );
      return;
    }
    setUpdatingContent(true);
    const { isError } = await handleUpdateContentStatus(
      {
        contentId: product.id,
        ContentStatus: 2
      },
      product.spaceId
    );
    if (!isError) {
      await reload(product.id, 'update');
      NotificationManager.success(
        <IntlMessages id="content-ready-suc" />,
        <IntlMessages id="req.success" />,
        3000,
        null,
        null,
        ''
      );
    } else {
      NotificationManager.error(
        <IntlMessages id="content-ready-error" />,
        <IntlMessages id="req.failed" />,
        3000,
        null,
        null,
        ''
      );
    }
    setUpdatingContent(false);
  };

  const handleDelete = () => {
    toggleModal();
  };

  const resetImageUploadStatus = async () => {
    setUpdatingContent(true);
    const updatedResp = await updateContentUploadStatus(
      {
        ContentIds: [product.contentId],
        UploadStatus: 3
      },
      product.spaceId
    );

    if (!updatedResp?.isError) {
      NotificationManager.success(
        <IntlMessages id="content-opt" />,
        <IntlMessages id="req.success" />,
        3000,
        null,
        null,
        ''
      );
    } else {
      NotificationManager.error(
        <IntlMessages id="content-opt-failed" />,
        <IntlMessages id="req.failed" />,
        3000,
        null,
        null,
        ''
      );

      await handleUpdateContentStatus(
        {
          contentId: product.id,
          ContentStatus: 4
        },
        product.spaceId
      );
    }
    await reload(product.id, 'update');
    setUpdatingContent(false);
  };

  const handleShare = () => {
    if (+product.workflowStatus === 1 || !workFlowEnabled) {
      setShareModalOpen(!shareModalOpen);
      setEntity(product);
    } else {
      handleContentApprovedMessage(roleType);
    }
  };

  const restartContentEncoding = async () => {
    setUpdatingContent(true);
    const payloadForEncoding = (() => {
      const common = {
        spaceId: product.spaceId,
        contentId: product.id,
        MediaType: product.mediaType,
        watermarkId: product.watermarkId,
        quality: product.quality,
        isDrm: product.isDRM
      };
      if (product.streaming) {
        return {
          ...common,
          adaptiveBitRate: true
        };
      }
      return {
        ...common,
        optimize: product.optimization
      };
    })();
    const encodedresp = await handleContentEncoding(payloadForEncoding, true);
    if (!encodedresp?.isError && encodedresp?.data) {
      NotificationManager.success(
        <IntlMessages id="content-opt" />,
        <IntlMessages id="req.success" />,
        3000,
        null,
        null,
        ''
      );
    } else {
      NotificationManager.error(
        <IntlMessages id="content-opt-failed" />,
        <IntlMessages id="req.failed" />,
        3000,
        null,
        null,
        ''
      );
      await handleUpdateContentStatus(
        {
          contentId: product.id,
          ContentStatus: 4
        },
        product.spaceId
      );
    }
    await reload(product.id, 'update');
    setUpdatingContent(false);
  };

  const handleRestart = () => {
    if (isImageModal) {
      resetImageUploadStatus();
    } else {
      restartContentEncoding();
    }
  };

  useEffect(() => {
    setProduct(productData);
  }, [productData]);

  const handleStatusFilter = () => {
    if (selectedStatus === product?.status) setSelectedStatus('');
    else setSelectedStatus(product?.status);
  };

  const handleIsShowcaseFilter = () => {
    if (isShowcaseSelected) {
      setIsShowcaseSelected(false);
    } else {
      setIsShowcaseSelected(true);
    }
  };
  const handleIsFlaggedFilter = () => {
    if (isFlaggedSelected) {
      setIsFlaggedSelected(false);
    } else {
      setIsFlaggedSelected(true);
    }
  };
  const handleSharedFilter = () => {
    if (isSharedSelected) {
      setIsSharedSelected(false);
    } else {
      setIsSharedSelected(true);
    }
  };

  const handleReviewFilter = (status) => {
    if (selectedReview) {
      setSelectedReview(null);
    } else {
      setSelectedReview(status);
    }
  };

  const handleStreamingFilter = () => {
    if (isStreamingSelected) {
      setIsStreamingSelected(false);
    } else {
      setIsStreamingSelected(true);
    }
  };
  const isRemoteFile = product?.contentSource === 2 && (isVideo || isAudio);

  useEffect(() => {
    const handleCDNRefreshing = async () => {
      await refreshCDNSynching();
    };
    if (someThingWentWrong) {
      handleCDNRefreshing();
    }
  }, [product?.id, someThingWentWrong]);

  const isReady = (() => {
    const statusText = getStatusText({
      status: product?.status,
      isStreaming: product?.streaming
    });
    return statusText === 'READY';
  })();

  const handleRedirectToParent = () => {
    const parentContentId = product.parentId;
    const parentSpaceId = product.spaceId;
    const newUrl = `${window.location.origin}${spaceHomeRoute}/space/${parentSpaceId}/manage-content/${parentContentId}/edit`;
    window.open(newUrl, '_blank');
  };

  const viewProps = {
    isSelect,
    product,
    handleEdit,
    previewSrc,
    isPreviewValid,
    icon,
    buttonIsHovered,
    setButtonHovered,
    roleType,
    handleDelete,
    isShowcased,
    isVideo,
    isImageModal,
    handleWatermarkChange,
    setDraftWarning,
    handleReady,
    setEntity,
    handleRestart,
    history,
    setFlagModalOpen,
    toggleShowCaseModal,
    setReviewModalOpen,
    handleShare,
    workFlowEnabled,
    isRemoteFile,
    someThingWentWrong,
    optimizationEnabled,
    isReady,
    handleRedirectToParent,
    // filters
    selectedStatus,
    isShowcaseSelected,
    isSharedSelected,
    isFlaggedSelected,
    handleStatusFilter,
    handleIsShowcaseFilter,
    handleIsFlaggedFilter,
    handleSharedFilter,
    handleReviewFilter,
    handleStreamingFilter,
    singleContentLoading
  };

  return (
    <>
      <DeleteContentModal
        openModal={openModal}
        toggleModal={toggleModal}
        spaceId={product?.spaceId}
        contentId={product?.id}
        reload={reload}
        aiContentKind={product?.contentKind}
        parentId={product?.parentId}
        mediaTitle={product?.title || 'Content Title'}
        setUpdatingContent={setUpdatingContent}
      />

      <ReadyToDraftConfirmation
        modalOpen={draftWarning}
        handleDraft={handleDraft}
        toggleModal={() => setDraftWarning(!draftWarning)}
      />
      {isOpenShowCaseModal && (
        <ShowcaseModal
          isOpenShowCaseModal={isOpenShowCaseModal}
          toggleShowCaseModal={toggleShowCaseModal}
          setIsShowcased={setIsShowcased}
          isShowcased={isShowcased}
          tenantDetails={tenantDetails}
          setItems={setItems}
          items={items}
          spaceId={spaceId}
          product={product}
          isContent
        />
      )}
      {typeof associatedStoryCount === 'number' && associatedStoryCount > 0 && (
        <ConfirmationModal
          openConfirmationModal={openAssociatedConfirmation}
          toggleConfirmationModal={() => {
            toggleAssociatedConfirmation();
          }}
          handleConfirm={() => {
            handleDraft(true, true);
            toggleAssociatedConfirmation();
          }}
          handleClose={() => {
            toggleAssociatedConfirmation();
          }}
          type="review"
          mediaTitle={product?.title}
          description={
            <p>
              {associatedStoryCount}&nbsp;
              <IntlMessages id="del-content-associated-story" />
            </p>
          }
        />
      )}
      <>
        {displayMode === 'imagelist' && (
          <NewCardView
            key={product?.id}
            isLoading={displayMode === 'imagelist' && updatingContent}
            {...viewProps}
          />
        )}
        {displayMode === 'thumblist' && (
          <ListView
            key={product?.id}
            isLoading={displayMode === 'thumblist' && updatingContent}
            {...viewProps}
          />
        )}
      </>
    </>
  );
};

const NewCardView = ({
  isLoading,
  isSelect,
  product,
  handleEdit,
  previewSrc,
  isPreviewValid,
  icon,
  buttonIsHovered,
  setButtonHovered,
  roleType,
  handleDelete,
  isShowcased,
  isVideo,
  isImageModal,
  handleWatermarkChange,
  setDraftWarning,
  handleReady,
  setEntity,
  handleRestart,
  history,
  toggleShowCaseModal,
  setReviewModalOpen,
  setFlagModalOpen,
  handleShare,
  workFlowEnabled,
  isRemoteFile,
  someThingWentWrong,
  optimizationEnabled,
  isReady,
  handleRedirectToParent,
  // --filters
  handleStatusFilter,
  selectedStatus,
  handleIsShowcaseFilter,
  handleIsFlaggedFilter,
  handleSharedFilter,
  handleReviewFilter,
  handleStreamingFilter,
  singleContentLoading
}) => {
  if (isLoading || singleContentLoading === product?.id) {
    return (
      <Colxx sm="6" lg="4" xl="3" className="mb-5 centered" key={product?.id}>
        <Card
          className={classNames(
            {
              active: isSelect
            },
            { 'imgview-container': true }
          )}
        >
          <div className="dataloading" />
        </Card>
      </Colxx>
    );
  }
  return (
    <Colxx sm="6" lg="4" xl="3" className="mb-5 centered" key={product?.id}>
      <Card
        className={classNames(
          {
            active: isSelect
          },
          { 'imgview-container': true }
        )}
      >
        {someThingWentWrong && (
          <span
            className="position-absolute c-pointer"
            style={{ color: 'red', right: 5, top: 5 }}
          >
            <SomeThingWent
              id={`${product?.id}-something-card`}
              name={product?.title}
            />
          </span>
        )}
        <div className="w-full d-flex p-2">
          <ListThumbnail
            isCard
            handleEdit={handleEdit}
            icon={icon}
            isReady={isReady}
            content={product}
            roleType={roleType}
            isPreviewValid={isPreviewValid}
            previewSrc={previewSrc}
          />
          <Badge
            color={getStatusColor(product?.status)}
            pill
            className="position-absolute badge-top-left"
            onClick={handleStatusFilter}
          >
            {selectedStatus === product?.status && (
              <i className="iconsminds-align-center mr-2" />
            )}
            {getStatusText({
              status: product?.status,
              isStreaming: product?.streaming
            })}
          </Badge>
        </div>
        <CardBody
          className="w-full"
          style={{ paddingTop: 0, paddingRight: '1rem', paddingLeft: '1rem' }}
        >
          {/* title more and flag  */}
          <div
            className="d-flex flex-row justify-content-between align-items-center mb-2"
            style={{ height: '20px' }}
          >
            <span
              style={{
                width: '54%'
              }}
            >
              <ListTitle
                roleType={roleType}
                product={product}
                handleEdit={handleEdit}
              />
            </span>
            <div
              style={{ width: '46%' }}
              className="d-flex flex-row justify-content-between align-items-center"
            >
              {product?.optimization && product.mediaType !== 3 && (
                <ListTag type={3} />
              )}
              {product?.isAES && <ListTag type={1} />}
            </div>
          </div>

          {/* id streaming and more dropdown  */}
          <div
            className="d-flex flex-row justify-content-between align-items-center mb-2"
            style={{ height: '20px' }}
          >
            <div className="w-80">
              <CopyId id={product.id} />
            </div>
            {product?.streaming && (
              <img
                onClick={handleStreamingFilter}
                src="/assets/img/ins/stream.svg"
                className="c-pointer"
                alt="streaming"
                height={18}
                width={18}
              />
            )}
            <span>
              {roleType <= RoleTypes.CONTRIBUTOR && (
                <ListDropdown
                  getStatusText={getStatusText}
                  product={product}
                  handleDelete={handleDelete}
                  handleEdit={handleEdit}
                  isImageModal={isImageModal}
                  handleWatermarkChange={handleWatermarkChange}
                  setDraftWarning={setDraftWarning}
                  handleReady={handleReady}
                  setEntity={setEntity}
                  handleRestart={handleRestart}
                  history={history}
                  toggleShowCaseModal={toggleShowCaseModal}
                  setReviewModalOpen={setReviewModalOpen}
                  roleType={roleType}
                  isShowcased={isShowcased}
                  setFlagModalOpen={setFlagModalOpen}
                  handleShare={handleShare}
                  workFlowEnabled={workFlowEnabled}
                  isRemoteFile={isRemoteFile}
                  optimizationEnabled={optimizationEnabled}
                />
              )}
            </span>
          </div>

          <div
            className="d-flex align-items-center justify-content-end mb-2"
            style={{ height: '15px' }}
          >
            <Aifeatures
              status={product?.contentKind}
              onClick={handleRedirectToParent}
            />
          </div>

          {/* category and duration  */}
          <div
            className="d-flex flex-row justify-content-between align-items-center mb-2"
            style={{ height: '20px' }}
          >
            {isVideo ? (
              <CardText className="text-xsmall font-weight-light mb-0">
                {product?.width &&
                  product?.height &&
                  `${product?.width} x ${product?.height}`}{' '}
              </CardText>
            ) : (
              <span />
            )}
            {product?.category ? (
              <span className="customBadge">{product?.category}</span>
            ) : (
              <span />
            )}

            {!isImageModal ? (
              <CardText className="text-xsmall font-weight-light mb-0">
                {returnFileDuration(product?.duration).durationInText}{' '}
              </CardText>
            ) : (
              <span />
            )}
          </div>

          {/* showcased shatred and flag  */}
          <div
            className="d-flex flex-row justify-content-between align-items-center mb-2"
            style={{ height: '20px' }}
          >
            <div className="d-flex flex-row align-items-center">
              <Pill
                condition={product?.shareId && product?.status !== 3}
                isShowcased={false}
                isShared
                onClick={handleSharedFilter}
              />
              <Pill
                condition={isShowcased}
                isShowcased
                onClick={handleIsShowcaseFilter}
              />
            </div>
            <span className="mr-2">
              {product?.flagId && (
                <span onClick={handleIsFlaggedFilter}>
                  <i
                    className="simple-icon-flag ml-2 c-pointer"
                    style={{ color: '#922c88' }}
                  />
                </span>
              )}
            </span>
          </div>

          {/* workflow cdn and creation date time  */}
          <div
            className="d-flex flex-row justify-content-between align-items-center mt-1"
            style={{ height: '20px' }}
          >
            <div className="d-flex flex-column justify-content-center">
              {workFlowEnabled && (
                <WorkflowPill
                  status={product?.workflowStatus}
                  onClick={() => handleReviewFilter(product?.workflowStatus)}
                />
              )}
              <CDNsynching
                id={product?.id}
                condition={product?.isCdnCopyInProgress}
              />
            </div>
            <CardText
              className=" text-small font-weight-light d-flex flex-row"
              style={{ fontSize: '10px' }}
            >
              {formatTime(product?.creationDateTime)}{' '}
              {formatDate(product?.creationDateTime)}
            </CardText>
          </div>
        </CardBody>
      </Card>
    </Colxx>
  );
};

const ListView = ({
  isLoading,
  isSelect,
  product,
  handleEdit,
  previewSrc,
  isPreviewValid,
  icon,
  buttonIsHovered,
  setButtonHovered,
  roleType,
  handleDelete,
  isShowcased,
  isVideo,
  isImageModal,
  handleWatermarkChange,
  setDraftWarning,
  handleReady,
  setEntity,
  handleRestart,
  history,
  toggleShowCaseModal,
  setReviewModalOpen,
  setFlagModalOpen,
  handleShare,
  workFlowEnabled,
  isRemoteFile,
  someThingWentWrong,
  optimizationEnabled,
  isReady,
  handleRedirectToParent,
  // filters
  handleStatusFilter,
  selectedStatus,
  handleIsShowcaseFilter,
  handleIsFlaggedFilter,
  handleSharedFilter,
  handleReviewFilter,
  handleStreamingFilter,
  singleContentLoading
}) => {
  if (isLoading || singleContentLoading === product?.id) {
    return (
      <Colxx xxs="12" key={product?.id} className="mb-3">
        <Card
          className={classNames(
            'd-flex align-items-center justify-content-center thumblist-body loading-body',
            { active: isSelect }
          )}
        >
          <div className="dataloading" />
        </Card>
      </Colxx>
    );
    // eslint-disable-next-line no-else-return
  } else {
    return (
      <Colxx xxs="12" key={product?.id} className="mb-3">
        <Card
          // style={{ border: someThingWentWrong && "1px solid red" }}
          className={classNames('d-flex flex-row p-2 position-relative', {
            active: isSelect
          })}
        >
          {someThingWentWrong && (
            <span
              className="position-absolute c-pointer"
              style={{ color: 'red', right: 5 }}
            >
              <SomeThingWent
                id={`${product?.id}-something-list`}
                name={product?.title}
              />
            </span>
          )}
          {/* icon  */}
          <div
            style={{ width: '15%' }}
            className="centered position-relative overflow-hidden"
          >
            <ListThumbnail
              handleEdit={handleEdit}
              icon={icon}
              isReady={isReady}
              content={product}
              roleType={roleType}
              isPreviewValid={isPreviewValid}
              previewSrc={previewSrc}
            />
          </div>
          {/* title  */}
          <div style={{ width: '10%' }} className="centered">
            <span className="w-full">
              <ListTitle
                roleType={roleType}
                product={product}
                handleEdit={handleEdit}
              />
              <div className="d-flex flex-row flex-wrap">
                {product?.isDRM && <ListTag type={2} />}
                {product?.isAES && <ListTag type={1} />}
                {product?.optimization && product.mediaType !== 3 && (
                  <ListTag type={3} />
                )}
              </div>
              <CopyId id={product.id} />
            </span>
          </div>
          {/* streaming  */}
          <div
            style={{ width: '1.25%', gap: '10px' }}
            className="centered d-flex flex-row"
          >
            <div className="centered">
              {product?.streaming && (
                <img
                  onClick={handleStreamingFilter}
                  className="c-pointer"
                  src="/assets/img/ins/stream.svg"
                  alt="streaming"
                  height={18}
                  width={18}
                />
              )}
            </div>
          </div>
          {/* date  */}
          <div style={{ width: '10%' }} className="centered">
            <CardText
              className="text-xsmall font-weight-light mb-0 d-flex flex-column"
              style={{ fontSize: '10px' }}
            >
              <span className="mb-2 text-center" style={{ fontSize: '12px' }}>
                {formatTime(product?.creationDateTime)}
              </span>
              {formatDate(product?.creationDateTime)}
            </CardText>
          </div>
          {/* duration  */}
          <div style={{ width: '8%' }} className="centered">
            {!isImageModal ? (
              <CardText className="text-xsmall font-weight-light mb-0">
                {returnFileDuration(product?.duration).durationInText}{' '}
              </CardText>
            ) : (
              <span />
            )}
          </div>
          {/* dimension  */}
          <div style={{ width: '8%' }} className="centered">
            {isVideo ? (
              <CardText className="text-xsmall font-weight-light mb-0">
                {product?.width &&
                  product?.height &&
                  `${product?.width} x ${product?.height}`}{' '}
              </CardText>
            ) : (
              <span />
            )}
          </div>
          {/* showcased  */}
          <div style={{ width: '10%' }} className="centered">
            <Pill
              condition={isShowcased}
              isShowcased
              onClick={handleIsShowcaseFilter}
            />
          </div>
          {/* workflow  */}
          <div style={{ width: '14%' }} className="centered">
            {workFlowEnabled && (
              <>
                <WorkflowPill
                  status={product?.workflowStatus}
                  onClick={() => handleReviewFilter(product?.workflowStatus)}
                />
                <span style={{ fontSize: '12px' }}>
                  {product?.assignedUser?.fullName}
                </span>
              </>
            )}
          </div>
          {/* shared  */}
          <div style={{ width: '10%' }} className="centered">
            <Pill
              condition={product?.shareId && product?.status !== 3}
              isShowcased={false}
              isShared
              onClick={handleSharedFilter}
            />
          </div>
          {/* flag  */}
          <div style={{ width: '1.25%' }} className="centered">
            {/* eslint-disable-next-line jsx-a11y/no-static-element-interactions */}
            {product?.flagId && (
              <span onClick={handleIsFlaggedFilter}>
                <i
                  className="simple-icon-flag mt-2 c-pointer"
                  style={{ color: '#922c88' }}
                />
              </span>
            )}
          </div>
          {/* status  */}
          <div style={{ width: '10%' }} className="centered">
            <Badge
              color={getStatusColor(product?.status)}
              pill
              className="statusbadge mb-1"
              onClick={handleStatusFilter}
            >
              {selectedStatus === product?.status && (
                <i className="iconsminds-align-center mr-2" />
              )}
              {getStatusText({
                status: product?.status,
                isStreaming: product?.streaming
              })}{' '}
            </Badge>
            <CDNsynching
              id={product?.id}
              condition={product?.isCdnCopyInProgress}
              className="position-absolute"
              style={{ bottom: 15 }}
            />
            <Aifeatures
              status={product?.contentKind}
              onClick={handleRedirectToParent}
            />
          </div>

          {/* dropdown  */}
          <div style={{ width: '2.5%' }} className="centered">
            {roleType <= RoleTypes.CONTRIBUTOR && (
              <ListDropdown
                getStatusText={getStatusText}
                product={product}
                handleDelete={handleDelete}
                handleEdit={handleEdit}
                isImageModal={isImageModal}
                handleWatermarkChange={handleWatermarkChange}
                setDraftWarning={setDraftWarning}
                handleReady={handleReady}
                setEntity={setEntity}
                handleRestart={handleRestart}
                history={history}
                toggleShowCaseModal={toggleShowCaseModal}
                setReviewModalOpen={setReviewModalOpen}
                roleType={roleType}
                isShowcased={isShowcased}
                setFlagModalOpen={setFlagModalOpen}
                handleShare={handleShare}
                workFlowEnabled={workFlowEnabled}
                isRemoteFile={isRemoteFile}
                optimizationEnabled={optimizationEnabled}
              />
            )}
          </div>
        </Card>
      </Colxx>
    );
  }
};

const SomeThingWent = ({ id, name }) => {
  return (
    <InfoIcon
      iconStyle={{ color: 'orange' }}
      iconName="iconsminds-danger"
      name={id}
      customBody={
        <p className="m-0">
          <IntlMessages id="something-wrong-1" />
          <Link to={`${adminRoot}/help-support?id=${id}&title="${name}"`}>
            <strong className="text-primary c-pointer">
              <IntlMessages id="something-wrong-mid" />
            </strong>
          </Link>
          <IntlMessages id="something-wrong-2" />
        </p>
      }
    />
  );
};

const ListTag = ({ type }) => {
  const text = (() => {
    if (type === 1) {
      return 'aes-enabled-label';
    } else if (type === 2) {
      return 'drm-enabled-label';
    } else {
      return 'list-optimized';
    }
  })();
  const color = type === 3 ? '#0C8CE9' : 'gray';
  return (
    <span
      style={{
        fontSize: 7,
        padding: '2px 4px',
        borderRadius: 20,
        border: `1px solid ${color}`,
        fontWeight: 600,
        marginBottom: 1,
        marginRight: 1,
        color
      }}
    >
      <IntlMessages id={text} />
    </span>
  );
};
