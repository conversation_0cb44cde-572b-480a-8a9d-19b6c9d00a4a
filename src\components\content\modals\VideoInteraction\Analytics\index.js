/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import { getInteractionTypes } from 'helpers/Utils';
import Pill from '../components/Pill';
import TimeRow from '../components/TimeRow';
import URL from '../components/URL';
import Charts from './Charts';
import Sessions from './Sessions';
import SelectedSessionDetails from './SelectedSessionDetails';
import { getInteractionAnalytics } from 'functions/api/analyticsApi';
import { GRAPH_TYPES } from 'constants/VideoInteraction';
import { InteractionLoading } from './Loading';

const initialState = {
  date: {
    sessions: [],
    counts: [],
    lastUpdated: '',
    isFetched: false
  },
  hour: {
    sessions: [],
    counts: [],
    lastUpdated: '',
    isFetched: false
  },
  month: {
    sessions: [],
    counts: [],
    lastUpdated: '',
    isFetched: false
  }
};

const Analytics = ({ setSelectedInteractions, selectedInteractions }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [active, setActive] = useState(GRAPH_TYPES.DATE);
  const [data, setData] = useState(initialState);
  const [selectedSessionDetails, setSelectedSessionDetails] = useState(null);
  const { isImage, isPoll, isRichText, isSurvey } =
    getInteractionTypes(selectedInteractions);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const { data: respData, isError } = await getInteractionAnalytics({
          type: selectedInteractions.type,
          contentId: selectedInteractions.contentId,
          by: active,
          interactionId: selectedInteractions.id
        });
        if (isError) {
          setData(initialState);
        } else {
          const storeData = () => {
            return {
              sessions: respData?.sessions ?? [],
              counts: respData?.analytics ?? [],
              lastUpdated: respData?.lastUpdated,
              isFetched: true
            };
          };
          const newData = (() => {
            if (GRAPH_TYPES.DATE === +active) {
              return {
                ...data,
                date: storeData()
              };
            }
            if (GRAPH_TYPES.HOUR === +active) {
              return {
                ...data,
                hour: storeData()
              };
            }
            return {
              ...data,
              month: storeData()
            };
          })();
          setData(newData);
          // setAllData(newData);
        }
        setIsLoading(false);
      } catch (error) {
        setIsLoading(false);
        // setAllData(null);
      }
    };
    if (setSelectedInteractions) {
      fetchData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setSelectedInteractions, active]);

  if (selectedSessionDetails) {
    return (
      <SelectedSessionDetails
        selectedInteractions={selectedInteractions}
        selectedSessionDetails={selectedSessionDetails}
        setSelectedSessionDetails={setSelectedSessionDetails}
      />
    );
  }

  return (
    <div className="InteractionAnalytics">
      <div className="IALeft">
        <span
          className="centered c-pointer"
          style={{
            border: '1px solid #992288',
            height: 30,
            width: 30,
            borderRadius: 15
          }}
        >
          <svg
            onClick={() => {
              setSelectedInteractions(null);
            }}
            width="12"
            height="12"
            viewBox="0 0 12 23"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M11.5003 20.7975L10.194 22.125L0.611547 12.3875C0.380923 12.1496 0.251953 11.8313 0.251953 11.5C0.251953 11.1687 0.380923 10.8504 0.611547 10.6125L10.194 0.875L11.5003 2.20375L2.3528 11.5L11.5003 20.7975Z"
              fill="#992288"
            />
          </svg>
        </span>
      </div>
      <div className="IARight">
        <h4 className="m-0">
          <b>Analytics</b>
        </h4>
        <div className="IARightTop">
          <Pill type={selectedInteractions.type} />
          <TimeRow
            startTime={selectedInteractions?.showAtTimecode}
            endTime={
              !selectedInteractions?.pauseOnShow &&
              selectedInteractions?.endAtTimecode
            }
          />
        </div>
        {isImage && (
          <div className="IARightImage">
            <img
              height={40}
              width={40}
              src={selectedInteractions?.data?.imageUrl}
              alt="interaction_image"
            />
            <URL url={selectedInteractions?.data?.title} />
          </div>
        )}
        <div>
          {isLoading ? (
            <InteractionLoading />
          ) : (
            <>
              <Charts
                title={(() => {
                  if (isImage) {
                    const count = (() => {
                      if (active === GRAPH_TYPES.DATE) {
                        return data.date.sessions.length ?? 0;
                      }
                      if (active === GRAPH_TYPES.HOUR) {
                        return data.hour.sessions.length ?? 0;
                      }
                      return data.month.sessions.length ?? 0;
                    })();

                    console.log('data lmao', data);

                    return `No of clicks: ${count}`;
                  }
                  if (isPoll || isSurvey) {
                    return 'No of responses';
                  }
                  return '';
                })()}
                active={active}
                setActive={setActive}
                data={data}
              />
              <Sessions
                list={(() => {
                  if (active === GRAPH_TYPES.DATE) {
                    return data.date.sessions ?? [];
                  }
                  if (active === GRAPH_TYPES.HOUR) {
                    return data.hour.sessions ?? [];
                  }
                  return data.month.sessions ?? [];
                })()}
                showSelectedSession={isPoll || isSurvey}
                setSelectedSessionDetails={setSelectedSessionDetails}
              />
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default Analytics;
