@charset "UTF-8";
@import url('https://fonts.googleapis.com/css?family=Nunito:300,400,400i,600,700');
html {
  width: 100%;
  height: 100%;
  background: #f8f8f8;
}

:root {
  --theme-color-1: -color-1;
  --theme-color-2: #4556ac;
  --theme-color-3: #af67a4;
  --theme-color-4: #743c6e;
  --theme-color-5: #4b5480;
  --theme-color-6: #795d75;
  --theme-color-1-10: rgba(146, 44, 136, 0.1);
  --theme-color-2-10: rgba(69, 86, 172, 0.1);
  --theme-color-3-10: rgba(175, 103, 164, 0.1);
  --theme-color-4-10: rgba(116, 60, 110, 0.1);
  --theme-color-5-10: rgba(75, 84, 128, 0.1);
  --theme-color-6-10: rgba(121, 93, 117, 0.1);
  --primary-color: #474d66;
  --foreground-color: white;
  --separator-color: #d7d7d7;
}

#root {
  height: 100%;
}

body {
  font-family: 'Nunito', sans-serif;
  font-size: 1rem;
  font-weight: 400;
  color: #474d66;
  background: #f8f8f8;
}
@media (max-width: 767px) {
  body {
    font-size: 0.8rem;
  }
}
body.background {
  height: 100%;
}
body.background main {
  margin: 0 !important;
  height: 100%;
}
body.background main .container {
  height: 100%;
}

.fixed-background {
  background: url('/assets/img/login/woman.jpg') no-repeat center center fixed;
  background-size: cover;
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  filter: blur(5px);
  -webkit-filter: blur(5px);
}

.inviteBackground {
  background: url('/assets/img/invite/invitationbg.svg') no-repeat center center
    fixed;
  background-size: cover;
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

h1 {
  font-size: 1.75rem;
  padding-bottom: 10px;
  display: inline-block;
}
@media (max-width: 767px) {
  h1 {
    font-size: 1.3rem;
    padding-top: 0.5rem;
  }
}

h2 {
  font-size: 1.4rem;
}
@media (max-width: 767px) {
  h2 {
    font-size: 1.1rem;
  }
}

h3 {
  font-size: 1.3rem;
}
@media (max-width: 767px) {
  h3 {
    font-size: 1rem;
  }
}

h3 {
  font-size: 1.2rem;
}
@media (max-width: 767px) {
  h3 {
    font-size: 1rem;
  }
}

h4 {
  font-size: 1.15rem;
}
@media (max-width: 767px) {
  h4 {
    font-size: 0.9rem;
  }
}

h5 {
  font-size: 1.1rem;
}
@media (max-width: 767px) {
  h5 {
    font-size: 0.9rem;
  }
}

h6 {
  font-size: 1rem;
}
@media (max-width: 767px) {
  h6 {
    font-size: 0.85rem;
  }
}

hr {
  border-top: 1px solid #d7d7d7;
}

.disable-text-selection {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

::-moz-selection {
  background: #d2aacc;
}

::selection {
  background: #d2aacc;
}

::-moz-selection {
  background: #d2aacc;
}

.map-item {
  height: 400px;
  width: 100%;
}

.scrollbar-container {
  margin-right: -15px;
  padding-right: 15px;
  position: relative;
}

.ps__rail-y {
  width: 5px;
}

.ps__thumb-y {
  left: 0;
  width: 5px;
  right: 0;
}

.ps__rail-y:hover > .ps__thumb-y,
.ps__rail-y:focus > .ps__thumb-y,
.ps__rail-y.ps--clicking .ps__thumb-y {
  width: 5px;
  left: 0;
}

.ps__thumb-y {
  background-color: #d7d7d7;
}

.video-play-icon {
  width: 100%;
  height: 100%;
  position: absolute;
}
.video-play-icon span {
  position: absolute;
  background: rgba(255, 255, 255, 0.7);
  height: 1.25em;
  border-radius: 0.75em;
  line-height: 0.65em;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.75em;
  width: 2em;
  text-align: center;
}
.video-play-icon span:before {
  content: '';
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 8px 0 8px 12px;
  font-size: 0;
  border-color: transparent transparent transparent #922c88;
}
.video-play-icon:hover span,
.video-play-icon:active span {
  background: rgba(255, 255, 255, 0.85);
}

.logo-single {
  width: 110px;
  height: 35px;
  background: url('/assets/logos/logo.svg') no-repeat;
  background-size: contain;
  background-position: center center;
  display: inline-block;
  margin-bottom: 60px;
}

/* form logos and headers*/
.logo-innerloop {
  font-family: Roboto Slab;
  font-style: normal;
  font-weight: bold;
  font-size: 32px;
  line-height: 42px;
  color: #ffffff;
}

.il-form-header {
  background: #797272;
  border-radius: 5px;
  width: inherit;
  height: 70px;
  padding-top: 1em;
  padding-left: 1.5em;
  background-color: #922c88;
}
@media (max-width: 767px) {
  .il-form-header {
    padding-top: 1em;
    padding-left: 0;
    display: flex;
    justify-content: center;
  }
}

/*end of form logos and headers */
.list-item-heading-container {
  width: calc(100% - 125px);
}

.list-item-heading {
  font-size: 1rem;
}
@media (max-width: 450px) {
  .list-item-heading {
    font-size: 0.8rem;
  }
}

.list-item-text {
  font-size: 0.85rem;
}
@media (max-width: 450px) {
  .list-item-text {
    font-size: 0.75rem;
    line-height: 0.9rem;
  }
}

@media (min-width: 991px) {
  .increaseWidth {
    width: 10% !important;
  }
}
@media (max-width: 991px) {
  .increaseWidth {
    width: 100% !important;
  }
}

.truncate {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.no-transition {
  transition: none !important;
}

@media (max-width: 767px) {
  .display-1 {
    font-size: 2.8rem;
  }
}

@media (max-width: 767px) {
  .display-2 {
    font-size: 2.6rem;
  }
}

@media (max-width: 767px) {
  .display-3 {
    font-size: 2.4rem;
  }
}

@media (max-width: 767px) {
  .display-4 {
    font-size: 2.2rem;
  }
}

.lead {
  font-size: 1.8rem;
  font-weight: 300;
  line-height: 2rem;
}
@media (max-width: 767px) {
  .lead {
    font-size: 1.1rem;
    line-height: 1.6rem;
  }
}

a {
  color: #474d66;
  transition: color 200ms;
}
a:hover,
a:active {
  text-decoration: initial;
  color: #922c88;
}

.authlink {
  color: #922c88;
}

p {
  font-size: 0.85rem;
  line-height: 1.3rem;
  font-family: 'Nunito', sans-serif;
}

.text-large {
  font-size: 1.9rem !important;
}

.text-one {
  font-size: 1rem !important;
}

.text-xlarge {
  font-size: 2.7rem !important;
}

.text-small {
  font-size: 0.76rem;
  line-height: 0.9rem;
}

.text-xsmall {
  font-size: 0.73rem;
  line-height: 0.7rem;
}

.text-white {
  color: #fff !important;
}

.text-extra-small {
  font-size: 0.6rem;
}

.text-default {
  color: #474d66 !important;
}

.text-muted {
  color: #909090 !important;
}

.text-semi-muted {
  color: #8f8f8f !important;
}

.font-weight-medium {
  font-weight: 500;
}

.font-weight-semibold {
  font-weight: 600;
}

.color-theme-1 {
  color: #922c88;
}

.color-theme-2 {
  color: #4556ac;
}

.view-icon {
  font-size: 20px;
  color: #8f8f8f;
}
.view-icon:hover {
  color: #922c88;
}
.view-icon.s {
  font-size: 18px;
}

#displayOptions a {
  cursor: pointer;
}
#displayOptions a.active i {
  color: #922c88;
}
#displayOptions button {
  border-color: #8f8f8f;
  color: #8f8f8f;
}
#displayOptions button:hover {
  background-color: #922c88;
  border-color: #922c88;
  color: #fff;
}
#displayOptions .btn-outline-dark:not(:disabled):not(.disabled):active,
#displayOptions .btn-outline-dark:not(:disabled):not(.disabled).active,
#displayOptions .show > .btn-outline-dark.dropdown-toggle {
  background-color: #922c88;
  border-color: #922c88;
  color: #fff;
}
#displayOptions .view-icon svg {
  width: 22px;
}
#displayOptions .view-icon .view-icon-svg {
  fill: #8f8f8f;
}
#displayOptions .view-icon:hover .view-icon-svg,
#displayOptions .view-icon.active .view-icon-svg {
  fill: #922c88;
}

.text-theme-2 {
  color: #4556ac !important;
}

.text-theme-3 {
  color: #af67a4 !important;
}

.text-error {
  color: red !important;
}

.text-primary,
.text-theme-1 {
  color: #922c88 !important;
}

.text-secondary {
  color: #474d66 !important;
}

.main-heading {
  border-bottom: 1px solid #d7d7d7;
}

.separator {
  border-bottom: 1px solid #d7d7d7;
}

.alert-dismissible .close {
  padding: 0.5rem 1.25rem;
}

.rounded {
  border-radius: 50px !important;
}

.img-thumbnail {
  border-radius: 0.1rem;
  padding: 0;
  border: initial;
}

.img-fluid {
  width: 100%;
}

.white {
  color: #fff !important;
}

.opacity-25 {
  opacity: 0.25;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-75 {
  opacity: 0.75;
}

.jumbotron {
  background: initial;
}

.c-pointer {
  cursor: pointer !important;
}

.right-menu footer.page-footer {
  padding-right: 280px;
}
@media (max-width: 1199px) {
  .right-menu footer.page-footer {
    padding-right: 0;
  }
}

.rtl * {
  text-align: right;
}

.rtl {
  direction: rtl;
}
.rtl .text-center p,
.rtl .text-center div,
.rtl .text-center span,
.rtl .text-center i,
.rtl .text-center a,
.rtl .text-center h1,
.rtl .text-center h2,
.rtl .text-center h3,
.rtl .text-center h4,
.rtl .text-center h5,
.rtl .text-center h6 {
  text-align: center;
}
.rtl .list-unstyled {
  padding-right: 0;
}
.rtl .dropdown-menu:not(.datepicker-dropdown) {
  right: initial !important;
}
.rtl .dropdown-menu-right {
  right: initial !important;
  left: 0 !important;
}
.rtl .dropdown-menu-left {
  right: 0 !important;
  left: initial !important;
}
.rtl .float-right {
  float: left !important;
}
.rtl .float-left {
  float: right !important;
}
@media (min-width: 1439px) {
  .rtl .float-xxl-left {
    float: right !important;
  }
  .rtl .float-xxl-right {
    float: left !important;
  }
}
@media (min-width: 1199px) {
  .rtl .float-xl-left {
    float: right !important;
  }
  .rtl .float-xl-right {
    float: left !important;
  }
}
@media (min-width: 991px) {
  .rtl .float-lg-left {
    float: right !important;
  }
  .rtl .float-lg-right {
    float: left !important;
  }
}
@media (min-width: 767px) {
  .rtl .float-md-left {
    float: right !important;
  }
  .rtl .float-md-right {
    float: left !important;
  }
}
@media (min-width: 575px) {
  .rtl .float-sm-left {
    float: right !important;
  }
  .rtl .float-sm-right {
    float: left !important;
  }
}
@media (min-width: 419px) {
  .rtl .float-xs-left {
    float: right !important;
  }
  .rtl .float-xs-right {
    float: left !important;
  }
}
.rtl .r-0 {
  left: 0;
  right: initial;
}
.rtl .heading-number {
  margin-right: 0;
  margin-left: 10px;
}
.rtl .app-row {
  padding-right: initial;
  padding-left: 280px;
}
@media (max-width: 1199px) {
  .rtl .app-row {
    padding-left: 0;
  }
}
.rtl .app-menu {
  left: 0;
  right: initial;
}
@media (max-width: 1199px) {
  .rtl .app-menu {
    transform: translateX(-280px);
  }
}
.rtl .app-menu.shown {
  transform: translateX(0);
}
.rtl .app-menu .app-menu-button {
  left: calc(280px - 2px);
  box-shadow: 4px 0 5px rgba(0, 0, 0, 0.04);
}
.rtl .ps__rail-y {
  right: initial !important;
  left: 0 !important;
}
.rtl .scroll {
  margin-left: -15px;
  padding-left: 15px;
  margin-right: 0;
  padding-right: 0;
}
.rtl .icon-button {
  text-align: center;
}
.rtl .heading-icon {
  margin-right: initial;
  margin-left: 5px;
}
.rtl .simple-icon-arrow-left:before {
  content: '\e606';
}
.rtl .simple-icon-arrow-right:before {
  content: '\e605';
}

.dropdown-radix {
  min-width: 140px;
  color: #474d66;
  text-align: left;
  list-style: none;
  background-color: #f8f8f8;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
}

.rounded .app-menu {
  border-top-left-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
}
.rounded .dropdown-menu {
  border-radius: 0.75rem;
}
.rounded .btn.default {
  border-radius: 0.1rem;
}
.rounded .list-thumbnail {
  border-radius: 0.75rem;
}

.auth-btn {
  width: 125px;
}

.ins-tab {
  display: flex;
  justify-content: space-around;
  border-top: 0.5px solid #e3e3e3;
  border-bottom: 0.5px solid #e3e3e3;
}

.ins-no-bg-btn {
  font-size: 1.2rem;
  cursor: pointer;
  height: 20px;
  color: #922c88;
}

.close-btn {
  font-size: 22px;
  cursor: pointer;
  color: #922c88;
}

.w-10 {
  width: 10% !important;
}

.w-5 {
  width: 5% !important;
}

.w-8 {
  width: 8% !important;
}

.w-7 {
  width: 7% !important;
}

.w-90 {
  width: 90% !important;
}

.w-95 {
  width: 95% !important;
}

.w-12 {
  width: 12% !important;
}

.w-88 {
  width: 88% !important;
}

.w-15 {
  width: 15% !important;
}

.w-14 {
  width: 14% !important;
}

.w-85 {
  width: 85% !important;
}

.w-20 {
  width: 20% !important;
}

.h-50 {
  height: 50% !important;
}

.w-80 {
  width: 80% !important;
}

.w-85 {
  width: 85% !important;
}

.w-30 {
  width: 30% !important;
}

.w-70 {
  width: 70% !important;
}

.w-40 {
  width: 40% !important;
}

.w-60 {
  width: 60% !important;
}

@media (max-width: 767px) {
  .w-xs-100 {
    width: 100% !important;
  }
}

@media (max-width: 991px) {
  .w-sm-100 {
    width: 100% !important;
  }
}

.r-0 {
  right: 0;
}

.l-0 {
  left: 0;
}

.depth-1 {
  box-shadow: 0 1px 15px rgba(0, 0, 0, 0.04), 0 1px 6px rgba(0, 0, 0, 0.04);
}

.depth-2 {
  box-shadow: 0 3px 30px rgba(0, 0, 0, 0.1), 0 3px 20px rgba(0, 0, 0, 0.1);
}

.min-width-zero {
  min-width: 0;
}

.mb-5,
.my-5 {
  margin-bottom: 2rem !important;
}

@media (max-width: 575px) {
  .float-none-xs {
    float: initial !important;
  }
}

main {
  margin-left: 410px;
  margin-top: 150px;
  margin-right: 60px;
  margin-bottom: 40px;
  transition: margin-left 300ms;
}
main.sub-hidden {
  margin-left: 180px;
}
main.main-hidden {
  margin-left: 60px;
}
main .container-fluid {
  padding-left: 0;
  padding-right: 0;
}
@media (max-width: 1439px) {
  main {
    margin-left: 390px;
    margin-right: 50px;
    margin-top: 145px -10;
    margin-bottom: 30px;
  }
}
@media (max-width: 1199px) {
  main {
    margin-left: 370px;
    margin-right: 40px;
    margin-top: 125px -10;
    margin-bottom: 20px;
  }
}
@media (max-width: 767px) {
  main {
    margin-left: 15px !important;
    margin-right: 15px !important;
    margin-top: 90px !important;
    margin-bottom: 0;
  }
}
@media (max-width: 575px) {
  main {
    margin-bottom: 0;
  }
}

.rtl main {
  margin-right: 410px;
  margin-left: 60px !important;
  transition: margin-right 300ms;
}
@media (max-width: 1439px) {
  .rtl main {
    margin-right: 390px;
    margin-left: 50px !important;
  }
}
@media (max-width: 1199px) {
  .rtl main {
    margin-right: 370px;
    margin-left: 40px !important;
  }
}
@media (max-width: 767px) {
  .rtl main {
    margin-right: 15px !important;
    margin-left: 15px !important;
  }
}
@media (max-width: 575px) {
  .rtl main {
    margin-bottom: 0;
  }
}

.alert {
  border-radius: 0;
}

.alert-primary {
  color: #922c88;
  background-color: rgba(146, 44, 136, 0.2);
  border-color: rgba(146, 44, 136, 0.1);
}

.alert-secondary {
  color: #4556ac;
  background-color: rgba(69, 86, 172, 0.2);
  border-color: rgba(69, 86, 172, 0.1);
}

.alert-success {
  color: #3e884f;
  background-color: rgba(62, 136, 79, 0.2);
  border-color: rgba(62, 136, 79, 0.1);
}

.alert-info {
  color: #3195a5;
  background-color: rgba(49, 149, 165, 0.2);
  border-color: rgba(49, 149, 165, 0.1);
}

.alert-warning {
  color: #b69329;
  background-color: rgba(182, 147, 41, 0.2);
  border-color: rgba(182, 147, 41, 0.1);
}

.alert-danger {
  color: #c43d4b;
  background-color: rgba(196, 61, 75, 0.2);
  border-color: rgba(196, 61, 75, 0.1);
}

.alert-light {
  color: #d4d4d4;
  background-color: rgba(212, 212, 212, 0.2);
  border-color: rgba(212, 212, 212, 0.1);
}

.alert-dark {
  color: #575057;
  background-color: rgba(87, 80, 87, 0.2);
  border-color: rgba(87, 80, 87, 0.1);
}

.alert-dismissible .close {
  text-shadow: initial;
}

.alert *[data-notify='title'] {
  display: block;
  font-size: 0.9rem;
}

div[data-notify='container'] {
  padding: 18px;
}

.bg-theme-2,
.badge-theme-2 {
  background-color: #4556ac !important;
  color: #fff;
}

.bg-theme-3,
.badge-theme-3 {
  background-color: #af67a4 !important;
  color: #fff;
}

.bg-primary,
.bg-theme-1,
.badge-primary,
.badge-theme-1 {
  background-color: #922c88 !important;
  color: #fff;
}

.bg-secondary,
.badge-secondary {
  background-color: #4556ac !important;
  color: #fff;
}

.bg-muted {
  background-color: #909090;
}

.bg-semi-muted {
  background-color: #f8f8f8;
}

.badge-warning {
  background-color: #b69329;
}

.badge-success {
  background-color: #3e884f;
}

.badge-info {
  background-color: #3195a5;
}

.badge-danger {
  background-color: #c43d4b;
}

.badge-success,
.badge-danger,
.badge-warning,
.badge-info {
  color: #fff;
}

.badge {
  padding: 0.55em 0.75em 0.6em 0.75em;
  font-size: 74%;
}
.badge.badge-pill {
  padding-right: 1.25em;
  padding-left: 1.25em;
}
.badge.badge-top-left {
  top: 10px;
  left: -7px;
}
.badge.badge-top-left-2 {
  top: 40px;
  left: -7px;
}
.badge.badge-top-left-3 {
  top: 70px;
  left: -7px;
}
.badge.badge-top-right {
  top: 8px;
  right: -7px;
}
.badge.badge-top-right-2 {
  top: 40px;
  right: -7px;
}

.badge-light {
  background-color: #d4d4d4;
  color: #575057;
}

.badge-dark {
  background-color: #575057;
  color: #d4d4d4;
}

.badge-outline-primary,
.badge-outline-theme-1 {
  background: unset;
  border: 1px solid #922c88;
  color: #922c88;
}
.badge-outline-primary:hover,
.badge-outline-theme-1:hover {
  color: #922c88;
}

.badge-outline-secondary,
.badge-outline-theme-2 {
  background: unset;
  border: 1px solid #4556ac;
  color: #4556ac;
}

.badge-outline-theme-3 {
  background: unset;
  border: 1px solid #af67a4;
  color: #af67a4;
}

.badge-outline-success {
  background: unset;
  border: 1px solid #3e884f;
  color: #3e884f;
}

.badge-outline-danger {
  background: unset;
  border: 1px solid #c43d4b;
  color: #c43d4b;
}

.badge-outline-warning {
  background: unset;
  border: 1px solid #b69329;
  color: #b69329;
}

.badge-outline-info {
  background: unset;
  border: 1px solid #3195a5;
  color: #3195a5;
}

.badge-outline-light {
  background: unset;
  border: 1px solid #d4d4d4;
  color: #d4d4d4;
}

.badge-outline-dark {
  background: unset;
  border: 1px solid #575057;
  color: #575057;
}

.rtl .badge.badge-top-left {
  left: initial;
  right: -7px;
}
.rtl .badge.badge-top-left-2 {
  left: initial;
  right: -7px;
}
.rtl .badge.badge-top-right {
  left: -7px;
  right: initial;
}
.rtl .badge.badge-top-right-2 {
  left: -7px;
  right: initial;
}

.border {
  border: 1px solid #f3f3f3 !important;
}

.border-right {
  border-right: 1px solid #f3f3f3 !important;
}

.border-left {
  border-left: 1px solid #f3f3f3 !important;
}

.border-top {
  border-top: 1px solid #f3f3f3 !important;
}

.border-bottom {
  border-bottom: 1px solid #f3f3f3 !important;
}

.border-primary,
.border-theme-1 {
  border-color: #922c88 !important;
}

.border-theme-2 {
  border-color: #4556ac !important;
}

.border-theme-3 {
  border-color: #af67a4 !important;
}

.border-secondary {
  border-color: #474d66 !important;
}

.remove-last-border > *:last-child {
  border-bottom: initial !important;
}

.remove-last-margin > *:last-child {
  margin-bottom: initial !important;
}

.remove-last-padding > *:last-child {
  padding-bottom: initial !important;
}

.rounded .border-radius {
  border-radius: 0.75rem;
}

@media (max-width: 991px) {
  .breadcrumb-container .breadcrumb {
    padding: 0;
  }
}

.breadcrumb {
  background-color: transparent;
  margin-bottom: 0.5rem;
}

.breadcrumb-item + .breadcrumb-item::before {
  content: '|';
}

.rtl .breadcrumb-item + .breadcrumb-item {
  padding-left: initial;
  padding-right: 0.5rem;
}
.rtl .breadcrumb-item + .breadcrumb-item::before {
  padding-left: 0.5rem;
  padding-right: initial;
}

button {
  color: #474d66;
  outline: initial !important;
}

.btn-arrow {
  display: inline-block;
  text-align: center;
  border-radius: 30px !important;
  width: 42px;
  height: 42px;
  line-height: 24px;
}

.btn-arrow i {
  font-size: 15px;
  display: inline-block;
  text-align: center;
}

.btn-sm.btn-arrow {
  width: 34px;
  height: 34px;
  line-height: 17px;
}

.btn-sm.btn-arrow i {
  font-size: 13px;
  line-height: 10px;
}

.btn {
  border-radius: 50px;
  outline: initial !important;
  box-shadow: none !important;
  box-shadow: initial !important;
  font-size: 1rem;
  padding: 0.5rem 1.25rem 0.5rem 1.25rem;
  transition: background-color box-shadow 0.1s linear;
}

.btn-shadow {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15), 0 1px 3px 1px rgba(0, 0, 0, 0.15) !important;
  transition: background-color box-shadow 0.1s linear;
}
.btn-shadow:hover,
.btn-shadow:focus {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.15), 0 4px 6px 2px rgba(0, 0, 0, 0.15) !important;
}

.btn-empty {
  background: transparent !important;
}

.btn-lg,
.btn-group-lg > .btn,
.btn-group-sm > .btn {
  border-radius: 50px;
}

.btn.default {
  border-radius: 0.1rem;
}

.btn-primary {
  background-color: #922c88;
  border-color: #922c88;
  color: #fff;
}
.btn-primary:hover {
  color: #fff;
  background-color: #73236b;
  border-color: #73236b;
}

.check-button {
  cursor: default !important;
}

.check-button.btn-primary {
  background-color: #922c88 !important;
  border-color: #922c88 !important;
  opacity: 1;
}

.check-button .custom-control {
  min-height: 1.1rem;
  margin-top: -7px;
}

.dropdown-menu {
  font-size: 0.8rem;
  border-radius: 0.1rem;
  background: white;
  border-color: rgba(71, 77, 102, 0.15);
}

.dropdown-item {
  padding: 0.5rem 1.25rem;
  color: #474d66;
}

.dropdown-item:hover,
.dropdown-item:focus {
  background-color: #f8f8f8;
  color: #474d66;
}

.dropdown-item.active,
.dropdown-item:active {
  color: #fff;
  text-decoration: none;
  background-color: #922c88;
}

.dropdown-divider {
  border-color: #d7d7d7;
}

.dot-dropitem {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 14%;
}

.dot-dropitem-radix {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  grid-gap: 14%;
  font-size: 14px;
  gap: 14%;
  padding-left: 8px;
  margin-bottom: 6px;
  cursor: pointer;
}
.dot-dropitem-radix::after,
.dot-dropitem-radix:focus,
.dot-dropitem-radix:focus-visible,
.dot-dropitem-radix:hover {
  background-color: #f8f8f8;
  outline: none !important;
}
.dot-dropitem-radix:first-child {
  margin-top: 8px;
}
.dot-dropitem-radix:last-child {
  margin-bottom: 8px;
}

.btn-primary:not(:disabled):not(.disabled):active,
.btn-primary:not(:disabled):not(.disabled).active,
.show > .btn-primary.dropdown-toggle {
  background-color: #631e5c;
  border-color: #631e5c;
  color: #fff;
}

.btn-secondary {
  background-color: #4556ac;
  border-color: #4556ac;
  color: #fff;
}
.btn-secondary:hover {
  color: #fff;
  background-color: #39478f;
  border-color: #39478f;
}

.btn-secondary:not(:disabled):not(.disabled):active,
.btn-secondary:not(:disabled):not(.disabled).active,
.show > .btn-secondary.dropdown-toggle {
  background-color: #334080;
  border-color: #334080;
  color: #fff;
}

.btn-primary.btn-primary-gradient {
  background: linear-gradient(to right, #922c88, #571a51);
}
.btn-primary.btn-primary-gradient:hover {
  background: linear-gradient(to right, #922c88, #73236b);
}

.btn-primary-gradient:not(:disabled):not(.disabled):active,
.btn-primary-gradient:not(:disabled):not(.disabled).active,
.show > .btn-primary-gradient.dropdown-toggle {
  background: linear-gradient(to right, #922c88, #571a51);
}

.btn-secondary-gradient {
  background: linear-gradient(to right, #4556ac, #2f3b75);
}
.btn-secondary-gradient:hover {
  background: linear-gradient(to right, #4556ac, #39478f);
}

.btn-secondary-gradient:not(:disabled):not(.disabled):active,
.btn-secondary-gradient:not(:disabled):not(.disabled).active,
.show > .btn-secondary-gradient.dropdown-toggle {
  background: linear-gradient(to right, #4556ac, #2f3b75);
}

.btn-warning {
  background-color: #b69329;
  border-color: #b69329;
}

.btn-success,
.btn-info,
.btn-danger,
.btn-warning {
  color: #fff;
}
.btn-success:hover,
.btn-info:hover,
.btn-danger:hover,
.btn-warning:hover {
  color: #fff;
}

.btn-outline-success:hover,
.btn-outline-info:hover,
.btn-outline-danger:hover,
.btn-outline-warning:hover {
  color: white;
}

.btn-light {
  color: #575057;
  background-color: #d4d4d4;
  border-color: #d4d4d4;
}
.btn-light:hover {
  color: #575057;
  background-color: silver;
  border-color: silver;
}

.btn-dark {
  color: #d4d4d4;
  background-color: #575057;
  border-color: #575057;
}
.btn-dark:hover {
  color: #d4d4d4;
  background-color: #4a444a;
  border-color: #4a444a;
}

.btn-outline-dark {
  color: #575057;
  border-color: #575057;
}
.btn-outline-dark:hover {
  color: white;
  background-color: #575057;
  border-color: #575057;
}

.btn-outline-white {
  color: #fff;
  border-color: #fff;
  background-color: initial;
}
.btn-outline-white:hover {
  color: #922c88;
  background-color: #fff;
}

.btn-outline-light {
  color: #d4d4d4;
  border-color: #d4d4d4;
}
.btn-outline-light:hover {
  color: white;
  background-color: #d4d4d4;
  border-color: #d4d4d4;
}

.btn-outline-primary {
  color: #922c88;
  border-color: #922c88;
}
.btn-outline-primary:hover {
  color: white;
  background-color: #922c88;
  border-color: #922c88;
}

.btn-outline-primary-imp {
  color: #922c88 !important;
  border-color: #922c88 !important;
}
.btn-outline-primary-imp:hover {
  color: white !important;
  background-color: #922c88 !important;
  border-color: #922c88 !important;
}

.btn-outline-theme-3 {
  background: unset;
  color: #af67a4;
  border-color: #af67a4;
}
.btn-outline-theme-3:hover {
  background-color: #af67a4;
  border-color: #af67a4;
  color: white;
}

.btn-outline-primary:not(:disabled):not(.disabled):active,
.btn-outline-primary:not(:disabled):not(.disabled).active,
.show > .btn-outline-primary.dropdown-toggle {
  background-color: #922c88;
  border-color: #922c88;
  color: white;
}

.btn-outline-secondary {
  color: #4556ac;
  border-color: #4556ac;
}
.btn-outline-secondary:hover {
  background-color: #4556ac;
  border-color: #4556ac;
  color: white;
}

.btn-outline-secondary:not(:disabled):not(.disabled):active,
.btn-outline-secondary:not(:disabled):not(.disabled).active,
.show > .btn-outline-secondary.dropdown-toggle {
  background-color: #4556ac;
  border-color: #4556ac;
  color: white;
}

.btn-header-light {
  color: #d7d7d7;
  border-color: transparent;
  background: transparent;
}
.btn-header-light:hover {
  background-color: transparent;
  border-color: #d7d7d7;
}

.btn-header-primary {
  color: #922c88;
  border-color: transparent;
  background: transparent;
}
.btn-header-primary:hover {
  background-color: transparent;
  border-color: #922c88;
}

.btn-header-secondary {
  color: #4556ac;
  border-color: transparent;
  background: transparent;
}
.btn-header-secondary:hover {
  background-color: transparent;
  border-color: #4556ac;
}

.btn-header-primary-light {
  color: #af67a4;
  border-color: transparent;
  background: transparent;
}
.btn-header-primary-light:hover {
  background-color: transparent;
  border-color: #af67a4;
}

.btn-hover-color:hover {
  background-color: #922c88;
}
.btn-hover-color:hover i {
  color: white;
}

.btn-xl,
.btn-group-xl > .btn {
  line-height: 1.5;
  font-weight: 700;
  letter-spacing: 0.05rem;
  padding: 1rem 3.5rem 0.9rem;
}

.btn-lg,
.btn-group-lg > .btn {
  line-height: 1.5;
  font-weight: 700;
  letter-spacing: 0.05rem;
  padding: 0.6rem 2.5rem 0.6rem 2.5rem;
}

.btn-sm,
.btn-group-sm > .btn {
  padding: 0.45rem 1rem 0.45rem 1rem;
  font-size: 0.8rem;
  line-height: 1.5;
}

.btn-xs,
.btn-group-xs > .btn {
  padding: 0.25rem 0.75rem 0.25rem 0.75rem;
  font-size: 0.76rem;
  line-height: 1.3;
}

.btn-primary.disabled,
.btn-primary:disabled {
  background-color: #922c88;
  border-color: #922c88;
  color: white;
}

.btn-secondary.disabled,
.btn-secondary:disabled {
  background-color: #4556ac;
  border-color: #4556ac;
  color: white;
}

.btn-group > .btn:not(:last-child):not(.dropdown-toggle),
.btn-group > .btn-group:not(:last-child) > .btn {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.btn-group > .btn:not(:first-child),
.btn-group > .btn-group:not(:first-child) > .btn {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

.btn-link {
  color: #922c88;
}

.btn-link:hover {
  color: rgba(146, 44, 136, 0.8);
  text-decoration: underline;
}

.white-underline-link {
  color: #fff;
  text-decoration: underline;
}
.white-underline-link:hover,
.white-underline-link:active {
  color: #fff;
  text-decoration: initial;
}

.btn-multiple-state {
  position: relative;
  transition: opacity 500ms;
}
.btn-multiple-state .spinner,
.btn-multiple-state .icon {
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  opacity: 0;
  visibility: hidden;
  top: 50%;
  transform: translateY(-50%);
  transition: opacity 500ms;
  color: #fff;
}
.btn-multiple-state .icon i {
  vertical-align: text-bottom;
  font-size: 18px;
}
.btn-multiple-state .label {
  transition: opacity 500ms;
}
.btn-multiple-state.show-spinner .label {
  opacity: 0;
}
.btn-multiple-state.show-spinner .spinner {
  opacity: 1;
  visibility: visible;
}
.btn-multiple-state.show-success .label {
  opacity: 0;
}
.btn-multiple-state.show-success .icon.success {
  opacity: 1;
  visibility: visible;
}
.btn-multiple-state.show-fail .label {
  opacity: 0;
}
.btn-multiple-state.show-fail .icon.fail {
  opacity: 1;
  visibility: visible;
}
.btn-multiple-state.btn-primary:disabled {
  opacity: 1;
  background: #6b2063;
  border-color: #6b2063;
}
.btn-multiple-state.btn-secondary:disabled {
  opacity: 1;
  border-color: #364488;
}

.icon-button {
  padding: 0;
  font-size: 14px;
  width: 34px;
  height: 34px;
  line-height: 34px;
}
.icon-button.large {
  width: 44px;
  height: 44px;
  font-size: 18px;
}
.icon-button.small-icon {
  font-size: 12px;
  line-height: 32px;
}

.top-right-button-single {
  width: unset;
}
@media (max-width: 991px) {
  .top-right-button-single {
    width: 100%;
  }
}

.top-right-button-container {
  float: right;
  position: relative;
}
.top-right-button-container .btn-group .btn {
  line-height: 1;
}
@media (max-width: 575px) {
  .top-right-button-container {
    float: initial;
    display: flex;
    justify-content: space-between;
  }
}
@media (max-width: 575px) {
  .top-right-button-container .top-right-button {
    display: flex;
    flex-grow: 1;
    justify-content: center;
    align-items: center;
    margin-right: 5px;
  }
}
@media (max-width: 575px) {
  .top-right-button-container .dropdown {
    display: flex;
    flex-grow: 1;
    margin-right: 5px;
  }
}

.card {
  border: initial;
  background: white;
  border-radius: calc(0.15rem - 1px);
  box-shadow: 0 1px 15px rgba(0, 0, 0, 0.04), 0 1px 6px rgba(0, 0, 0, 0.04);
}
.card .card-header .card-icon {
  right: 5px;
  top: 5px;
}
.card .card-header .card-icon i {
  font-size: 12px;
  color: #d7d7d7;
}
.card .card-subtitle {
  margin: 0;
  margin-bottom: 1rem;
}
.card .cardSubtitle-text {
  width: 175px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.card .card-header .handle {
  cursor: default;
}
.card .card-body {
  padding: 1.75rem;
}
@media (max-width: 575px) {
  .card .card-body {
    padding: 1.25rem;
  }
}
.card .card-body.sm {
  padding: 1.25rem 1.75rem;
}
.card .card-title {
  margin-bottom: 1.5rem;
}
@media (max-width: 767px) {
  .card .card-title {
    margin-bottom: 1.25rem;
  }
}

.card-title {
  font-size: 1.1rem;
}
@media (max-width: 767px) {
  .card-title {
    font-size: 0.9rem;
  }
}

.card-img {
  border-radius: calc(0.25rem - 1px);
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  max-height: 200px;
  width: unset;
}

.card-img-fluid {
  border-radius: calc(0.25rem - 1px);
  -o-object-fit: cover;
  object-fit: cover;
}

.card-img-bottom {
  width: 100%;
  border-bottom-left-radius: calc(0.15rem - 1px);
  border-bottom-right-radius: calc(0.15rem - 1px);
}

.card-img-top {
  width: 100%;
  border-top-left-radius: calc(0.15rem - 1px);
  border-top-right-radius: calc(0.15rem - 1px);
}

.card-img-left {
  width: 100%;
  border-top-left-radius: calc(0.15rem - 1px);
  border-bottom-left-radius: calc(0.15rem - 1px);
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}

.card-img-right {
  width: 100%;
  border-top-right-radius: calc(0.15rem - 1px);
  border-bottom-right-radius: calc(0.15rem - 1px);
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}

.card-img-overlay {
  background: rgba(0, 0, 0, 0.5);
  padding: 1.75rem;
}
@media (max-width: 575px) {
  .card-img-overlay {
    padding: 1rem;
  }
}

.card-top-buttons {
  padding: 1.3rem;
  right: 0;
  top: 0;
}
@media (max-width: 575px) {
  .card-top-buttons {
    padding: 0.35rem;
  }
}

.card-header {
  border: initial;
  background: initial;
  padding-top: 0;
}

.item-check {
  pointer-events: none;
}

.responsive-thumbnail {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 140px;
  max-width: 140px;
  max-height: 85px;
  overflow: hidden;
  margin: auto;
}
@media (max-width: 991px) {
  .responsive-thumbnail {
    width: 200px;
    max-width: 200px;
    max-height: 100px;
  }
}
@media (max-width: 575px) {
  .responsive-thumbnail {
    width: 100px;
    min-width: 100px;
    max-height: 85px;
  }
}
@media (max-width: 390px) {
  .responsive-thumbnail {
    width: 80px;
    min-width: 80px;
    max-height: 85px;
  }
}

.responsive-img {
  -o-object-fit: contain;
  object-fit: contain;
  max-height: 85px;
  min-width: 140px;
  margin: auto;
}
@media (max-width: 991px) {
  .responsive-img {
    min-width: 200px;
    max-height: 100px;
  }
}
@media (max-width: 575px) {
  .responsive-img {
    min-width: 100px;
    max-height: 85px;
  }
}

@media (max-width: 991px) {
  .responsive-md-story {
    max-height: 85px;
    min-width: 140px;
  }
}

.thumblist-body {
  padding: 25px;
  flex-grow: 1;
}
@media (max-width: 560px) {
  .thumblist-body {
    padding: 10px 5px;
  }
}

.loading-body {
  -o-object-fit: contain;
  object-fit: contain;
  height: 85px;
  margin: auto;
}
@media (max-width: 991px) {
  .loading-body {
    height: 200px;
  }
}
@media (max-width: 575px) {
  .loading-body {
    height: 192px;
  }
}

.list-thumbnail {
  border-radius: 0.1rem;
  padding: 0;
  border: initial;
  height: auto;
  max-width: unset;
  height: 85px;
  -o-object-fit: contain;
  object-fit: contain;
  width: unset !important;
}
@media (max-width: 991px) {
  .list-thumbnail {
    height: 80px;
  }
}
@media (max-width: 575px) {
  .list-thumbnail {
    height: 70px;
  }
}
@media (max-width: 991px) {
  .list-thumbnail.responsive {
    width: unset;
    height: 136px;
  }
}
@media (max-width: 575px) {
  .list-thumbnail.responsive {
    width: 110px !important;
    height: 100%;
  }
}
@media (max-width: 419px) {
  .list-thumbnail.responsive {
    width: 90px !important;
    height: 100%;
  }
}
.list-thumbnail.small {
  height: 60px;
  font-size: 1rem;
}
@media (max-width: 991px) {
  .list-thumbnail.small {
    height: 55px;
  }
}
@media (max-width: 575px) {
  .list-thumbnail.small {
    height: 50px;
  }
}
.list-thumbnail.xsmall {
  height: 40px;
  font-size: 1rem;
}
@media (max-width: 991px) {
  .list-thumbnail.xsmall {
    height: 40px;
  }
}
@media (max-width: 575px) {
  .list-thumbnail.xsmall {
    height: 40px;
  }
}

.list-thumbnail-letters {
  width: 85px;
  height: 85px;
  background: #922c88;
  align-items: center;
  display: flex;
  justify-content: center;
  font-size: 1.25rem;
  color: #fff;
}
@media (max-width: 991px) {
  .list-thumbnail-letters {
    width: 80px;
    height: 80px;
  }
}
@media (max-width: 575px) {
  .list-thumbnail-letters {
    width: 70px;
    height: 70px;
  }
}
.list-thumbnail-letters.small {
  width: 60px;
  height: 60px;
  font-size: 1rem;
}
@media (max-width: 991px) {
  .list-thumbnail-letters.small {
    width: 55px;
    height: 55px;
  }
}
@media (max-width: 575px) {
  .list-thumbnail-letters.small {
    width: 50px;
    height: 50px;
  }
}

.imgview-container {
  height: 300px;
  width: 100%;
  max-width: 270px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.img-thumbnail {
  height: 150px;
  width: auto;
  margin: 0 auto;
}

.icon-cards-small {
  height: 136px !important;
  width: 150px;
  align-items: center;
  justify-content: center;
}

.icon-cards-row {
  margin-left: -5px;
  margin-right: -5px;
  margin-top: -10px;
}
.icon-cards-row .glide__slides {
  padding-bottom: 0px;
  padding-top: 10px;
}
.icon-cards-row .glide__slides .icon-row-item {
  padding-left: 5px;
  padding-right: 5px;
}
.icon-cards-row .icon-row-item {
  max-width: 90vw;
}
.icon-cards-row [class*='col-'] {
  padding-left: 5px;
  padding-right: 5px;
}
.icon-cards-row .card-body {
  padding: 2rem 0.5rem;
}
.icon-cards-row .card-text {
  color: #8f8f8f;
  height: 30px;
  line-height: 26px;
}
.icon-cards-row .lead {
  color: #922c88;
  margin-bottom: 0;
}
.icon-cards-row i {
  font-size: 46px;
  line-height: 66px;
  color: #922c88;
}
.icon-cards-row .card {
  transition: box-shadow 1000ms;
  cursor: pointer;
}
@media (max-width: 1439px) {
  .icon-cards-row .card-text {
    height: 48px;
    line-height: 18px;
  }
  .icon-cards-row .lead {
    font-size: 1.6rem;
  }
  .icon-cards-row i {
    font-size: 32px;
    line-height: 47px;
  }
}

.rounded .card-img,
.rounded .card-img-fluid,
.rounded .card-img-overlay {
  border-radius: 0.75rem;
}
.rounded .card-img-bottom,
.rounded .card-img-bottom .vjs-poster,
.rounded .card-img-bottom .vjs-tech {
  border-bottom-left-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
  border-top-left-radius: initial;
  border-top-right-radius: initial;
}
.rounded .card-img-top,
.rounded .card-img-top .vjs-poster,
.rounded .card-img-top .vjs-tech {
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
  border-bottom-left-radius: initial;
  border-bottom-right-radius: initial;
}
.rounded .card-img-left {
  border-top-left-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
  border-top-right-radius: initial !important;
  border-bottom-right-radius: initial !important;
}
.rounded .card-img-right {
  border-top-right-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
  border-top-left-radius: initial !important;
  border-bottom-left-radius: initial !important;
}
.rounded .card {
  border-radius: 0.75rem;
}

.rtl .card .card-header .card-icon {
  left: 10px;
  right: initial;
}
.rtl .card-top-buttons {
  right: initial;
  left: 0;
}
.rtl.rounded .card-img,
.rtl.rounded .card-img-fluid,
.rtl.rounded .card-img-overlay {
  border-radius: 0.75rem;
}
.rtl.rounded .card-img-left {
  border-top-right-radius: 0.75rem !important;
  border-bottom-right-radius: 0.75rem !important;
  border-top-left-radius: initial !important;
  border-bottom-left-radius: initial !important;
}
.rtl.rounded .card-img-right {
  border-top-left-radius: 0.75rem !important;
  border-bottom-left-radius: 0.75rem !important;
  border-top-right-radius: initial !important;
  border-bottom-right-radius: initial !important;
}

.card.active {
  box-shadow: 0 3px 30px rgba(0, 0, 0, 0.1), 0 3px 20px rgba(0, 0, 0, 0.1);
}

.dashboard-cards-row {
  display: flex;
  width: 100%;
  flex-direction: row;
  justify-content: stretch;
  align-items: stretch;
  padding: 25px;
  position: relative;
}
.dashboard-cards-row canvas {
  background-color: white;
}

.dashboard-card {
  position: relative;
  display: flex;
  flex: 1 1 auto;
  justify-self: stretch;
  margin-bottom: 32px;
}
.dashboard-card i {
  font-size: 22px;
  line-height: 47px;
  color: #922c88;
}
.dashboard-card .card-text {
  height: 48px;
  line-height: 18px;
}
.dashboard-card .card {
  flex: 1 1 auto;
}

.fullnameParent .space-fullname {
  display: none;
  font-size: 12px;
  position: absolute;
  border-radius: 4px;
  background: #535353;
  transform: translate(-5px, 25px);
  padding: 4px 10px;
  color: white;
  z-index: 100;
  font-weight: 100;
}
.fullnameParent:hover .space-fullname {
  display: block;
}

.modal .modal-header,
.modal .modal-body,
.modal .modal-footer {
  padding: 1.75rem;
}
.modal .modal-header {
  border-bottom: 1px solid #d7d7d7;
}
.modal .modal-footer {
  border-top: 1px solid #d7d7d7;
}
.modal .close {
  color: #474d66;
  text-shadow: initial;
}

.modal-content {
  border: initial;
  border-radius: 0.1rem;
  background: white;
}

.modal-right {
  padding-right: 0 !important;
}
.modal-right .modal-dialog {
  margin: 0 auto;
  margin-right: 0;
  margin-top: 0;
  margin-bottom: 0;
  height: 100%;
  max-width: 380px;
}
.modal-right .modal-content {
  min-height: 100%;
}
.modal-right .modal-header {
  height: 105px;
}
@media (max-width: 1439px) {
  .modal-right .modal-header {
    height: 95px;
  }
}
@media (max-width: 1199px) {
  .modal-right .modal-header {
    height: 85px;
  }
}
@media (max-width: 767px) {
  .modal-right .modal-header {
    height: 75px;
  }
}
.modal-right .modal-footer {
  justify-content: center;
}
.modal-right .modal.fade .modal-dialog {
  transform: translate(25%, 0) !important;
}
.modal-right .modal.show .modal-dialog {
  transform: translate(0, 0) !important;
}

.rtl .modal .modal-header,
.rtl .modal .modal-body,
.rtl .modal .modal-footer {
  padding: 1.75rem;
}
@media (max-width: 575px) {
  .rtl .modal .modal-header,
  .rtl .modal .modal-body,
  .rtl .modal .modal-footer {
    padding: 1.5rem;
  }
}

.popover,
.tooltip {
  top: -145px !important;
}
@media (max-width: 1439px) {
  .popover,
  .tooltip {
    top: -130px !important;
  }
}
@media (max-width: 1199px) {
  .popover,
  .tooltip {
    top: -110px !important;
  }
}
@media (max-width: 767px) {
  .popover,
  .tooltip {
    top: -85px !important;
  }
}

.popover {
  border-radius: 0.1rem;
  background-color: white;
  border-color: #d7d7d7;
  z-index: 1000000;
}
.popover .popover-body {
  color: #474d66;
  padding: 0.5rem 0.75rem;
}

.popover-header {
  background-color: transparent;
  border-bottom: initial;
}

.tooltip-inner {
  padding: 0.5rem 0.75rem;
  color: #474d66;
  background-color: white;
  border-radius: 0.1rem;
  border: 1px solid #d7d7d7;
}

.tooltip.show {
  opacity: 1;
}

.bs-popover-right .arrow::before,
.bs-popover-auto[x-placement^='right'] .arrow::before {
  border-right-color: #d7d7d7;
}

.bs-popover-right .arrow::after,
.bs-popover-auto[x-placement^='right'] .arrow::after {
  border-right-color: white;
}

.bs-popover-left .arrow::before,
.bs-popover-auto[x-placement^='left'] .arrow::before {
  border-left-color: #d7d7d7;
}

.bs-popover-left .arrow::after,
.bs-popover-auto[x-placement^='left'] .arrow::after {
  border-left-color: white;
}

.bs-popover-bottom .arrow::before,
.bs-popover-auto[x-placement^='bottom'] .arrow::before {
  border-bottom-color: #d7d7d7;
}

.bs-popover-bottom .arrow::after,
.bs-popover-auto[x-placement^='bottom'] .arrow::after {
  border-bottom-color: white;
}

.bs-popover-top .arrow::before,
.bs-popover-auto[x-placement^='top'] .arrow::before {
  border-top-color: #d7d7d7;
}

.bs-popover-top .arrow::after,
.bs-popover-auto[x-placement^='top'] .arrow::after {
  border-top-color: white;
}

.tooltip .arrow::before,
.tooltip .arrow::after {
  position: absolute;
  display: block;
  content: '';
  border-color: transparent;
  border-style: solid;
}

.bs-tooltip-right .arrow::before,
.bs-tooltip-auto[x-placement^='right'] .arrow::before,
.bs-tooltip-right .arrow::after,
.bs-tooltip-auto[x-placement^='right'] .arrow::after {
  border-width: 0.5rem 0.5rem 0.5rem 0;
}

.bs-tooltip-right .arrow::before,
.bs-tooltip-auto[x-placement^='right'] .arrow::before {
  left: 0;
  border-right-color: #d7d7d7;
}

.bs-tooltip-right .arrow::after,
.bs-tooltip-auto[x-placement^='right'] .arrow::after {
  left: 1px;
  border-right-color: white;
}

.bs-tooltip-right .arrow::before,
.bs-tooltip-auto[x-placement^='right'] .arrow::before,
.bs-tooltip-right .arrow::after,
.bs-tooltip-auto[x-placement^='right'] .arrow::after {
  border-width: 0.4rem 0.4rem 0.4rem 0;
}

.bs-tooltip-top .arrow::before,
.bs-tooltip-auto[x-placement^='top'] .arrow::before {
  bottom: 0;
  border-top-color: #d7d7d7;
}

.bs-tooltip-top .arrow::before,
.bs-tooltip-auto[x-placement^='top'] .arrow::before,
.bs-tooltip-top .arrow::after,
.bs-tooltip-auto[x-placement^='top'] .arrow::after {
  border-width: 0.5rem 0.5rem 0;
}

.bs-tooltip-top .arrow::after,
.bs-tooltip-auto[x-placement^='top'] .arrow::after {
  bottom: 1px;
  border-top-color: white;
}

.bs-tooltip-top .arrow::before,
.bs-tooltip-auto[x-placement^='top'] .arrow::before,
.bs-tooltip-top .arrow::after,
.bs-tooltip-auto[x-placement^='top'] .arrow::after {
  border-width: 0.4rem 0.4rem 0;
}

.bs-tooltip-bottom .arrow::before,
.bs-tooltip-auto[x-placement^='bottom'] .arrow::before,
.bs-tooltip-bottom .arrow::after,
.bs-tooltip-auto[x-placement^='bottom'] .arrow::after {
  border-width: 0 0.5rem 0.5rem 0.5rem;
}

.bs-tooltip-bottom .arrow::before,
.bs-tooltip-auto[x-placement^='bottom'] .arrow::before {
  top: 0;
  border-bottom-color: #d7d7d7;
}

.bs-tooltip-bottom .arrow::before,
.bs-tooltip-auto[x-placement^='bottom'] .arrow::before,
.bs-tooltip-bottom .arrow::after,
.bs-tooltip-auto[x-placement^='bottom'] .arrow::after {
  border-width: 0 0.5rem 0.5rem 0.5rem;
}

.bs-tooltip-bottom .arrow::after,
.bs-tooltip-auto[x-placement^='bottom'] .arrow::after {
  top: 1px;
  border-bottom-color: white;
}

.bs-tooltip-left .arrow::before,
.bs-tooltip-auto[x-placement^='left'] .arrow::before,
.bs-tooltip-left .arrow::after,
.bs-tooltip-auto[x-placement^='left'] .arrow::after {
  border-width: 0.5rem 0 0.5rem 0.5rem;
}

.bs-tooltip-left .arrow::before,
.bs-tooltip-auto[x-placement^='left'] .arrow::before {
  right: 0;
  border-left-color: #d7d7d7;
}

.bs-tooltip-left .arrow::before,
.bs-tooltip-auto[x-placement^='left'] .arrow::before,
.bs-tooltip-left .arrow::after,
.bs-tooltip-auto[x-placement^='left'] .arrow::after {
  border-width: 0.5rem 0 0.5rem 0.5rem;
}

.bs-tooltip-left .arrow::after,
.bs-tooltip-auto[x-placement^='left'] .arrow::after {
  right: 0;
  border-left-color: white;
}

.rtl .tooltip-label-right .invalid-feedback::before {
  left: initial;
  right: -5px;
  border-right: initial;
  border-left: solid 5px #922c88;
}
.rtl .tooltip-label-right .invalid-feedback::after {
  right: -4px;
  left: initial;
  border-left: solid 5px white;
  border-right: initial;
}
.rtl .tooltip-label-right .invalid-feedback {
  left: initial;
  right: 50px;
}
.rtl .error-l-0 .invalid-feedback {
  left: initial;
  right: 0;
}
.rtl .error-l-25 .invalid-feedback {
  left: initial;
  right: 25px;
}
.rtl .error-l-50 .invalid-feedback {
  left: initial;
  right: 50px;
}
.rtl .error-l-75 .invalid-feedback {
  left: initial;
  right: 75px;
}
.rtl .error-l-100 .invalid-feedback {
  left: initial;
  right: 100px;
}
.rtl .error-l-125 .invalid-feedback {
  left: initial;
  right: 125px;
}
.rtl .error-l-150 .invalid-feedback {
  left: initial;
  right: 150px;
}
.rtl .error-l-175 .invalid-feedback {
  left: initial;
  right: 175px;
}
.rtl .error-l-200 .invalid-feedback {
  left: initial;
  right: 200px;
}
.rtl .error-l-225 .invalid-feedback {
  left: initial;
  right: 225px;
}
.rtl .error-l-250 .invalid-feedback {
  left: initial;
  right: 250px;
}
.rtl .error-l-275 .invalid-feedback {
  left: initial;
  right: 275px;
}

.tooltip-right-bottom .invalid-feedback {
  left: initial;
  right: 0;
  transform: translateX(0);
}
.tooltip-right-bottom .invalid-feedback::before,
.tooltip-right-bottom .invalid-feedback::after {
  left: initial;
  right: 25px;
  margin-left: 0;
}

.tooltip-left-bottom .invalid-feedback {
  left: 0;
  right: initial;
  transform: translateX(0);
}
.tooltip-left-bottom .invalid-feedback::before,
.tooltip-left-bottom .invalid-feedback::after {
  left: 25px;
  right: initial;
  margin-left: 0;
}

.tooltip-center-top .invalid-feedback {
  bottom: 80%;
  transform: translateX(-50%) translateY(50%);
  top: initial;
}
.tooltip-center-top .invalid-feedback::before {
  content: '';
  position: absolute;
  top: initial;
  bottom: -5px;
  border-top: solid 5px #922c88;
  border-bottom: initial;
}
.tooltip-center-top .invalid-feedback::after {
  content: '';
  position: absolute;
  top: initial;
  bottom: -4px;
  border-top: solid 5px white;
  border-bottom: initial;
}

.tooltip-right-top .invalid-feedback {
  bottom: 80%;
  transform: translateX(0) translateY(50%);
  top: initial;
  left: initial;
  right: 0;
}
.tooltip-right-top .invalid-feedback::before {
  content: '';
  position: absolute;
  top: initial;
  bottom: -5px;
  border-top: solid 5px #922c88;
  border-bottom: initial;
  left: initial;
  right: 25px;
  margin-left: 0;
}
.tooltip-right-top .invalid-feedback::after {
  content: '';
  position: absolute;
  top: initial;
  bottom: -4px;
  border-top: solid 5px white;
  border-bottom: initial;
  left: initial;
  right: 25px;
  margin-left: 0;
}

.tooltip-left-top .invalid-feedback {
  bottom: 80%;
  transform: translateX(0) translateY(50%);
  top: initial;
  left: 0;
  right: initial;
}
.tooltip-left-top .invalid-feedback::before {
  content: '';
  position: absolute;
  top: initial;
  bottom: -5px;
  border-top: solid 5px #922c88;
  border-bottom: initial;
  left: 25px;
  right: initial;
  margin-left: 0;
}
.tooltip-left-top .invalid-feedback::after {
  content: '';
  position: absolute;
  top: initial;
  bottom: -4px;
  border-top: solid 5px white;
  border-bottom: initial;
  left: 25px;
  right: initial;
  margin-left: 0;
}

.tooltip-label-right .invalid-feedback {
  transform: translateX(0) translateY(-50%);
  top: 16px;
  left: 50px;
}
.tooltip-label-right .invalid-feedback::before {
  content: '';
  position: absolute;
  left: -5px;
  right: initial;
  margin-left: 0;
  border: initial;
  border-top: solid 5px transparent;
  border-bottom: solid 5px transparent;
  border-right: solid 5px #922c88;
  bottom: initial;
  top: 12px;
  width: 5px;
}
.tooltip-label-right .invalid-feedback::after {
  content: '';
  position: absolute;
  top: initial;
  left: -4px;
  right: initial;
  margin-left: 0;
  width: 5px;
  border: initial;
  border-top: solid 5px transparent;
  border-bottom: solid 5px transparent;
  border-right: solid 5px white;
  bottom: initial;
  top: 12px;
  width: 5px;
}

.error-l-0 .invalid-feedback {
  left: 0px;
}

.error-l-25 .invalid-feedback {
  left: 25px;
}

.error-l-50 .invalid-feedback {
  left: 50px;
}

.error-l-75 .invalid-feedback {
  left: 75px;
}

.error-l-100 .invalid-feedback {
  left: 100px;
}

.error-l-125 .invalid-feedback {
  left: 125px;
}

.error-l-150 .invalid-feedback {
  left: 150px;
}

.error-l-175 .invalid-feedback {
  left: 175px;
}

.error-l-200 .invalid-feedback {
  left: 200px;
}

.error-l-225 .invalid-feedback {
  left: 225px;
}

.error-l-250 .invalid-feedback {
  left: 250px;
}

.error-l-275 .invalid-feedback {
  left: 275px;
}

.error-t-negative .invalid-feedback {
  top: -10px;
}

.rounded .popover,
.rounded .tooltip-inner {
  border-radius: 0.75rem;
}
.rounded .invalid-feedback,
.rounded .valid-tooltip,
.rounded div.error {
  border-radius: 10px;
}

.nav-tabs.separator-tabs {
  border-bottom: 1px solid #d7d7d7;
}

.nav-tabs .nav-link {
  border: initial;
  padding-top: 1rem;
  cursor: pointer;
}

.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  background: initial;
}

.nav-tabs .nav-link.active::before,
.nav-tabs .nav-item.show .nav-link::before {
  content: ' ';
  background: #922c88;
  border-radius: 10px;
  position: absolute;
  width: calc(100% - 1rem);
  height: 5px;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}

.nav-tabs.separator-tabs .nav-link.active::before,
.nav-tabs.separator-tabs .nav-item.show .nav-link::before {
  content: ' ';
  background: #922c88;
  border-radius: 10px;
  position: absolute;
  width: 100%;
  height: 2px;
  left: 0;
  bottom: 0;
  top: unset;
  transform: initial;
}

.nav-tabs.separator-tabs .nav-link {
  border: initial;
  padding-top: 1rem;
  background: initial;
  padding-left: 0;
  padding-top: 0.5rem;
  padding-right: 0;
  margin-right: 1.5rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  color: #8f8f8f;
}

.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  border: initial;
  position: relative;
  color: #922c88;
}

.nav-tabs .nav-link:hover,
.nav-tabs .nav-link:focus {
  border: initial;
  color: #922c88;
}

.navbar {
  background: white;
  height: 105px;
  padding: 1.5rem 0;
  box-shadow: 0 1px 15px rgba(0, 0, 0, 0.04), 0 1px 6px rgba(0, 0, 0, 0.04);
}
.navbar #notificationDropdown {
  width: 260px;
  padding: 1.5rem !important;
  height: 280px;
  right: 15px;
}
.navbar #iconMenuDropdown {
  width: 240px;
  padding: 1.5rem !important;
  height: 280px;
}
.navbar .icon-menu-item {
  width: 90px;
  display: inline-block;
  text-align: center;
  margin-bottom: 1.7rem;
  color: rgba(71, 77, 102, 0.8);
}
.navbar .icon-menu-item i {
  font-size: 28px;
  line-height: 42px;
}
.navbar .icon-menu-item span {
  text-align: center;
  padding: 0 10px;
  line-height: 14px;
}
.navbar .icon-menu-item:hover,
.navbar .icon-menu-item:focus {
  color: #922c88;
}
.navbar .menu-button-mobile {
  color: #8f8f8f;
  text-align: center;
  margin-left: 15px;
}
.navbar .menu-button-mobile svg {
  height: 12px;
  fill: #474d66;
}
@media (max-width: 767px) {
  .navbar .menu-button-mobile {
    width: 20px;
  }
}
.navbar .menu-button {
  color: #8f8f8f;
  width: 120px;
  text-align: center;
}
.navbar .menu-button svg {
  height: 12px;
}
.navbar .menu-button .main {
  fill: #474d66;
  transition: fill 300ms;
  width: 10px;
}
.navbar .menu-button .sub {
  fill: #474d66;
  transition: fill 300ms;
}
.navbar .menu-button:hover {
  color: #922c88;
}
@media (max-width: 1439px) {
  .navbar .menu-button {
    width: 110px;
  }
}
@media (max-width: 1199px) {
  .navbar .menu-button {
    width: 100px;
  }
}
@media (max-width: 767px) {
  .navbar .menu-button {
    width: 60px;
  }
}
.navbar .navbar-left {
  flex-basis: 40%;
}
@media (max-width: 900px) {
  .navbar .hidewhen900 {
    display: none !important;
  }
}
.navbar .avatar-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.navbar .navbar-right {
  flex-basis: 40%;
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.navbar .navbar-right .rc-switch {
  margin-top: -2px;
}
.navbar .navbar-right .user {
  margin-right: 60px;
}
@media (max-width: 1439px) {
  .navbar .navbar-right .user {
    margin-right: 50px;
  }
}
@media (max-width: 1199px) {
  .navbar .navbar-right .user {
    margin-right: 40px;
  }
}
@media (max-width: 767px) {
  .navbar .navbar-right .user {
    margin-right: 15px;
  }
}
.navbar .navbar-logo {
  width: 110px;
  height: 35px;
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
}
.navbar .navbar-logo .logo {
  display: flex;
  position: relative;
  width: 100%;
  height: 100%;
  background: url('/assets/logos/logo.svg') no-repeat;
  background-size: contain;
  background-position: center center;
}
.navbar .navbar-logo .logo::after {
  content: 'Public Beta';
  background-color: #f7f7f7;
  position: absolute;
  padding: 5px 10px;
  border-radius: 10px;
  left: 100%;
  width: -moz-max-content;
  width: max-content;
  font-size: 11px;
  margin-top: 5px;
  pointer-events: none;
}
@media (max-width: 1000px) {
  .navbar .navbar-logo .logo::after {
    padding: 3px 7px;
    bottom: -50%;
    left: 0%;
    right: 0%;
    font-size: 10px;
    margin: 0 auto;
  }
}
@media (max-width: 360px) {
  .navbar .navbar-logo .logo::after {
    left: 100%;
    font-size: 10px;
    padding: 3px 5px;
    margin-top: 2px;
    bottom: 22%;
  }
}
@media (max-width: 767px) {
  .navbar .navbar-logo {
    width: 90px;
  }
}
@media (max-width: 360px) {
  .navbar .navbar-logo {
    position: absolute;
    top: -10px;
  }
}
.navbar .language-button {
  background: #f8f8f8;
  border: initial;
  font-size: 0.8rem;
  color: #8f8f8f;
  padding: 0.6rem 1rem;
}
@media (max-width: 575px) {
  .navbar .language-button {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
}
.navbar .search {
  position: relative;
  width: 230px;
  border-radius: 20px;
  background: #f8f8f8;
}
.navbar .search input {
  border: initial;
  background: transparent;
  outline: initial !important;
  padding: 0.5rem 1rem;
  line-height: 1.75;
  font-size: 0.8rem;
  width: 93%;
  color: #474d66;
}
.navbar .search .search-icon {
  font-size: 17px;
  border-radius: 10px;
  color: #d7d7d7;
  position: absolute;
  width: 40px;
  height: 40px;
  bottom: -10px;
  right: 3px;
  text-align: center;
  cursor: pointer;
}
.navbar .search .search-icon:hover {
  color: #922c88;
}
@media (max-width: 1199px) {
  .navbar .search {
    width: 161px;
  }
  .navbar .search input {
    width: 85%;
  }
}
@media (max-width: 991px) {
  .navbar .search {
    width: 115px;
  }
  .navbar .search input {
    width: 85%;
  }
}
@media (max-width: 767px) {
  .navbar .search {
    width: 30px;
    height: 30px;
    background: initial;
    margin-left: 0.6rem;
    color: rgba(71, 77, 102, 0.7);
  }
  .navbar .search input {
    display: none;
  }
  .navbar .search .search-icon {
    font-size: 17px;
    width: 30px;
    height: 30px;
    bottom: -3px;
    right: 0;
    color: inherit;
  }
  .navbar .search.mobile-view {
    display: block;
    width: 100%;
    position: fixed;
    z-index: 2;
    background: white;
    left: 0;
    top: 0;
    height: 75px;
    margin-left: 15px;
  }
  .navbar .search.mobile-view input {
    display: block;
    width: 100%;
    height: 70px;
    padding-left: 0;
  }
  .navbar .search.mobile-view span {
    top: 50%;
    transform: translateY(-50%);
    right: 25px;
  }
}
.navbar .header-icons {
  margin-right: 1rem;
}
@media (max-width: 575px) {
  .navbar .header-icons {
    margin-right: 0;
  }
}
.navbar .header-icon {
  font-size: 16px;
  color: #8f8f8f;
  padding-left: 0.6rem;
  padding-right: 0.6rem;
  vertical-align: initial;
}
@media (max-width: 575px) {
  .navbar .header-icon {
    padding-left: 0.3rem;
    padding-right: 0.3rem;
  }
}
.navbar .header-icon:hover {
  color: #922c88;
}
.navbar .header-icon.notificationButton {
  cursor: pointer;
}
.navbar .header-icon.notificationButton .count {
  font-size: 9px;
  color: #922c88;
  border: 1px solid #922c88;
  border-radius: 10px;
  position: absolute;
  width: 15px;
  height: 15px;
  text-align: center;
  font-weight: 700;
  top: -5px;
  right: 1px;
  line-height: 11px;
}
@media (max-width: 575px) {
  .navbar .header-icon.notificationButton .count {
    right: -1px;
  }
}
.navbar .user {
  position: relative;
  color: #474d66;
}
.navbar .user button {
  color: #474d66;
}
.navbar .user img {
  margin-left: 10px;
  border-radius: 30px;
  width: 40px;
}
@media (max-width: 991px) {
  .navbar .user .name {
    display: none;
  }
}
@media (max-width: 767px) {
  .navbar .user {
    margin-left: initial;
  }
  .navbar .user img {
    width: 30px;
  }
  .navbar .user:after {
    font-size: 11px;
    width: 14px;
    height: 14px;
    bottom: -3px;
    right: -3px;
  }
}
.navbar .navname {
  width: 130px;
  white-space: nowrap;
  overflow: hidden;
  margin-top: 2px;
  text-overflow: ellipsis;
  font-size: 12px;
  text-align: center;
  bottom: -20px;
  right: -50px;
}
@media (max-width: 1439px) {
  .navbar {
    height: 95px;
  }
}
@media (max-width: 1199px) {
  .navbar {
    height: 85px;
    padding: 1rem 0 1rem 0;
  }
  .navbar .navname {
    width: 100px;
    bottom: -20px;
    right: -16px;
  }
}
@media (max-width: 767px) {
  .navbar {
    height: 75px;
    padding: 10px 0;
  }
}

.rtl .navbar .navbar-right {
  text-align: left;
}
.rtl .navbar .menu-button-mobile {
  margin-right: 15px;
  margin-left: initial;
}
.rtl .navbar .menu-button-mobile:hover {
  color: #922c88 !important;
  fill: #922c88 !important;
}
.rtl .navbar .menu-button {
  text-align: center;
}
.rtl .navbar .icon-menu-item {
  text-align: center;
}
.rtl .navbar .icon-menu-item i {
  text-align: center;
}
.rtl .navbar .icon-menu-item span {
  text-align: center;
}
.rtl .navbar .search .search-icon {
  right: initial;
  left: 5px;
}
@media (max-width: 767px) {
  .rtl .navbar .search {
    margin-right: 0.6rem;
    margin-left: initial;
  }
  .rtl .navbar .search input {
    display: none;
  }
  .rtl .navbar .search.mobile-view {
    margin-left: initial;
    margin-right: 15px;
  }
  .rtl .navbar .search.mobile-view input {
    display: block;
    width: 100%;
    height: 70px;
    padding-left: 0;
  }
  .rtl .navbar .search.mobile-view span {
    left: 15px;
    right: initial;
  }
}
.rtl .navbar .navbar-right {
  text-align: left;
}
.rtl .navbar .navbar-right .user {
  margin-left: 60px;
  margin-right: 0;
}
@media (max-width: 1439px) {
  .rtl .navbar .navbar-right .user {
    margin-left: 50px;
    margin-right: 0;
  }
}
@media (max-width: 1199px) {
  .rtl .navbar .navbar-right .user {
    margin-left: 40px;
    margin-right: 0;
  }
}
@media (max-width: 767px) {
  .rtl .navbar .navbar-right .user {
    margin-left: 15px;
    margin-right: 0;
  }
}
.rtl .navbar .navbar-right .user img {
  margin-right: 10px;
  margin-left: initial;
}
.rtl .navbar .navbar-right .header-icons {
  margin-right: initial;
  margin-left: 0.5rem;
}
@media (max-width: 575px) {
  .rtl .navbar .navbar-right .header-icons {
    margin-left: 0;
  }
}
.rtl .navbar .navbar-right #notificationButton .count {
  text-align: center;
}
@media (max-width: 575px) {
  .rtl .navbar .navbar-right #notificationButton .count {
    left: -1px;
  }
}
.rtl .navbar .navbar-right #userDropdown,
.rtl .navbar .navbar-right #userDropdown * {
  text-align: left;
}

@media (max-width: 705px) {
  .hideDropName {
    display: none !important;
  }
}
.navbarcont {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.navbarParent {
  display: flex;
  flex-direction: column;
}

@media (min-width: 600px) {
  .hideabove600 {
    display: none !important;
  }
}

.navTheme {
  font-size: 10px !important;
}

.rcChecked span {
  left: 1px !important;
  top: -1px !important;
}

.rcUnChecked span {
  left: 16px !important;
  top: -1px !important;
}

.app-menu {
  z-index: 1;
  box-shadow: 0 1px 15px rgba(0, 0, 0, 0.04), 0 1px 6px rgba(0, 0, 0, 0.04);
  width: 280px;
  float: right;
  background: white;
  transition: transform 300ms;
  height: calc(100% - 105px);
  position: fixed;
  right: 0;
  top: 105px;
}
@media (max-width: 1439px) {
  .app-menu {
    top: 95px;
    height: calc(100% - 95px);
  }
}
@media (max-width: 1199px) {
  .app-menu {
    top: 85px;
    transform: translateX(280px);
    height: calc(100% - 85px);
  }
}
@media (max-width: 767px) {
  .app-menu {
    top: 75px;
    height: calc(100% - 75px);
  }
}
.app-menu .scrollbar-container {
  margin-right: unset;
  padding-right: unset;
}
.app-menu .scrollbar-container .ps__rail-y {
  right: 2px !important;
}
.app-menu.shown {
  transform: translateX(0);
}
.app-menu .app-menu-button {
  cursor: pointer;
  position: absolute;
  left: -28px;
  background: white;
  top: 45px;
  padding: 12px 8px 12px 5px;
  border-radius: 0.2rem;
  color: #8f8f8f !important;
  box-shadow: -2px 0px 5px rgba(0, 0, 0, 0.04);
  font-size: 1rem;
  line-height: 0;
}
@media (max-width: 1439px) {
  .app-menu .app-menu-button {
    top: 36px;
  }
}
@media (max-width: 1199px) {
  .app-menu .app-menu-button {
    top: 27px;
  }
}
@media (max-width: 767px) {
  .app-menu .app-menu-button {
    top: 13px;
  }
}
.app-menu ul li {
  margin-bottom: 5px;
}
.app-menu ul li a {
  font-size: 13px;
  display: block;
  padding: 3px 0;
}
.app-menu ul li a:hover i {
  color: #922c88;
}
.app-menu ul li i {
  font-size: 1.2em;
  margin-right: 10px;
  color: #8f8f8f;
  vertical-align: unset;
}
@media (max-width: 767px) {
  .app-menu ul li i {
    font-size: 20px;
  }
}
.app-menu ul li.active i,
.app-menu ul li.active a {
  color: #922c88;
}

.app-row {
  padding-right: 280px;
}
@media (max-width: 1199px) {
  .app-row {
    padding-right: 0;
  }
}

.av-invalid .av-label,
.form-group.text-danger,
.was-validated .custom-control-input:invalid ~ .custom-control-label,
.custom-control-input.is-invalid ~ .custom-control-label,
.was-validated .form-check-input:invalid ~ .form-check-label,
.form-check-input.is-invalid ~ .form-check-label {
  color: #474d66 !important;
}

.was-validated .form-control:invalid,
.form-control.is-invalid,
.was-validated .custom-select:invalid,
.custom-select.is-invalid,
.was-validated .form-control:invalid:focus,
.form-control.is-invalid:focus,
.was-validated .custom-select:invalid:focus,
.custom-select.is-invalid:focus {
  border-color: #d7d7d7;
  background: initial;
}

.invalid-feedback {
  border-radius: 0.1rem;
  padding: 0.5rem 1rem;
  font-size: 0.76rem;
  color: #474d66;
  background: white;
  border: 1px solid #922c88;
  text-align: center;
  width: unset !important;
  position: absolute;
  z-index: 4;
  margin-top: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  box-shadow: 0 1px 15px rgba(0, 0, 0, 0.04), 0 1px 6px rgba(0, 0, 0, 0.04);
}
.invalid-feedback::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -2.5px;
  margin-left: 50%;
  width: 10px;
  height: 5px;
  border-bottom: solid 5px #922c88;
  border-left: solid 5px transparent;
  border-right: solid 5px transparent;
}
.invalid-feedback::after {
  content: '';
  position: absolute;
  top: -4px;
  left: -2.5px;
  margin-left: 50%;
  width: 10px;
  height: 5px;
  border-bottom: solid 5px white;
  border-left: solid 5px transparent;
  border-right: solid 5px transparent;
}

@media (max-width: 575px) {
  .form-inline .form-group {
    width: 100%;
  }
}

.form-check-label,
.custom-control-label {
  line-height: 24px !important;
}

.form-control:focus {
  background: white;
  color: #474d66;
}

.bootstrap-tagsinput {
  width: 100%;
}

.bootstrap-tagsinput input {
  padding: 0;
}

.form-control:focus {
  border-color: rgba(146, 44, 136, 0.6);
}

select.form-control:not([size]):not([multiple]) {
  height: initial;
}

.custom-control-input:disabled ~ .custom-control-label::before {
  background-color: rgba(71, 77, 102, 0.25) !important;
}

.custom-control-input:active ~ .custom-control-label::before {
  background-color: transparent;
}

.custom-checkbox .custom-control-label.indeterminate::after {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath stroke='%23FFFFFF' d='M0 2h4'/%3E%3C/svg%3E");
}

.custom-checkbox .custom-control-input:checked ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23FFFFFF' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E");
}

.custom-radio .custom-control-input:checked ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23FFFFFF'/%3E%3C/svg%3E");
}

.custom-control-label,
.custom-control-input {
  outline: initial !important;
  box-shadow: initial !important;
}

.was-validated .custom-control-input:invalid ~ .custom-control-label::before,
.custom-control-input.is-invalid ~ .custom-control-label::before {
  background-color: initial;
}

.custom-control-input {
  left: 1px;
  top: 3px;
  opacity: 0;
  z-index: 1;
}

.custom-control-label::before {
  border: 1px solid #909090;
  background: initial;
}

.was-validated .custom-control-input:invalid ~ .custom-control-label::before,
.custom-control-input.is-invalid ~ .custom-control-label::before {
  border-color: #909090;
}

.custom-checkbox
  .custom-control-input:indeterminate
  ~ .custom-control-label::before {
  background-color: #922c88;
}

.custom-control-input:checked ~ .custom-control-label::before {
  background-color: #922c88;
  box-shadow: initial !important;
  border: 1px solid #922c88;
}

.custom-checkbox .custom-control-input:checked ~ .custom-control-label::before,
.custom-radio .custom-control-input:checked ~ .custom-control-label::before {
  background-color: #922c88;
  box-shadow: initial !important;
}

.custom-checkbox .custom-control-input:checked ~ .custom-control-label::before,
.custom-radio .custom-control-input:checked ~ .custom-control-label::before {
  background-color: #922c88;
  box-shadow: initial !important;
}

.custom-control-label::after,
.custom-control-label::before {
  box-shadow: initial !important;
  outline: initial !important;
}

.check-all {
  padding-top: 0;
}
.check-all label.custom-control-label {
  vertical-align: top;
  line-height: initial !important;
}
.check-all label.custom-control-label:before,
.check-all label.custom-control-label:after {
  border: initial !important;
  background: initial !important;
}
.check-all span.custom-control-label {
  line-height: 1.1rem !important;
}
.check-all span.custom-control-label:before {
  top: 0 !important;
}
.check-all span.custom-control-label.indeterminate:after {
  top: 0 !important;
}
.check-all .custom-control {
  margin-top: 11px;
}
.check-all .custom-control-input:checked ~ .custom-control-label span::after {
  top: 0 !important;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23FFFFFF' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E");
}

.custom-control-label::before {
  top: 0.2rem;
}

.custom-control-label::after {
  top: 0.2rem;
}

.btn.rotate-icon-click i {
  transition: transform 0.5s;
}

.btn.rotate i {
  transform: rotate(180deg);
}

.btn .custom-control-label::before {
  border: 1px solid #fff;
}

.btn .custom-control-input:checked ~ .custom-control-label::before {
  border: 1px solid #fff;
}

.btn-group-icon {
  line-height: 22px;
}

.input-group-text {
  border-radius: 0.1rem;
  background-color: white;
  border-color: #d7d7d7;
  color: #474d66;
  font-size: 0.8rem;
  line-height: 1;
  padding: 0.5rem 0.75rem;
}

.form-group {
  position: relative;
}

.blank-space-name {
  width: 100%;
  height: 100%;
  z-index: 1;
}

.form-control {
  border-radius: 0.1rem;
  outline: initial !important;
  box-shadow: initial !important;
  font-size: 0.8rem;
  border: 1px solid #d7d7d7;
  background: white;
  color: #474d66;
  height: initial;
  padding: 0.5rem 0.75rem;
  line-height: 1;
}

textarea.form-control {
  line-height: 1.5;
}

@keyframes autofill {
  to {
    color: #474d66;
    background: transparent;
  }
}
input:-webkit-autofill {
  animation-name: autofill;
  animation-fill-mode: both;
}

input:-webkit-autofill {
  -webkit-text-fill-color: #474d66 !important;
}

.form-control-sm,
.input-group-sm > .form-control,
.input-group-sm > .input-group-prepend > .input-group-text,
.input-group-sm > .input-group-append > .input-group-text,
.input-group-sm > .input-group-prepend > .btn,
.input-group-sm > .input-group-append > .btn {
  border-radius: 0.1rem;
}

.form-control-lg,
.input-group-lg > .form-control,
.input-group-lg > .input-group-prepend > .input-group-text,
.input-group-lg > .input-group-append > .input-group-text,
.input-group-lg > .input-group-prepend > .btn,
.input-group-lg > .input-group-append > .btn {
  border-radius: 0.1rem;
}

.custom-select {
  border-radius: 0.1rem;
  padding: 0.75rem 0.75rem 0.5rem 0.75rem;
}

.input-group > .form-control:not(:first-child),
.input-group > .custom-select:not(:first-child) {
  outline: initial !important;
  box-shadow: initial !important;
}

.custom-select {
  height: calc(2.5rem + 2px);
}

.custom-select:focus {
  border-color: #922c88;
}

.custom-file-input:focus ~ .custom-file-label {
  border-color: rgba(146, 44, 136, 0.6);
}

.custom-file-label::after {
  background: white;
  color: #474d66;
  border-color: #d7d7d7;
}

.custom-file-input {
  box-shadow: initial !important;
}

.custom-file-label {
  background: white;
  border-color: #d7d7d7;
}

.custom-file-label {
  box-shadow: initial !important;
  border-radius: 0.1rem;
  height: calc(2.5rem + 2px);
  padding: 0.75rem 0.75rem 0.5rem 0.75rem;
}

.custom-file {
  height: calc(2.5rem + 2px);
}

.custom-file-label:focus,
.custom-file-input:focus {
  border-color: #922c88;
}

.custom-file-label::after {
  height: calc(2.5rem + 2px - 2px);
  padding: 0.75rem 0.75rem 0.5rem 0.75rem;
}

.react-tagsinput-input {
  padding-top: 2px !important;
  padding-bottom: 2px !important;
  margin-top: 0px !important;
  font-family: 'Nunito', sans-serif !important;
  width: -moz-fit-content !important;
  width: fit-content !important;
}

.search-sm {
  position: relative;
  border: 1px solid #8f8f8f;
  border-radius: 15px;
}
.search-sm input {
  outline: initial !important;
  border: none;
  background: none;
  padding: 0.5rem 1rem 0.5rem 1rem;
  padding-left: 40px;
  padding-right: 30px;
  font-size: 1rem;
  line-height: 1.3;
  width: 100% !important;
  max-width: 80vw;
  color: #474d66;
}
.search-sm input::-moz-placeholder {
  font-size: 12px;
}
.search-sm input::placeholder {
  font-size: 12px;
}
.search-sm .search-sm-btn {
  position: absolute;
  background-color: transparent;
  border: none;
  outline: none;
  width: 40px;
  min-height: 30px;
  left: 4px;
  top: 8px;
  height: -moz-fit-content;
  height: fit-content;
}
.search-sm .search-sm-btn:hover {
  background-color: transparent !important;
  color: #8f8f8f !important;
}
.search-sm .search-sm-btn .search-sm-icon {
  font-size: 1.2rem;
  text-align: center;
}
.search-sm .search-sm-btn .search-sm-icon:hover {
  color: #922c88;
}
.search-sm .search-sm-btn-space {
  position: absolute;
  background-color: transparent;
  border: none;
  outline: none;
  width: 40px;
  min-height: 30px;
  left: 4px;
  top: -13px;
  height: -moz-fit-content;
  height: fit-content;
}
@media (max-width: 1438px) {
  .search-sm .search-sm-btn-space {
    top: -5px;
  }
}
.search-sm .search-sm-btn-space:hover {
  background-color: transparent !important;
  color: #8f8f8f !important;
}
.search-sm .search-sm-btn-space .search-sm-icon {
  font-size: 1.2rem;
  text-align: center;
}
.search-sm .search-sm-btn-space .search-sm-icon:hover {
  color: #922c88;
}
.search-sm .search-sm-btn-2 {
  position: absolute;
  background-color: transparent;
  border: none;
  outline: none;
  width: 40px;
  min-height: 30px;
  right: 1px;
  top: 4px;
  height: -moz-fit-content;
  height: fit-content;
}
.search-sm .search-sm-btn-2:hover {
  background-color: transparent !important;
  color: #8f8f8f !important;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0px 1000px white inset !important;
}

.rtl .btn-group > .btn:first-child {
  margin-left: -1px;
}
.rtl .top-right-button-container {
  float: left;
}
@media (max-width: 575px) {
  .rtl .top-right-button-container {
    float: initial;
  }
}
@media (max-width: 575px) {
  .rtl .top-right-button-container .top-right-button {
    margin-left: 5px;
  }
}
.rtl .comment-container input {
  border-top-left-radius: initial;
  border-bottom-left-radius: initial;
  border-top-right-radius: 50px !important;
  border-bottom-right-radius: 50px !important;
  padding-left: 0.75rem;
  padding-right: 20px;
}
.rtl .dropdown-toggle-split:after,
.rtl .dropright .dropdown-toggle-split:after,
.rtl .dropup .dropdown-toggle-split:after {
  margin-left: 2px;
  margin-right: 2px;
}
.rtl
  .input-group
  > .input-group-append:last-child
  > .btn:not(:last-child):not(.dropdown-toggle),
.rtl
  .input-group
  > .input-group-append:last-child
  > .input-group-text:not(:last-child),
.rtl .input-group > .input-group-append:not(:last-child) > .btn,
.rtl .input-group > .input-group-append:not(:last-child) > .input-group-text,
.rtl .input-group > .input-group-prepend > .btn,
.rtl .input-group > .input-group-prepend > .input-group-text {
  border-radius: 0.1rem;
}
.rtl .btn-group.dropleft .dropdown-toggle-split {
  border-radius: 0.1rem;
}
.rtl .input-group-append .btn + .btn,
.rtl .input-group-append .btn + .input-group-text,
.rtl .input-group-append .input-group-text + .btn,
.rtl .input-group-append .input-group-text + .input-group-text,
.rtl .input-group-prepend .btn + .btn,
.rtl .input-group-prepend .btn + .input-group-text,
.rtl .input-group-prepend .input-group-text + .btn,
.rtl .input-group-prepend .input-group-text + .input-group-text {
  margin-left: 0px;
  margin-right: -1px;
}
.rtl .input-group > .input-group-append > .btn,
.rtl .input-group > .input-group-append > .input-group-text,
.rtl .input-group > .input-group-prepend:first-child > .btn:not(:first-child),
.rtl
  .input-group
  > .input-group-prepend:first-child
  > .input-group-text:not(:first-child),
.rtl .input-group > .input-group-prepend:not(:first-child) > .btn,
.rtl .input-group > .input-group-prepend:not(:first-child) > .input-group-text {
  border-radius: 0.1rem;
}
.rtl .input-group-text {
  border-radius: 0.1rem !important;
}
.rtl .input-group-prepend {
  margin-right: 0;
  margin-left: -1px;
}
.rtl
  .input-group
  > .input-group-append:last-child
  > .btn:not(:last-child):not(.dropdown-toggle),
.rtl
  .input-group
  > .input-group-append:last-child
  > .input-group-text:not(:last-child),
.rtl .input-group > .input-group-append:not(:last-child) > .btn,
.rtl .input-group > .input-group-append:not(:last-child) > .input-group-text,
.rtl .input-group > .input-group-prepend > .btn,
.rtl .input-group > .input-group-prepend > .input-group-text {
  border-radius: 50px;
}
.rtl .input-group > .input-group-append > .btn,
.rtl .input-group > .input-group-append > .input-group-text,
.rtl .input-group > .input-group-prepend:first-child > .btn:not(:first-child),
.rtl
  .input-group
  > .input-group-prepend:first-child
  > .input-group-text:not(:first-child),
.rtl .input-group > .input-group-prepend:not(:first-child) > .btn,
.rtl .input-group > .input-group-prepend:not(:first-child) > .input-group-text {
  border-radius: 50px;
}
.rtl
  .input-group
  > .input-group-append:last-child
  > .btn:not(:last-child):not(.dropdown-toggle),
.rtl
  .input-group
  > .input-group-append:last-child
  > .input-group-text:not(:last-child),
.rtl .input-group > .input-group-append:not(:last-child) > .btn,
.rtl .input-group > .input-group-append:not(:last-child) > .input-group-text,
.rtl .input-group > .input-group-prepend > .btn,
.rtl .input-group > .input-group-prepend > .input-group-text {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.rtl .input-group > .input-group-prepend:first-child > .btn:not(:first-child) {
  border-radius: 0 !important;
}
.rtl .input-group > .input-group-append > .btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.rtl
  .input-group
  > .input-group-append:last-child
  > .btn:not(:last-child):not(.dropdown-toggle) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.rtl .btn-group > .btn:not(:first-child),
.rtl .btn-group > .btn-group:not(:first-child) > .btn {
  border-radius: initial;
}
.rtl .btn-group > .btn:first-child {
  border-top-right-radius: 50px !important;
  border-bottom-right-radius: 50px !important;
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}
.rtl .btn-group > .btn:last-of-type {
  border-top-left-radius: 50px !important;
  border-bottom-left-radius: 50px !important;
}
.rtl .custom-control {
  padding-right: 1.5rem;
  padding-left: inherit;
  margin-right: inherit;
  margin-left: initial;
}
.rtl .react-tagsinput {
  padding-right: 5px;
  padding-left: 0;
}
.rtl .custom-control-inline {
  margin-right: 0;
  margin-left: 1rem;
}
.rtl .form-check-inline {
  margin-left: 0.75rem;
  margin-right: 0;
}
.rtl .form-check-input {
  margin-right: 0;
}
.rtl .form-check {
  padding-left: 0;
  padding-right: 0;
}
.rtl .form-check-label {
  padding-right: 0;
}
.rtl .select2-selection__arrow {
  right: initial;
  left: 12px;
}
.rtl .custom-switch .custom-switch-input + .custom-switch-btn:after {
  left: 0;
}
.rtl .custom-switch .custom-switch-input:checked + .custom-switch-btn:after {
  left: -28px;
}
.rtl
  .custom-switch-small
  .custom-switch-input:checked
  + .custom-switch-btn:after {
  left: -18px;
}
.rtl .bootstrap-tagsinput .tag {
  padding-right: 10px;
  padding-left: 20px;
}
.rtl .bootstrap-tagsinput .tag span {
  margin-left: 0px;
  position: relative;
}
.rtl .bootstrap-tagsinput .tag span:after {
  position: absolute;
  top: -2px;
  left: -15px;
}
.rtl .select-from-library .modal-body.scroll {
  margin-left: 0;
}
.rtl .select-from-library .modal-body.scroll .ps__rail-y {
  left: 10px !important;
}
.rtl .select-from-library .sfl-item-container .card-body {
  padding-right: 1.75rem !important;
}
@media (max-width: 575px) {
  .rtl .select-from-library .sfl-item-container .card-body {
    padding: 1.5rem !important;
  }
}
.rtl .select-from-library .sfl-item-container .custom-control {
  padding-left: 0.25rem !important;
}

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: white;
  background-color: #922c88;
}

.nav-pills .nav-link {
  border-radius: 50px;
}

.nav-link {
  padding: 0.5rem 1rem;
}
@media (max-width: 767px) {
  .nav-link {
    padding: 0.5rem 0.5rem;
  }
}
.nav-link.active {
  color: #922c88;
}

.page-item {
  padding: 10px;
}
@media (max-width: 767px) {
  .page-item {
    padding: 3px;
  }
}
.page-item .page-link {
  outline: initial !important;
  box-shadow: initial !important;
  line-height: 1.7;
  border-radius: 40px !important;
  min-width: 38px;
  text-align: center;
  height: 38px;
  padding: 0.55rem 0;
}
@media (max-width: 575px) {
  .page-item .page-link {
    min-width: 30px;
    height: 30px;
    line-height: 0.9;
    font-size: 0.76rem;
  }
}
.page-item .page-link.next {
  background: #922c88;
  color: white;
  border: 1px solid #922c88;
}
.page-item .page-link.prev,
.page-item .page-link.prev {
  background: #922c88;
  border: 1px solid #922c88;
  color: white;
}
.page-item .page-link.first,
.page-item .page-link.last {
  background: transparent;
  color: #922c88;
  border: 1px solid #922c88;
  border-radius: 30px;
}
.page-item .page-link.first:hover,
.page-item .page-link.last:hover {
  background: #922c88;
  color: white;
  border: 1px solid #922c88;
}
.page-item .page-link:hover {
  background-color: transparent;
  border-color: #b938ad;
  color: #922c88;
}
.page-item.active .page-link {
  background: transparent;
  border: 1px solid #922c88;
  color: #922c88;
}
.page-item.disabled .page-link {
  border-color: #d7d7d7;
  color: #d7d7d7;
  background: transparent;
}

.page-link {
  background-color: transparent;
  border-color: transparent;
  color: #474d66;
}

.btn-sm.page-link {
  padding: 0.5rem 0.5rem;
}

.pagination-lg .page-item {
  padding: 15px;
}
@media (max-width: 767px) {
  .pagination-lg .page-item {
    padding: 3px;
  }
}
.pagination-lg .page-item .page-link {
  min-width: 50px;
  height: 50px;
}
@media (max-width: 767px) {
  .pagination-lg .page-item .page-link {
    min-width: 38px;
    height: 38px;
    padding: 0.55rem 0;
    line-height: 1.5;
    font-size: 0.8rem;
  }
}
@media (max-width: 575px) {
  .pagination-lg .page-item .page-link {
    min-width: 30px;
    height: 30px;
    line-height: 0.9;
    font-size: 0.76rem;
  }
}

.pagination-sm .page-item {
  padding: 6px;
}
@media (max-width: 767px) {
  .pagination-sm .page-item {
    padding: 3px;
  }
}
.pagination-sm .page-item .page-link {
  min-width: 30px;
  height: 30px;
  line-height: 0.9;
  font-size: 0.76rem;
}

.rtl .pagination .page-link.prev i::before {
  content: '\e606';
}
.rtl .pagination .page-link.next i::before {
  content: '\e605';
}
.rtl .pagination .page-link.first i::before {
  content: '\e074';
}
.rtl .pagination .page-link.last i::before {
  content: '\e06f';
}
.rtl .nav,
.rtl .pagination {
  padding-right: 0;
}
.rtl .nav-tabs.separator-tabs .nav-link {
  margin-left: 1.5rem;
  margin-right: 0;
}
.rtl .nav-pills .nav-link {
  text-align: center;
}

#notificationDropdown .nav-link {
  padding: 0 !important;
}

.StoryLHeader {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
@media (max-width: 1440px) {
  .StoryLHeader {
    padding: 0 15% 0 15%;
  }
  .StoryLHeaderTitle {
    width: 31%;
  }
  .StoryLHeaderCC {
    width: 30%;
  }
  .StoryLHeaderCD {
    width: 43%;
  }
  .StoryLHeaderStatus {
    width: 12%;
  }
}
@media (max-width: 1024px) {
  .StoryLHeader {
    padding: 0px 13% 0 11%;
  }
  .StoryLHeaderTitle {
    width: 28%;
  }
  .StoryLHeaderCC {
    width: 25%;
  }
  .StoryLHeaderCD {
    width: 37%;
  }
  .StoryLHeaderStatus {
    width: 11%;
  }
}
@media (max-width: 700px) {
  .StoryLHeader {
    padding: 0px 18% 0 19%;
  }
  .StoryLHeaderTitle {
    width: 28%;
  }
  .StoryLHeaderCC {
    width: 25%;
  }
  .StoryLHeaderCD {
    width: 43%;
  }
  .StoryLHeaderStatus {
    width: 11%;
  }
}
@media (max-width: 575px) {
  .StoryLHeader {
    padding: 0 20% 0 23%;
  }
  .StoryLHeaderTitle {
    width: 16%;
  }
  .StoryLHeaderCC {
    width: 12%;
  }
  .StoryLHeaderCD {
    width: 51%;
  }
  .StoryLHeaderStatus {
    width: 10%;
  }
}
@media (max-width: 525px) {
  .StoryLHeader {
    display: none;
  }
}
@media (min-width: 1440px) {
  .StoryLHeader {
    padding: 0 14% 0 13%;
  }
  .StoryLHeaderTitle {
    width: 28%;
  }
  .StoryLHeaderCC {
    width: 29%;
  }
  .StoryLHeaderCD {
    width: 39%;
  }
  .StoryLHeaderStatus {
    width: 10%;
  }
}

.loading-overlay {
  z-index: 2050;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
}

.loading {
  display: inline-block;
  width: 50px;
  height: 50px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  background-image: url('../../../logo.gif');
  left: calc(50% - 15px);
  top: calc(50% - 15px);
  position: fixed;
  z-index: 1;
}
@media (min-width: 700px) {
  .loading {
    width: 80px;
    height: 80px;
  }
}

.leftFifty {
  left: 50% !important;
}

.cardloading {
  width: 40px;
  height: 40px;
  display: inline-block;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  background-image: url('../../../logo.gif');
  left: 45%;
  top: 45%;
  position: absolute;
  z-index: 1;
}

.notificationLoading {
  position: relative;
  height: 200px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.notificationLoading .icon {
  width: 80px;
  height: 80px;
  display: inline-block;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  background-image: url('../../../logo.gif');
  z-index: 1;
}

.rel {
  position: relative;
}

.reduceopacity {
  cursor: not-allowed !important;
  opacity: 0.5;
}

.dataloading-overlay {
  width: 100% !important;
  height: 50vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dataloading {
  display: inline-block;
  width: 50px;
  height: 50px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  background-image: url('../../../logo.gif');
  z-index: 1;
}
@media (min-width: 700px) {
  .dataloading {
    width: 80px;
    height: 80px;
  }
}

@keyframes spin {
  to {
    -webkit-transform: rotate(360deg);
  }
}
.MainLoader {
  width: 100vw;
  height: 100vh;
  z-index: 100;
  top: 0;
  left: 0;
  opacity: 0.7;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fafafa;
}

.modalbg {
  background-color: #fafafa;
  height: 100%;
}

.loadingContainer {
  position: fixed;
  width: 100%;
  height: 100%;
  opacity: 0.7;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.teamwhole {
  min-height: 80vh;
  width: 100%;
  max-height: 80vh;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  background-color: #fafafa;
}

body {
  min-height: calc(100% - 150px);
  position: relative;
}
@media (max-width: 1439px) {
  body {
    min-height: calc(100% - 130px);
  }
}
@media (max-width: 1199px) {
  body {
    min-height: calc(100% - 110px);
  }
}
@media (max-width: 767px) {
  body {
    min-height: calc(100% - 85px);
  }
}
@media (max-width: 575px) {
  body {
    padding-bottom: 60px;
  }
}
body.no-footer {
  padding-bottom: initial;
}
body.no-footer footer {
  display: none;
}

footer.page-footer {
  padding-top: 2.2rem;
  padding-bottom: 2.2rem;
  border-top: 1px solid #d7d7d7;
  width: 100%;
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 90px;
}
@media (max-width: 575px) {
  footer.page-footer {
    height: 60px;
    padding-top: 1.2rem;
    padding-bottom: 1.2rem;
  }
}
footer.page-footer .breadcrumb-item + .breadcrumb-item::before {
  color: #922c88;
}
footer.page-footer .container-fluid {
  padding-left: 0;
  padding-right: 0;
}
footer.page-footer .footer-content {
  margin-left: 410px;
  margin-right: 60px;
}
@media (max-width: 1439px) {
  footer.page-footer .footer-content {
    margin-left: 390px;
    margin-right: 50px;
  }
}
@media (max-width: 1199px) {
  footer.page-footer .footer-content {
    margin-left: 370px;
    margin-right: 40px;
  }
}
@media (max-width: 767px) {
  footer.page-footer .footer-content {
    margin-left: 15px !important;
    margin-right: 15px !important;
  }
}

/* 18.Calendar */
.big-calendar-header {
  margin-bottom: 1em;
}

.rbc-month-header {
  min-height: 50px;
}

.rbc-today {
  background: initial;
}

.calendar-prev-btn,
.calendar-next-btn {
  outline: initial !important;
  box-shadow: initial !important;
  border-radius: 40px !important;
  text-align: center;
  min-width: 30px;
  height: 30px;
  padding: 0.55rem 0;
  background: #922c88;
  color: white;
  border: 1px solid #922c88;
  line-height: 0.9 !important;
  font-size: 0.76rem;
  font-weight: normal !important;
}
.calendar-prev-btn span,
.calendar-next-btn span {
  line-height: 1 !important;
  font-size: 0.76rem;
  font-weight: normal !important;
}
.calendar-prev-btn:hover,
.calendar-next-btn:hover {
  background-color: transparent;
  border-color: #b938ad;
  color: #922c88;
}

.calendar-prev-btn {
  margin-right: 5px;
}

.calendar-today-btn {
  padding: 0.4em 1.3em !important;
  height: unset !important;
}

.rbc-month-row {
  min-height: 5em;
  z-index: 1;
}
@media (max-width: 575px) {
  .rbc-month-row {
    min-height: 3em;
  }
}

.rbc-month-view {
  border: initial;
}

.rbc-off-range-bg {
  background: initial;
}

.rbc-off-range {
  color: #474d66;
  opacity: 0.3;
}

.rbc-day-bg + .rbc-day-bg,
.rbc-month-row + .rbc-month-row,
.rbc-header + .rbc-header,
.rbc-header {
  border-color: #f3f3f3 !important;
}

.rbc-header {
  padding: 15px 5px;
  color: #922c88;
}

.rbc-date-cell {
  padding: 10px;
}
.rbc-date-cell a {
  border-radius: 50%;
  width: 28px;
  height: 28px;
  text-align: center;
  vertical-align: middle;
  padding: 5px;
  font-weight: initial;
  display: inline-block;
}

.rbc-date-cell.rbc-now a {
  background: #922c88;
  color: white;
}

.rbc-event {
  font-size: 0.85em;
  border-radius: 25px;
  text-align: center;
  padding: 0px 5px;
  background: #4556ac;
}

.rtl .ReactTable .rt-resizer {
  right: initial;
  left: -18px;
}
.rtl .rbc-header {
  text-align: center;
}
.rtl .rbc-header:last-of-type {
  border-left: initial;
}
.rtl .rbc-header:first-of-type {
  border-left: 1px solid #f3f3f3 !important;
}
.rtl .rbc-day-bg:last-of-type {
  border-left: initial;
}
.rtl .rbc-day-bg:first-of-type {
  border-left: 1px solid #f3f3f3 !important;
}

.glide__slides {
  min-width: 100%;
  padding: 10px 2px !important;
}
.glide__slides > * {
  white-space: initial;
}

.glide-item {
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 0.5rem;
}

.slider-nav {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}
.slider-nav .left-arrow,
.slider-nav .right-arrow {
  font-size: 20px;
  color: #922c88;
  display: inline-block;
  vertical-align: middle;
  margin: 0 5px;
  padding: 5px;
}
.slider-nav .slider-dot-container {
  display: inline-block;
}
.slider-nav .btn:hover,
.slider-nav .btn:focus,
.slider-nav .btn:active {
  text-decoration: initial;
}

.slider-dot {
  width: 6px;
  height: 6px;
  border-radius: 10px;
  background: #d7d7d7;
  outline: initial !important;
  border: initial;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}
.slider-dot.glide__bullet--active {
  background: #922c88;
}

.glide__slides .card .card-body {
  flex-direction: column;
  display: flex;
  justify-content: space-between;
}

.glide__slides .card .w-50 {
  display: flex;
}

.rtl .glide__arrow.glide__arrow--left .simple-icon-arrow-left:before {
  content: '\e605';
}
.rtl .glide__arrow.glide__arrow--right .simple-icon-arrow-right:before {
  content: '\e606';
}

.chart-container {
  height: 300px;
}

.rtl .chart {
  direction: ltr;
}
.rtl .chart canvas {
  direction: rtl;
}

.react-contextmenu {
  box-shadow: initial;
  border-radius: 0.1rem;
  background: white;
  border: 1px solid rgba(71, 77, 102, 0.15);
  padding: 0.5rem 0;
  opacity: 0;
}

.react-contextmenu.react-contextmenu--visible {
  opacity: 1;
  pointer-events: auto;
  z-index: 9999;
}

.react-contextmenu-item {
  padding: 0.5rem 1.5rem;
  background: white;
  color: #474d66;
  cursor: pointer;
}
.react-contextmenu-item span {
  font-family: 'Nunito', sans-serif;
  font-size: 0.8rem;
  font-weight: 400;
  margin-left: 0.5rem;
  line-height: 15px;
  display: inline-block;
}
.react-contextmenu-item:hover {
  color: #474d66;
  text-decoration: none;
  background-color: #f8f8f8;
}

.react-contextmenu-item:not(.react-contextmenu-item--disabled):hover {
  color: #474d66;
  text-decoration: none;
  background-color: #f8f8f8;
}

.card.react-contextmenu--visible {
  box-shadow: 0 3px 30px rgba(0, 0, 0, 0.1), 0 3px 20px rgba(0, 0, 0, 0.1);
}

.rtl .react-contextmenu-item span {
  margin-left: initial;
  margin-right: 0.5rem;
}

.custom-switch {
  display: block !important;
  background: #d7d7d7 !important;
  width: 58px !important;
  height: 27px !important;
  border: 1px solid #d7d7d7 !important;
}
.custom-switch:after {
  width: 18px !important;
  height: 18px !important;
  top: 3px !important;
  margin-left: 3px !important;
  box-shadow: initial;
  transform: initial !important;
  animation-name: unset !important;
  background: white !important;
}
.custom-switch.rc-switch:after {
  box-shadow: none !important;
}
.custom-switch.rc-switch-checked:after {
  left: 30px !important;
  box-shadow: none !important;
}
.custom-switch.rc-switch-checked.custom-switch-primary {
  background: #922c88 !important;
  border: 1px solid #922c88 !important;
}
.custom-switch.rc-switch-checked.custom-switch-secondary {
  background: #4556ac !important;
  border: 1px solid #4556ac !important;
}
.custom-switch.custom-switch-primary-inverse {
  border: 1px solid #d7d7d7 !important;
}
.custom-switch.rc-switch-checked.custom-switch-primary-inverse {
  background: white !important;
  border: 1px solid #922c88 !important;
}
.custom-switch.rc-switch-checked.custom-switch-primary-inverse:after {
  background: #922c88 !important;
}
.custom-switch.custom-switch-secondary-inverse {
  border: 1px solid #d7d7d7 !important;
}
.custom-switch.rc-switch-checked.custom-switch-secondary-inverse {
  background: white !important;
  border: 1px solid #4556ac !important;
}
.custom-switch.rc-switch-checked.custom-switch-secondary-inverse:after {
  background: #4556ac !important;
}

.rc-switch:focus {
  box-shadow: initial !important;
}

.custom-switch .custom-switch-input + .custom-switch-btn {
  background: #d7d7d7 !important;
}

.custom-switch.custom-switch-primary
  .custom-switch-input:checked
  + .custom-switch-btn {
  background: #922c88 !important;
  border: 1px solid #922c88 !important;
}

.custom-switch.custom-switch-secondary
  .custom-switch-input:checked
  + .custom-switch-btn {
  background: #4556ac !important;
  border: 1px solid #4556ac !important;
}

.custom-switch.custom-switch-primary-inverse
  .custom-switch-input
  + .custom-switch-btn {
  border: 1px solid #d7d7d7 !important;
}

.custom-switch.custom-switch-primary-inverse
  .custom-switch-input:checked
  + .custom-switch-btn {
  background: white !important;
  border: 1px solid #922c88 !important;
}

.custom-switch.custom-switch-primary-inverse
  .custom-switch-input:checked
  + .custom-switch-btn:after {
  background: #922c88 !important;
}

.custom-switch.custom-switch-secondary-inverse
  .custom-switch-input
  + .custom-switch-btn {
  border: 1px solid #d7d7d7 !important;
}

.custom-switch.custom-switch-secondary-inverse
  .custom-switch-input:checked
  + .custom-switch-btn {
  background: white !important;
  border: 1px solid #4556ac !important;
}

.custom-switch.custom-switch-secondary-inverse
  .custom-switch-input:checked
  + .custom-switch-btn:after {
  background: #4556ac !important;
}

.custom-switch .custom-switch-input + .custom-switch-btn:after {
  background: white !important;
}

.custom-switch .custom-switch-input + .custom-switch-btn {
  border-color: #d7d7d7 !important;
}

.rc-switch.custom-switch.custom-switch-small {
  width: 34px !important;
  height: 19px !important;
}

.rc-switch.custom-switch.custom-switch-small:after {
  width: 12px !important;
  height: 12px !important;
  left: 0px !important;
  top: 2px !important;
}

.rc-switch-checked.custom-switch.custom-switch-small:after {
  left: 14px !important;
}

.dropzone {
  min-height: 115px !important;
  border: 1px solid #d7d7d7 !important;
  background: white !important;
  padding: 10px 10px !important;
  border-radius: 0.1rem !important;
  color: #474d66 !important;
  height: auto !important;
}
.dropzone .img-thumbnail {
  height: 58px !important;
  width: 100% !important;
  -o-object-fit: cover !important;
  object-fit: cover !important;
}

.dropzone.dz-clickable .dz-message,
.dropzone.dz-clickable .dz-message * {
  position: relative;
  transform: translateY(-50%);
  top: 24px !important;
  margin: 0;
}

.dropzone .dz-preview.dz-image-preview,
.dropzone .dz-preview.dz-file-preview {
  width: 260px;
  height: 60px;
  min-height: unset;
  border: 1px solid #d7d7d7 !important;
  border-radius: 0.1rem !important;
  background: white !important;
  color: #474d66 !important;
}
.dropzone .dz-preview.dz-image-preview .preview-container,
.dropzone .dz-preview.dz-file-preview .preview-container {
  transition: initial !important;
  animation: initial !important;
  margin-left: 0;
  margin-top: 0;
  position: relative;
  width: 100%;
  height: 100%;
}
.dropzone .dz-preview.dz-image-preview .preview-container i,
.dropzone .dz-preview.dz-file-preview .preview-container i {
  color: #922c88;
  font-size: 20px;
  position: absolute;
  left: 50%;
  top: 29px;
  transform: translateX(-50%) translateY(-50%) !important;
  height: 22px;
}
.dropzone .dz-preview.dz-image-preview strong,
.dropzone .dz-preview.dz-file-preview strong {
  font-weight: normal;
}
.dropzone .dz-preview.dz-image-preview .remove,
.dropzone .dz-preview.dz-file-preview .remove {
  position: absolute;
  right: 5px;
  top: 5px;
  color: #922c88 !important;
}
.dropzone .dz-preview.dz-image-preview .dz-details,
.dropzone .dz-preview.dz-file-preview .dz-details {
  position: static;
  display: block;
  opacity: 1;
  text-align: left;
  min-width: unset;
  z-index: initial;
  color: #474d66 !important;
}
.dropzone .dz-preview.dz-image-preview .dz-error-mark,
.dropzone .dz-preview.dz-file-preview .dz-error-mark {
  color: #fff !important;
  top: 15px;
  left: 25px;
  margin-left: 0;
  margin-top: 0;
}
.dropzone .dz-preview.dz-image-preview .dz-error-mark span,
.dropzone .dz-preview.dz-file-preview .dz-error-mark span {
  display: inline-block;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 28 28'%3E%3Cpath style='fill:%23922C88;' d='M4.1,23.9A13.51,13.51,0,0,1,0,14,13.52,13.52,0,0,1,4.1,4.1,13.52,13.52,0,0,1,14,0a13.52,13.52,0,0,1,9.9,4.1A13.52,13.52,0,0,1,28,14a13.51,13.51,0,0,1-4.1,9.9A13.52,13.52,0,0,1,14,28,13.52,13.52,0,0,1,4.1,23.9Z'/%3E%3Cpath style='fill:%23FFFFFF;' d='M13.13,19.35V6.17a.88.88,0,1,1,1.75,0V19.35Z'/%3E%3Crect style='fill:%23FFFFFF;' x='13.13' y='21.07' width='1.75' height='1.64'/%3E%3C/svg%3E");
  width: 28px;
  height: 28px;
}
.dropzone .dz-preview.dz-image-preview .dz-success-mark,
.dropzone .dz-preview.dz-file-preview .dz-success-mark {
  color: #fff;
  top: 15px;
  left: 25px;
  margin-left: 0;
  margin-top: 0;
}
.dropzone .dz-preview.dz-image-preview .dz-success-mark span,
.dropzone .dz-preview.dz-file-preview .dz-success-mark span {
  display: inline-block;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 28 28'%3E%3Cpath style='fill:%23922C88;' d='M4.1,23.9A13.51,13.51,0,0,1,0,14,13.52,13.52,0,0,1,4.1,4.1,13.52,13.52,0,0,1,14,0a13.52,13.52,0,0,1,9.9,4.1A13.52,13.52,0,0,1,28,14a13.51,13.51,0,0,1-4.1,9.9A13.52,13.52,0,0,1,14,28,13.52,13.52,0,0,1,4.1,23.9Z'/%3E%3Cpath style='fill:%23FFFFFF;' d='M20.14,8.81A.77.77,0,0,1,21.2,9a.81.81,0,0,1,.25.61.83.83,0,0,1-.25.62L12.48,19l-.11.1a.82.82,0,0,1-1.23,0L6.79,14.74l-.11-.16a.49.49,0,0,1-.08-.18,1.06,1.06,0,0,1,0-.19.61.61,0,0,1,0-.19,1.16,1.16,0,0,1,0-.18,1.26,1.26,0,0,1,.08-.18,1,1,0,0,1,.11-.15.87.87,0,0,1,1.26,0l3.69,3.7L19.94,9A.72.72,0,0,1,20.14,8.81Z'/%3E%3C/svg%3E");
  width: 28px;
  height: 28px;
}
.dropzone .dz-preview.dz-image-preview .dz-progress,
.dropzone .dz-preview.dz-file-preview .dz-progress {
  width: 84%;
  margin-left: 0;
  margin-top: 0;
  right: 0;
  height: 5px !important;
  left: 15px;
}
.dropzone .dz-preview.dz-image-preview .dz-progress .dz-upload,
.dropzone .dz-preview.dz-file-preview .dz-progress .dz-upload {
  width: 100%;
  background: #922c88 !important;
}
.dropzone .dz-preview.dz-image-preview .dz-error-message,
.dropzone .dz-preview.dz-file-preview .dz-error-message {
  border-radius: 15px;
  background: #c43d4b !important;
  top: 60px;
  left: 0;
  width: 160%;
}
.dropzone .dz-preview.dz-image-preview .dz-error-message:after,
.dropzone .dz-preview.dz-file-preview .dz-error-message:after {
  border-bottom: 6px solid #c43d4b !important;
}
.dropzone .dz-preview.dz-image-preview [data-dz-name],
.dropzone .dz-preview.dz-file-preview [data-dz-name] {
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 90%;
  display: inline-block;
  overflow: hidden;
}

.dropzone .dz-preview.dz-file-preview .img-thumbnail {
  display: none;
}

.dropzone .dz-error.dz-preview.dz-file-preview .preview-icon {
  display: none;
}
.dropzone .dz-error.dz-preview.dz-file-preview .dz-error-mark,
.dropzone .dz-error.dz-preview.dz-file-preview .dz-success-mark {
  color: #922c88 !important;
}

.dropzone .dz-preview.dz-image-preview .preview-icon {
  display: none;
}
@keyframes pulse-inner {
  0% {
    transform: scale(1, 1);
  }
  10% {
    transform: scale(0.8, 1);
  }
  20% {
    transform: scale(1, 1);
  }
}
.dropzone .dz-preview:not(.dz-processing) .dz-progress {
  animation: pulse-inner 3s ease infinite;
}

.rtl .dropzone .dz-preview.dz-image-preview .dz-progress {
  right: 50%;
  left: initial;
  transform: translateX(50%);
}
.rtl .dropzone .dz-preview.dz-file-preview .remove,
.rtl .dropzone .dz-preview.dz-image-preview .remove {
  right: initial;
  left: 5px;
}

.wizard-basic-step {
  min-height: 85px;
}

.wizard-buttons {
  display: flex;
}
.wizard-buttons .disabled {
  opacity: 0.5;
}

.wizard {
  margin-top: -2rem;
}

.wizard ul.nav {
  list-style: none;
  display: flex;
  flex-wrap: wrap;
  padding-left: initial;
  padding: initial;
  background: initial;
  position: relative;
  border: initial;
  margin-bottom: 1.5rem;
}
.wizard ul.nav:before {
  content: ' ';
  position: absolute;
  bottom: 1px;
  width: 100%;
  height: 1px;
  z-index: 0;
  background: #f3f3f3;
}

.wizard-default ul.nav li {
  position: relative;
  padding: 0.5rem 1rem;
  text-align: center;
}
.wizard-default ul.nav li a,
.wizard-default ul.nav li a:focus {
  color: #d7d7d7;
}
.wizard-default ul.nav li a span,
.wizard-default ul.nav li a small,
.wizard-default ul.nav li a:focus span,
.wizard-default ul.nav li a:focus small {
  color: #d7d7d7;
  text-align: center;
}
.wizard-default ul.nav li a:active,
.wizard-default ul.nav li a:hover {
  color: #922c88;
}
.wizard-default ul.nav li a:active span,
.wizard-default ul.nav li a:active small,
.wizard-default ul.nav li a:hover span,
.wizard-default ul.nav li a:hover small {
  color: #922c88;
}
.wizard-default ul.nav li span {
  display: block;
  font-weight: 700;
  color: #d7d7d7;
}
.wizard-default ul.nav li.step-doing a {
  color: #922c88;
}
.wizard-default ul.nav li.step-doing a span,
.wizard-default ul.nav li.step-doing a small {
  color: #922c88;
}
.wizard-default ul.nav li a:before {
  content: ' ';
  position: absolute;
  margin-top: 10px;
  display: block;
  border-radius: 50%;
  color: initial;
  background: #f3f3f3;
  border: none;
  width: 18px;
  height: 18px;
  text-decoration: none;
  z-index: 1;
  left: 50%;
  transform: translateX(-50%);
  bottom: -6px;
}
.wizard-default ul.nav li.step-doing a:after,
.wizard-default ul.nav li.step-done a:after {
  content: ' ';
  position: absolute;
  margin-top: 10px;
  display: block;
  border-radius: 50%;
  color: initial;
  background: #922c88;
  border: none;
  width: 10px;
  height: 10px;
  text-decoration: none;
  z-index: 2;
  left: 50%;
  transform: translateX(-50%);
  bottom: -2px;
}
.wizard-default ul.nav li.step-done a:after {
  background: initial;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23922C88' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E");
}
.wizard-default ul.nav.disabled a,
.wizard-default ul.nav.disabled a:active,
.wizard-default ul.nav.disabled a:hover {
  cursor: default;
  color: #d7d7d7;
}
.wizard-default ul.nav.disabled a span,
.wizard-default ul.nav.disabled a small,
.wizard-default ul.nav.disabled a:active span,
.wizard-default ul.nav.disabled a:active small,
.wizard-default ul.nav.disabled a:hover span,
.wizard-default ul.nav.disabled a:hover small {
  color: #d7d7d7;
}
.wizard-default ul.nav.disabled .step-doing a {
  color: #922c88 !important;
}
.wizard-default ul.nav.disabled .step-doing a span,
.wizard-default ul.nav.disabled .step-doing a small {
  color: #922c88 !important;
}

@media (max-width: 767px) {
  .wizard-default ul.nav li small,
  .wizard-default ul.nav li span {
    display: none;
  }
}
.InputStyleInActive {
  background-color: #f8f8f8 !important;
}

.InputStyleActive {
  border: 1px solid #922c88 !important;
}

.DescriptionStyleInActive .ql-container.ql-bubble {
  background-color: #f8f8f8 !important;
}
.DescriptionStyleInActive .ql-editor {
  background-color: #f8f8f8 !important;
}

.DescriptionStyleActive .ql-container.ql-bubble {
  border: 1px solid #922c88 !important;
}

.SpaceDetailDescription .ql-container.ql-bubble {
  border: none;
}

.overflow-hidden-quill .ql-editor {
  overflow: hidden;
}

.showcaseHomepageQuill .ql-container.ql-bubble {
  background-color: #f8f8f8 !important;
}
.showcaseHomepageQuill .ql-editor {
  background-color: #f8f8f8 !important;
  text-align: center;
}

.has-float-label {
  display: block;
  position: relative;
}

.has-float-label label::after,
.has-float-label > span::after {
  background: white !important;
}

.has-float-label2 label::after {
  background: #f8f8f8 !important;
}

.has-top-label label,
.has-top-label > span,
.has-float-label label,
.has-float-label > span {
  color: rgba(71, 77, 102, 0.99);
}

.has-top-label .react-select__value-container {
  height: calc(3.2rem + 3px);
}

.has-float-label label,
.has-float-label > span:last-of-type {
  position: absolute;
  cursor: text;
  font-size: 80%;
  opacity: 1;
  top: -0.4em;
  left: 0.75rem;
  z-index: 3;
  line-height: 1;
  padding: 0 1px;
}

.has-float-label label::after,
.has-float-label > span::after {
  content: ' ';
  display: block;
  position: absolute;
  height: 14px;
  top: 0px;
  left: -0.2em;
  right: -0.2em;
  z-index: -1;
}

.has-float-label .form-control::-moz-placeholder {
  opacity: 1;
}

.has-float-label .form-control::placeholder {
  opacity: 1;
}

.input-group .has-float-label {
  flex-grow: 1;
  margin-bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.input-group .has-float-label .form-control {
  width: 100%;
}

.input-group .has-float-label:not(:last-child),
.input-group .has-float-label:not(:last-child) .form-control {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
  border-right: 0;
}

.input-group .has-float-label:not(:first-child),
.input-group .has-float-label:not(:first-child) .form-control {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}

.form-group.has-top-label .form-control,
.form-group.has-top-label .react-tagsinput,
.form-group.has-top-label .react-datepicker__input-container input,
.form-group.has-top-label .react-select__value-container {
  padding: 1.7rem 0.75rem 0.5rem 0.75rem !important;
}

.rtl .has-float-label label,
.rtl .has-float-label > span:last-of-type {
  right: 0.75rem;
  left: initial;
}

.has-top-label {
  display: block;
  position: relative;
}
.has-top-label .react-tagsinput-input {
  padding: 0;
  margin: 0;
  font-size: 0.8rem;
  line-height: 1;
}
.has-top-label .react-select__value-container .css-b8ldur-Input {
  padding-top: 0;
  margin: 0;
}

.has-top-label .react-select__value-container .css-rsyb7x {
  margin: 0 !important;
  padding: 0 !important;
}

.has-top-label label,
.has-top-label > span:last-of-type {
  position: absolute;
  cursor: text;
  font-size: 70%;
  opacity: 1;
  top: 0.7rem;
  left: 0.75rem;
  z-index: 3;
  line-height: 1;
  padding: 0 1px;
  font-weight: 600;
}

.has-top-label label::after,
.has-top-label > span::after {
  content: ' ';
  display: block;
  position: absolute;
  height: 2px;
  top: 50%;
  left: -0.2em;
  right: -0.2em;
  z-index: -1;
}

.has-top-label .form-control::-moz-placeholder {
  opacity: 1;
}

.has-top-label .form-control::placeholder {
  opacity: 1;
}

.has-top-label
  .form-control:-moz-placeholder-shown:not(:focus)::-moz-placeholder {
  opacity: 0;
}

.has-top-label .form-control:placeholder-shown:not(:focus)::-moz-placeholder {
  opacity: 0;
}

.has-top-label .form-control:-moz-placeholder-shown:not(:focus)::placeholder {
  opacity: 0;
}

.has-top-label .form-control:placeholder-shown:not(:focus)::placeholder {
  opacity: 0;
}

.has-top-label .form-control:-moz-placeholder-shown:not(:focus) + * {
  font-size: 150%;
  opacity: 0.5;
  top: 0.3em;
}

.has-top-label .form-control:placeholder-shown:not(:focus) + * {
  font-size: 150%;
  opacity: 0.5;
  top: 0.3em;
}

.has-top-label .react-select__single-value {
  top: 35px !important;
  bottom: -3px !important;
  margin-left: 0 !important;
}

.input-group .has-top-label {
  flex-grow: 1;
  margin-bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.input-group .has-top-label .form-control {
  width: 100%;
}

.input-group .has-top-label:not(:last-child),
.input-group .has-top-label:not(:last-child) .form-control {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
  border-right: 0;
}

.input-group .has-top-label:not(:first-child),
.input-group .has-top-label:not(:first-child) .form-control {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}

.form-group.has-top-label .form-control,
.form-group.has-top-label .react-tagsinput,
.form-group.has-top-label .react-datepicker__input-container input,
.form-group.has-top-label .react-select__value-container {
  min-height: calc(3.2rem + 2px) !important;
}

.form-group.has-top-label select.form-control:not([size]):not([multiple]) {
  height: calc(1rem + 2px);
  padding: 0.5rem 0.75rem 0.5rem 0.5rem;
}

.rtl .has-top-label label,
.rtl .has-top-label > span:last-of-type {
  right: 0.75rem;
  left: initial;
}

.html-editor {
  height: 350px !important;
  width: 100% !important;
}

.ql-snow.ql-toolbar button:hover,
.ql-snow .ql-toolbar button:hover,
.ql-snow.ql-toolbar button.ql-active,
.ql-snow .ql-toolbar button.ql-active,
.ql-snow.ql-toolbar .ql-picker-label:hover,
.ql-snow .ql-toolbar .ql-picker-label:hover,
.ql-snow.ql-toolbar .ql-picker-label.ql-active,
.ql-snow .ql-toolbar .ql-picker-label.ql-active,
.ql-snow.ql-toolbar .ql-picker-item:hover,
.ql-snow .ql-toolbar .ql-picker-item:hover,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected {
  color: #922c88;
}

.ql-snow.ql-toolbar button:hover .ql-fill,
.ql-snow .ql-toolbar button:hover .ql-fill,
.ql-snow.ql-toolbar button.ql-active .ql-fill,
.ql-snow .ql-toolbar button.ql-active .ql-fill,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-fill,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-fill,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-fill,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-fill,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-fill,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-fill,
.ql-snow.ql-toolbar button:hover .ql-stroke.ql-fill,
.ql-snow .ql-toolbar button:hover .ql-stroke.ql-fill,
.ql-snow.ql-toolbar button.ql-active .ql-stroke.ql-fill,
.ql-snow .ql-toolbar button.ql-active .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill {
  fill: #922c88;
}

.ql-snow.ql-toolbar button:hover .ql-stroke,
.ql-snow .ql-toolbar button:hover .ql-stroke,
.ql-snow.ql-toolbar button.ql-active .ql-stroke,
.ql-snow .ql-toolbar button.ql-active .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
.ql-snow.ql-toolbar button:hover .ql-stroke-mitter,
.ql-snow .ql-toolbar button:hover .ql-stroke-mitter,
.ql-snow.ql-toolbar button.ql-active .ql-stroke-mitter,
.ql-snow .ql-toolbar button.ql-active .ql-stroke-mitter,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke-mitter,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke-mitter,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke-mitter,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke-mitter,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke-mitter,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke-mitter,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-mitter,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-mitter {
  stroke: #922c88;
}

.ql-toolbar.ql-snow {
  border-color: #d7d7d7;
}

.ql-container.ql-snow {
  border-color: #d7d7d7;
}

.html-editor-bubble {
  height: 200px !important;
}

.html-editor-bubble .ql-editor {
  border: 1px solid #d7d7d7;
}

.ql-tooltip {
  z-index: 4;
}

.ql-editor {
  background: white;
  min-height: 110px !important;
  max-height: 110px;
  overflow: hidden;
  overflow-y: scroll;
}

.ql-container.ql-bubble {
  border: 1px solid #d7d7d7;
}

.react-quill-auto {
  height: auto !important;
  width: 95%;
  font-size: 1rem;
  background-color: white;
}

.react-quill {
  width: 95%;
  height: 100%;
  font-size: 1rem;
  background-color: white;
}

.disabled-ql {
  background-color: white;
  cursor: not-allowed;
}

.rtl .ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg {
  right: initial;
  left: 0;
}

.ai-editor .ql-editor {
  background: #fff5fe !important;
  min-height: 100% !important;
  max-height: 500px;
}
.ai-editor .ql-container,
.ai-editor .ql-bubble {
  border: none !important;
}

.simple-line-icons .glyph,
.mind-icons .glyph {
  width: 14.28%;
  text-align: center !important;
  float: left;
  height: 100px;
}
.simple-line-icons .glyph .glyph-icon,
.simple-line-icons .glyph .fa,
.mind-icons .glyph .glyph-icon,
.mind-icons .glyph .fa {
  font-size: 32px;
  text-align: center !important;
}
.simple-line-icons .glyph .author-name,
.mind-icons .glyph .author-name {
  display: none;
}
.simple-line-icons .glyph .class-name,
.mind-icons .glyph .class-name {
  font-size: 0.76rem;
  color: #909090 !important;
  text-align: center !important;
}
@media (max-width: 1199px) {
  .simple-line-icons .glyph,
  .mind-icons .glyph {
    width: 16.66%;
  }
}
@media (max-width: 991px) {
  .simple-line-icons .glyph,
  .mind-icons .glyph {
    width: 20%;
  }
}
@media (max-width: 767px) {
  .simple-line-icons .glyph,
  .mind-icons .glyph {
    width: 25%;
  }
}
@media (max-width: 575px) {
  .simple-line-icons .glyph,
  .mind-icons .glyph {
    width: 50%;
  }
}

.notification {
  border-radius: 0.1rem;
  box-shadow: 0 1px 15px rgba(0, 0, 0, 0.04), 0 1px 6px rgba(0, 0, 0, 0.04);
  padding: 25px 25px 25px 25px;
}

.notification:before {
  content: '';
  display: none;
}

.notification-primary {
  color: #922c88;
  background-color: white;
  border: 1px solid #922c88;
  opacity: 1;
}
.notification-primary .notification-message {
  z-index: 1000000;
}
.notification-primary .notification-message .title {
  color: #922c88;
}
.notification-primary .notification-message .message {
  color: #474d66;
}
.notification-primary.filled {
  color: white;
  background-color: #922c88;
}
.notification-primary.filled .notification-message .title {
  color: white;
}
.notification-primary.filled .notification-message .message {
  color: white;
}

.notification-secondary {
  color: #4556ac;
  background-color: white;
  border: 1px solid #4556ac;
  opacity: 1;
}
.notification-secondary .notification-message .title {
  color: #4556ac;
}
.notification-secondary .notification-message .message {
  color: #8f8f8f;
}
.notification-secondary.filled {
  color: white;
  background-color: #4556ac;
}
.notification-secondary.filled .notification-message .title {
  color: white;
}
.notification-secondary.filled .notification-message .message {
  color: white;
}

.notification-info {
  color: #3195a5;
  background-color: white;
  border: 1px solid #3195a5;
  opacity: 1;
}
.notification-info .notification-message .title {
  color: #3195a5;
}
.notification-info .notification-message .message {
  color: #8f8f8f;
}
.notification-info.filled {
  color: white;
  background-color: #3195a5;
}
.notification-info.filled .notification-message .title {
  color: white;
}
.notification-info.filled .notification-message .message {
  color: white;
}

.notification-success {
  color: #3e884f;
  background-color: white;
  border: 1px solid #3e884f;
  opacity: 1;
}
.notification-success .notification-message .title {
  color: #3e884f;
}
.notification-success .notification-message .message {
  color: #8f8f8f;
}
.notification-success.filled {
  color: white;
  background-color: #3e884f;
}
.notification-success.filled .notification-message .title {
  color: white;
}
.notification-success.filled .notification-message .message {
  color: white;
}

.notification-warning {
  color: #b69329;
  background-color: white;
  border: 1px solid #b69329;
  opacity: 1;
}
.notification-warning .notification-message .title {
  color: #b69329;
}
.notification-warning .notification-message .message {
  color: #8f8f8f;
}
.notification-warning.filled {
  color: white;
  background-color: #b69329;
}
.notification-warning.filled .notification-message .title {
  color: white;
}
.notification-warning.filled .notification-message .message {
  color: white;
}

.notification-error {
  color: #c43d4b;
  background-color: white;
  border: 1px solid #c43d4b;
  opacity: 1;
}
.notification-error .notification-message .title {
  color: #c43d4b;
}
.notification-error .notification-message .message {
  color: #8f8f8f;
}
.notification-error.filled {
  color: white;
  background-color: #c43d4b;
}
.notification-error.filled .notification-message .title {
  color: white;
}
.notification-error.filled .notification-message .message {
  color: white;
}

.notification-container {
  box-sizing: border-box;
  position: fixed;
  top: 0;
  right: 0;
  z-index: 999999;
  width: 320px;
  padding: 0px 15px;
  max-height: calc(100% - 30px);
  overflow-x: hidden;
  overflow-y: auto;
}

.notification {
  box-sizing: border-box;
  padding: 15px 15px 15px 58px;
  border-radius: 2px;
  cursor: pointer;
  font-size: 1em;
  line-height: 1.2em;
  position: relative;
  opacity: 0.9;
  margin-top: 15px;
  z-index: 10000000000000000;
}

.notification .title {
  font-size: 1em;
  line-height: 1.2em;
  font-weight: bold;
  margin: 0 0 5px 0;
}

.notification:hover,
.notification:focus {
  opacity: 1;
}

.notification-enter {
  visibility: hidden;
  transform: translate3d(100%, 0, 0);
}

.notification-enter.notification-enter-active {
  visibility: visible;
  transform: translate3d(0, 0, 0);
  transition: all 0.4s;
}

.notification-leave {
  visibility: visible;
  transform: translate3d(0, 0, 0);
}

.notification-leave.notification-leave-active {
  visibility: hidden;
  transform: translate3d(100%, 0, 0);
  transition: all 0.4s;
}

.rounded .notification {
  border-radius: 0.75rem;
}

.progress {
  background-color: #f3f3f3;
  height: 3px;
}

.progress-bar {
  background-color: #922c88;
}

.CircularProgressbar-text {
  fill: #474d66 !important;
}

.progress-bar-circle {
  width: 54px;
  height: 54px;
}
.progress-bar-circle svg path:first-of-type {
  stroke: #d7d7d7;
}
.progress-bar-circle svg path:last-of-type {
  stroke: #922c88;
}
.progress-bar-circle.progress-bar-banner svg path:first-of-type {
  stroke: #571a51;
}
.progress-bar-circle.progress-bar-banner svg path:last-of-type {
  stroke: #fff;
}

.progress-banner {
  height: 200px;
  background-image: linear-gradient(to right top, #832579, #922c88, #a9449f);
  transition: 0.5s;
  background-size: 200% auto;
  cursor: pointer;
}
.progress-banner .CircularProgressbar .CircularProgressbar-text {
  fill: #fff !important;
}
.progress-banner .lead {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}
@media (max-width: 1199px) {
  .progress-banner .lead {
    font-size: 1.2rem;
    margin-bottom: 0.2rem;
  }
}
.progress-banner i {
  font-size: 2.7rem;
  margin-bottom: 1rem;
}
@media (max-width: 1199px) {
  .progress-banner i {
    font-size: 2rem;
    margin-bottom: 0.2rem;
  }
}
.progress-banner .progress-bar-circle.progress-bar-banner {
  width: 120px;
  height: 120px;
}
.progress-banner
  .progress-bar-circle.progress-bar-banner
  svg
  path:last-of-type {
  stroke: #e6e6e6;
}
@media (max-width: 1199px) {
  .progress-banner .progress-bar-circle.progress-bar-banner {
    width: 80px;
    height: 80px;
  }
}
.progress-banner .progress-bar-banner .progressbar-text {
  color: #474d66 !important;
  font-size: 1.7rem;
  width: 110px;
  font-weight: 300;
}
@media (max-width: 1199px) {
  .progress-banner .progress-bar-banner .progressbar-text {
    font-size: 1.2rem;
    margin-bottom: 0.2rem;
  }
}
.progress-banner:hover {
  background-position: right top;
}

.br-theme-bootstrap-stars .br-widget a.br-active:after {
  color: #922c88 !important;
}

.br-theme-bootstrap-stars .br-widget a.br-selected:after {
  color: #922c88 !important;
}

.react-rater {
  display: block !important;
  font-size: 16px !important;
  color: #d7d7d7 !important;
}

.react-rater-star {
  -webkit-font-smoothing: antialiased;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1 !important;
  font-family: 'simple-line-icons';
  color: #d7d7d7 !important;
  margin-right: 3px !important;
  font-size: 18px !important;
  float: left !important;
}
.react-rater-star.is-active,
.react-rater-star.will-be-active {
  color: #922c88 !important;
}
.react-rater-star:after {
  content: '\e09b';
}

.autosuggest {
  position: relative;
}

.react-autosuggest__suggestions-container {
  border-radius: 0.1rem;
  z-index: 20;
  box-shadow: initial;
  margin-top: -1px;
  background: white;
  position: absolute;
  width: 100%;
  box-sizing: border-box;
  top: 100%;
}

.react-autosuggest__suggestions-list {
  list-style: none;
  padding: 0;
  margin-bottom: 0;
}

.react-autosuggest__suggestion {
  cursor: default;
  display: block;
  font-size: inherit;
  padding: 8px 12px;
  width: 100%;
  background: white !important;
  color: #474d66 !important;
}
.react-autosuggest__suggestion:hover,
.react-autosuggest__suggestion:active {
  background: #b938ad !important;
  color: white !important;
}

.react-autosuggest__input--open {
  border: 1px solid rgba(146, 44, 136, 0.6) !important;
}

.react-autosuggest__suggestions-container--open {
  border: 1px solid rgba(146, 44, 136, 0.6);
  border-top: initial;
  border-width: 1px !important;
}

.react-datepicker {
  background-color: white !important;
  border: #d7d7d7 !important;
}

.react-datepicker__current-month,
.react-datepicker-time__header {
  text-align: center;
}

.react-datepicker__input-container input:focus {
  border-color: rgba(146, 44, 136, 0.6) !important;
}

.react-datepicker-popper {
  z-index: 20 !important;
}

.react-datepicker-wrapper {
  width: 100% !important;
}

.react-datepicker__input-container {
  width: 100% !important;
}

.react-datepicker__input-container input {
  background-color: white !important;
  border: #d7d7d7 !important;
  font-size: 0.8rem !important;
  border: 1px solid #d7d7d7 !important;
  border-radius: 0.1rem !important;
  width: 100% !important;
  outline: initial !important;
  padding: 0.5rem 0.75rem !important;
  line-height: 1;
}

.react-datepicker {
  border: 1px solid #d7d7d7 !important;
  border-radius: 0.1rem !important;
  font-family: 'Nunito', sans-serif;
}

.react-datepicker__header {
  background-color: white !important;
  border-bottom: initial !important;
}

.react-datepicker__day-name,
.react-datepicker__day,
.react-datepicker__time-name {
  width: 35px !important;
  height: 35px !important;
  line-height: 2.3rem !important;
  border-radius: 0 !important;
  margin: 0 !important;
  outline: initial !important;
}

.react-datepicker__day:hover {
  background: #d7d7d7;
}

.react-datepicker__day--keyboard-selected:hover,
.react-datepicker__day--keyboard-selected {
  background: #922c88;
}

.react-datepicker__day--keyboard-selected {
  background: #922c88 !important;
  color: #fff !important;
}

.react-datepicker-popper[data-placement^='bottom'] .react-datepicker__triangle {
  border-bottom-color: white !important;
}

.react-datepicker-popper[data-placement^='bottom']
  .react-datepicker__triangle::before {
  border-bottom-color: #d7d7d7 !important;
}

.react-datepicker__current-month,
.react-datepicker-time__header {
  color: #474d66 !important;
}

.react-datepicker__day-name,
.react-datepicker__day,
.react-datepicker__time-name {
  color: #474d66 !important;
}

.react-datepicker__input-container input {
  color: #474d66 !important;
}

.react-datepicker__time-container {
  border-left: 1px solid #d7d7d7 !important;
  width: 71px !important;
}

.react-datepicker__time-container .react-datepicker__time {
  background-color: white !important;
  color: #474d66 !important;
}

.react-datepicker__day--selected,
.react-datepicker__day--in-selecting-range,
.react-datepicker__day--in-range {
  color: #fff !important;
}

.react-datepicker-popper[data-placement^='top'] .react-datepicker__triangle,
.react-datepicker__year-read-view--down-arrow,
.react-datepicker__month-read-view--down-arrow,
.react-datepicker__month-year-read-view--down-arrow {
  border-top-color: white !important;
}

.react-datepicker-popper[data-placement^='top']
  .react-datepicker__triangle::before,
.react-datepicker__year-read-view--down-arrow::before,
.react-datepicker__month-read-view--down-arrow::before,
.react-datepicker__month-year-read-view--down-arrow::before {
  border-top-color: #d7d7d7 !important;
}

.react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box
  ul.react-datepicker__time-list
  li.react-datepicker__time-list-item:hover {
  background: #f8f8f8 !important;
}

.react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box
  ul.react-datepicker__time-list
  li.react-datepicker__time-list-item {
  text-align: center;
}

.react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box
  ul.react-datepicker__time-list
  li.react-datepicker__time-list-item--selected:hover {
  background: #922c88 !important;
}

.react-datepicker__triangle {
  left: 30px !important;
  transform: none !important;
}

.react-datepicker__day--selected,
.react-datepicker__day--in-selecting-range,
.react-datepicker__day--in-range {
  background: #922c88 !important;
  border-radius: 0.1rem !important;
}

.react-datepicker__day--in-selecting-range:not(.react-datepicker__day--in-range) {
  background: #d7d7d7 !important;
}

.react-datepicker.embedded {
  border: initial !important;
  width: 100% !important;
}
.react-datepicker.embedded .react-datepicker__day-name,
.react-datepicker.embedded .react-datepicker__day,
.react-datepicker.embedded .react-datepicker__time-name {
  width: 14.28% !important;
}
.react-datepicker.embedded .react-datepicker__month-container {
  width: 100% !important;
}

.react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box {
  width: 85px !important;
}

.react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box
  ul.react-datepicker__time-list {
  padding-left: 0 !important;
  padding-right: 30px !important;
}

.react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box
  ul.react-datepicker__time-list
  li.react-datepicker__time-list-item--selected {
  background: #922c88 !important;
}

.react-datepicker__day--today {
  font-weight: 400 !important;
  background: #d7d7d7;
  color: #fff !important;
}

.rtl
  .react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box
  ul.react-datepicker__time-list {
  padding-left: 30px !important;
  padding-right: 0 !important;
}
.rtl .datepicker-dropdown {
  right: initial;
}

.rc-slider-tooltip {
  background: white !important;
  color: #474d66 !important;
  border: 1px solid #d7d7d7 !important;
  border-radius: 0.1rem !important;
  text-align: center !important;
  top: 150% !important;
  bottom: unset !important;
  padding: 5px !important;
  transform: translateX(-50%) !important;
  cursor: pointer !important;
}

.rc-slider-handle {
  cursor: pointer !important;
  width: 20px !important;
  height: 20px !important;
  border-radius: 20px !important;
  background: white !important;
  cursor: default !important;
  border: 1px solid #d7d7d7 !important;
  box-shadow: initial !important;
}

.rc-slider-track,
.rc-slider-rail,
.rc-slider-step {
  cursor: pointer !important;
  height: 7px !important;
}

.rc-slider-rail {
  cursor: pointer !important;
  border: 1px solid #d7d7d7 !important;
  background: white !important;
}

.rc-slider-handle {
  cursor: pointer !important;
  margin-top: -7px !important;
}

.rc-slider-track {
  cursor: pointer !important;
  background: #922c88 !important;
}

.react-select {
  outline: initial !important;
  box-shadow: none !important;
}

.react-select__value-container {
  outline: initial !important;
  box-shadow: none !important;
  padding: 0.75rem 0.75rem 0.4rem 0.75rem !important;
  background: white !important;
  padding-top: 2px !important;
  padding-bottom: 2px !important;
  padding-left: 9px !important;
}

.react-select .react-select__single-value,
.react-select .react-select__multi-value__label {
  bottom: initial !important;
}

.react-select .react-select__dropdown-indicator {
  color: #8f8f8f;
}

.react-select .react-select__menu-list {
  padding-bottom: 0;
  padding-top: 0;
}

.react-select .react-select__single-value,
.react-select .react-select__multi-value__label {
  color: #474d66;
}

.react-select .react-select__dropdown-indicator,
.react-select
  .react-select__control--is-focused
  .react-select__dropdown-indicator,
.react-select .react-select__clear-indicator,
.react-select
  .react-select__control--is-focused
  .react-select__clear-indicator {
  outline: initial;
  box-shadow: initial;
}
.react-select .react-select__dropdown-indicator:active,
.react-select .react-select__dropdown-indicator:focus,
.react-select .react-select__dropdown-indicator:hover,
.react-select
  .react-select__control--is-focused
  .react-select__dropdown-indicator:active,
.react-select
  .react-select__control--is-focused
  .react-select__dropdown-indicator:focus,
.react-select
  .react-select__control--is-focused
  .react-select__dropdown-indicator:hover,
.react-select .react-select__clear-indicator:active,
.react-select .react-select__clear-indicator:focus,
.react-select .react-select__clear-indicator:hover,
.react-select
  .react-select__control--is-focused
  .react-select__clear-indicator:active,
.react-select
  .react-select__control--is-focused
  .react-select__clear-indicator:focus,
.react-select
  .react-select__control--is-focused
  .react-select__clear-indicator:hover {
  color: #922c88 !important;
}

.react-select__control {
  border-radius: 0.1rem !important;
  border: 1px solid #d7d7d7 !important;
  background: white !important;
  outline: initial !important;
  box-shadow: none !important;
}

.react-select__indicator-separator {
  display: none;
}

.react-select__dropdown-indicator svg {
  width: 15px;
  height: 15px;
}

.react-select__option {
  background: white !important;
  color: #474d66 !important;
}
.react-select__option:hover,
.react-select__option:active {
  background: #922c88 !important;
  color: #fff !important;
}

.react-select__option--is-selected {
  background: #922c88 !important;
  color: #fff !important;
}

.react-select__control--is-focused {
  border-color: rgba(146, 44, 136, 0.6) !important;
}

.react-select__multi-value {
  background: transparent !important;
  border: 1px solid #d7d7d7 !important;
}

.react-select__multi-value__remove:hover,
.react-select__multi-value__remove:active {
  background: transparent !important;
  color: #922c88 !important;
}

.react-select .react-select__menu {
  border-radius: 0.1rem !important;
  z-index: 20 !important;
  box-shadow: initial !important;
  border: 1px solid rgba(146, 44, 136, 0.6) !important;
  border-top: initial !important;
  margin-top: -1px !important;
  background-color: white !important;
  border-width: 1px !important;
}

.react-select__option—is-focused {
  background: #922c88 !important;
  color: #fff !important;
}

.react-select__single-value {
  bottom: 0 !important;
  top: 50% !important;
  padding-top: 2px !important;
}

/* 19.Datatable */
.r-table {
  border: initial;
}
.r-table th.sortable:hover {
  color: #922c88 !important;
}
.r-table thead th {
  box-shadow: initial;
  border: initial;
  text-align: left;
  font-weight: 700;
}
.r-table thead th.sorted-asc {
  box-shadow: inset 0 3px 0 0 #922c88;
}
.r-table thead th.sorted-desc {
  box-shadow: inset 0 -3px 0 0 #922c88;
}
.r-table tr td,
.r-table tr th {
  padding-top: 20px;
  padding-bottom: 16px;
  border: initial;
}
.r-table tr th {
  padding-bottom: 20px;
}
.r-table tbody tr td {
  border-bottom: 1px solid #f3f3f3;
}
.r-table tbody tr:last-of-type td {
  border-bottom: initial;
}

.table-divided {
  border-collapse: separate;
  border-spacing: 0px 0.6rem;
  width: 100% !important;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.table-divided td {
  padding-top: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f3f3f3;
  outline: initial !important;
  background: white;
}
.table-divided tr:last-of-type td {
  border-bottom: initial;
}
.table-divided p,
.table-divided h6 {
  margin-bottom: initial;
}
.table-divided tbody > tr[role='row'] > td:first-child:before,
.table-divided tbody > tr[role='row'] > th:first-child:before {
  top: unset;
  box-shadow: initial;
  background-color: #922c88;
  font-size: 12px;
}
.table-divided tbody tr {
  box-shadow: 0 1px 15px rgba(0, 0, 0, 0.04), 0 1px 6px rgba(0, 0, 0, 0.04);
}
.table-divided tbody tr.selected {
  box-shadow: 0 3px 30px rgba(0, 0, 0, 0.1), 0 3px 20px rgba(0, 0, 0, 0.1);
}
.table-divided tbody tr.child td {
  padding: 0.75rem 2rem;
}
.table-divided tbody tr.child td li {
  padding: 0 !important;
}
.table-divided td,
.table-divided th {
  padding: 1.5rem;
  border: initial;
}
.table-divided th.empty:before,
.table-divided th.empty:after {
  content: '';
}
.table-divided .itemCheck {
  text-align: right;
  pointer-events: none;
}

.order-with-arrow thead th {
  padding-top: 0.6em;
  padding-bottom: 0.6em;
  border: initial;
}
.order-with-arrow thead th.sorted-asc {
  box-shadow: initial !important;
}
.order-with-arrow thead th.sorted-desc {
  box-shadow: initial !important;
}
.order-with-arrow thead .sortable,
.order-with-arrow thead .sorted-asc,
.order-with-arrow thead .sorted-desc,
.order-with-arrow thead .sorted-asc_disabled,
.order-with-arrow thead .sorted-desc_disabled {
  cursor: pointer;
  position: relative;
}
.order-with-arrow thead .sortable:before,
.order-with-arrow thead .sortable:after,
.order-with-arrow thead .sorted-asc:before,
.order-with-arrow thead .sorted-asc:after,
.order-with-arrow thead .sorted-desc:before,
.order-with-arrow thead .sorted-desc:after,
.order-with-arrow thead .sorted-asc_disabled:before,
.order-with-arrow thead .sorted-asc_disabled:after,
.order-with-arrow thead .sorted-desc_disabled:before,
.order-with-arrow thead .sorted-desc_disabled:after {
  position: absolute;
  top: 0.4em;
  display: block;
  opacity: 0.3;
}
.order-with-arrow thead .sorted-asc:before,
.order-with-arrow thead .sorted-desc:after {
  opacity: 1;
}
.order-with-arrow thead .sortable:before,
.order-with-arrow thead .sorted-asc:before,
.order-with-arrow thead .sorted-desc:before,
.order-with-arrow thead .sorted-asc_disabled:before,
.order-with-arrow thead .sorted-desc_disabled:before {
  right: 1em;
  content: '↑';
}
.order-with-arrow thead .sortable:after,
.order-with-arrow thead .sorted-asc:after,
.order-with-arrow thead .sorted-desc:after,
.order-with-arrow thead .sorted-asc_disabled:after,
.order-with-arrow thead .sorted-desc_disabled:after {
  right: 0.5em;
  content: '↓';
}

.rounded .table-divided tr {
  border-radius: 0.75rem;
}
.rounded .table-divided td {
  border-radius: initial;
}
.rounded .table-divided td:first-child {
  border-top-left-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
}
.rounded .table-divided td:last-child {
  border-top-right-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
}

.rtl .rounded .table-dividedtable td {
  border-radius: initial;
}
.rtl .rounded .table-dividedtable td:first-child {
  border-top-right-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
}
.rtl .rounded .table-dividedtable td:last-child {
  border-top-left-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
}

@media only screen and (max-width: 800px) {
  .responsive-table {
    /* Force table to not be like tables anymore */
    /* Hide table headers (but not display: none;, for accessibility) */
    /*
    Label the data
    */
  }
  .responsive-table table,
  .responsive-table thead,
  .responsive-table tbody,
  .responsive-table th,
  .responsive-table td,
  .responsive-table tr {
    display: block;
  }
  .responsive-table thead tr {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }
  .responsive-table tr {
    border: 1px solid #ccc;
  }
  .responsive-table td {
    /* Behave  like a "row" */
    border: none;
    border-bottom: 1px solid #eee;
    position: relative;
    padding-left: 50%;
    white-space: normal;
    text-align: left;
  }
  .responsive-table td:before {
    /* Now like a table header */
    position: absolute;
    /* Top/left values mimic padding */
    top: 6px;
    left: 6px;
    width: 45%;
    padding-right: 10px;
    white-space: nowrap;
    text-align: left;
    font-weight: bold;
  }
  .responsive-table td:before {
    content: attr(data-title);
  }
}

.react-tagsinput {
  background-color: white !important;
  border: 1px solid #d7d7d7 !important;
  outline: initial !important;
  box-shadow: initial !important;
}

.react-tagsinput-tag {
  background: #922c88 !important;
  border-radius: 15px !important;
  padding: 1px 10px !important;
  margin-bottom: 2px !important;
  display: inline-block !important;
  font-size: 12px !important;
  color: white !important;
  border: initial !important;
}

/* 10.Menu*/
.sidebar {
  position: fixed;
  top: 0;
  bottom: 0;
  padding-top: 105px;
  z-index: 22;
  height: calc(100% - 105px);
  transition: border-radius 300ms;
}
.sidebar .scrollbar-container {
  margin-right: 0;
  padding-right: 0;
}
.sidebar .main-menu {
  width: 120px;
  height: calc(100% - 105px);
  background: white;
  z-index: 3;
  position: fixed;
  transition: transform 300ms;
  padding-top: 10px;
  padding-bottom: 10px;
  left: 0;
}
.sidebar .main-menu .scroll {
  padding-right: unset;
  margin-right: unset;
  height: 100%;
}
.sidebar .main-menu .scroll .ps__thumb-y {
  right: 0;
}
.sidebar .main-menu.main-hidden {
  transform: translateX(-120px);
}
.sidebar .main-menu ul li {
  position: relative;
}
.sidebar .main-menu ul li span {
  text-align: center;
  padding: 0 10px;
  line-height: 14px;
}
.sidebar .main-menu ul li a {
  height: 75px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 1rem;
  font-style: normal;
  font-weight: 400;
  color: #474d66;
  transition: color 300ms;
  transition: background 300ms;
}
.sidebar .main-menu ul li a:hover,
.sidebar .main-menu ul li a:focus {
  color: #922c88;
}
.sidebar .main-menu ul li i {
  font-size: 32px;
  line-height: 42px;
}
.sidebar .main-menu ul li.active a {
  color: #922c88;
}
.sidebar .main-menu ul li.active:after {
  content: ' ';
  background: #922c88;
  border-radius: 10px;
  position: absolute;
  width: 6px;
  height: 80px;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
}
.sidebar .main-menu ul li:last-of-type a {
  border-bottom: initial;
}
@media (max-width: 1439px) {
  .sidebar .main-menu {
    width: 110px;
    height: calc(100% - 95px);
  }
}
@media (max-width: 1199px) {
  .sidebar .main-menu {
    width: 100px;
    height: calc(100% - 85px);
  }
}
@media (max-width: 767px) {
  .sidebar .main-menu {
    width: 175px;
    height: calc(100% - 75px);
  }
  .sidebar .main-menu ul li i {
    font-size: 28px;
    line-height: 38px;
  }
  .sidebar .main-menu ul li a {
    height: 50px;
  }
  .sidebar .main-menu ul li.active:after {
    width: 3px;
    height: 60px;
  }
}
.sidebar .sub-menu {
  width: 230px;
  background: white;
  z-index: 2;
  position: fixed;
  left: 120px;
  border-left: 1px solid #f3f3f3;
  box-shadow: 0 3px 30px rgba(0, 0, 0, 0.1), 0 3px 20px rgba(0, 0, 0, 0.1);
  transition: transform 300ms;
  height: calc(100% - 105px);
  padding-top: 10px;
  padding-bottom: 10px;
  padding-right: 0;
}
.sidebar .sub-menu .scroll {
  margin-right: unset;
  padding-right: unset;
  height: 100%;
}
.sidebar .sub-menu .scroll .ps__thumb-y {
  right: 0;
}
.sidebar .sub-menu .scroll .scrollbar-container {
  padding-top: 15px;
  padding-bottom: 15px;
}
.sidebar .sub-menu ul {
  display: none;
}
.sidebar .sub-menu ul li {
  margin-bottom: 0;
  margin-left: 30px;
  position: relative;
}
.sidebar .sub-menu ul li a {
  font-size: 13px;
  display: block;
  padding: 8px 0;
  margin-bottom: 10px;
}
.sidebar .sub-menu ul li i {
  font-size: 1.3em;
  margin-right: 10px;
  color: #8f8f8f;
  vertical-align: middle;
  display: inline-block;
}
.sidebar .sub-menu ul li.active i,
.sidebar .sub-menu ul li.active a {
  color: #922c88;
}
.sidebar .sub-menu ul li.active:after {
  content: ' ';
  background: #922c88;
  border-radius: 10px;
  position: absolute;
  width: 4px;
  height: 4px;
  top: 50%;
  transform: translateY(-50%);
  left: -16px;
}
.sidebar .sub-menu ul li span {
  vertical-align: middle;
  padding-top: 3px;
  display: inline-block;
}
.sidebar .sub-menu ul li.has-sub-item.active:after {
  content: ' ';
  background: initial;
}
.sidebar .sub-menu ul ul.third-level-menu {
  display: block !important;
}
.sidebar .sub-menu ul ul.third-level-menu li a,
.sidebar .sub-menu ul ul.third-level-menu li i {
  color: #474d66;
}
.sidebar .sub-menu ul ul.third-level-menu li a:hover,
.sidebar .sub-menu ul ul.third-level-menu li.active i,
.sidebar .sub-menu ul ul.third-level-menu li.active a {
  color: #922c88;
}
.sidebar .sub-menu ul ul.third-level-menu li.active:after {
  left: -22px;
}
.sidebar .sub-menu ul .rotate-arrow-icon i {
  transition: transform 200ms;
  transform: rotate(0);
}
.sidebar .sub-menu ul .rotate-arrow-icon.collapsed i {
  transform: rotate(-90deg);
}
@media (max-width: 1439px) {
  .sidebar .sub-menu {
    left: 110px;
    width: 230px;
    height: calc(100% - 95px);
  }
}
@media (max-width: 1199px) {
  .sidebar .sub-menu {
    left: 100px;
    width: 230px;
    height: calc(100% - 85px);
  }
}
@media (max-width: 767px) {
  .sidebar .sub-menu {
    left: 175px;
    width: 230px;
    height: calc(100% - 75px);
  }
}
@media (max-width: 1439px) {
  .sidebar {
    padding-top: 95px;
    height: calc(100% - 95px);
  }
}
@media (max-width: 1199px) {
  .sidebar {
    padding-top: 85px;
    height: calc(100% - 85px);
  }
}
@media (max-width: 767px) {
  .sidebar {
    padding-top: 75px;
    height: calc(100% - 75px);
    box-shadow: 0 1px 15px rgba(0, 0, 0, 0.04), 0 1px 6px rgba(0, 0, 0, 0.04);
  }
}

.ribbon {
  padding-top: 5px;
  align-items: center;
  justify-content: center;
  width: 100%;
  z-index: 2;
  position: fixed;
  display: flex;
  gap: 6px;
  right: 0;
  display: flex;
  background-color: #f0f0f0;
  padding-bottom: 8px;
}
.ribbon p {
  margin: 0;
  font-size: 15px;
}
.ribbon span {
  font-size: 15px;
  font-weight: 600;
  color: #922c88;
  cursor: pointer;
}

.sub-hidden .menu-button .sub,
.menu-sub-hidden .menu-button .sub {
  fill: #8f8f8f;
}

.main-hidden .menu-button .main,
.main-hidden .menu-button .sub,
.menu-hidden .menu-button .main,
.menu-hidden .menu-button .sub {
  fill: #8f8f8f;
}

.showcaseTopBtn {
  color: #922c88;
  cursor: pointer;
}

.sub-hidden .sub-menu,
.menu-sub-hidden .sub-menu,
.menu-hidden .sub-menu {
  transform: translateX(-230px);
}
@media (max-width: 1439px) {
  .sub-hidden .sub-menu,
  .menu-sub-hidden .sub-menu,
  .menu-hidden .sub-menu {
    transform: translateX(-230px);
  }
}
@media (max-width: 1199px) {
  .sub-hidden .sub-menu,
  .menu-sub-hidden .sub-menu,
  .menu-hidden .sub-menu {
    transform: translateX(-230px);
  }
}
@media (max-width: 767px) {
  .sub-hidden .sub-menu,
  .menu-sub-hidden .sub-menu,
  .menu-hidden .sub-menu {
    transform: translateX(-230px);
  }
}

.main-hidden .main-menu,
.menu-hidden .main-menu {
  transform: translateX(-120px);
}
@media (max-width: 1439px) {
  .main-hidden .main-menu,
  .menu-hidden .main-menu {
    transform: translateX(-110px);
  }
}
@media (max-width: 1199px) {
  .main-hidden .main-menu,
  .menu-hidden .main-menu {
    transform: translateX(-100px);
  }
}
@media (max-width: 767px) {
  .main-hidden .main-menu,
  .menu-hidden .main-menu {
    transform: translateX(-175px);
  }
}

.main-hidden.sub-hidden .sub-menu,
.menu-hidden .sub-menu {
  transform: translateX(-350px);
}
@media (max-width: 1439px) {
  .main-hidden.sub-hidden .sub-menu,
  .menu-hidden .sub-menu {
    transform: translateX(-340px);
  }
}
@media (max-width: 1199px) {
  .main-hidden.sub-hidden .sub-menu,
  .menu-hidden .sub-menu {
    transform: translateX(-330px);
  }
}
@media (max-width: 767px) {
  .main-hidden.sub-hidden .sub-menu,
  .menu-hidden .sub-menu {
    transform: translateX(-405px);
  }
}

.menu-mobile .main-menu {
  transform: translateX(-175px);
}
.menu-mobile .sub-menu {
  transform: translateX(-455px);
}

.main-show-temporary .main-menu {
  transform: translateX(0);
}
.main-show-temporary .sub-menu {
  transform: translateX(-230px);
}
@media (max-width: 1439px) {
  .main-show-temporary .main-menu {
    transform: translateX(0);
  }
  .main-show-temporary .sub-menu {
    transform: translateX(-230px);
  }
}
@media (max-width: 1199px) {
  .main-show-temporary .main-menu {
    transform: translateX(0);
  }
  .main-show-temporary .sub-menu {
    transform: translateX(-230px);
  }
}
@media (max-width: 767px) {
  .main-show-temporary .sub-menu {
    transform: translateX(-230px);
  }
}

.sub-show-temporary .sub-menu,
.menu-mobile.sub-show-temporary .sub-menu {
  transform: translateX(0);
}

.sub-hidden main,
.menu-sub-hidden main,
.menu-hidden main {
  margin-left: 180px;
}

.main-hidden main,
.menu-hidden main {
  margin-left: 60px;
}

@media (max-width: 1439px) {
  .sub-hidden main,
  .menu-sub-hidden main,
  .menu-hidden main {
    margin-left: 160px;
  }
  .main-hidden main,
  .menu-hidden main {
    margin-left: 50px;
  }
}
@media (max-width: 1199px) {
  .sub-hidden main,
  .menu-sub-hidden main,
  .menu-hidden main {
    margin-left: 140px;
  }
  .main-hidden main,
  .menu-hidden main {
    margin-left: 40px;
  }
}
.rtl .sidebar {
  right: 0;
  left: initial;
}
.rtl .sidebar .main-menu {
  left: initial;
  right: 0;
}
.rtl .sidebar .main-menu ul li {
  position: relative;
}
.rtl .sidebar .main-menu ul li.active:after {
  content: ' ';
  left: initial;
  right: 0;
}
@media (max-width: 767px) {
  .rtl .sidebar .main-menu ul li.active:after {
    right: 2px;
  }
}
.rtl .sidebar .sub-menu {
  left: initial;
  right: 120px;
  border-right: 1px solid #f3f3f3;
  box-shadow: 0 3px 30px rgba(0, 0, 0, 0.1), 0 3px 20px rgba(0, 0, 0, 0.1);
}
.rtl .sidebar .sub-menu ul li {
  margin-left: initial;
  margin-right: 30px;
}
.rtl .sidebar .sub-menu ul li i {
  margin-right: initial;
  margin-left: 10px;
}
.rtl .sidebar .sub-menu ul li.active:after {
  left: initial;
  right: -16px;
}
.rtl .sidebar .sub-menu ul.third-level-menu li.active:after {
  left: initial;
  right: -22px;
}
@media (max-width: 1439px) {
  .rtl .sidebar .sub-menu {
    left: initial;
    right: 110px;
  }
}
@media (max-width: 1199px) {
  .rtl .sidebar .sub-menu {
    left: initial;
    right: 100px;
  }
}
@media (max-width: 767px) {
  .rtl .sidebar .sub-menu {
    left: initial;
    right: 175px;
  }
}
.rtl .sidebar .sub-menu .scroll .ps__thumb-y,
.rtl .sidebar .main-menu .scroll .ps__thumb-y {
  right: 0;
}
.rtl .sub-hidden .sub-menu,
.rtl .menu-sub-hidden .sub-menu,
.rtl .menu-hidden .sub-menu {
  transform: translateX(230px);
}
@media (max-width: 1439px) {
  .rtl .sub-hidden .sub-menu,
  .rtl .menu-sub-hidden .sub-menu,
  .rtl .menu-hidden .sub-menu {
    transform: translateX(230px);
  }
}
@media (max-width: 1199px) {
  .rtl .sub-hidden .sub-menu,
  .rtl .menu-sub-hidden .sub-menu,
  .rtl .menu-hidden .sub-menu {
    transform: translateX(230px);
  }
}
@media (max-width: 767px) {
  .rtl .sub-hidden .sub-menu,
  .rtl .menu-sub-hidden .sub-menu,
  .rtl .menu-hidden .sub-menu {
    transform: translateX(230px);
  }
}
.rtl .main-hidden .main-menu,
.rtl .menu-hidden .main-menu {
  transform: translateX(120px);
}
@media (max-width: 1439px) {
  .rtl .main-hidden .main-menu,
  .rtl .menu-hidden .main-menu {
    transform: translateX(110px);
  }
}
@media (max-width: 1199px) {
  .rtl .main-hidden .main-menu,
  .rtl .menu-hidden .main-menu {
    transform: translateX(100px);
  }
}
@media (max-width: 767px) {
  .rtl .main-hidden .main-menu,
  .rtl .menu-hidden .main-menu {
    transform: translateX(175px);
  }
}
.rtl .main-hidden.sub-hidden .sub-menu,
.rtl .menu-hidden .sub-menu {
  transform: translateX(350px);
}
@media (max-width: 1439px) {
  .rtl .main-hidden.sub-hidden .sub-menu,
  .rtl .menu-hidden .sub-menu {
    transform: translateX(340px);
  }
}
@media (max-width: 1199px) {
  .rtl .main-hidden.sub-hidden .sub-menu,
  .rtl .menu-hidden .sub-menu {
    transform: translateX(330px);
  }
}
@media (max-width: 767px) {
  .rtl .main-hidden.sub-hidden .sub-menu,
  .rtl .menu-hidden .sub-menu {
    transform: translateX(405px);
  }
}
.rtl .menu-main-hidden .main-menu {
  width: 0;
}
.rtl .menu-main-hidden .sub-menu {
  right: 0;
}
.rtl .menu-mobile .main-menu {
  transform: translateX(175px);
}
.rtl .menu-mobile .sub-menu {
  transform: translateX(455px);
}
.rtl .main-show-temporary .main-menu {
  transform: translateX(0);
}
.rtl .main-show-temporary .sub-menu {
  transform: translateX(230px);
}
@media (max-width: 1439px) {
  .rtl .main-show-temporary .main-menu {
    transform: translateX(0);
  }
  .rtl .main-show-temporary .sub-menu {
    transform: translateX(230px);
  }
}
@media (max-width: 1199px) {
  .rtl .main-show-temporary .main-menu {
    transform: translateX(0);
  }
  .rtl .main-show-temporary .sub-menu {
    transform: translateX(230px);
  }
}
@media (max-width: 767px) {
  .rtl .main-show-temporary .sub-menu {
    transform: translateX(230px);
  }
}
.rtl .sub-show-temporary .sub-menu,
.rtl .menu-mobile.sub-show-temporary .sub-menu,
.rtl .menu-main-hidden.menu-mobile.main-show-temporary .sub-menu {
  transform: translateX(0);
}
.rtl .sub-hidden main,
.rtl .menu-sub-hidden main,
.rtl .menu-hidden main {
  margin-right: 180px;
}
.rtl .main-hidden main,
.rtl .menu-hidden main {
  margin-right: 60px;
}
.rtl .menu-main-hidden main {
  margin-right: 290px;
}
.rtl .menu-main-hidden.menu-hidden main {
  margin-right: 60px;
}
@media (max-width: 1439px) {
  .rtl .sub-hidden main,
  .rtl .menu-sub-hidden main,
  .rtl .menu-hidden main {
    margin-right: 160px;
  }
  .rtl .main-hidden main,
  .rtl .menu-hidden main {
    margin-right: 50px;
  }
  .rtl .menu-main-hidden main {
    margin-right: 280px;
  }
  .rtl .menu-main-hidden.menu-hidden main {
    margin-right: 50px;
  }
}
@media (max-width: 1199px) {
  .rtl .sub-hidden main,
  .rtl .menu-sub-hidden main,
  .rtl .menu-hidden main {
    margin-right: 140px;
  }
  .rtl .main-hidden main,
  .rtl .menu-hidden main {
    margin-right: 40px;
  }
  .rtl .menu-main-hidden main {
    margin-right: 270px;
  }
  .rtl .menu-main-hidden.menu-hidden main {
    margin-right: 40px;
  }
}
.rtl.rounded .app-menu {
  border-top-right-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
}
.rtl.rounded .sub-menu {
  border-radius: 0.75rem 0 0 0.75rem;
}
.rtl.rounded .sub-hidden .main-menu,
.rtl.rounded .menu-sub-hidden .main-menu {
  border-radius: 0.75rem 0 0 0.75rem;
}
.rtl.rounded .sub-show-temporary .main-menu {
  border-radius: initial;
}

.rounded .sub-menu {
  border-radius: 0 0.75rem 0.75rem 0;
}
.rounded .sub-hidden .main-menu,
.rounded .menu-sub-hidden .main-menu {
  border-radius: 0 0.75rem 0.75rem 0;
}
.rounded .sub-show-temporary .main-menu {
  border-radius: initial;
}

#app-container.sub-hidden .footer-content,
#app-container.menu-sub-hidden .footer-content,
#app-container.menu-hidden .footer-content {
  margin-left: 180px;
}
#app-container.main-hidden .footer-content,
#app-container.menu-hidden .footer-content {
  margin-left: 60px;
}
#app-container.menu-main-hidden .footer-content {
  margin-left: 290px;
}
#app-container.menu-main-hidden.menu-hidden .footer-content {
  margin-left: 60px;
}
@media (max-width: 1439px) {
  #app-container.sub-hidden .footer-content,
  #app-container.menu-sub-hidden .footer-content,
  #app-container.menu-hidden .footer-content {
    margin-left: 160px;
  }
  #app-container.main-hidden .footer-content,
  #app-container.menu-hidden .footer-content {
    margin-left: 50px;
  }
  #app-container.menu-main-hidden .footer-content {
    margin-left: 280px;
  }
  #app-container.menu-main-hidden.menu-hidden .footer-content {
    margin-left: 50px;
  }
}
@media (max-width: 1199px) {
  #app-container.sub-hidden .footer-content,
  #app-container.menu-sub-hidden .footer-content,
  #app-container.menu-hidden .footer-content {
    margin-left: 140px;
  }
  #app-container.main-hidden .footer-content,
  #app-container.menu-hidden .footer-content {
    margin-left: 40px;
  }
  #app-container.menu-main-hidden .footer-content {
    margin-left: 270px;
  }
  #app-container.menu-main-hidden.menu-hidden .footer-content {
    margin-left: 40px;
  }
}

.separate-border-cont .separate-border {
  border-bottom: 1px solid #f3f3f3;
}
@media (max-width: 767px) {
  .separate-border-cont {
    display: none !important;
  }
}

@media (max-width: 767px) {
  .side-small {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
  }
}

.sortable {
  cursor: default;
}

.sortable span {
  vertical-align: middle;
}

.sortable-ghost {
  opacity: 0.5;
}

.btn-multiple-state .spinner {
  width: 36px;
  text-align: center;
}
.btn-multiple-state .spinner > span {
  width: 6px;
  height: 6px;
  background-color: #fff;
  border-radius: 100%;
  display: inline-block;
  animation: sk-bouncedelay 1.2s infinite ease-in-out both;
}
.btn-multiple-state .spinner .bounce1 {
  animation-delay: -0.32s;
}
.btn-multiple-state .spinner .bounce2 {
  animation-delay: -0.16s;
}
@keyframes sk-bouncedelay {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
.table {
  color: #474d66;
}

.table th,
.table td {
  border-color: #f3f3f3 !important;
}

.table .thead-light th {
  background-color: #f3f3f3 !important;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: #f3f3f3 !important;
}

.rtl table.dataTable thead > tr > th.sorting_asc,
.rtl table.dataTable thead > tr > th.sorting_desc,
.rtl table.dataTable thead > tr > th.sorting,
.rtl table.dataTable thead > tr > td.sorting_asc,
.rtl table.dataTable thead > tr > td.sorting_desc,
.rtl table.dataTable thead > tr > td.sorting {
  padding-right: 0;
  padding-left: 30px;
}
.rtl table.dataTable thead .sorting:before,
.rtl table.dataTable thead .sorting_asc:before,
.rtl table.dataTable thead .sorting_asc_disabled:before,
.rtl table.dataTable thead .sorting_desc:before,
.rtl table.dataTable thead .sorting_desc_disabled:before {
  right: initial;
  left: 2em;
}
.rtl table.dataTable thead .sorting:after,
.rtl table.dataTable thead .sorting_asc:after,
.rtl table.dataTable thead .sorting_asc_disabled:after,
.rtl table.dataTable thead .sorting_desc:after,
.rtl table.dataTable thead .sorting_desc_disabled:after {
  right: initial;
  left: 1.5em;
}

.theme-colors {
  width: 280px !important;
  position: fixed;
  z-index: 1030;
  top: 50%;
  right: 0;
  background: white;
  box-shadow: 0 1px 15px rgba(0, 0, 0, 0.04), 0 1px 6px rgba(0, 0, 0, 0.04);
  transform: translate(280px, -50%);
  padding-top: 10px;
  padding-bottom: 10px;
}
.theme-colors .theme-button {
  position: absolute;
  left: -32px;
  background: white;
  padding: 13px 7px 13px 7px;
  border-radius: 0.2rem;
  color: #474d66;
  box-shadow: -2px 0px 5px rgba(0, 0, 0, 0.04);
  font-size: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #922c88;
}
.theme-colors .theme-color {
  width: 24px;
  height: 24px;
  display: inline-block;
  border-radius: 20px;
  transition: background 0.25s;
}
.theme-colors .theme-color.active,
.theme-colors .theme-color:hover {
  background: white;
}
.theme-colors .theme-color-purplemonster {
  border: 3px solid #922c88;
  background: #922c88;
}
.theme-colors .theme-color-blueyale {
  border: 3px solid #145388;
  background: #145388;
}
.theme-colors .theme-color-blueolympic {
  border: 3px solid #008ecc;
  background: #008ecc;
}
.theme-colors .theme-color-bluenavy {
  border: 3px solid #00365a;
  background: #00365a;
}
.theme-colors .theme-color-orangecarrot {
  border: 3px solid #ed7117;
  background: #ed7117;
}
.theme-colors .theme-color-greenmoss {
  border: 3px solid #576a3d;
  background: #576a3d;
}
.theme-colors .theme-color-greenlime {
  border: 3px solid #6fb327;
  background: #6fb327;
}
.theme-colors .theme-color-redruby {
  border: 3px solid #900604;
  background: #900604;
}
.theme-colors .theme-color-greysteel {
  border: 3px solid #48494b;
  background: #48494b;
}
.theme-colors .theme-color-yellowgranola {
  border: 3px solid #c0a145;
  background: #c0a145;
}
.theme-colors.shown {
  transform: translate(0, calc(-50% + 0.5px));
  transition: transform 0.4s ease-out;
}
.theme-colors.hidden {
  transform: translate(280px, -50%);
  transition: transform 0.4s ease-out;
}

.rtl .theme-colors {
  transform: translate(-280px, -50%);
  left: 0;
  right: initial;
}
.rtl .theme-colors .theme-button {
  left: 278px;
  box-shadow: 3px 0 5px rgba(0, 0, 0, 0.04);
}
.rtl .theme-colors.shown {
  transform: translate(0, -50%);
}

.video-js .vjs-big-play-button {
  background: white;
  height: 1.2em;
  border-radius: 0.75em;
  line-height: initial;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 3.5em;
  width: 2.5em;
  border: 0.06666em solid white;
}
.video-js .vjs-big-play-button .vjs-icon-placeholder {
  color: #922c88;
}

.vjs-poster,
.vjs-tech {
  border-radius: 0.1rem;
  background-color: transparent !important;
}

.vjs-tech {
  background: initial;
  -o-object-fit: cover;
  object-fit: cover;
}

.video-js:hover .vjs-big-play-button,
.video-js .vjs-big-play-button:focus {
  background-color: #f2f2f2;
  border-color: #f2f2f2;
}

.vjs-control {
  text-shadow: initial !important;
  outline: initial !important;
}

.video-js .vjs-control-bar {
  background: initial;
  margin: 1.75rem;
  width: calc(100% - 3.5rem);
}
.video-js .vjs-control-bar .vjs-control.vjs-button,
.video-js .vjs-control-bar .vjs-remaining-time,
.video-js .vjs-control-bar .vjs-volume-panel {
  margin-right: 0.5em;
  background: white;
  color: #922c88;
  border-radius: 15px;
  box-shadow: 0 3px 30px rgba(0, 0, 0, 0.1), 0 3px 20px rgba(0, 0, 0, 0.1);
}
.video-js .vjs-control-bar .vjs-progress-control.vjs-control {
  background: white;
  border-radius: 15px;
  margin-right: 0.5em;
  box-shadow: 0 3px 30px rgba(0, 0, 0, 0.1), 0 3px 20px rgba(0, 0, 0, 0.1);
}
.video-js .vjs-control-bar .vjs-mute-control.vjs-control {
  box-shadow: initial;
}
.video-js .vjs-control-bar .vjs-progress-holder {
  height: 3px;
  font-size: 1.6em !important;
}
.video-js .vjs-control-bar .vjs-load-progress,
.video-js .vjs-control-bar .vjs-load-progress div {
  background-color: rgba(146, 44, 136, 0.2);
}
.video-js .vjs-control-bar .vjs-play-progress:before {
  font-size: 0.55em;
  top: -0.2em;
}
.video-js .vjs-control-bar .vjs-progress-holder {
  margin: 0 17px;
}
.video-js .vjs-control-bar .vjs-slider {
  text-shadow: initial !important;
  outline: initial !important;
  background-color: #dadada;
}
.video-js .vjs-control-bar .vjs-play-progress {
  background: #922c88;
}
.video-js .vjs-control-bar .vjs-play-progress:before {
  color: #922c88;
}
.video-js .vjs-control-bar .vjs-volume-horizontal {
  margin-left: -1.5em;
  width: 4em;
}
.video-js .vjs-control-bar .vjs-volume-panel .vjs-volume-level {
  background: #922c88;
}

.video-js.audio {
  background: initial;
}
.video-js.audio .vjs-big-play-button {
  display: none;
}
.video-js.audio .vjs-control-bar {
  display: flex;
}
.video-js.audio .vjs-fullscreen-control {
  display: none;
}
.video-js.audio .vjs-control-bar {
  margin-bottom: 0;
}
.video-js.audio .vjs-control.vjs-button,
.video-js.audio .vjs-remaining-time,
.video-js.audio .vjs-volume-panel {
  box-shadow: 0 0px 2px rgba(0, 0, 0, 0.15), 0 0px 1px rgba(0, 0, 0, 0.2);
}
.video-js.audio .vjs-progress-control.vjs-control {
  box-shadow: 0 0px 2px rgba(0, 0, 0, 0.15), 0 0px 1px rgba(0, 0, 0, 0.2);
}
.video-js.audio .vjs-mute-control {
  box-shadow: initial !important;
}
.video-js.audio .vjs-loading-spinner {
  display: none !important;
}

.video-js.side-bar-video {
  width: 100%;
  height: 280px;
  background-color: #f8f8f8;
  overflow: hidden;
}
.video-js.side-bar-video video {
  -o-object-fit: cover;
  object-fit: cover;
}
.video-js.side-bar-video .vjs-poster {
  background-size: cover;
}

.video-js.video-content {
  width: 100%;
  height: 400px;
  background-color: #f8f8f8;
  overflow: hidden;
  max-height: unset;
}
.video-js.video-content video {
  -o-object-fit: cover;
  object-fit: cover;
}
.video-js.video-content .vjs-poster {
  background-size: cover;
}

.content-audio-preview {
  background-size: cover;
  width: 100%;
  background-color: #f8f8f8;
  overflow: hidden;
  -o-object-fit: scale-down;
  object-fit: scale-down;
  height: 400px;
  max-height: unset;
}

.rounded .vjs-poster,
.rounded .vjs-tech {
  border-radius: 0.75rem;
}

.auth-card {
  display: flex;
  flex-direction: row;
  box-shadow: 0 1px 15px rgba(0, 0, 0, 0.04), 0 1px 6px rgba(0, 0, 0, 0.04);
  /* form side*/
  /*end login form side*/
}
.auth-card .image-side {
  width: 40%;
  background: url('/assets/img/login/woman.jpg') no-repeat left top;
  position: relative;
  z-index: 0;
  background-size: cover;
  padding: 80px 40px;
}
.auth-card .image-side .backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  background-color: #3a3a3a;
  opacity: 0.3;
}
.auth-card .image-side .text-part {
  z-index: 10;
}
.auth-card .image-side .h3 {
  line-height: 0.8rem;
}
.auth-card select option {
  max-width: 150px !important;
}
.auth-card .form-side {
  width: 60%;
  padding: 40px 80px;
}
.auth-card .login-form-side {
  width: 100%;
  padding: 80px;
}
.auth-card .il-form-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
@media (max-width: 991px) {
  .auth-card {
    flex-direction: column;
  }
  .auth-card .image-side {
    width: 100%;
    padding: 60px;
    display: none;
  }
  .auth-card .form-side {
    width: 100%;
    padding: 20px 60px;
  }
  .auth-card .login-form-side {
    padding: 60px;
  }
}
@media (max-width: 767px) {
  .auth-card p.h2 {
    font-size: 1.6rem;
  }
}
@media (max-width: 575px) {
  .auth-card {
    flex-direction: column;
  }
  .auth-card .image-side {
    padding: 35px 30px;
  }
  .auth-card .form-side {
    padding: 25px 30px;
  }
  .auth-card .logo-single {
    margin-bottom: 20px;
  }
  .auth-card p.h2 {
    font-size: 1.4rem;
  }
}

.rounded .card.auth-card {
  border-top-left-radius: 1.25rem;
  border-bottom-left-radius: 1.25rem;
}
@media (max-width: 991px) {
  .rounded .card.auth-card {
    border-top-left-radius: 1.25rem;
    border-top-right-radius: 1.25rem;
    border-bottom-right-radius: 0.75rem;
    border-bottom-left-radius: 0.75rem;
  }
}
.rounded .auth-card .image-side {
  border-top-left-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
}
@media (max-width: 991px) {
  .rounded .auth-card .image-side {
    border-bottom-right-radius: initial;
    border-bottom-left-radius: initial;
    border-top-left-radius: 0.75rem;
    border-top-right-radius: 0.75rem;
  }
}
.rtl.rounded .auth-card .image-side {
  border-top-left-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
}
.rtl.rounded .card.auth-card {
  border-top-left-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
  border-top-right-radius: 1.25rem;
  border-bottom-right-radius: 1.25rem;
}
.rtl.rounded .auth-card .image-side {
  border-top-left-radius: initial;
  border-bottom-left-radius: initial;
  border-top-right-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
}

.ins-password-input {
  z-index: 0 !important;
}

.simple-icon-eye {
  font-size: 1rem;
}

.hide-password-icon {
  color: #3a3a3a;
  font-size: 1rem;
}

.dashboard-line-chart {
  height: 283px;
}

.dashboard-quick-post {
  min-height: 263px;
}

.dashboard-list-with-thumbs {
  height: 500px;
}

.dashboard-logs {
  height: 270px;
}

.dashboard-list-with-user {
  height: 270px;
}

.dashboard-donut-chart {
  height: 270px;
}

.dashboard-small-chart {
  height: 150px;
}
.dashboard-small-chart .chart {
  height: 55px;
}
.dashboard-small-chart .lead {
  font-size: 1.4rem;
}

.dashboard-small-chart-analytics {
  height: 180px;
}
.dashboard-small-chart-analytics .chart {
  height: 85px;
}
.dashboard-small-chart-analytics .lead {
  font-size: 1.4rem;
}

.dashboard-filled-line-chart {
  height: 340px;
}
.dashboard-filled-line-chart .chart {
  height: 200px;
}

.dashboard-sq-banner {
  background-image: linear-gradient(to right top, #832579, #922c88, #a9449f);
  background-size: cover;
  height: 385px;
  transition: 0.5s;
  background-size: 350% auto;
  cursor: pointer;
}
.dashboard-sq-banner .card-body {
  width: 270px;
}
.dashboard-sq-banner .lead {
  line-height: 2.3rem;
}
.dashboard-sq-banner:hover {
  background-position: right top;
}

.dashboard-link-list {
  height: 385px;
}

.dashboard-progress {
  height: 385px;
}

.dashboard-top-rated {
  height: 300px;
}
@media (max-width: 991px) {
  .dashboard-top-rated {
    height: unset;
  }
}
.dashboard-top-rated .glide img {
  height: 120px;
  display: flex;
  -o-object-fit: cover;
  object-fit: cover;
  width: 100%;
}
.dashboard-top-rated .glide .react-rater {
  display: inline-block !important;
}

.dashboard-search {
  height: 650px;
  background: url(/assets/img/login/balloon-lg.jpg);
  background-size: contain;
  background-repeat: no-repeat;
  background-color: white;
}
.dashboard-search .card-body {
  padding: 120px 50px 30px 50px;
}
@media (max-width: 1439px) {
  .dashboard-search .card-body {
    padding: 80px 30px 30px 30px;
  }
}
.dashboard-search .form-container {
  height: 400px;
  border-radius: 0.1rem;
  box-shadow: 0px -10px 15px 0px rgba(0, 0, 0, 0.04);
  padding: 2rem;
  background-color: white;
}

.log-indicator {
  width: 13px;
  height: 13px;
  border: 2px solid #922c88;
  border-radius: 14px;
  display: inline-block;
}

.dashboard-list-with-thumbs .list-thumbnail {
  max-width: 100px;
}

.rounded .dashboard-top-rated .glide img {
  border-radius: 0.75rem;
}

@media (max-width: 991px) {
  .chart-container {
    min-height: 400px;
    max-height: 400px;
  }
}

.summary-container {
  min-height: 300px;
  height: -moz-fit-content;
  height: fit-content;
}
@media (max-width: 991px) {
  .summary-container {
    min-height: 225px;
    max-height: 225px;
  }
}

.dashboard-content-container {
  margin: 20px auto;
}

.dashboard-media-upload-container {
  border: 0.15rem dashed #922c88;
  box-sizing: border-box;
}

.dashbaord-upload-title-container {
  padding-top: 5px;
  padding-bottom: 5px;
  border-bottom: 0.7px solid #922c88;
  font-style: normal;
  font-weight: 500;
}

.dashboard-upload-heading {
  font-size: 1rem;
}

.dashboard-upload-button-container {
  display: flex;
  justify-content: center;
  flex-direction: column;
}

.dashboard-upload-button {
  width: -moz-fit-content;
  width: fit-content;
  text-align: center;
  font-weight: 500;
}

.dashboard-center-this-item {
  margin: 0 auto;
}

.dashboard-alternate-container {
  display: flex;
  justify-content: center;
}

.dashboard-link-upload-section {
  display: flex;
  flex-direction: row;
  background: #e4e4e4;
  border-radius: 0.3px;
  width: 70%;
  margin: 0 auto;
}

.dashboard-link-label {
  width: 10%;
  display: flex;
  justify-content: center;
  padding-top: 10px;
  background-color: #922c88;
  font-weight: 600;
  font-size: 12.5px;
  margin-bottom: 0;
  padding-bottom: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
@media (max-width: 767px) {
  .dashboard-link-label {
    font-size: 12.5px;
    width: -moz-fit-content;
    width: fit-content;
  }
}

.dashboard-action-button-container {
  display: flex;
  justify-content: center;
}

.dashboard-whitespace {
  width: 20px;
}

.dashboard-file-input {
  display: none;
}

.dashboard-preview-video_audio {
  display: block;
  border-radius: 0.75rem;
  width: 100%;
  height: auto;
  box-shadow: 0 1px 15px rgba(0, 0, 0, 0.04), 0 1px 6px rgba(0, 0, 0, 0.04);
}
@media (max-width: 767px) {
  .dashboard-preview-video_audio {
    width: 100%;
  }
}

.video-preview-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin: 0 1rem 2rem;
}
@media (max-width: 767px) {
  .video-preview-container {
    margin: 0;
    margin-bottom: 1rem;
  }
}

.video-twoside-preview {
  display: grid;
  gap: 20px;
  grid-template-columns: 60% 40%;
}
@media (max-width: 991px) {
  .video-twoside-preview {
    grid-template-columns: 100%;
  }
}

.audio-twoside-preview {
  display: grid;
  gap: 20px;
  grid-template-columns: 40% 60%;
}
@media (max-width: 991px) {
  .audio-twoside-preview {
    grid-template-columns: 100%;
  }
}

.dashboard-video-meta-panel {
  margin-top: 1rem;
}

.video-other-options {
  width: 100%;
}

.markSize {
  width: 75px;
  margin-left: 5px;
  margin-right: 5px;
}

.video-watermark-preview-container {
  position: relative;
  width: 40%;
  height: auto;
  margin: 20px auto;
}
@media (max-width: 991px) {
  .video-watermark-preview-container {
    width: 70%;
  }
}
@media (max-width: 767px) {
  .video-watermark-preview-container {
    width: 90%;
  }
}
@media (max-width: 575px) {
  .video-watermark-preview-container {
    width: 100%;
  }
}

.image-watermark-preview-container {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 325px;
  height: 171px;
  margin: 40px auto;
}

.player-preview-image {
  width: 100%;
  height: 100%;
}

.imgicon-preview-image {
  position: relative;
  font-size: 300px;
  font-weight: lighter !important;
  color: #444 !important;
  background: -webkit-linear-gradient(
    top,
    white 10%,
    grey 88%,
    white 12%
  ) !important;
}
@media (max-width: 767px) {
  .imgicon-preview-image {
    font-size: 200px;
  }
}

.video-watermark-preview-image-bl {
  position: absolute;
  bottom: 33%;
  left: 2%;
}
@media (max-width: 767px) {
  .video-watermark-preview-image-bl {
    bottom: 33%;
  }
}

.video-watermark-preview-image-br {
  position: absolute;
  bottom: 33%;
  right: 2%;
}
@media (max-width: 767px) {
  .video-watermark-preview-image-br {
    bottom: 33%;
  }
}

.video-watermark-preview-image-tl {
  position: absolute;
  top: 3%;
  left: 2%;
}
@media (max-width: 767px) {
  .video-watermark-preview-image-tl {
    top: 3%;
  }
}

.video-watermark-preview-image-tr {
  position: absolute;
  top: 3%;
  right: 2%;
}
@media (max-width: 767px) {
  .video-watermark-preview-image-tr {
    top: 3%;
  }
}

.image-watermark-preview-image {
  position: absolute;
  bottom: 24%;
  right: 4%;
}
@media (max-width: 767px) {
  .image-watermark-preview-image {
    bottom: 28%;
  }
}

.dashboard-video-details {
  border-bottom: 0.5px solid #922c88;
}

.dash-detail-list {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: left;
  list-style: none;
  margin: 0;
  padding: 0;
  height: -moz-fit-content;
  height: fit-content;
}
.dash-detail-list li {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  padding-bottom: 1.2rem;
  width: 100%;
}
.dash-detail-list li span {
  position: relative;
  bottom: 0.25rem;
}
.dash-detail-list i {
  font-size: 25px;
  line-height: 90px;
  color: #922c88;
  position: relative;
  top: 3px;
}

.detail-list-first-item {
  font-size: 1rem;
  text-transform: capitalize;
  font-weight: 600;
}

.progressBar-container-house {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.progressBar-container {
  display: grid;
  width: 100%;
  padding: 3rem;
}

.progress-status {
  display: flex;
  justify-content: center;
  font-size: 1rem;
}

.progress-number {
  display: flex;
  justify-content: center;
}

.innerloop-progress-bar {
  height: 1rem !important;
  width: 90%;
  margin: 0 auto;
  outline: black;
  border: 0.01rem solid #922c88;
}

.up-sucess-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  font-size: 16px;
}

.up-content-container {
  border: 1px solid #cdcaca;
  padding: 1rem 1.5rem;
}
.up-content-container p.thank-you {
  font-size: 14px;
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.upload-success-icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 20px auto;
}

.upload-success-icon {
  color: green;
  font-size: 5rem;
}

.dash-videonclose {
  display: inline-flex;
  flex-direction: row-reverse;
  background: #fff;
  border: 1px solid #922c88;
  border-radius: 0.9rem;
  width: 95%;
  height: 85%;
  max-height: 300px;
  margin: 10px 0;
}
@media (max-width: 991px) {
  .dash-videonclose {
    background-color: none;
    width: 100%;
    height: 100%;
  }
}

.dash-audionclose {
  display: inline-flex;
  flex-direction: row-reverse;
  background: #fff;
  border: 1px solid #922c88;
  border-radius: 0.9rem;
  width: 90%;
  height: 265px;
}
@media (max-width: 991px) {
  .dash-audionclose {
    width: 75%;
    background-color: none;
  }
}
@media (max-width: 767px) {
  .dash-audionclose {
    width: 100%;
    background-color: none;
  }
}

.dash-close-video-btn {
  position: absolute;
  padding: 3px 2px 0px 2px;
  margin-top: 0.25rem;
  margin-right: 0.25rem;
  height: 2rem;
  background-color: white;
  color: black;
  border-color: white;
}
.dash-close-video-btn > i {
  font-size: 1.6rem;
}
@media (max-width: 767px) {
  .dash-close-video-btn {
    margin-top: 0.45rem;
  }
}

.impression-scale-container .speedometer {
  margin: 0 auto;
  display: flex;
}

.dashboard-chart-row {
  display: flex;
  padding: 2rem;
}
@media (max-width: 991px) {
  .dashboard-chart-row {
    flex-direction: column;
  }
}

.space-sum-card {
  width: 49%;
  height: -moz-fit-content;
  height: fit-content;
}
@media (max-width: 900px) {
  .space-sum-card {
    margin-top: 20px;
    width: 100%;
  }
}

.chat-app .scrollbar-container {
  padding-left: 10px;
  margin-left: -10px;
  padding-right: 10px;
  margin-right: 0;
  height: calc(100vh - 105px - 270px);
}
@media (max-width: 1439px) {
  .chat-app .scrollbar-container {
    height: calc(100vh - 95px - 270px);
  }
}
@media (max-width: 1199px) {
  .chat-app .scrollbar-container {
    height: calc(100vh - 85px - 270px);
  }
}
@media (max-width: 767px) {
  .chat-app .scrollbar-container {
    height: calc(100vh - 75px - 220px);
  }
}
.chat-app .scrollbar-container .ps__thumb-y {
  right: 10px;
}
.chat-app .chat-text-left {
  padding-left: 64px;
}
.chat-app .chat-text-right {
  padding-right: 64px;
}
@media (max-width: 767px) {
  .chat-app .list-item-heading {
    font-size: 0.9rem;
  }
}
@media (max-width: 767px) {
  .chat-app .card .card-body {
    padding: 0.75rem;
  }
}

.chat-app-tab-content {
  height: calc(100% - 45px);
}

.chat-app-tab-pane {
  height: calc(100% - 45px);
}

.chat-input-container {
  width: 100%;
  height: 90px;
  background: white;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  box-shadow: 0 1px 15px rgba(0, 0, 0, 0.04), 0 1px 6px rgba(0, 0, 0, 0.04);
  transition: padding 300ms;
  padding-left: 410px;
  padding-right: 340px;
}
@media (max-width: 1439px) {
  .chat-input-container {
    padding-left: 160px;
    padding-right: 330px;
  }
}
@media (max-width: 1199px) {
  .chat-input-container {
    padding-left: 140px;
    padding-right: 40px;
  }
}
@media (max-width: 767px) {
  .chat-input-container {
    padding-right: 15px;
    padding-left: 15px;
  }
}
.chat-input-container input {
  border: initial;
  width: unset;
  height: 90px;
  padding-left: 0;
  display: inline-block;
}
@media (max-width: 991px) {
  .chat-input-container .send-button {
    padding-left: 0.7rem;
    padding-right: 0.7rem;
  }
}

.menu-sub-hidden .chat-input-container,
.sub-hidden .chat-input-container {
  padding-left: 180px;
}
@media (max-width: 1439px) {
  .menu-sub-hidden .chat-input-container,
  .sub-hidden .chat-input-container {
    padding-left: 160px;
  }
}
@media (max-width: 1199px) {
  .menu-sub-hidden .chat-input-container,
  .sub-hidden .chat-input-container {
    padding-left: 140px;
  }
}
@media (max-width: 767px) {
  .menu-sub-hidden .chat-input-container,
  .sub-hidden .chat-input-container {
    padding-right: 15px;
    padding-left: 15px;
  }
}

.menu-main-hidden .chat-input-container,
.menu-hidden .chat-input-container,
.main-hidden .chat-input-container {
  padding-left: 60px;
}
@media (max-width: 1439px) {
  .menu-main-hidden .chat-input-container,
  .menu-hidden .chat-input-container,
  .main-hidden .chat-input-container {
    padding-left: 50px;
  }
}
@media (max-width: 1199px) {
  .menu-main-hidden .chat-input-container,
  .menu-hidden .chat-input-container,
  .main-hidden .chat-input-container {
    padding-left: 40px;
  }
}
@media (max-width: 767px) {
  .menu-main-hidden .chat-input-container,
  .menu-hidden .chat-input-container,
  .main-hidden .chat-input-container {
    padding-right: 15px;
    padding-left: 15px;
  }
}

.rtl {
  /*Chat Input Container*/
}
.rtl .chat-input-container {
  padding-right: 410px;
  padding-left: 340px;
}
@media (max-width: 1439px) {
  .rtl .chat-input-container {
    padding-right: 390px;
    padding-left: 330px;
  }
}
@media (max-width: 1199px) {
  .rtl .chat-input-container {
    padding-right: 370px;
    padding-left: 40px;
  }
}
@media (max-width: 767px) {
  .rtl .chat-input-container {
    padding-right: 15px;
    padding-left: 15px;
  }
}
.rtl .menu-sub-hidden .chat-input-container,
.rtl .sub-hidden .chat-input-container {
  padding-right: 180px;
}
@media (max-width: 1439px) {
  .rtl .menu-sub-hidden .chat-input-container,
  .rtl .sub-hidden .chat-input-container {
    padding-right: 160px;
  }
}
@media (max-width: 1199px) {
  .rtl .menu-sub-hidden .chat-input-container,
  .rtl .sub-hidden .chat-input-container {
    padding-right: 140px;
  }
}
@media (max-width: 767px) {
  .rtl .menu-sub-hidden .chat-input-container,
  .rtl .sub-hidden .chat-input-container {
    padding-right: 15px;
    padding-left: 15px;
  }
}
.rtl .menu-main-hidden .chat-input-container,
.rtl .menu-hidden .chat-input-container,
.rtl .main-hidden .chat-input-container {
  padding-right: 60px;
}
@media (max-width: 1439px) {
  .rtl .menu-main-hidden .chat-input-container,
  .rtl .menu-hidden .chat-input-container,
  .rtl .main-hidden .chat-input-container {
    padding-right: 50px;
  }
}
@media (max-width: 1199px) {
  .rtl .menu-main-hidden .chat-input-container,
  .rtl .menu-hidden .chat-input-container,
  .rtl .main-hidden .chat-input-container {
    padding-right: 40px;
  }
}
@media (max-width: 767px) {
  .rtl .menu-main-hidden .chat-input-container,
  .rtl .menu-hidden .chat-input-container,
  .rtl .main-hidden .chat-input-container {
    padding-right: 15px;
    padding-left: 15px;
  }
}

.glide {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.glide.details img {
  margin-bottom: 0;
}
.glide.details .glide__slides {
  margin-bottom: 0;
}

.glide.thumbs {
  cursor: pointer;
  width: 340px;
  margin: 0 auto;
}
@media (max-width: 767px) {
  .glide.thumbs {
    width: 300px;
  }
}
@media (max-width: 575px) {
  .glide.thumbs {
    width: calc(100% - 80px);
  }
}
.glide.thumbs .glide__slide {
  text-align: center;
  opacity: 0.4;
  transition: opacity 200ms;
}
.glide.thumbs .glide__slide.active,
.glide.thumbs .glide__slide:hover {
  opacity: 1;
}
.glide.thumbs .glide__slide img {
  width: 60px;
  margin-bottom: 0;
}
.glide.thumbs .glide__slides {
  margin-bottom: 0;
  margin: 0 auto;
}
.glide.thumbs .glide__arrows {
  width: 100%;
}
.glide.thumbs .glide__arrows .glide__arrow--left {
  border: initial;
  background: initial;
  box-shadow: initial;
  top: 23px;
  left: -50px;
  font-size: 20px;
  color: #922c88;
}
.glide.thumbs .glide__arrows .glide__arrow--right {
  border: initial;
  background: initial;
  box-shadow: initial;
  top: 23px;
  right: -50px;
  font-size: 20px;
  color: #922c88;
}

.ReactModal__Overlay {
  z-index: 1070 !important;
}

.ril__toolbar {
  background-color: initial !important;
}

.ril__builtinButton {
  background-size: 16px !important;
  vertical-align: top;
  width: 40px;
  height: 38px;
}

.ril__toolbarItem {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 50px;
  margin: 10px 5px;
  width: 40px;
  height: 40px;
}

.ril__toolbarRightSide {
  padding-right: initial;
}

.ril__toolbarLeftSide li {
  display: none;
}

.ril__navButtons {
  border-radius: 50px;
  width: 35px;
  height: 35px;
  padding: 25px;
  padding: 21px;
}

.ril__navButtonNext {
  background-size: 12px;
  right: 10px;
}

.ril__navButtonPrev {
  background-size: 12px;
  left: 10px;
}

.invoice-react .invoice-contents {
  background-color: white;
  height: 1200px;
  max-width: 830px;
  position: relative;
}

@media print {
  body {
    background: white;
    height: 100%;
  }
  main {
    margin: 0 !important;
  }
  .navbar,
  .sidebar,
  .theme-colors {
    display: none;
  }
  main .container-fluid .row:not(.invoice) {
    display: none;
  }
  .invoice {
    width: 100%;
    max-width: 100%;
  }
  .invoice.row {
    margin: 0;
  }
  .invoice [class*='col'] {
    padding: 0;
  }
  .invoice .invoice-contents {
    width: 100%;
    max-width: 100% !important;
    height: 1370px !important;
  }
}
.landing-page {
  font-size: 1rem;
  line-height: 1.6rem;
  color: #8f8f8f;
}
@media (max-width: 767px) {
  .landing-page {
    font-size: 0.9rem;
    line-height: 1.4rem;
  }
}
.landing-page .mobile-menu {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  transform: translateX(300px);
  z-index: 25;
  width: 300px;
  background: #f8f8f8;
  box-shadow: initial;
  transition: transform 0.5s, box-shadow 0.5s;
}
.landing-page .main-container {
  overflow: hidden;
}
.landing-page.show-mobile-menu .mobile-menu {
  transform: translateX(0);
  box-shadow: 0 3px 30px rgba(0, 0, 0, 0.1), 0 3px 20px rgba(0, 0, 0, 0.1);
}
.landing-page.show-mobile-menu .main-container .content-container,
.landing-page.show-mobile-menu .main-container nav {
  transform: translateX(-300px);
}
.landing-page .content-container {
  transition: transform 0.5s;
}
.landing-page p {
  font-size: 1rem;
  line-height: 1.6rem;
  color: #8f8f8f;
}
@media (max-width: 767px) {
  .landing-page p {
    font-size: 0.9rem;
    line-height: 1.4rem;
  }
}
.landing-page h1 {
  font-size: 3rem;
  color: #922c88;
  margin-bottom: 2rem;
}
@media (max-width: 767px) {
  .landing-page h1 {
    font-size: 2rem;
  }
}
.landing-page h2 {
  font-size: 1.8rem;
  color: #922c88;
  margin-bottom: 2rem;
}
@media (max-width: 991px) {
  .landing-page h2 {
    font-size: 1.6rem;
  }
}
@media (max-width: 767px) {
  .landing-page h2 {
    font-size: 1.2rem;
  }
}
.landing-page h3 {
  font-size: 1.4rem;
  line-height: 1.4;
  color: #922c88;
}
@media (max-width: 767px) {
  .landing-page h3 {
    font-size: 1.1rem;
  }
}
.landing-page .semi-rounded {
  border-radius: 1rem !important;
}
.landing-page .dropdown-toggle::after {
  opacity: 0.7;
}
.landing-page .btn-outline-semi-light {
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #fff;
  background: transparent;
}
.landing-page .btn-outline-semi-light:hover,
.landing-page .btn-outline-semi-light:active {
  border: 1px solid #6b2063;
  background: #6b2063;
}
.landing-page .landing-page-nav {
  z-index: 5 !important;
  top: 0;
  position: fixed;
  width: 100%;
  background: transparent;
  box-shadow: initial;
}
.landing-page .landing-page-nav nav {
  transition: transform 0.5s;
}
.landing-page .landing-page-nav .headroom {
  transition: top 0.5s, background 0.5s, box-shadow 0.2s, transform 0.5s !important;
}
.landing-page .landing-page-nav .container {
  height: 105px;
}
@media (max-width: 1439px) {
  .landing-page .landing-page-nav .container {
    height: 95px;
  }
}
@media (max-width: 1199px) {
  .landing-page .landing-page-nav .container {
    height: 85px;
  }
  .landing-page .landing-page-nav .container .navbar-logo {
    width: 100px;
    height: 30px;
  }
  .landing-page .landing-page-nav .container .navbar-logo .white,
  .landing-page .landing-page-nav .container .navbar-logo .dark {
    width: 100px;
    height: 30px;
  }
}
@media (max-width: 767px) {
  .landing-page .landing-page-nav .container {
    height: 75px;
  }
}
.landing-page .landing-page-nav .btn {
  padding-right: 1.75rem !important;
  padding-left: 1.75rem !important;
}
.landing-page .landing-page-nav .navbar-logo {
  width: 120px;
  height: 40px;
}
.landing-page .landing-page-nav .navbar-logo .white {
  display: inline-block;
  width: 120px;
  height: 40px;
  background: url('/assets/logos/logo.svg');
  background-size: contain;
  background-repeat: no-repeat;
}
.landing-page .landing-page-nav .navbar-logo .dark {
  width: 120px;
  height: 40px;
  background: url('/assets/logos/logo.svg');
  background-size: contain;
  background-repeat: no-repeat;
  display: none;
}
.landing-page .landing-page-nav .mobile-menu-button {
  font-size: 20px;
  display: none;
  cursor: pointer;
}
@media (max-width: 991px) {
  .landing-page .landing-page-nav .mobile-menu-button {
    display: block;
  }
}
.landing-page .landing-page-nav .navbar-nav {
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
}
.landing-page .landing-page-nav .navbar-nav li {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  position: relative;
  margin-bottom: initial;
}
.landing-page .landing-page-nav .navbar-nav li:last-of-type {
  padding-right: 0;
}
.landing-page .landing-page-nav .navbar-nav li.active::before {
  content: ' ';
  background: #fff;
  border-radius: 10px;
  position: absolute;
  width: calc(100% - 1.5rem);
  height: 4px;
  left: 0;
  right: 0;
  margin: 0 auto;
  top: 0;
}
@media (max-width: 1199px) {
  .landing-page .landing-page-nav .navbar-nav li {
    padding-left: 0.85rem;
    padding-right: 0.85rem;
  }
  .landing-page .landing-page-nav .navbar-nav li:last-of-type {
    padding-right: 0;
  }
}
.landing-page .landing-page-nav .navbar-nav .nav-item > a,
.landing-page .landing-page-nav .navbar-nav .nav-item > .dropdown > a {
  color: #fff;
  font-family: Nunito, sans-serif;
  font-size: 1rem;
  display: inline-block;
  margin-top: 0;
}
.landing-page
  .landing-page-nav
  .navbar-nav
  .nav-item:not(.active)
  > a:hover:not(.btn),
.landing-page
  .landing-page-nav
  .navbar-nav
  .nav-item:not(.active)
  > a:active:not(.btn),
.landing-page
  .landing-page-nav
  .navbar-nav
  .nav-item:not(.active)
  .dropdown
  > a:hover:not(.btn),
.landing-page
  .landing-page-nav
  .navbar-nav
  .nav-item:not(.active)
  .dropdown
  > a:active:not(.btn) {
  opacity: 0.8;
}
.landing-page .landing-page-nav .navbar-nav .dropdown {
  height: 80px;
  position: relative;
}
.landing-page .landing-page-nav .navbar-nav .dropdown:hover > .dropdown-menu {
  display: block;
}
.landing-page
  .landing-page-nav
  .navbar-nav
  .dropdown
  > .dropdown-toggle:active {
  pointer-events: none;
}
.landing-page .landing-page-nav .navbar-nav .dropdown .dropdown-menu {
  position: absolute;
  margin-top: 0;
}
.landing-page .landing-page-nav .navbar-nav .dropdown a.dropdown-item {
  font-size: 0.8rem;
  color: #474d66;
  padding-bottom: 0.5rem;
}
.landing-page .landing-page-nav .navbar-nav .dropdown a.dropdown-item.active,
.landing-page .landing-page-nav .navbar-nav .dropdown a.dropdown-item:active {
  color: #fff;
}
.landing-page .landing-page-nav .headroom--pinned {
  background: white;
  box-shadow: 0 1px 15px rgba(0, 0, 0, 0.04), 0 1px 6px rgba(0, 0, 0, 0.04);
}
.landing-page .landing-page-nav .headroom--pinned .navbar-logo .white {
  display: none;
}
.landing-page .landing-page-nav .headroom--pinned .navbar-logo .dark {
  display: inline-block;
}
.landing-page .landing-page-nav .headroom--pinned .navbar-nav a {
  color: #474d66;
}
.landing-page .landing-page-nav .headroom--pinned .navbar-nav a:hover,
.landing-page .landing-page-nav .headroom--pinned .navbar-nav a:active {
  color: #922c88;
}
.landing-page
  .landing-page-nav
  .headroom--pinned
  .navbar-nav
  a.dropdown-item:focus {
  color: #fff;
}
.landing-page
  .landing-page-nav
  .headroom--pinned
  .navbar-nav
  li.active::before {
  content: ' ';
  background-color: #922c88;
}
.landing-page
  .landing-page-nav
  .headroom--pinned
  .navbar-nav
  .btn-outline-semi-light {
  border: 1px solid rgba(146, 44, 136, 0.3);
  color: #922c88;
}
.landing-page
  .landing-page-nav
  .headroom--pinned
  .navbar-nav
  .btn-outline-semi-light:hover,
.landing-page
  .landing-page-nav
  .headroom--pinned
  .navbar-nav
  .btn-outline-semi-light:active {
  border: 1px solid #922c88;
  background: #922c88;
  color: #fff;
}
.landing-page .landing-page-nav .headroom--pinned .headroom--unpinned {
  background: transparent;
}
.landing-page .landing-page-nav .headroom--unfixed .mobile-menu-button {
  color: #fff;
}
.landing-page .section {
  margin-bottom: 150px;
  padding-top: 100px;
  padding-bottom: 100px;
}
@media (max-width: 991px) {
  .landing-page .section {
    margin-bottom: 100px;
    padding-top: 60px;
  }
}
.landing-page .section.home {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 2600 1440'%3E%3Cdefs%3E%3ClinearGradient id='linear-gradient' x1='862.49' y1='-420.67' x2='1546.24' y2='981.21' gradientUnits='userSpaceOnUse'%3E%3Cstop offset='0.1' stop-color='%2352125A'/%3E%3Cstop offset='0.27' stop-color='%23511452'/%3E%3Cstop offset='0.63' stop-color='%23B02BA6'/%3E%3Cstop offset='0.88' stop-color='%23CB33BD'/%3E%3C/linearGradient%3E%3C/defs%3E%3Ctitle%3Ebackground-home%3C/title%3E%3Cg%3E%3Cpath style='fill:url(%23linear-gradient);' d='M0,0V952.71l497.08,428.77c44.64,38.51,122.25,50,172.46,25.57L2600,467.26V0Z'/%3E%3Cpath style='fill:%23CB33BD;opacity:0.3;' d='M624.43,1418.11c-44.33,4.25-94.67-9.44-127.35-37.63L0,952.48v50.82l452.08,389.18C496.69,1431,574.21,1442.48,624.43,1418.11Z'/%3E%3C/g%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position-y: 0;
  background-color: transparent;
  height: 1440px;
  margin-bottom: 0;
  padding-top: 0;
  padding-bottom: 0;
  position: relative;
  background-size: cover;
}
@media (max-width: 575px) {
  .landing-page .section.home {
    height: 1340px;
  }
}
.landing-page .section.subpage {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 2600 1040'%3E%3Cdefs%3E%3ClinearGradient id='linear-gradient' x1='929.23' y1='-453.21' x2='1676' y2='1077.89' gradientUnits='userSpaceOnUse'%3E%3Cstop offset='0.1' stop-color='%2352125A'/%3E%3Cstop offset='0.27' stop-color='%23511452'/%3E%3Cstop offset='0.63' stop-color='%23B02BA6'/%3E%3Cstop offset='0.88' stop-color='%23CB33BD'/%3E%3C/linearGradient%3E%3C/defs%3E%3Ctitle%3Ebackground-sub%3C/title%3E%3Cg%3E%3Cpath style='fill:url(%23linear-gradient);' d='M170.66,739.24,522.89,996.51c31.63,23.1,89.75,35.48,129.15,27.5L2166.74,717.1,2600,627.22V0H0V614Z'/%3E%3Cpath style='fill:%23CB33BD;opacity:0.3;' d='M442.89,993.51c31.63,23.1,89.75,35.48,129.15,27.5l4.62-.94c-20.5-5.19-39.74-13.32-53.77-23.56L170.66,739.24,0,614v55.73l90.66,66.55Z'/%3E%3C/g%3E%3C/svg%3E");
  background-position-y: -370px;
  background-repeat: no-repeat;
  background-size: auto 1040px;
  height: 670px;
}
@media (max-width: 1199px) {
  .landing-page .section.subpage {
    background-position-y: -450px;
    height: 590px;
  }
}
@media (max-width: 991px) {
  .landing-page .section.subpage {
    background-position-y: -470px;
    height: 570px;
  }
}
@media (max-width: 767px) {
  .landing-page .section.subpage {
    background-position-y: -530px;
    height: 510px;
  }
}
.landing-page .section.subpage .btn-circle.hero-circle-button {
  bottom: initial;
}
.landing-page .section.subpage-long {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 2600 1040'%3E%3Cdefs%3E%3ClinearGradient id='linear-gradient' x1='929.23' y1='-453.21' x2='1676' y2='1077.89' gradientUnits='userSpaceOnUse'%3E%3Cstop offset='0.1' stop-color='%2352125A'/%3E%3Cstop offset='0.27' stop-color='%23511452'/%3E%3Cstop offset='0.63' stop-color='%23B02BA6'/%3E%3Cstop offset='0.88' stop-color='%23CB33BD'/%3E%3C/linearGradient%3E%3C/defs%3E%3Ctitle%3Ebackground-sub%3C/title%3E%3Cg%3E%3Cpath style='fill:url(%23linear-gradient);' d='M170.66,739.24,522.89,996.51c31.63,23.1,89.75,35.48,129.15,27.5L2166.74,717.1,2600,627.22V0H0V614Z'/%3E%3Cpath style='fill:%23CB33BD;opacity:0.3;' d='M442.89,993.51c31.63,23.1,89.75,35.48,129.15,27.5l4.62-.94c-20.5-5.19-39.74-13.32-53.77-23.56L170.66,739.24,0,614v55.73l90.66,66.55Z'/%3E%3C/g%3E%3C/svg%3E");
  height: 1040px;
  background-repeat: no-repeat;
  background-position-y: -200px;
}
.landing-page .section.footer {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 2600 1100'%3E%3Cdefs%3E%3ClinearGradient id='linear-gradient' x1='-153' y1='642.88' x2='3129.11' y2='642.88' gradientUnits='userSpaceOnUse'%3E%3Cstop offset='0.1' stop-color='%2352125A'/%3E%3Cstop offset='0.27' stop-color='%23511452'/%3E%3Cstop offset='0.63' stop-color='%23B02BA6'/%3E%3Cstop offset='0.88' stop-color='%23CB33BD'/%3E%3C/linearGradient%3E%3ClinearGradient id='linear-gradient-2' y1='264.94' x2='3129.11' y2='264.94' xlink:href='%23linear-gradient'/%3E%3C/defs%3E%3Cg%3E%3Cpath style='fill:%23CB33BD;opacity:0.3;' d='M2019.51,188.57c17.85,4.08,35.64,13,52.49,29.43l528,635.74V787.56L2127,218C2092.59,184.43,2054.24,182.23,2019.51,188.57Z'/%3E%3Cpath style='fill:url(%23linear-gradient);' d='M1945,193,0,714v386H2600V853.33L2072,218C2031,178,1984.41,182.53,1945,193Z'/%3E%3Cpath style='fill:url(%23linear-gradient-2);' d='M2308.31,91c-60.24,17.09-77.23,69.94-37.76,117.43l210.94,253.81c26.74,32.19,72.83,54,118.51,59.37V8.25Z'/%3E%3C/g%3E%3C/svg%3E");
  background-color: white;
  background-repeat: no-repeat;
  padding-bottom: 0;
  background-size: cover;
  background-size: auto 1100px;
  padding-top: 340px;
  background-position-y: 150px;
  margin-top: -150px;
}
@media (max-width: 991px) {
  .landing-page .section.footer {
    padding-top: 380px;
  }
}
@media (max-width: 575px) {
  .landing-page .section.footer {
    padding-top: 380px;
  }
}
.landing-page .section.background {
  background: white;
  position: relative;
  padding-top: 100px;
  padding-bottom: 100px;
}
@media (max-width: 991px) {
  .landing-page .section.background {
    padding-top: 60px;
    padding-bottom: 60px;
  }
}
.landing-page .section.background::before {
  content: ' ';
  width: 100%;
  height: 70px;
  position: absolute;
  top: -70px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 2600 70'%3E%3Cg%3E%3Cpath style='fill:%23FFFFFF' d='M2600,70v-.71C2254.44,26.19,1799.27,0,1300.5,0,801.25,0,345.68,26.24,0,69.42V70Z'/%3E%3C/g%3E%3C/svg%3E");
  background-size: cover;
  background-position: center;
}
.landing-page .section.background::after {
  content: ' ';
  width: 100%;
  height: 70px;
  position: absolute;
  bottom: -70px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 2600 70'%3E%3Cg%3E%3Cpath style='fill:%23FFFFFF' d='M0,0V.71C345.56,43.81,800.73,70,1299.5,70,1798.75,70,2254.32,43.76,2600,.58V0Z'/%3E%3C/g%3E%3C/svg%3E");
  background-size: cover;
  background-position: center;
}
.landing-page .section.background.background-no-bottom::after {
  content: '';
  background: initial;
  width: initial;
  height: initial;
}
.landing-page .background-white {
  background: #fff;
}
.landing-page .mobile-hero {
  margin-left: 50%;
  transform: translateX(-50%);
  max-width: 135%;
  margin-bottom: 3rem;
}
.landing-page .home-row {
  padding-top: 180px;
  margin-bottom: 70px;
}
@media (max-width: 1439px) {
  .landing-page .home-row {
    padding-top: 120px;
  }
}
@media (max-width: 991px) {
  .landing-page .home-row {
    padding-top: 110px;
  }
}
.landing-page .home-text {
  margin-top: 40px;
}
.landing-page .home-text p {
  color: #fff;
  font-size: 1.1rem;
}
@media (max-width: 767px) {
  .landing-page .home-text p {
    font-size: 0.9rem;
    line-height: 1.4rem;
  }
}
.landing-page .home-text .display-1 {
  font-size: 3rem;
  line-height: 3.2rem;
  margin-bottom: 1.1em;
  color: #fff;
}
@media (max-width: 991px) {
  .landing-page .home-text .display-1 {
    font-size: 1.8rem;
    line-height: 2.5rem;
  }
}
@media (max-width: 767px) {
  .landing-page .home-text .display-1 {
    font-size: 1.7rem;
    line-height: 2.3rem;
  }
}
@media (max-width: 575px) {
  .landing-page .home-text .display-1 {
    font-size: 1.6rem;
    line-height: 2.2rem;
  }
}
@media (max-width: 767px) {
  .landing-page .home-text {
    margin-top: 0;
  }
}
.landing-page .review-carousel {
  margin-top: 5rem;
}
.landing-page .review-carousel .owl-stage-outer {
  padding-top: 5px;
}
.landing-page .home-carousel {
  cursor: grab;
}
.landing-page .home-carousel ::-moz-selection {
  /* Code for Firefox */
  color: inherit;
  background: inherit;
}
.landing-page .home-carousel ::selection {
  color: inherit;
  background: inherit;
}
.landing-page .home-carousel .card .detail-text {
  padding: 1rem 0.25rem;
  margin-bottom: 0;
}
@media (max-width: 767px) {
  .landing-page .home-carousel .card .detail-text {
    padding: 0.25rem;
  }
}
@media (max-width: 767px) {
  .landing-page .home-carousel .card .btn-link {
    padding: 0.25rem;
  }
}
.landing-page i.large-icon {
  font-size: 60px;
  line-height: 110px;
  color: #922c88;
  display: initial;
}
@media (max-width: 767px) {
  .landing-page i.large-icon {
    line-height: 90px;
  }
}
.landing-page .btn-circle {
  padding: 0;
  width: 50px;
  height: 50px;
  border-radius: 30px;
  text-align: center;
  font-size: 14px;
}
.landing-page .btn-circle.hero-circle-button {
  position: absolute;
  bottom: 80px;
  margin-left: 10px;
}
.landing-page .btn-circle.hero-circle-button i {
  padding-top: 18px;
  display: inline-block;
}
.landing-page .newsletter-input-container {
  margin-top: 5rem;
}
.landing-page .newsletter-input-container .input-group {
  border-radius: 50px;
  box-shadow: 0 3px 30px rgba(0, 0, 0, 0.1), 0 3px 20px rgba(0, 0, 0, 0.1);
}
.landing-page .newsletter-input-container input {
  border-top-left-radius: 50px;
  border-bottom-left-radius: 50px;
  border-color: transparent !important;
  padding: 0.75rem 1.75rem 0.75rem;
  font-size: 0.9rem;
}
@media (max-width: 767px) {
  .landing-page .newsletter-input-container button {
    padding: 1rem 1.5rem 0.9rem;
  }
}
.landing-page .footer .footer-content {
  padding-top: 210px;
}
@media (max-width: 767px) {
  .landing-page .footer .footer-content {
    padding-top: 140px;
  }
}
@media (max-width: 575px) {
  .landing-page .footer .footer-content {
    padding-top: 80px;
  }
}
.landing-page .footer .footer-circle-button {
  margin-top: 80px;
  margin-right: 20px;
}
@media (max-width: 991px) {
  .landing-page .footer .footer-circle-button {
    margin-top: 110px;
  }
}
.landing-page .footer .footer-circle-button i {
  padding-top: 15px;
  display: inline-block;
}
.landing-page .footer .footer-logo {
  width: 140px;
  margin-bottom: 70px;
}
.landing-page .footer .footer-menu p {
  color: #fff;
  font-size: 1rem;
}
.landing-page .footer .footer-menu a {
  font-size: 1rem;
  color: #fff;
}
.landing-page .footer .footer-menu a:hover,
.landing-page .footer .footer-menu a:focus {
  color: #44143f;
}
.landing-page .footer .footer-menu .collapse-button i {
  font-size: 0.75rem;
  margin-left: 5px;
}
.landing-page .footer .footer-menu .collapse-button:hover,
.landing-page .footer .footer-menu .collapse-button:focus {
  color: #fff;
}
.landing-page .footer .separator {
  opacity: 0.3;
}
.landing-page .footer .copyright p {
  color: #fff;
}
.landing-page .footer .social-icons ul {
  margin-bottom: 0;
}
.landing-page .footer .social-icons li {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.landing-page .footer .social-icons li a {
  color: #fff;
  font-size: 1.2rem;
}
.landing-page .footer .social-icons li a:hover,
.landing-page .footer .social-icons li a:active {
  color: rgba(255, 255, 255, 0.7);
}
.landing-page .feature-image-left {
  float: right;
}
@media (max-width: 767px) {
  .landing-page .feature-image-left,
  .landing-page .feature-image-right {
    margin-left: 50%;
    transform: translateX(-50%);
    max-width: 115%;
    float: initial;
  }
}
@media (max-width: 767px) {
  .landing-page .heading-team {
    text-align: center;
  }
}
.landing-page .mobile-menu {
  text-align: center;
}
.landing-page .mobile-menu img {
  width: 100px;
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.landing-page .mobile-menu ul {
  text-align: left;
}
.landing-page .mobile-menu ul li {
  padding: 0.5rem 2.5rem;
  margin-bottom: unset;
}
.landing-page .mobile-menu ul li a {
  font-size: 0.9rem;
}
.landing-page .mobile-menu .dropdown .dropdown-menu {
  position: static !important;
  background: initial;
  border: initial;
  padding: initial;
  margin: initial;
  transform: initial !important;
  margin-top: 0.5rem;
}
.landing-page .mobile-menu .dropdown a.dropdown-item {
  color: #474d66;
  padding-bottom: 0.5rem;
}
.landing-page .mobile-menu .dropdown a.dropdown-item.active,
.landing-page .mobile-menu .dropdown a.dropdown-item:active {
  color: #fff;
}
@media (max-width: 991px) {
  .landing-page .side-bar {
    margin-top: 5rem;
  }
}
.landing-page .side-bar .side-bar-content {
  margin-bottom: 5rem;
}
@media (max-width: 767px) {
  .landing-page .side-bar h2 {
    margin-bottom: 1rem;
  }
}
.landing-page .listing-card-container {
  background: initial;
  box-shadow: initial;
}
.landing-page .listing-card-container .listing-heading {
  height: 65px;
  overflow: hidden;
}
.landing-page .listing-card-container .listing-desc {
  height: 68px;
  overflow: hidden;
  line-height: 1.4;
  font-size: 1rem;
}
@media (max-width: 1199px) {
  .landing-page .listing-card-container .listing-desc {
    height: 46px;
  }
}
.landing-page .video-heading {
  height: 38px;
  overflow: hidden;
}
.landing-page .table-heading {
  box-shadow: initial;
  background: initial;
}
.landing-page form.dark-background input:-webkit-autofill {
  color: white !important;
  -webkit-text-fill-color: white !important;
}
.landing-page form.dark-background .has-float-label > span,
.landing-page form.dark-background .has-float-label label,
.landing-page form.dark-background .has-top-label > span,
.landing-page form.dark-background .has-top-label label {
  color: rgba(255, 255, 255, 0.6);
}
.landing-page form.dark-background .bootstrap-tagsinput,
.landing-page form.dark-background .form-control {
  background: transparent;
  border-color: rgba(255, 255, 255, 0.3);
  color: #fff;
}
.landing-page form.dark-background .bootstrap-tagsinput:focus,
.landing-page form.dark-background .bootstrap-tagsinput:active,
.landing-page form.dark-background .form-control:focus,
.landing-page form.dark-background .form-control:active {
  border-color: rgba(255, 255, 255, 0.6);
}
.landing-page .has-float-label > span,
.landing-page .has-float-label label,
.landing-page .has-top-label > span,
.landing-page .has-top-label label {
  font-size: 64%;
}
.landing-page .video-js.blog-video {
  width: 100%;
  height: 442px;
  background-color: #f8f8f8;
}
@media (max-width: 991px) {
  .landing-page .video-js.blog-video {
    height: 280px;
  }
}
.landing-page .video-js.blog-video .vjs-poster {
  background-size: cover;
}
.landing-page .video-js.side-bar-video {
  width: 100%;
  height: 300px;
  background-color: #f8f8f8;
}
.landing-page .video-js.side-bar-video .vjs-poster {
  background-size: cover;
}
.landing-page .feature-icon-container .detail-text {
  min-height: 100px;
}
@media (max-width: 991px) {
  .landing-page .feature-icon-container .detail-text {
    min-height: 70px;
  }
}
.landing-page .screenshots .nav-tabs {
  margin-bottom: 0 !important;
  border: initial;
}
.landing-page .screenshots .nav-tabs .nav-link {
  border: initial;
  background: initial !important;
  padding-right: 40px;
  padding-left: 40px;
}
.landing-page .screenshots .nav-tabs .nav-item.show .nav-link:before,
.landing-page .screenshots .nav-tabs .nav-link.active:before {
  top: initial;
  bottom: 0;
}
.landing-page .app-image {
  width: 100%;
  box-shadow: 0 3px 30px rgba(0, 0, 0, 0.1), 0 3px 20px rgba(0, 0, 0, 0.1);
  border-radius: 1.2rem;
}
.landing-page .doc-search {
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: transparent;
  border-radius: 40px;
  padding: 0.85rem 0.75rem 0.8rem;
  max-width: 360px;
}
.landing-page .doc-search input {
  color: #fff;
  background: transparent;
  width: 93%;
  padding: 0 0.75rem;
  outline: initial !important;
  border: initial;
}
.landing-page .doc-search input::-moz-placeholder {
  color: #fff;
  opacity: 0.7;
}
.landing-page .doc-search input::placeholder {
  color: #fff;
  opacity: 0.7;
}
.landing-page .doc-search i {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
}
.landing-page .feedback-container a {
  font-size: 1.3em;
  color: #8f8f8f;
  margin: 0.5rem;
}
.landing-page .feedback-container a:hover,
.landing-page .feedback-container a:active {
  color: #922c88;
}
.landing-page .video-play-icon {
  width: 100%;
  height: 100%;
  position: absolute;
}
.landing-page .video-play-icon span {
  position: absolute;
  font-family: VideoJS;
  font-weight: 400;
  font-style: normal;
  background: rgba(255, 255, 255, 0.7);
  height: 1.25em;
  border-radius: 0.75em;
  line-height: 1.25em;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.75em;
  width: 2em;
  text-align: center;
}
.landing-page .video-play-icon span:before {
  color: #922c88;
  content: '\f101';
}
.landing-page .video-play-icon:hover span,
.landing-page .video-play-icon:active span {
  background: rgba(255, 255, 255, 0.85);
}
.landing-page .page-item .page-link {
  line-height: 1.2;
}

.components-image {
  position: relative;
  left: 50%;
  transform: translateX(-50%);
}
@media (max-width: 991px) {
  .components-image {
    width: 1600px;
  }
}
@media (max-width: 767px) {
  .components-image {
    width: 1200px;
  }
}

.color-left {
  border-top-left-radius: 1rem;
  border-bottom-left-radius: 1rem;
  width: 33.33%;
  height: 20px;
}

.color-center {
  width: 33.33%;
  height: 20px;
}

.color-right {
  border-top-right-radius: 1rem;
  border-bottom-right-radius: 1rem;
  width: 33.33%;
  height: 20px;
}

.color-container {
  height: 20px;
  display: flex;
  justify-content: stretch;
  border-radius: 1rem;
  background-color: initial;
}

.bluenavy-light-1 {
  background-color: #00365a;
}

.bluenavy-light-2 {
  background-color: #fff;
}

.bluenavy-light-3 {
  background-color: #184f90;
}

.bluenavy-dark-1 {
  background-color: #236591;
}

.bluenavy-dark-2 {
  background-color: #1e2022;
}

.bluenavy-dark-3 {
  background-color: #1d477a;
}

.blueolympic-light-1 {
  background-color: #008ecc;
}

.blueolympic-light-2 {
  background-color: #fff;
}

.blueolympic-light-3 {
  background-color: #73c2fb;
}

.blueolympic-dark-1 {
  background-color: #3e83a1;
}

.blueolympic-dark-2 {
  background-color: #1e2022;
}

.blueolympic-dark-3 {
  background-color: #304d72;
}

.blueyale-light-1 {
  background-color: #145388;
}

.blueyale-light-2 {
  background-color: #fff;
}

.blueyale-light-3 {
  background-color: #2a93d5;
}

.blueyale-dark-1 {
  background-color: #38688b;
}

.blueyale-dark-2 {
  background-color: #1e2022;
}

.blueyale-dark-3 {
  background-color: #3e8ab9;
}

.greenmoss-light-1 {
  background-color: #576a3d;
}

.greenmoss-light-2 {
  background-color: #fff;
}

.greenmoss-light-3 {
  background-color: #dd9c02;
}

.greenmoss-dark-1 {
  background-color: #627745;
}

.greenmoss-dark-2 {
  background-color: #1e2022;
}

.greenmoss-dark-3 {
  background-color: #8f7b39;
}

.greenlime-light-1 {
  background-color: #6fb327;
}

.greenlime-light-2 {
  background-color: #fff;
}

.greenlime-light-3 {
  background-color: #51c878;
}

.greenlime-dark-1 {
  background-color: #63883b;
}

.greenlime-dark-2 {
  background-color: #1e2022;
}

.greenlime-dark-3 {
  background-color: #4d7058;
}

.carrotorange-light-1 {
  background-color: #ed7117;
}

.carrotorange-light-2 {
  background-color: #fff;
}

.carrotorange-light-3 {
  background-color: #e7523e;
}

.carrotorange-dark-1 {
  background-color: #ad7140;
}

.carrotorange-dark-2 {
  background-color: #1e2022;
}

.carrotorange-dark-3 {
  background-color: #aa4f43;
}

.rubyred-light-1 {
  background-color: #900604;
}

.rubyred-light-2 {
  background-color: #fff;
}

.rubyred-light-3 {
  background-color: #e7284a;
}

.rubyred-dark-1 {
  background-color: #913a47;
}

.rubyred-dark-2 {
  background-color: #1e2022;
}

.rubyred-dark-3 {
  background-color: #aa5e6c;
}

.monsterpurple-light-1 {
  background-color: #922c88;
}

.monsterpurple-light-2 {
  background-color: #fff;
}

.monsterpurple-light-3 {
  background-color: #4556ac;
}

.monsterpurple-dark-1 {
  background-color: #7e4877;
}

.monsterpurple-dark-2 {
  background-color: #1e2022;
}

.monsterpurple-dark-3 {
  background-color: #3c4b9a;
}

.steelgrey-light-1 {
  background-color: #48494b;
}

.steelgrey-light-2 {
  background-color: #fff;
}

.steelgrey-light-3 {
  background-color: #999da0;
}

.steelgrey-dark-1 {
  background-color: #767e8d;
}

.steelgrey-dark-2 {
  background-color: #1e2022;
}

.steelgrey-dark-3 {
  background-color: #4d5a5f;
}

.granolayellow-light-1 {
  background-color: #c0a145;
}

.granolayellow-light-2 {
  background-color: #fff;
}

.granolayellow-light-3 {
  background-color: #e3b778;
}

.granolayellow-dark-1 {
  background-color: #8a722c;
}

.granolayellow-dark-2 {
  background-color: #1e2022;
}

.granolayellow-dark-3 {
  background-color: #a88048;
}

.rtl .landing-page .color-left {
  border-radius: initial;
  border-top-right-radius: 1rem;
  border-bottom-right-radius: 1rem;
}
.rtl .landing-page .color-right {
  border-radius: initial;
  border-top-left-radius: 1rem;
  border-bottom-left-radius: 1rem;
}
.rtl .landing-page .footer-circle-button {
  float: right;
}
.rtl .landing-page .components-image {
  left: initial;
  right: 50%;
  transform: translateX(50%);
}
.rtl .landing-page .feature-image-left {
  float: left;
}
@media (max-width: 767px) {
  .rtl .landing-page .feature-image-right {
    float: initial;
    margin-left: initial;
    margin-right: 50%;
    transform: translateX(50%);
  }
}
.rtl .landing-page .feature-row {
  margin-top: 80px;
}
@media (max-width: 767px) {
  .rtl .landing-page .feature-row {
    margin-top: 40px;
  }
}
.rtl .landing-page .mobile-hero {
  margin-left: initial;
  transform: translateX(50%);
  margin-right: 50%;
}
.rtl .landing-page .mobile-menu ul {
  padding: initial;
}
.rtl .landing-page .mobile-menu ul .nav-item + .nav-item {
  margin-right: initial;
  margin-left: initial;
}
.rtl .landing-page .mobile-menu-cta {
  padding-left: 1rem;
  padding-right: 1rem;
}

.equal-height-container .col-item .card {
  height: 100%;
}
@media (max-width: 991px) {
  .equal-height-container .col-item .card-body {
    text-align: left;
  }
}
@media (max-width: 575px) {
  .equal-height-container .col-item .card-body {
    text-align: center;
  }
}
.equal-height-container .col-item .price-top-part {
  text-align: center;
}
.equal-height-container .col-item .price-top-part * {
  text-align: center !important;
}
@media (max-width: 991px) {
  .equal-height-container .col-item .price-top-part {
    padding-left: 0;
    padding-right: 0;
    width: 40%;
  }
}
@media (max-width: 575px) {
  .equal-height-container .col-item .price-top-part {
    width: initial;
  }
}
.equal-height-container .col-item .price-feature-list {
  justify-content: space-between;
}
.equal-height-container .col-item .price-feature-list ul {
  margin: 0 auto;
  align-self: flex-start;
  margin-bottom: 1rem;
}
.equal-height-container .col-item .price-feature-list ul li p {
  text-align: center !important;
}

.table-heading {
  box-shadow: initial;
  background: initial;
}

i.large-icon {
  font-size: 38px;
  line-height: 90px;
  color: #922c88;
}
@media (max-width: 767px) {
  i.large-icon {
    line-height: 70px;
  }
}

.large-icon.initial-height {
  line-height: initial;
}
@media (max-width: 767px) {
  .large-icon.initial-height {
    line-height: initial;
  }
}

.social-header {
  width: 100%;
  height: 380px;
  -o-object-fit: cover;
  object-fit: cover;
  max-height: 380px;
}

.social-profile-img {
  height: 120px;
  border: 3px solid white;
  position: absolute;
  left: 50%;
  z-index: 1;
  transform: translateX(-50%);
  top: -75px;
}

.social-icons ul {
  margin-bottom: 0;
}
.social-icons li {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.social-icons li a {
  color: #8f8f8f;
  font-size: 1.2rem;
}
.social-icons li a:hover,
.social-icons li a:active {
  color: rgba(143, 143, 143, 0.7);
}

.post-icon i {
  font-size: 1.1rem;
  vertical-align: middle;
}
.post-icon span {
  display: inline-block;
  padding-top: 3px;
  vertical-align: middle;
}

.comment-likes {
  text-align: right;
  flex-basis: 90px;
  flex-shrink: 0;
}

.comment-container input {
  border-top-left-radius: 50px;
  border-bottom-left-radius: 50px;
  padding-left: 20px;
}

.social-image-row [class*='col-'] {
  margin-bottom: 1rem;
}
.social-image-row [class*='col-']:nth-last-child(-n + 2) {
  margin-bottom: 0;
}
.social-image-row [class*='col-']:nth-child(odd) {
  padding-right: 0.5rem;
}
.social-image-row [class*='col-']:nth-child(even) {
  padding-left: 0.5rem;
}

.gallery-page.row {
  margin-left: -5px;
  margin-right: -5px;
}
.gallery-page.row [class*='col-'] {
  margin-bottom: 10px;
  padding-left: 5px;
  padding-right: 5px;
}

.col-left {
  padding-right: 20px;
}
@media (max-width: 991px) {
  .col-left {
    padding-right: 15px;
  }
}

.col-right {
  padding-left: 20px;
}
@media (max-width: 991px) {
  .col-right {
    padding-left: 15px;
  }
}

.survey-app .answers .badge {
  color: #8f8f8f;
  font-size: 13px;
  padding-left: 0.75em;
  padding-right: 0.75em;
}
.survey-app .answers input {
  padding-right: 70px;
}
.survey-app .question.edit-quesiton .edit-mode {
  display: block;
}
.survey-app .question.edit-quesiton .view-mode {
  display: none;
}
.survey-app .question.edit-quesiton .view-button {
  display: inline-block;
}
.survey-app .question.edit-quesiton .edit-button {
  display: none;
}
.survey-app .question.view-quesiton .edit-mode {
  display: none;
}
.survey-app .question.view-quesiton .view-mode {
  display: block;
}
.survey-app .question.view-quesiton .view-button {
  display: none;
}
.survey-app .question.view-quesiton .edit-button {
  display: inline-block;
}
.survey-app .survey-question-types .btn-group-icon {
  font-size: 21px;
  line-height: 28px;
}
.survey-app .survey-question-types .btn {
  padding-bottom: 0.3rem;
}

.input-icons {
  position: absolute;
  right: 0;
  top: 9px;
}

.rtl .survey-app .answers input {
  padding-right: 0.75rem;
  padding-left: 70px;
}
.rtl .input-icons {
  left: 0;
  right: initial;
}

.heading-icon {
  font-size: 20px;
  color: #922c88;
  margin-right: 5px;
  line-height: inherit;
  vertical-align: middle;
}

.todo-details {
  margin-top: 20px;
  margin-left: 40px;
}

.heading-number {
  border: 1px solid #922c88;
  padding: 4px;
  vertical-align: middle;
  margin-right: 10px;
  border-radius: 20px;
  width: 34px;
  height: 34px;
  text-align: center;
  color: #922c88;
}

.StoryTitle {
  color: #000;
  cursor: pointer;
  font-weight: normal;
}

.spaces-card {
  height: 180px;
  width: 220px;
  border-radius: 1rem;
  cursor: pointer;
}

.create-spacecard-width {
  width: 280px !important;
  border-radius: 0.4rem !important;
}

#space-upload-button {
  color: #fff !important;
}

.default-space-title {
  padding-top: 0.75rem;
}

.spaces-header {
  justify-content: space-between;
  font-style: normal;
  font-weight: 500;
}
.spaces-header p {
  font-size: 1.5rem;
}

.space-create-card {
  border: 2px dashed #922c88;
  box-sizing: border-box;
  height: 52.32px !important;
}

.space-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.create-space-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 2rem !important;
  height: 50.32px !important;
}
.create-space-box h6 {
  line-height: 0 !important;
  height: 0 !important;
  margin-bottom: 0 !important;
  margin-left: 10px;
  color: #922c88 !important;
}
.create-space-box i {
  font-size: 23px !important;
}

.bb-2 {
  margin-bottom: 2rem;
  box-shadow: 0px 2px 0px 0px #922c88;
}

.space-filter-btn {
  margin: 0 auto;
}

#spaces-media-menu {
  color: #922c88;
}

#spaces-media-menu:hover {
  background-color: #fff;
  border-color: #fff;
  color: #922c88;
}

.modal-space-className {
  padding: 0% 14%;
}
@media (max-width: 690px) {
  .modal-space-className {
    padding: 0%;
  }
}

.modal-space {
  padding-right: 0 !important;
}
.modal-space .modal-dialog {
  margin: 0 auto;
  height: -moz-fit-content;
  height: fit-content;
  max-width: 80%;
}
@media (max-width: 767px) {
  .modal-space .modal-dialog {
    max-width: 100%;
  }
}
.modal-space .modal-content {
  min-height: 100%;
}
.modal-space .modal-header {
  height: 105px;
}
@media (max-width: 1439px) {
  .modal-space .modal-header {
    height: 95px;
  }
}
@media (max-width: 1199px) {
  .modal-space .modal-header {
    height: 85px;
  }
}
@media (max-width: 767px) {
  .modal-space .modal-header {
    height: 75px;
  }
}
.modal-space .modal-footer {
  justify-content: center;
}
.modal-space .modal.fade .modal-dialog {
  transform: translate(25%, 0) !important;
}
.modal-space .modal.show .modal-dialog {
  transform: translate(0, 0) !important;
}

.modal-space-cancel {
  display: none;
}
@media (max-width: 767px) {
  .modal-space-cancel {
    display: flex;
    justify-content: center;
    margin: 0 auto;
  }
}

.space-media-title-editor {
  background: white;
  border: 0.1px dashed #922c88;
  box-sizing: border-box;
  padding: 1rem;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.space-media-desc-editor {
  border: 0.1px dashed #922c88;
  box-sizing: border-box;
  padding: 1rem;
  width: 50%;
  height: 160px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
@media (max-width: 991px) {
  .space-media-desc-editor {
    width: 75%;
  }
}
@media (max-width: 767px) {
  .space-media-desc-editor {
    width: 100%;
  }
}

.story-media-desc-editor {
  border: 0.1px dashed #922c88;
  box-sizing: border-box;
  padding: 1rem;
  width: 100%;
  height: 160px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
@media (max-width: 991px) {
  .story-media-desc-editor {
    width: 75%;
  }
}
@media (max-width: 767px) {
  .story-media-desc-editor {
    width: 100%;
  }
}

.space-header-desc-editor {
  border: none;
  padding: 0;
  box-shadow: none;
  background-color: transparent;
}
@media (min-width: 991px) {
  .space-header-desc-editor {
    width: 75%;
  }
}

@media (max-width: 1439px) {
  .share-btn {
    margin-left: 1rem;
    margin-bottom: 1rem;
  }
}
@media (max-width: 767px) {
  .share-btn {
    margin-left: 0;
  }
}

.space-content-details .row {
  margin-bottom: 1.5rem;
  font-size: 1rem;
  width: 100%;
}

.textbox-content-details {
  width: calc(100% - 785px);
}
@media (max-width: 1200px) {
  .textbox-content-details {
    width: 100%;
  }
}

.space-content-detail-list {
  min-height: 275px;
}
.space-content-detail-list .row {
  margin-bottom: 1.5rem;
  font-size: 1rem;
  width: 100%;
}

.details-age-container {
  display: flex;
  flex-direction: column;
}

.dotted-border-primary-box {
  border: 0.1px dashed #922c88;
  box-sizing: border-box;
  padding: 2px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 200px;
}
@media (max-width: 575px) {
  .dotted-border-primary-box {
    max-width: 100%;
  }
}

.thumbnails-container {
  max-width: 258px;
  padding: 0;
  margin-right: auto;
}
@media (max-width: 767px) {
  .thumbnails-container {
    width: 100%;
  }
}

.thumbnail-size {
  height: 65px;
  width: 65px;
}

.watermark-size {
  height: 50px;
  width: 50px;
}

.solid-border-primary-box {
  border: 0.1px solid #aaa;
  box-sizing: border-box;
  padding: 2.8px;
  margin-right: 15px;
  margin-bottom: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: -moz-fit-content;
  width: fit-content;
  transition: 0.3s all;
}
@media (max-width: 575px) {
  .solid-border-primary-box {
    max-width: 100%;
    margin-bottom: 5px;
  }
}
.solid-border-primary-box:hover {
  padding: 1.2px;
  border: 2px solid #922c88;
}

.solid-active-primary-box {
  border: 2px solid #922c88;
}

.space-media-subtitle {
  width: 250px;
}
@media (max-width: 575px) {
  .space-media-subtitle {
    width: 100%;
  }
}

.space-media-subtitle::-moz-placeholder {
  text-align: center;
}

.space-media-subtitle::placeholder {
  text-align: center;
}

.media-thumbnail-btn,
.media-thumbnail-btn:hover,
.media-thumbnail-btn:active {
  display: flex;
  flex-direction: row;
  background-color: transparent;
  border: none;
  color: #922c88;
  padding: 2px 1px;
}

.thumbnail-add-icon {
  margin: 0.2rem 0.4rem;
}

.thumbnail-remove-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.thumbnail-remove-icon-btn {
  padding: 0;
  font-size: 1.5rem;
  color: #c43d4b;
}

.thumbnail-remove-icon-btn :hover {
  color: #922c88;
}

.space-media-value {
  display: flex;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
}

.default-spaces-title {
  text-transform: capitalize;
  width: -moz-fit-content;
  width: fit-content;
  font-size: 1.5rem;
  color: #922c88;
}

.default-space-header {
  border-bottom: 2px solid currentColor;
}

.default-explore-text {
  font-size: 1.5rem;
  color: #922c88;
  font-weight: 350;
}

.about-spaces-container {
  font-size: 1.1rem;
  line-height: 1.5;
  font-weight: 200;
}

.default-spaces-text-section {
  font-size: 1.1rem;
  line-height: 1.5;
  font-weight: 200;
}

.default-space-link {
  display: flex;
  flex-direction: column;
}

.default-spaces-image-container {
  padding: 1.5rem;
}
@media (max-width: 767px) {
  .default-spaces-image-container {
    display: none;
  }
}

.default-spaces-image {
  width: 320px;
}
@media (max-width: 767px) {
  .default-spaces-image {
    width: 100%;
  }
}

.spaces-media-processing-row {
  text-align: left;
  padding-bottom: 20px;
}
.spaces-media-processing-row .rc-slider-disabled {
  background-color: inherit !important;
}

.preview-media-container {
  padding-bottom: 0;
}

.upload-media-container {
  width: 70%;
  margin: 0 auto;
}

.media-label-title {
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 600;
}

.upload-cancel-btn {
  display: flex;
  justify-content: center;
  margin: 0 auto;
}

.media-space > .modal-content {
  border-radius: 0.75rem;
}

.space-card-list-row {
  display: flex;
  justify-content: space-between;
}

.spaces-analytics-card {
  border-radius: 1rem;
}

.spaces-analytics-card-body {
  text-align: center;
}

.analytics-justify-center {
  display: flex;
  justify-content: center;
}

.card-chart-container {
  height: 150px;
  background-color: grey;
  display: flex;
  justify-content: center;
}

.insert-large-space {
  padding: 3rem;
}

.media-space > .modal-content {
  border-radius: 0.75rem;
}

.detailed-content {
  width: 760px;
  height: 515px;
}
@media (max-width: 991px) {
  .detailed-content {
    width: 100%;
    max-height: 515px;
  }
}
@media (max-width: 767px) {
  .detailed-content {
    width: 100%;
    max-height: 400px;
  }
}

.detailed-image {
  max-width: 100%;
  height: auto;
  max-height: 515px;
}

.ins-player-list-item {
  cursor: pointer;
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  margin: 5px 0px;
  border-radius: 5px;
  padding: 10px;
}
.ins-player-list-item .img {
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80px;
  overflow: hidden;
}

.list-view-row {
  height: 85px;
  margin-bottom: 10px;
}

.disable-player-card {
  background: #d7d7d7;
}

.text-field-ctn img {
  cursor: pointer;
  position: absolute;
  bottom: 10px;
  right: 35px;
  width: 25px;
  height: 25px;
  font-size: 2em;
}

.custom-css-text-area {
  border: 0.15px dotted #922c88;
  box-sizing: border-box;
  height: 200px !important;
  padding-right: 40px;
  padding-top: 10px;
  background: white;
}

.custom-css-text-area::-moz-placeholder {
  font-style: italic;
}

.custom-css-text-area::placeholder {
  font-style: italic;
}

.css-yk16xz-control,
.css-1pahdxg-control,
.select__menu {
  background-color: white !important;
  border-color: transparent !important;
  border: none !important;
}

.settings-field {
  padding: 0;
  margin-right: 55px;
}
@media (max-width: 768px) {
  .settings-field {
    margin-right: 20px;
  }
}

.space-home-btn {
  font-size: 1.2rem;
  margin-bottom: 10px;
}

.countries-modal {
  height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0 10px;
}

.ins-tabbar-item {
  display: flex;
  background-color: white;
  width: 100%;
  height: 50px;
  text-align: center;
  align-items: center;
  justify-content: center;
  border-left: 1px solid rgba(51, 51, 51, 0.2);
  border-right: 1px solid rgba(51, 51, 51, 0.2);
  cursor: pointer;
}

.selected {
  background-color: rgba(51, 51, 51, 0.1333333333);
  font-size: medium;
  text-align: center;
  border-left: 1px solid rgba(51, 51, 51, 0.2);
  border-right: 1px solid rgba(51, 51, 51, 0.2);
}

.selected-bar {
  position: fixed;
  margin: 0;
  bottom: 0;
  width: 100%;
  padding: 10px;
  padding-right: 20px;
  z-index: 200000;
  background-color: #f8f8f8;
  box-shadow: 0 -4px 8px 0 rgba(0, 0, 0, 0.2);
}

.items-bar {
  display: flex;
  flex-direction: row;
  overflow-y: hidden;
  overflow-x: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
  padding: 10px;
}

.items-bar::-webkit-scrollbar {
  height: 5px;
}

.items-bar::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

.items-bar::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.8);
}

.selected-item {
  padding: 5px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  height: 110px;
  min-width: 110px;
  max-width: 110px;
  color: #922c88;
  box-shadow: 2px 2px 5px 1px rgba(0, 0, 0, 0.2);
}
.selected-item .iconsminds-close {
  position: absolute;
  cursor: pointer;
  overflow: visible;
  right: -10px;
  top: -10px;
  color: #922c88;
  border-radius: 50%;
  background-color: #f8f8f8;
}
.selected-item strong {
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 5px;
  width: 100%;
  text-align: center;
  max-width: 100px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-size: 0.8em;
}

.watermark-item {
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  min-width: 60px;
  max-width: 60px;
  color: #922c88;
  box-shadow: 2px 2px 5px 1px rgba(0, 0, 0, 0.2);
}
.watermark-item .iconsminds-close {
  position: absolute;
  cursor: pointer;
  overflow: visible;
  right: -12px;
  top: -12px;
  color: #922c88;
  border-radius: 50%;
  background-color: #f8f8f8;
}

.grid-container {
  -moz-column-gap: 20px;
  column-gap: 20px;
  row-gap: 20px;
  margin: 0 auto;
  justify-content: flex-start;
  align-items: center;
}

.grid-item {
  padding: 10px 0;
  font-size: 30px;
  height: 180px;
  width: 180px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
.grid-item p {
  margin: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.grid-item img {
  height: 93px;
}
.grid-item span {
  position: absolute;
  bottom: -10px;
  right: -10px;
  color: white;
  padding: 3px;
  width: 30px;
  height: 30px;
  font-size: medium;
  border-radius: 100%;
  background-color: #922c88;
}
.grid-item .storyicon {
  height: 50px;
  margin: 20px;
}

@media only screen and (max-width: 500px) {
  .grid-container {
    -moz-column-gap: 10px;
    column-gap: 10px;
    row-gap: 10px;
    margin: 0 auto;
    justify-content: center;
    align-items: center;
  }
}
.list-default-icon-style {
  font-size: 50px;
  padding-left: 15px;
  padding-top: 15px;
}

.story-add-icon-style {
  font-size: 35px;
  margin-left: 25px;
}

.card-default-icon-style {
  font-size: 150px;
  display: flex;
  justify-content: center;
  width: 270px;
  background: darkgray;
}

.diableHoverStyle:hover {
  color: #474d66 !important;
}

.embed-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 760px;
  min-height: 515px;
}
@media (max-width: 991px) {
  .embed-container {
    min-width: 100%;
    min-height: 515px;
  }
}
@media (max-width: 767px) {
  .embed-container {
    min-width: 100%;
    min-height: 400px;
  }
}

.reactQuill-border-remove div {
  border: none !important;
}
.reactQuill-border-remove div div {
  padding: 0 !important;
}

.image-embed-container {
  display: flex;
  justify-content: flex-start;
  align-items: left;
  max-width: 760px;
  max-height: 515px;
}
@media (max-width: 991px) {
  .image-embed-container {
    max-width: 100%;
    max-height: 515px;
  }
}
@media (max-width: 767px) {
  .image-embed-container {
    max-width: 100%;
    max-height: 400px;
  }
}

.story-embed-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 615px;
  min-width: 860px;
}
@media (max-width: 991px) {
  .story-embed-container {
    min-height: 515px;
    min-width: 100%;
  }
}
@media (max-width: 767px) {
  .story-embed-container {
    min-height: 400px;
    min-width: 100%;
  }
}

.sSelection {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  margin-left: 1px;
  gap: 1%;
}
@media (max-width: 450px) {
  .sSelection {
    width: 100%;
    padding-left: 0px !important;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.sSelectionLeft {
  display: flex;
  grid-gap: 0% !important;
  height: -moz-fit-content;
  height: fit-content;
}
.sSelectionLeft .langDropdown {
  max-height: 180px;
  z-index: 1;
  overflow-y: auto;
  overflow-x: hidden;
}
.sSelectionRight {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  height: -moz-fit-content;
  height: fit-content;
}
.sSelectionRightSelector {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  height: 100%;
}
.sSelectionRightclose {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 1rem;
}

.caption-file-upload-wrapper {
  position: relative;
  overflow: hidden;
  display: inline-block;
}

.filechoose-btn {
  width: 240px;
  height: 38px;
  overflow: hidden;
  white-space: nowrap;
  padding: 4px 10px;
  font-size: 16px;
  text-align: left;
  margin: 0;
}

.file-dark {
  border: 1px solid #000;
  background-color: #1d1a1d;
}

.file-light {
  border: 1px solid #ccc;
  background-color: white;
}

.caption-file-upload-wrapper input[type='file'] {
  font-size: 100px;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
}

.labelContainer {
  min-width: 200px;
  width: 200px;
  height: auto;
}

.customfield-values {
  min-width: 200px;
  width: -moz-fit-content;
  width: fit-content;
  min-height: 36.9px;
  height: -moz-fit-content;
  height: fit-content;
  font-size: 12px;
}

.AdvanceOptions {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  width: 100%;
  flex-wrap: wrap;
  justify-content: space-between;
}
.AdvanceOptions > div {
  width: 45%;
  margin-top: 20px;
  border-radius: 10px;
  border: 1px solid silver;
  padding: 15px;
}
@media (max-width: 850px) {
  .AdvanceOptions > div {
    width: 100%;
  }
}

.Protection {
  font-size: 12px;
}
.Protection button {
  width: 33%;
  padding: 10px;
}
.Protection .active {
  color: #992288;
  font-weight: 600;
}
.Protection .activeButton {
  border: 1px solid #992288;
  background: rgba(153, 34, 136, 0.2509803922);
  color: #992288;
  font-weight: 700;
}
.Protection .inActiveButton {
  border: 1px solid silver;
  background: transparent;
}

.summary-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: stretch;
  align-items: stretch;
  gap: 32px;
  padding: 32px;
}
.summary-row canvas {
  background-color: white;
}

.summaryCard {
  position: relative;
  display: flex;
  flex: 1 1 auto;
  justify-self: stretch;
}
.summaryCard i {
  font-size: 32px;
  line-height: 47px;
  color: #922c88;
}
.summaryCard .card-text {
  height: 48px;
  line-height: 18px;
}
.summaryCard .card {
  flex: 1 1 auto;
}

.info {
  position: absolute;
  top: 0;
  right: 0;
  padding: 0;
  margin: 0;
  z-index: 1;
}
.info button {
  box-sizing: content-box;
  background: none;
  border: none;
  padding: 8px;
  margin: 0 !important;
  height: 24px;
  width: 24px;
}
.info button:hover,
.info button:active {
  background: none !important;
}
.info button i {
  font-size: 16px;
  line-height: 16px;
  color: #922c88;
}

.connected {
  font-size: 10px;
  color: #922c88;
  border-radius: 10px;
  border: 1px solid #922c88;
  background: white;
  padding: 1px 5px;
}

.customBadge {
  font-size: 8px;
  text-transform: capitalize !important;
  padding: 2px;
  color: #000;
  border: 1px solid #000;
  width: -moz-fit-content;
  width: fit-content;
  border-radius: 5px;
}

.customeBadge-outline {
  display: flex;
  text-align: center;
  font-size: 8px;
  text-transform: capitalize !important;
  padding: 2px;
  color: #922c88;
  border: 1px solid #922c88;
  width: 60px;
  border-radius: 5px;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.workflowPill {
  border-radius: 10px;
  font-size: 10px;
  padding: 4px;
  padding-left: 6px;
  padding-right: 6px;
}

@media screen and (max-width: 860px) {
  .wrap-for-mobile {
    display: flex;
    flex-wrap: wrap;
  }
}
@media screen and (min-width: 860px) {
  .wrap-for-mobile {
    display: flex;
    justify-content: space-around;
    align-items: start;
    margin-bottom: 2rem;
  }
}

.Home {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
  gap: 2%;
}
@media (max-width: 860px) {
  .Home {
    display: flex;
    flex-direction: column;
  }
}
.HomeAbout {
  display: flex;
  flex-direction: column;
  width: 39%;
}
@media (max-width: 860px) {
  .HomeAbout {
    width: 100%;
  }
}
.HomeAbout h3 {
  font-size: 15px;
  text-align: center;
}
.HomeAboutButtons {
  display: flex;
  flex-direction: row;
  margin-top: 20px;
  align-items: center;
  justify-content: flex-end;
}
.HomeAboutButtons button {
  padding: 5px 10px;
  font-size: 14px;
}
@media (max-width: 860px) {
  .HomeAboutButtons {
    justify-content: center;
  }
}
.HomeAbout .Tuts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.HomeAbout .Tuts > div {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-top: 3%;
}
.HomeAbout .Tuts > div video {
  height: 150px;
  border-radius: 7px;
  width: 50%;
  background: rgb(225, 223, 223);
}
.HomeAbout .Tuts > div span {
  font-size: 12px;
}
.HomeUpdate {
  display: flex;
  flex-direction: column;
  width: 59%;
}
@media (min-width: 860px) {
  .HomeUpdate {
    padding-left: 10px;
    border-left: 1px solid silver;
  }
}
@media (max-width: 860px) {
  .HomeUpdate {
    width: 100%;
    margin-top: 20px;
  }
}
.HomeUpdate h3 {
  text-align: center;
  font-size: 17px;
}

.HomeCard {
  display: flex;
  flex-direction: column;
  width: 100%;
  border-radius: 10px;
  margin-top: 10px;
  padding: 10px;
}
.HomeCard div {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 2% 5%;
  font-size: 12px;
}
.HomeCard div .HomeCardOne {
  font-size: 12px;
}
.HomeCard div .HomeCardThree {
  text-transform: capitalize;
  background: silver;
  color: #fff;
  border-radius: 10px;
  padding: 3px 7px;
}
.HomeCard p {
  margin-top: 5px;
  font-size: 12px;
  margin-bottom: 0px;
}

.HomeTabContent {
  display: grid;
  grid-template-columns: auto auto auto auto auto;
  row-gap: 3%;
  -moz-column-gap: 15px;
  column-gap: 15px;
}
@media (max-width: 1400px) {
  .HomeTabContent {
    grid-template-columns: auto auto auto auto;
  }
}
@media (max-width: 1150px) {
  .HomeTabContent {
    grid-template-columns: auto auto auto;
  }
}
@media (max-width: 800px) {
  .HomeTabContent {
    grid-template-columns: auto auto;
  }
}
@media (max-width: 490px) {
  .HomeTabContent {
    padding: 3%;
    grid-template-columns: auto;
  }
}
.HomeTabContent video {
  height: 200px;
  border-radius: 10px;
  background: #e3e3e3;
}

.HomeDot {
  position: absolute;
  top: -12px;
  left: 55%;
  font-size: 10px;
  text-align: center;
  color: #992288;
  border-radius: 50%;
  border: 1px solid #992288;
  padding: 1px;
  padding-left: 7px;
  padding-right: 7px;
}

.content-vtt-editor {
  border: 1px solid silver;
}

.content-vtt-editor:active,
.content-vtt-editor:focus,
.content-vtt-editor:hover {
  border: 1px solid #992288;
  outline: none;
}

.home-mid-cards {
  justify-content: space-between !important;
}
@media (max-width: 1200px) {
  .home-mid-cards {
    justify-content: center !important;
  }
}

.notification-card {
  padding: 1.5rem;
}
@media (max-width: 1000px) {
  .notification-card {
    padding: 1rem;
  }
}
.notification-card .header {
  text-align: center;
  font-size: 20px;
  margin-bottom: 0.5rem;
}
.notification-card .para {
  text-align: center;
  margin-bottom: 1.5rem;
  margin-top: 0.5rem;
}
.notification-card .date-cont {
  height: 15px;
}
.notification-card img,
.notification-card iframe {
  min-height: 315px;
  min-width: 560px;
  max-height: 315px;
  max-width: 560px;
}
@media (max-width: 900px) {
  .notification-card .header {
    text-align: left;
    font-size: 13px;
    font-weight: 600;
  }
  .notification-card .para {
    text-align: left;
    font-size: 11px;
    margin-bottom: 1rem;
    margin-top: 0.5rem;
  }
}

.home-class {
  min-height: 315px;
  min-width: 560px;
  max-height: 315px;
  max-width: 560px;
}
@media (max-width: 560px) {
  .home-class {
    min-width: 100%;
  }
}

.all-cont {
  margin-top: 6rem;
}
@media (max-width: 560px) {
  .all-cont {
    margin-top: 30px;
  }
}

.featrure-card {
  min-width: 250px;
  max-width: 250px;
  height: 150px;
  border-radius: 15;
  font-size: 20;
  margin: 1rem;
  padding: 1.5rem;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}
.featrure-card .icon-cont {
  height: 50%;
}
.featrure-card .text-cont {
  height: 50%;
  width: 100%;
}
.featrure-card .text-cont p {
  text-align: center;
}
@media (max-width: 560px) {
  .featrure-card {
    width: 100%;
    height: 80px;
    margin: 0;
    margin-top: 10px;
    flex-direction: row;
    max-width: 100%;
    justify-content: start;
    padding: 0.5rem;
  }
  .featrure-card .icon-cont {
    width: 20%;
    height: 100%;
  }
  .featrure-card .text-cont {
    width: 80%;
    height: 100%;
    display: flex;
    align-items: center;
  }
  .featrure-card .text-cont p {
    text-align: start;
  }
}

.top-info .left {
  position: absolute;
  top: 15px;
  right: 20px;
}
.top-info .right {
  position: absolute;
  top: 15px;
  left: 0px;
}
@media (max-width: 750px) {
  .top-info {
    display: flex;
    flex-direction: column-reverse;
    width: 100%;
  }
  .top-info .left,
  .top-info .right {
    position: relative;
    width: 100%;
    left: 0;
    right: 0;
    margin-left: auto;
    margin-right: auto;
  }
  .top-info #user-role {
    margin-left: 5%;
    text-align: left;
    margin-top: 10px;
    margin-bottom: 30px;
    font-size: 14px;
  }
}

.show-smaller {
  display: none;
}
@media (max-width: 750px) {
  .show-smaller {
    display: block;
  }
}

@media (max-width: 750px) {
  .hide-after {
    display: none;
  }
}

.acc-directory-cont {
  display: flex;
  flex-direction: row;
  width: 90%;
  border-radius: 70px;
  height: 50px;
  align-items: center;
  padding-right: 10px;
  padding-left: 10px;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 10px;
}
.acc-directory-cont .btn {
  cursor: pointer;
  background: #fdecfb;
  padding: 10px 15px;
  border-radius: 60px;
}
.acc-directory-cont .btn p {
  text-wrap: nowrap;
}
@media (max-width: 560px) {
  .acc-directory-cont {
    width: 100%;
    gap: 2px;
  }
  .acc-directory-cont p {
    font-size: 12px;
  }
  .acc-directory-cont h5 {
    font-size: 12px;
    text-align: left;
  }
}
@media (min-width: 760px) {
  .acc-directory-cont {
    display: none;
  }
}

.home-card {
  min-width: 500px;
}
@media (max-width: 500px) {
  .home-card {
    min-width: 100%;
  }
}

.Invitation {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}
.InvitationCard {
  width: 60%;
  background: #ffffff;
  border: 1px solid #a7a7a7;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  border-radius: 10px;
  z-index: 10;
}
@media (max-width: 1000px) {
  .InvitationCard {
    width: 70%;
  }
}
@media (max-width: 900px) {
  .InvitationCard {
    width: 80%;
  }
}
@media (max-width: 750px) {
  .InvitationCard {
    width: 85%;
  }
}
@media (max-width: 650px) {
  .InvitationCard {
    width: 90%;
  }
}
@media (max-width: 500px) {
  .InvitationCard {
    width: 100%;
  }
}
.InvitationCardHeader {
  padding: 2%;
  width: 100%;
  border-bottom: 1px solid #cac7ca;
}
.InvitationCardBody {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 4%;
}
.InvitationCardBody h1 {
  color: #444444;
  font-weight: 600;
  font-size: 20px;
  line-height: 34px;
  margin-bottom: 60px;
}
@media (max-width: 850px) {
  .InvitationCardBody h1 {
    font-size: 18px;
  }
}
@media (max-width: 650px) {
  .InvitationCardBody h1 {
    font-size: 16px;
  }
}
.InvitationCardBodyButtons {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.InvitationCardBodyButtons button {
  border-radius: 8px;
  font-weight: 700;
  font-size: 15px;
  line-height: 20px;
  padding: 10px 15px;
  outline: none;
  margin-right: 20px;
}
@media (max-width: 650px) {
  .InvitationCardBodyButtons button {
    font-size: 12px;
    line-height: 14px;
    margin-right: 10px;
  }
}
.InvitationCardBodyButtons .newAcc {
  background: #922c88;
  color: #ffffff;
  cursor: pointer;
  border: 1px solid #922c88;
  transition: background 100ms ease, color 2s ease;
}
.InvitationCardBodyButtons .newAcc:hover {
  background: #fff;
  color: #922c88;
}
.InvitationCardBodyButtons .exist {
  background: #ffffff;
  color: #922c88;
  cursor: pointer;
  border: 1px solid #922c88;
  transition: background 100ms ease, color 2s ease;
}
.InvitationCardBodyButtons .exist .bounce1,
.InvitationCardBodyButtons .exist .bounce2,
.InvitationCardBodyButtons .exist .bounce3 {
  background: #922c88 !important;
}
.InvitationCardBodyButtons .exist:hover {
  background: #922c88;
  color: #ffffff;
}
.InvitationCardBodyButtons .exist:hover .bounce1,
.InvitationCardBodyButtons .exist:hover .bounce2,
.InvitationCardBodyButtons .exist:hover .bounce3 {
  background: #fff !important;
}

.MobileTeams {
  display: flex;
  flex-direction: row;
  width: 100%;
  padding: 3.5%;
  border-radius: 10px;
  margin-bottom: 15px;
}
.MobileTeamsLeft {
  width: 95%;
}
@media (max-width: 400px) {
  .MobileTeamsLeft {
    width: 90%;
  }
}
.MobileTeamsLeftRow {
  display: flex;
  flex-direction: row;
  width: 100%;
  margin-bottom: 10px;
}
.MobileTeamsLeftRowText {
  width: 30%;
  font-size: 11px;
}
.MobileTeamsLeftRowValue {
  width: 70%;
  font-size: 13px !important;
}
.MobileTeamsLeftRowValue p {
  font-size: 13px !important;
}
.MobileTeamsRight {
  width: 5%;
}
@media (max-width: 400px) {
  .MobileTeamsRight {
    width: 10%;
  }
}

.tagInputNewLine {
  outline: initial !important;
  box-shadow: initial !important;
}
.tagInputNewLine span {
  display: flex;
  flex-direction: column;
  padding-top: 10px;
  padding-left: 8px;
  padding-bottom: 4px;
}
.tagInputNewLine span .react-tagsinput-tag {
  background: #922c88 !important;
  border-radius: 15px !important;
  padding: 1px 10px !important;
  margin-bottom: 2px !important;
  display: inline-block !important;
  font-size: 12px !important;
  color: white !important;
  width: -moz-fit-content;
  width: fit-content;
  border: initial !important;
}

.Fullwidth {
  width: 100% !important;
  outline: none;
  border: none;
  color: gray;
  font-size: 13px;
  background: white;
}

.copyToClip {
  font-size: 15px;
  color: #922c88;
  background: white;
}

.copyToClip-trans {
  font-size: 15px;
  color: #922c88;
}

.TeamAddButton {
  border-radius: 10px;
  cursor: pointer;
}
.TeamAddButton .cont {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 26px;
  color: #922c88;
}
.TeamAddButton .cont span {
  font-size: 16px;
}

.showcase {
  display: flex;
  flex-direction: column;
}

.inputs {
  width: 50%;
  border: 1px solid silver;
  border-radius: 10px;
}
@media (max-width: 700px) {
  .inputs {
    width: 80%;
  }
}
@media (max-width: 600px) {
  .inputs {
    width: 100%;
  }
}
.inputs input {
  width: 80%;
  border: none;
  background: transparent;
  padding: 5px;
}
.inputs input:active,
.inputs input:focus,
.inputs input:hover {
  border: none;
  outline: none;
}
.inputs button {
  width: 10%;
  background: transparent;
  border: none;
}

.LockedContent h1 {
  font-weight: bolder;
  margin-bottom: 10px;
}
.LockedContent h2 {
  font-size: 20px;
}

.showcaseModal {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.showcaseModal .tab {
  padding: 10px;
  font-size: 20px;
  font-weight: bolder;
}

.span-data {
  color: #922c88;
  font-size: 10px;
  position: relative;
  font-weight: bolder;
}
.span-data::after {
  position: absolute;
  content: attr(data);
  bottom: -15px;
  left: -15px;
  color: #474d66;
  font-weight: bolder;
  width: -moz-max-content;
  width: max-content;
}

.tagglide {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  padding: 5px;
  padding-left: 10px;
  padding-right: 10px;
  border: 1px solid #e6e6e6;
  background: #fff7fe;
  color: #992288;
  border-radius: 10px;
  width: -moz-max-content !important;
  width: max-content !important;
  text-align: center;
  text-wrap: nowrap;
}

.tags {
  gap: 1%;
  row-gap: 10px;
}

.ContentContainer {
  margin-top: 20px;
}
@media (max-width: 700px) {
  .ContentContainer {
    padding: 4%;
  }
}
.ContentContainer h2 {
  padding: 0 0 0 2%;
  color: #000;
  font-weight: bolder;
}
.ContentContainer hr {
  margin-top: 0px;
  width: 100%;
  border-top: 1px solid silver;
}
.ContentContainer .ContentCardParent {
  display: grid;
  grid-template-columns: 16.6% 16.6% 16.6% 16.6% 16.6% 16.6%;
  row-gap: 15px;
}
@media (max-width: 950px) {
  .ContentContainer .ContentCardParent {
    grid-template-columns: 20% 20% 20% 20% 20%;
  }
}
@media (max-width: 850px) {
  .ContentContainer .ContentCardParent {
    grid-template-columns: 25% 25% 25% 25%;
  }
}
@media (max-width: 700px) {
  .ContentContainer .ContentCardParent {
    grid-template-columns: 33.33% 33.33% 33.33%;
  }
}
@media (max-width: 490px) {
  .ContentContainer .ContentCardParent {
    padding: 3%;
    grid-template-columns: 50% 50%;
  }
}
@media (max-width: 440px) {
  .ContentContainer .ContentCardParent {
    grid-template-columns: 100%;
  }
}

.ContentCardParentAnonymous {
  display: grid;
  grid-template-columns: 14.28% 14.28% 14.28% 14.28% 14.28% 14.28% 14.28%;
  row-gap: 15px;
}
@media (max-width: 1400px) {
  .ContentCardParentAnonymous {
    grid-template-columns: 16.6% 16.6% 16.6% 16.6% 16.6% 16.6%;
  }
}
@media (max-width: 1050px) {
  .ContentCardParentAnonymous {
    grid-template-columns: 20% 20% 20% 20% 20%;
  }
}
@media (max-width: 925px) {
  .ContentCardParentAnonymous {
    grid-template-columns: 25% 25% 25% 25%;
  }
}
@media (max-width: 700px) {
  .ContentCardParentAnonymous {
    grid-template-columns: 33.33% 33.33% 33.33%;
  }
}
@media (max-width: 600px) {
  .ContentCardParentAnonymous {
    grid-template-columns: 50% 50%;
  }
}
@media (max-width: 550px) {
  .ContentCardParentAnonymous {
    padding: 3%;
    grid-template-columns: 50% 50%;
  }
}
@media (max-width: 440px) {
  .ContentCardParentAnonymous {
    grid-template-columns: 100%;
  }
}

.ContentCard {
  height: -moz-max-content;
  height: max-content;
  margin: 2%;
  align-items: center;
  width: 160px;
}
@media (max-width: 1150px) {
  .ContentCard {
    width: 120px;
  }
}
@media (max-width: 490px) {
  .ContentCard {
    width: 160px;
  }
}
@media (max-width: 440px) {
  .ContentCard {
    width: 350px;
  }
}
@media (max-width: 400px) {
  .ContentCard {
    width: 300px;
  }
}
@media (max-width: 350px) {
  .ContentCard {
    width: 250px;
  }
}
.ContentCard .imgContainer {
  position: relative;
}
@media (max-width: 400px) {
  .ContentCard .imgContainer {
    justify-content: flex-start !important;
  }
}
.ContentCard .posterimg {
  outline: 5px solid #eab2e5;
  height: 150px;
  width: 150px;
  border-radius: 50%;
  cursor: pointer;
}
.ContentCard .posterimg img {
  height: 125px;
  width: 125px;
  top: 8.5%;
  left: 9%;
  position: absolute;
  border-radius: 50%;
  z-index: 1;
}
@media (max-width: 1150px) {
  .ContentCard .posterimg {
    width: 110px;
    height: 110px;
  }
}
@media (max-width: 490px) {
  .ContentCard .posterimg {
    height: 150px;
    width: 150px;
  }
}
@media (max-width: 440px) {
  .ContentCard .posterimg {
    height: 250px;
    width: 250px;
  }
}
@media (max-width: 350px) {
  .ContentCard .posterimg {
    height: 225px;
    width: 225px;
  }
}
.ContentCard .img-style {
  position: absolute;
  height: 82%;
  width: 82%;
  border-radius: 50%;
}
.ContentCard .overlayer {
  position: absolute;
  min-height: 84%;
  max-height: 84%;
  min-width: 84%;
  max-width: 84%;
  border-radius: 50%;
}
.ContentCard .date {
  width: 100%;
  font-size: 12px;
}
@media (max-width: 1150px) {
  .ContentCard .date {
    font-size: 10px;
  }
}
@media (max-width: 490px) {
  .ContentCard .date {
    font-size: 9px;
  }
}
.ContentCard .title {
  display: block;
  font-size: 18px;
  font-weight: bolder;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
@media (max-width: 1150px) {
  .ContentCard .title {
    font-size: 14px;
    font-weight: bold;
  }
}
@media (max-width: 490px) {
  .ContentCard .title {
    font-size: 18px;
    font-weight: bolder;
  }
}
.ContentCard video {
  width: 100%;
}
.ContentCard .tagcontainer {
  display: block;
  width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  min-height: 18px;
  margin-top: -5px;
}
.ContentCard .tagcontainer .tag {
  font-size: 14px;
  color: #848484;
}
@media (max-width: 1150px) {
  .ContentCard .tagcontainer .tag {
    font-size: 12px;
  }
}
.ContentCard .tagcontainer .tag:not(:last-child)::after {
  content: ' | ';
}
.ContentCard .acc {
  padding: 0 1%;
}
.ContentCard .acc span > img {
  border-radius: 50%;
  border: 1px solid;
}
.ContentCard .acc .name {
  font-size: 15px;
  color: #000;
  display: block;
  width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
@media (max-width: 1150px) {
  .ContentCard .acc .name {
    font-size: 12px;
  }
}

.showcase-time {
  bottom: 0;
  left: 70%;
  position: absolute;
  font-size: 10px;
  color: #848484;
}
@media (max-width: 1150px) {
  .showcase-time {
    font-size: 9px;
  }
}
@media (max-width: 490px) {
  .showcase-time {
    font-size: 10px;
  }
}

.showcase-story-contents {
  bottom: 0;
  left: 70%;
  position: absolute;
  font-size: 10px;
  color: darkgray;
  padding-right: 4px;
  padding-left: 4px;
  padding-top: 2px;
  padding-bottom: 2px;
}
@media (max-width: 1150px) {
  .showcase-story-contents {
    font-size: 9px;
  }
}
@media (max-width: 490px) {
  .showcase-story-contents {
    font-size: 10px;
  }
}

.showcaseButton {
  display: flex;
  flex-direction: row;
}
.showcaseButton button {
  height: 35px;
  padding: 5px;
  width: 150px;
  font-size: 12px;
}
@media (max-width: 1180px) {
  .showcaseButton button {
    height: 35px;
    padding: 2px;
    width: 110px;
  }
}
.showcaseButton .public {
  border-top-left-radius: 50px;
  border-bottom-left-radius: 50px;
}
.showcaseButton .private {
  border-top-right-radius: 50px;
  border-bottom-right-radius: 50px;
}

.catCont .categories {
  width: 90%;
}
.catCont .reloadbtn {
  width: 10%;
}
@media (max-width: 980px) {
  .catCont {
    flex-direction: column !important;
  }
  .catCont .categories {
    width: 100%;
  }
  .catCont .reloadbtn {
    width: 100%;
  }
}

@media (max-width: 940px) {
  .showHeadCont {
    display: flex;
    flex-direction: column-reverse !important;
  }
}
.showHeadCont .btnCont {
  width: 100%;
  flex-wrap: nowrap !important;
  justify-content: end;
}
@media (max-width: 940px) {
  .showHeadCont .btnCont {
    justify-content: center;
  }
}
@media (max-width: 370px) {
  .showHeadCont .btnCont {
    display: flex;
    flex-direction: column;
    row-gap: 5px;
    justify-content: center;
  }
}

.btnCard {
  border-radius: 50px;
  width: 50%;
  height: 45px;
  font-size: 14px;
  overflow: hidden;
  cursor: pointer;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-right: 12px;
  padding-left: 12px;
  margin-right: 10px;
  width: 200px;
}
.btnCardusrName {
  font-size: 10px;
}
.btnCardactive {
  background: rgba(255, 4, 226, 0.1294117647);
}
.btnCard .left {
  width: 10%;
}
.btnCard .right {
  width: 90%;
}
@media (max-width: 940px) {
  .btnCard {
    margin-bottom: 10px;
  }
  .btnCardusrName {
    font-size: 8px;
  }
}
@media (max-width: 600px) {
  .btnCard {
    font-size: 12px;
  }
}
@media (max-width: 300px) {
  .btnCard {
    font-size: 10px;
  }
}

.showcase-headeing {
  font-size: 25px;
  font-weight: bolder;
  text-align: center;
}
.showcase-headeing img {
  height: 25px;
  width: 25px;
}
@media (max-width: 900px) {
  .showcase-headeing {
    font-size: 20px;
  }
  .showcase-headeing img {
    height: 20px;
    width: 20px;
  }
}
@media (max-width: 700px) {
  .showcase-headeing {
    font-size: 16px;
  }
  .showcase-headeing img {
    height: 16px;
    width: 16px;
  }
}
@media (max-width: 500px) {
  .showcase-headeing {
    font-size: 14px;
  }
  .showcase-headeing img {
    height: 14px;
    width: 14px;
  }
}

@media (max-width: 850px) {
  .hide-owner-top-input {
    display: none !important;
  }
}

.input-cont-hide {
  display: flex !important;
}
@media (max-width: 940px) {
  .input-cont-hide {
    display: none !important;
  }
}

.owner-button-cont {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 5px;
  margin-bottom: 10px;
}
@media (max-width: 750px) {
  .owner-button-cont {
    flex-direction: column-reverse;
    align-items: end;
    margin-bottom: 0px;
  }
  .owner-button-cont .card-and-theme {
    margin-top: 5px;
  }
}
@media (max-width: 480px) {
  .owner-button-cont {
    margin-top: 50px;
  }
}

.subscribe-share {
  display: flex;
  flex-direction: row;
  gap: 2%;
  align-items: center;
  justify-content: end;
}
@media (max-width: 750px) {
  .subscribe-share {
    display: none;
  }
}

.bottom-subscription {
  display: none;
}
@media (max-width: 750px) {
  .bottom-subscription {
    display: flex;
  }
}

.hide-bottom-search {
  display: none;
}
@media (max-width: 850px) {
  .hide-bottom-search {
    display: flex;
  }
}

.home-search {
  width: 400px;
}
@media (max-width: 400px) {
  .home-search {
    width: 100%;
  }
}

.owner-details {
  display: flex;
  flex-direction: column;
  margin-top: 100px;
}
.owner-details h1 {
  font-weight: 600;
  font-size: 30px;
}
.owner-details h5 {
  color: #4e5170;
  font-size: 20px;
  font-weight: 600;
}
.owner-details .owner-tag {
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 400;
  margin-right: 5px;
}
@media (max-width: 750px) {
  .owner-details h1 {
    font-size: 25px;
  }
  .owner-details h5 {
    font-size: 16px;
  }
}
@media (max-width: 480px) {
  .owner-details h1 {
    margin-top: 30px;
    font-size: 22px;
  }
}

.user-avatar-cont {
  height: 140px;
  width: 140px;
  outline: 5px solid #fff;
  border-radius: 70px;
  background: #d9d9d9;
  overflow: hidden;
  align-self: hidden;
}
@media (max-width: 1000px) {
  .user-avatar-cont {
    height: 120px;
    width: 120px;
    border-radius: 60px;
  }
}

.color-picker-btn {
  height: 40px;
  width: 40px;
  padding: 3px;
  border-radius: 50px;
}
@media (max-width: 850px) {
  .color-picker-btn {
    height: 35px;
    width: 35px;
    border-radius: 17px;
  }
  .color-picker-btn svg {
    height: 20px;
    width: 20px;
  }
}

.hover-effect:hover {
  color: #922c88;
  font-weight: bold;
}

.bellow-110px {
  display: none;
}

@media (max-width: 1000px) {
  .bellow-110px-none {
    display: none;
  }
  .card-below-110-px {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
  .bellow-110px {
    display: block;
  }
}
.pointer {
  cursor: pointer;
}

.RLThumbnail {
  width: 15%;
}
.RLTitle {
  width: 15%;
}
.RLWorkflow {
  width: 15%;
}
.RLRequestedTime {
  width: 20%;
}
.RLType {
  width: 10%;
}
.RLRequestorsName {
  width: 10%;
}
.RLReview {
  width: 15%;
}

.SLIcon {
  width: 10%;
}
@media (max-width: 1150px) {
  .SLIcon {
    width: 8%;
  }
  .SLIcon .responsive-thumbnail {
    width: auto;
  }
}
.SLTitle {
  width: 15%;
}
.SLFlag {
  width: 4%;
}
.SLCC {
  width: 10%;
}
.SLDate {
  width: 10%;
}
.SLShowcased {
  width: 12%;
}
.SLWorkflow {
  width: 16%;
}
.SLShared {
  width: 10%;
}
.SLStatus {
  width: 10%;
}
.SLMore {
  width: 3%;
}

.PLThumbnail {
  width: 20%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.PLTitle {
  display: flex;
  width: 20%;
  align-items: center;
  justify-content: center;
}
.PLCD {
  width: 20%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.PLConnected {
  display: flex;
  width: 20%;
  align-items: center;
  justify-content: center;
}
.PLStatus {
  width: 15%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.PLMore {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 5%;
}

.dataloading-overlay-showcase {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.SelectedContent {
  position: fixed;
  top: 0;
  z-index: 9999;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #f8f8f8;
  overflow-y: scroll;
}
.SelectedContentCard {
  border-radius: 12px;
  height: auto;
  max-height: auto;
  width: 96%;
  margin-bottom: 2%;
  margin-top: 2%;
}
.SelectedContentCard .SSbody {
  padding: 20px 30px;
  display: flex;
  height: auto;
  flex-direction: column;
}

.Tag-Expanded {
  background: #ffebfd;
  color: #992288;
}

.showcaseCircleButton {
  height: 40px;
  width: 40px;
  padding: 3px;
  border-radius: 50px;
  border: 1px solid #992288;
  color: #992288;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
.showcaseCircleButton:hover {
  background: #992288;
}
.showcaseCircleButton:hover svg > path {
  fill: #fff !important;
  color: #fff !important;
}

@media (max-width: 610px) {
  .header-top-btn {
    display: none !important;
  }
}

.scm-footer {
  display: flex;
  flex-direction: row;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  height: -moz-max-content;
  height: max-content;
  margin-bottom: 10px;
}
.scm-footer .hide-right-top-views {
  display: flex;
}
@media (max-width: 610px) {
  .scm-footer .hide-right-top-views {
    display: none;
  }
}
.scm-footer .hide-left-top-views {
  display: none;
}
@media (max-width: 610px) {
  .scm-footer .hide-left-top-views {
    display: flex;
  }
}
.scm-footer .show-share-in-footer {
  display: none;
}
@media (max-width: 610px) {
  .scm-footer .show-share-in-footer {
    display: flex;
  }
}

.show-bottom-cta {
  display: none;
}
@media (max-width: 610px) {
  .show-bottom-cta {
    display: block;
  }
}

.total-views {
  display: flex;
  flex-direction: row;
  border-radius: 4px;
  margin-top: -3px;
  width: -moz-fit-content;
  width: fit-content;
  height: -moz-fit-content;
  height: fit-content;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.flagCard {
  border: 1px solid #d8d8d8;
  border-radius: 10px;
  padding: 10px;
  margin-top: 10px;
  margin-bottom: 10px;
  justify-content: space-between;
}
.flagCardName {
  font-size: 16px;
}
.flagCardDate {
  color: #686868;
  font-size: 12px;
}
.flagCardContent {
  font-size: 14px;
  font-weight: 600;
}
.flagCardSubname {
  color: #686868;
  font-size: 15px;
}

.subscriptionDetailsCards {
  display: flex;
  flex-direction: row;
  width: 100%;
  padding: 20px;
  justify-content: space-between;
}
@media (max-width: 1100px) {
  .subscriptionDetailsCards {
    flex-direction: column;
    row-gap: 20px;
  }
}
.subscriptionDetailsCards .AccCard {
  width: 49%;
}
@media (max-width: 1100px) {
  .subscriptionDetailsCards .AccCard {
    width: 100%;
  }
}

.billingBorder {
  border: 1px solid rgba(228, 228, 228, 0.3490196078);
}

.CircularProgressbar-text {
  fill: #992288 !important;
}

.headerBorder {
  border-bottom: 1px solid rgba(228, 228, 228, 0.3490196078);
}

.subHeaderRow {
  width: 100% !important;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.custom-txtarea {
  border: 0.15px solid #ffe7fd;
  box-sizing: border-box;
  padding-right: 40px;
  padding-top: 10px;
  background: #ffe7fd;
}

.EventRow {
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100%;
  gap: 10px;
}
@media (max-width: 900px) {
  .EventRow {
    flex-direction: column;
  }
}
.EventRowdetails {
  width: 50%;
}
@media (max-width: 900px) {
  .EventRowdetails {
    width: 100%;
  }
}
.EventRowduration {
  width: 50%;
}
@media (max-width: 900px) {
  .EventRowduration {
    width: 100%;
  }
}

.aiInsights {
  display: flex;
  flex-direction: row;
  width: 100%;
  gap: 10px;
}
.aiInsights .tabs {
  width: 20%;
  display: flex;
  flex-direction: column;
}
.aiInsights .content {
  width: 80%;
  display: flex;
  flex-direction: column;
}

.clipCard {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  border: 1px solid silver;
}
.clipCard .preview {
  width: 15%;
}
.clipCard .mid {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 55%;
}
@media (max-width: 1050px) {
  .clipCard .mid {
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    row-gap: 5px;
  }
}
.clipCard .buttons {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 25%;
}
@media (max-width: 950px) {
  .clipCard .preview {
    width: 10%;
  }
  .clipCard .buttons {
    width: 20%;
  }
}
@media (max-width: 610px) {
  .clipCard {
    display: flex;
    flex-direction: column;
    width: 90%;
  }
  .clipCard .preview {
    width: 100%;
    height: 150px;
    text-align: center;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }
  .clipCard .buttons {
    width: 100%;
    justify-content: space-between !important;
  }
}

.warning-info {
  background: rgba(255, 252, 223, 0.4941176471);
  border-radius: 10px;
  padding: 10px 5px;
  color: #000;
  border: 1px solid #fffcdf;
  margin: 1rem 0px;
}
.warning-info p {
  margin-bottom: 0px;
}

.dubCard {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  border: 1px solid silver;
  margin-bottom: 1.5rem;
}
.dubCard .preview {
  width: 15%;
}
.dubCard .dubcard-mid {
  display: flex;
  width: 55%;
  justify-content: space-between;
  flex-direction: row;
}
.dubCard .buttons {
  width: 25%;
}
@media (max-width: 1150px) {
  .dubCard .dubcard-mid {
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
  }
}

.aspectRatioBox {
  height: 100px;
  width: 100px;
  border: 1px solid #cbcbcb;
  border-radius: 5px;
}
.aspectRatioBox span {
  background: #cbcbcb;
}
.aspectRatioBox p {
  font-size: 12px;
}
.aspectRatioBox:hover,
.aspectRatioBox-active {
  border: 1px solid #922c88 !important;
  background: #fff2fe;
}
.aspectRatioBox:hover span,
.aspectRatioBox-active span {
  background: #922c88;
}
.aspectRatioBox:hover p,
.aspectRatioBox-active p {
  color: #992288;
  font-size: 12px;
}

.RepurposeGroup {
  display: flex;
  flex-direction: column;
  border: 1px solid silver;
  padding: 1.5rem;
  border-radius: 10px;
  position: relative;
}
.RepurposeGroup .topCol {
  display: flex;
  flex-direction: row;
  width: 100%;
  align-items: center;
  justify-content: space-between;
}
.RepurposeGroup .topCol .topLeft {
  width: 50%;
}
.RepurposeGroup .topCol .topRight {
  width: 50%;
  text-align: right;
}
@media (max-width: 1000px) {
  .RepurposeGroup .topCol {
    flex-direction: column;
  }
  .RepurposeGroup .topCol .topLeft {
    width: 100%;
  }
  .RepurposeGroup .topCol .topRight {
    text-align: left;
    margin-top: 5px;
    width: 100%;
  }
}

.repurposeCard {
  padding-top: 2rem;
  padding-bottom: 2rem;
  padding: 1rem;
  border: 1px solid silver;
  display: flex;
  flex-direction: row;
  border-radius: 10px;
  align-items: center;
  font-size: 14px;
}
.repurposeCard p {
  font-size: 14px;
}
@media (max-width: 1000px) {
  .repurposeCard {
    font-size: 12px;
  }
  .repurposeCard p {
    font-size: 12px;
  }
}

.repurposeBtn {
  padding: 0px;
}

.chapterCard {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.chapterCard input:focus,
.chapterCard input:active {
  outline: none;
}
.chapterCard .mid-cont {
  display: flex;
  flex-direction: row;
}
.chapterCard .mid-cont .top {
  width: 30%;
}
.chapterCard .mid-cont .bottom {
  width: 70%;
}
@media (max-width: 700px) {
  .chapterCard .mid-cont {
    flex-direction: column;
  }
  .chapterCard .mid-cont .top {
    width: 100%;
  }
  .chapterCard .mid-cont .bottom {
    width: 100%;
  }
}

.narrative-card {
  width: 100%;
  border-radius: 5px;
  border: 1px solid rgba(192, 192, 192, 0.267);
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 2rem;
  height: 80px;
  cursor: pointer;
  gap: 1rem;
}

.narrative-card-active {
  background: rgba(254, 226, 251, 0.4862745098);
}

/** your custom css code **/
/* 
html {
    background-color: $theme-color-1 !important;
}
*/
html {
  scrollbar-color: #fff #bababa;
}

::-webkit-scrollbar {
  background-color: #fff;
}

::-webkit-scrollbar-thumb {
  background: #bababa;
}

input[type='datetime-local']::-webkit-calendar-picker-indicator {
  filter: brightness(0) saturate(100%) invert(20%) sepia(66%) saturate(2343%)
    hue-rotate(283deg) brightness(93%) contrast(88%);
}

.Activities-sticky {
  background-color: white;
}

svg > g > rect {
  fill: white !important;
}

.SwitchButton {
  color: #474d66;
  outline: none;
  border: none;
  padding: 10px 20px;
  border-radius: 25px;
  box-shadow: -1px 1px 30px 0px rgba(153, 34, 135, 0.2196078431);
  -webkit-box-shadow: -1px 1px 30px 0px rgba(153, 34, 135, 0.2196078431);
  -moz-box-shadow: -1px 1px 30px 0px rgba(153, 34, 135, 0.2196078431);
}

.AccDirectoryButton {
  border-radius: 40px;
  font-size: 14px;
  background: #fdecfb;
  padding: 10px 15px;
  font-weight: bold;
}
@media (max-width: 1300px) {
  .AccDirectoryButton {
    font-size: 12px;
    font-weight: bold;
  }
}
@media (max-width: 1000px) {
  .AccDirectoryButton {
    font-size: 10px;
    font-weight: bold;
  }
} /*# sourceMappingURL=gogo.light.purplemonster.css.map */
