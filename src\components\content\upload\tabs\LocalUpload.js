/* eslint-disable no-unused-vars */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable no-nested-ternary */
import { Button } from 'reactstrap';
import IntlMessages from 'helpers/IntlMessages';
import Switch from 'rc-switch';
import React from 'react';
import AdvancedOptions from '../components/AdvancedOptions';

function LocalUpload({
  condition,
  previewLink,
  isImageModal,
  isWatermarkModal,
  isStreamingModal,
  DRMStatus,
  setDRMStatus,
  handleChooseFile,
  remoteFileUrl,
  fileRef,
  acceptedMediaType,
  handleLocalFileChange,
  title,
  MediaType,
  drmEnabled,
  setAESStatus,
  AESStatus,
  aesEnabled,
}) {
  if (condition) {
    return (
      <div>
        {!previewLink && !isImageModal && !isWatermarkModal && (
          <>
            <div className="dashboard-upload-button-container mb-5 mt-3">
              {isStreamingModal && (
                <p className="dashboard-center-this-item pr-3 pl-3  text-center text-primary mb-3">
                  <strong>
                    <i className="simple-icon-info font-weight-bold"> </i>
                    {MediaType === 2 ? (
                      <IntlMessages id="up.aud-info" />
                    ) : (
                      MediaType === 1 && <IntlMessages id="up.vid-info" />
                    )}
                  </strong>
                </p>
              )}

              {isStreamingModal && (
                <AdvancedOptions
                  AESStatus={AESStatus}
                  DRMStatus={DRMStatus}
                  setAESStatus={setAESStatus}
                  showProtectionCard={isStreamingModal}
                  setDRMStatus={setDRMStatus}
                  aesEnabled={aesEnabled}
                  drmEnabled={drmEnabled}
                />
              )}

              <Button
                type="button"
                color="primary"
                className="dashboard-upload-button dashboard-center-this-item mb-2 mt-3 btn btn-primary"
                onClick={handleChooseFile}
                disabled={!!remoteFileUrl}
              >
                <IntlMessages id="up.select-file" />
              </Button>

              <input
                ref={fileRef}
                accept={acceptedMediaType}
                type="file"
                multiple={false}
                className="dashboard-file-input"
                id="file-upload-input"
                onChange={handleLocalFileChange}
              />

              <p className="dashboard-center-this-item pr-3 pl-3">
                {title === 'Image' && `You can select a maximum of 15 Images`}
              </p>
            </div>
          </>
        )}
      </div>
    );
  }
  return null;
}

export default LocalUpload;
