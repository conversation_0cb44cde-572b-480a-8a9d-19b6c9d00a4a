/* eslint-disable no-unused-vars */
import { NotificationManager } from 'components/common/react-notifications';
import {
  deleteInteraction,
  updateInteraction
} from 'functions/api/interactionApi';
import React, { useState } from 'react';

const useInteraction = ({
  spaceId,
  contentId,
  id,
  onDelete = () => {},
  onDisable = () => {},
  onEnable = () => {}
}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [isDisabling, setIsDisabling] = useState(false);
  const [isEnabling, setIsEnabling] = useState(false);

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      const { data, isError } = await deleteInteraction({
        spaceId,
        contentId,
        id
      });
      if (isError) {
        NotificationManager.success('Failed to delete the Interaction');
      } else {
        NotificationManager.success('Deleted the Interaction Successfully');
        onDelete(id);
        // setList((prev) => prev.filter((f) => f.id !== id));
      }
      setIsDeleting(false);
    } catch (error) {
      setIsDeleting(false);
      console.error('Something went wrong in handleDelete due to ', error);
    }
  };

  const handleDisable = async () => {
    try {
      setIsDisabling(true);
      const { data, isError } = await updateInteraction({
        spaceId,
        contentId,
        id,
        isEnabled: false
      });
      if (isError) {
        NotificationManager.error('Failed to disable the Interaction');
      } else {
        NotificationManager.success('Disabled the Interaction Successfully');
        onDisable(id);
      }
      setIsDisabling(false);
    } catch (error) {
      setIsDisabling(false);
      console.error('Something went wrong in handleDisable due to ', error);
    }
  };

  const handleEnable = async () => {
    const handleError = () => {
      NotificationManager.error('Failed to disable the Interaction');
      setIsEnabling(false);
    };

    try {
      setIsEnabling(true);
      const { data, isError } = await updateInteraction({
        spaceId,
        contentId,
        id,
        isEnabled: true
      });
      if (isError) {
        handleError();
      } else {
        NotificationManager.success('Enabled the Interaction Successfully');
        onEnable(id);
      }
      setIsEnabling(false);
    } catch (error) {
      console.error('Something went wrong in handleDisable due to ', error);
      handleError();
    }
  };

  return {
    isDeleting,
    handleDelete,
    handleDisable,
    isDisabling,
    handleEnable,
    isEnabling
  };
};

export default useInteraction;
