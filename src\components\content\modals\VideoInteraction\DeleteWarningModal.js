import React from 'react';
import { Button, Modal, ModalBody } from 'reactstrap';

const DeleteWarningModal = ({ isOpen, setIsOpen }) => {
  const handleClose = () => {
    setIsOpen(false);
  };
  return (
    <Modal show={isOpen} toggle={handleClose} zIndex={1100}>
      <ModalBody
        className="d-flex flex-column pt-0 m-3 p-3"
        style={{
          border: '1px solid #992288',
          borderRadius: '10px',
          zIndex: 100
        }}
      >
        <AlertIcon />
        <h1>Analytics Will Be Lost</h1>
        <p>This interaction has associated analytics data.</p>

        <div
          style={{
            background: '#FFF4F4',
            textAlign: 'center'
          }}
        >
          <DeleteIcon />
          <p>
            If you delete this Interaction, all related analytics will be
            permanently removed.
          </p>
        </div>

        <div
          style={{
            background: '#FFFBEB',
            textAlign: 'center'
          }}
        >
          <DisableIcon />
          <p>
            To preserve the data: Consider disabling the interaction instead.
          </p>
        </div>

        <div className="d-flex justify-content-end gap-2">
          <Button variant="outline-primary" onClick={handleClose}>
            Cancel
          </Button>
          <Button variant="danger" onClick={handleClose}>
            Disable
          </Button>
          <Button variant="danger" onClick={handleClose}>
            Delete
          </Button>
        </div>
      </ModalBody>
    </Modal>
  );
};

const AlertIcon = () => {
  return (
    <svg
      width="81"
      height="80"
      viewBox="0 0 81 80"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.58333 70C8.97221 70 8.41666 69.8478 7.91666 69.5433C7.41666 69.2389 7.02777 68.8356 6.74999 68.3333C6.47221 67.8311 6.31999 67.2889 6.29333 66.7067C6.26666 66.1244 6.41888 65.5556 6.74999 65L37.5833 11.6667C37.9167 11.1111 38.3478 10.6944 38.8767 10.4167C39.4055 10.1389 39.9467 10 40.5 10C41.0533 10 41.5955 10.1389 42.1267 10.4167C42.6578 10.6944 43.0878 11.1111 43.4167 11.6667L74.25 65C74.5833 65.5556 74.7367 66.1256 74.71 66.71C74.6833 67.2944 74.53 67.8356 74.25 68.3333C73.97 68.8311 73.5811 69.2344 73.0833 69.5433C72.5855 69.8522 72.03 70.0044 71.4167 70H9.58333ZM15.3333 63.3333H65.6667L40.5 20L15.3333 63.3333ZM40.5 60C41.4444 60 42.2367 59.68 42.8767 59.04C43.5167 58.4 43.8355 57.6089 43.8333 56.6667C43.8311 55.7244 43.5111 54.9333 42.8733 54.2933C42.2355 53.6533 41.4444 53.3333 40.5 53.3333C39.5555 53.3333 38.7644 53.6533 38.1267 54.2933C37.4889 54.9333 37.1689 55.7244 37.1667 56.6667C37.1644 57.6089 37.4844 58.4011 38.1267 59.0433C38.7689 59.6856 39.56 60.0044 40.5 60ZM40.5 50C41.4444 50 42.2367 49.68 42.8767 49.04C43.5167 48.4 43.8355 47.6089 43.8333 46.6667V36.6667C43.8333 35.7222 43.5133 34.9311 42.8733 34.2933C42.2333 33.6556 41.4422 33.3356 40.5 33.3333C39.5578 33.3311 38.7667 33.6511 38.1267 34.2933C37.4867 34.9356 37.1667 35.7267 37.1667 36.6667V46.6667C37.1667 47.6111 37.4867 48.4033 38.1267 49.0433C38.7667 49.6833 39.5578 50.0022 40.5 50Z"
        fill="#FF9500"
      />
    </svg>
  );
};

const DeleteIcon = () => {
  return (
    <svg
      width="37"
      height="39"
      viewBox="0 0 37 39"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.8029 27.5C13.3229 27.5 12.9143 27.3404 12.5771 27.0211C12.24 26.7019 12.0714 26.3153 12.0714 25.8614V13.2958H11V12.2812H15.2857V11.5H21.7143V12.2812H26V13.2958H24.9286V25.8614C24.9286 26.3282 24.7636 26.7181 24.4336 27.0313C24.1036 27.3444 23.6914 27.5007 23.1971 27.5H13.8029ZM23.8571 13.2958H13.1429V25.8614C13.1429 26.0434 13.2046 26.1929 13.3282 26.3099C13.4518 26.4269 13.61 26.4854 13.8029 26.4854H23.1982C23.3625 26.4854 23.5136 26.4205 23.6514 26.2906C23.7893 26.1607 23.8579 26.0174 23.8571 25.8604V13.2958ZM16.1514 24.4562H17.2229V15.325H16.1514V24.4562ZM19.7771 24.4562H20.8486V15.325H19.7771V24.4562Z"
        fill="#FF0000"
      />
    </svg>
  );
};

const DisableIcon = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.9375 3.9375L14.0625 14.0625M16.3125 9C16.3125 10.9394 15.5421 12.7994 14.1707 14.1707C12.7994 15.5421 10.9394 16.3125 9 16.3125C7.0606 16.3125 5.20064 15.5421 3.82928 14.1707C2.45792 12.7994 1.6875 10.9394 1.6875 9C1.6875 7.0606 2.45792 5.20064 3.82928 3.82928C5.20064 2.45792 7.0606 1.6875 9 1.6875C10.9394 1.6875 12.7994 2.45792 14.1707 3.82928C15.5421 5.20064 16.3125 7.0606 16.3125 9Z"
        stroke="#F59E0B"
        strokeWidth="2"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default DeleteWarningModal;
