/* eslint-disable react/no-array-index-key */
import React from 'react';
import QuesAnsPreview from './QuesAnsPreview';

const SurveyPreview = ({ polls }) => {
  console.log('SurveyPreview polls', polls);

  return (
    <div className="SurveyPreview">
      <h1>Preview</h1>
      <div className="SPCard">
        <div className="SPCardHeader">
          <h2>Survey</h2>
          <CloseButton />
        </div>
        <div className="SPCardBody">
          {polls.map((m, id) => (
            <QuesAnsPreview key={id} data={m} />
          ))}
        </div>
      </div>
    </div>
  );
};

const CloseButton = () => {
  return (
    <span>
      <svg
        width="23"
        height="23"
        viewBox="0 0 23 23"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M11.4995 1.4375C5.89326 1.4375 1.43701 5.89375 1.43701 11.5C1.43701 17.1062 5.89326 21.5625 11.4995 21.5625C17.1058 21.5625 21.562 17.1062 21.562 11.5C21.562 5.89375 17.1058 1.4375 11.4995 1.4375ZM11.4995 20.125C6.75576 20.125 2.87451 16.2437 2.87451 11.5C2.87451 6.75625 6.75576 2.875 11.4995 2.875C16.2433 2.875 20.1245 6.75625 20.1245 11.5C20.1245 16.2437 16.2433 20.125 11.4995 20.125Z"
          fill="black"
        />
        <path
          d="M15.3808 16.5312L11.4995 12.65L7.61826 16.5312L6.46826 15.3812L10.3495 11.5L6.46826 7.61875L7.61826 6.46875L11.4995 10.35L15.3808 6.46875L16.5308 7.61875L12.6495 11.5L16.5308 15.3812L15.3808 16.5312Z"
          fill="black"
        />
      </svg>
    </span>
  );
};

export default SurveyPreview;
