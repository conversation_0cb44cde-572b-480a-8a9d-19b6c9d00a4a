import { RESPONSE_TYPES } from 'constants/VideoInteraction';
import React from 'react';

const ResponsesIcon = ({ type = RESPONSE_TYPES.TEXT_BOX }) => {
  const sizes = {
    width: 15,
    height: 15
  };

  if (type === RESPONSE_TYPES.TEXT_BOX) {
    return (
      <svg
        {...sizes}
        viewBox="0 0 25 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M5.5 8H19.5M5.5 12H19.5M5.5 16H11.5"
          stroke="#4A5969"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    );
  }

  if (type === RESPONSE_TYPES.MCQ) {
    return (
      <svg
        {...sizes}
        viewBox="0 0 16 23"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M8 14.5C8.79565 14.5 9.55871 14.1839 10.1213 13.6213C10.6839 13.0587 11 12.2956 11 11.5C11 10.7044 10.6839 9.94129 10.1213 9.37868C9.55871 8.81607 8.79565 8.5 8 8.5C7.20435 8.5 6.44129 8.81607 5.87868 9.37868C5.31607 9.94129 5 10.7044 5 11.5C5 12.2956 5.31607 13.0587 5.87868 13.6213C6.44129 14.1839 7.20435 14.5 8 14.5ZM8 5.5C7.21207 5.5 6.43185 5.65519 5.7039 5.95672C4.97595 6.25825 4.31451 6.70021 3.75736 7.25736C3.20021 7.81451 2.75825 8.47595 2.45672 9.2039C2.15519 9.93185 2 10.7121 2 11.5C2 12.2879 2.15519 13.0681 2.45672 13.7961C2.75825 14.5241 3.20021 15.1855 3.75736 15.7426C4.31451 16.2998 4.97595 16.7417 5.7039 17.0433C6.43185 17.3448 7.21207 17.5 8 17.5C9.5913 17.5 11.1174 16.8679 12.2426 15.7426C13.3679 14.6174 14 13.0913 14 11.5C14 9.9087 13.3679 8.38258 12.2426 7.25736C11.1174 6.13214 9.5913 5.5 8 5.5ZM3 11.5C3 10.1739 3.52678 8.90215 4.46447 7.96447C5.40215 7.02678 6.67392 6.5 8 6.5C9.32608 6.5 10.5979 7.02678 11.5355 7.96447C12.4732 8.90215 13 10.1739 13 11.5C13 12.8261 12.4732 14.0979 11.5355 15.0355C10.5979 15.9732 9.32608 16.5 8 16.5C6.67392 16.5 5.40215 15.9732 4.46447 15.0355C3.52678 14.0979 3 12.8261 3 11.5Z"
          fill="#3B4148"
        />
      </svg>
    );
  }

  if (type === RESPONSE_TYPES.DATE) {
    return (
      <svg
        {...sizes}
        viewBox="0 0 15 15"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M2.5 4.375V5.625H12.5V4.375C12.5 4.04348 12.3683 3.72554 12.1339 3.49112C11.8995 3.2567 11.5815 3.125 11.25 3.125H3.75C3.41848 3.125 3.10054 3.2567 2.86612 3.49112C2.6317 3.72554 2.5 4.04348 2.5 4.375Z"
          fill="#4A5969"
        />
        <path
          d="M10 3.125H11.25C11.5815 3.125 11.8995 3.2567 12.1339 3.49112C12.3683 3.72554 12.5 4.04348 12.5 4.375V5.625H2.5V4.375C2.5 4.04348 2.6317 3.72554 2.86612 3.49112C3.10054 3.2567 3.41848 3.125 3.75 3.125H5M10 3.125V1.875M10 3.125H5M5 3.125V1.875M2.5 5.9375V11.875C2.5 12.2065 2.6317 12.5245 2.86612 12.7589C3.10054 12.9933 3.41848 13.125 3.75 13.125H11.25C11.5815 13.125 11.8995 12.9933 12.1339 12.7589C12.3683 12.5245 12.5 12.2065 12.5 11.875V5.9375"
          stroke="#4A5969"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    );
  }

  return (
    <svg {...sizes} viewBox="0 0 17 17" fill="none">
      <path
        d="M13.1667 2.5H3.83333C3.47971 2.5 3.14057 2.64048 2.89052 2.89052C2.64048 3.14057 2.5 3.47971 2.5 3.83333V13.1667C2.5 13.5203 2.64048 13.8594 2.89052 14.1095C3.14057 14.3595 3.47971 14.5 3.83333 14.5H13.1667C13.5203 14.5 13.8594 14.3595 14.1095 14.1095C14.3595 13.8594 14.5 13.5203 14.5 13.1667V3.83333C14.5 3.47971 14.3595 3.14057 14.1095 2.89052C13.8594 2.64048 13.5203 2.5 13.1667 2.5ZM13.1667 3.83333V13.1667H3.83333V3.83333H13.1667ZM7.16667 11.8333L4.5 9.16667L5.44 8.22L7.16667 9.94667L11.56 5.55333L12.5 6.5"
        fill="black"
      />
    </svg>
  );
};

export default ResponsesIcon;
