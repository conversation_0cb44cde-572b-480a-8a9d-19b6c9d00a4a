/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-vars */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import { Colxx } from 'components/common/CustomBootstrap';
import { getShowcaseAnalytics } from 'functions/api/showcaseApi';
import IntlMessages from 'helpers/IntlMessages';
import {
  convertNumberToShort,
  formatDate,
  formatTime,
  generateColorArrays,
  getDay
} from 'helpers/Utils';
import React, { useEffect, useState } from 'react';
import { Card, CardBody, Modal, ModalBody } from 'reactstrap';
import Bar<PERSON>hart from 'views/app/spaces/components/bar';
import Chart from 'react-google-charts';
import NextUpdateTime from 'components/nextUpdateTime';
import GeoChartContainer from 'components/charts/GeoChartContainer';
import GraphContainer from 'views/app/spaces/components/GraphContainer';
import GeoChart from 'components/geoChart/GeoChart';
import { Doughnut<PERSON><PERSON> } from 'components/charts';

const ShowcaseAnalyticsModal = ({ isOpen, toggleModal, expandShowcase }) => {
  const [impressions, setImpressions] = useState(null);
  const [hoursData, setHoursData] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [cities, setCities] = useState([]);
  const [countries, setCountries] = useState([]);
  const [type, setType] = useState('date');

  const spaceId = expandShowcase?.spaceId;
  const showcaseId = expandShowcase?.showcaseId;

  useEffect(() => {
    const fetchShowcaseImprData = async () => {
      const { data, isError } = await getShowcaseAnalytics(
        expandShowcase?.showcaseId
      );
      if (!isError && data) {
        setImpressions(data?.date ?? []);
        setHoursData(data?.hours ?? []);
        setCities(data?.cities ?? []);
        setCountries(data?.countries ?? []);
        setLastUpdated(data?.nextUpdateTime);
      }
    };
    if (expandShowcase?.showcaseId) {
      fetchShowcaseImprData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [expandShowcase?.showcaseId]);

  const cardsData = [
    {
      id: 1,
      icon: 'simple-icon-eye',
      name: 'Views',
      data: expandShowcase?.viewCount
        ? convertNumberToShort(expandShowcase?.viewCount)
        : 0
    },
    {
      id: 2,
      icon: 'simple-icon-mouse',
      name: 'Clicks',
      data: expandShowcase?.clickCount ?? 0
    },
    {
      id: 3,
      icon: 'iconsminds-clock',
      name: 'Created',
      data:
        expandShowcase?.creationDateTime &&
        formatDate(new Date(expandShowcase?.creationDateTime))
    },
    {
      id: 4,
      icon: 'iconsminds-sand-watch-2',
      name: 'Expiry',
      data:
        expandShowcase?.endDateTime === 1
          ? 'No Expiry'
          : expandShowcase?.endDateTime &&
            formatDate(new Date(expandShowcase?.endDateTime))
    }
  ];

  return (
    <Modal
      centered
      isOpen={isOpen}
      toggle={toggleModal}
      keyboard
      backdrop="static"
      style={{
        minWidth: '60%',
        maxWidth: '60%',
        minHeight: '60%'
      }}
      wrapClassName="modal-space"
    >
      <ModalBody className="dotted-border m-4">
        <div className="w-full d-flex align-items-center justify-content-between">
          <h2>{expandShowcase?.title}</h2>
          <span onClick={toggleModal}>
            <i className="simple-icon-close close-btn" />
          </span>
        </div>
        <div className="d-flex align-items-center justify-content-between">
          {cardsData.map((c) => (
            <Card key={c.id} style={{ width: '24%', minHeight: '70px' }}>
              <CardBody className="d-flex flex-column align-items-center justify-content-center">
                <div className="d-flex flex-row text-center mb-2">
                  <i
                    className={`${c.icon}  text-primary mb-1`}
                    style={{ fontSize: '15px' }}
                  />
                  &nbsp;
                  <p className="mb-2" style={{ fontSize: '15px' }}>
                    {c.name}
                  </p>
                </div>
                <p style={{ fontSize: '20px' }} className="mb-0">
                  {c.data}
                </p>
              </CardBody>
            </Card>
          ))}
        </div>
        <div className="d-flex flex-row  align-items-center justify-content-between mt-2">
          <Colxx className="card d-flex align-items-center justify-content-center p-2">
            <div className="mt-2">
              <GraphChanger setType={setType} type={type} />
            </div>
            <Colxx
              className="p-2 p-md-4 d-flex align-items-center justify-content-center flex-column"
              style={{ minHeight: '350px' }}
            >
              {type === 'date' ? (
                <ShowcaseByDate
                  latestImpressionCount={
                    +expandShowcase?.viewCount + +expandShowcase?.clickCount
                  }
                  impressions={impressions}
                  lastUpdated={lastUpdated}
                  expiry={expandShowcase?.endDateTime}
                />
              ) : type === 'hour' ? (
                <ShowcaseByHourDay
                  latestViewCount={expandShowcase.viewCount}
                  hoursData={hoursData}
                  lastUpdated={lastUpdated}
                />
              ) : type === 'city' ? (
                <ByCity
                  cities={cities}
                  setCities={setCities}
                  spaceId={spaceId}
                  showcaseId={showcaseId}
                  lastUpdated={lastUpdated}
                />
              ) : (
                <ByCountry
                  countries={countries}
                  setCountries={setCountries}
                  spaceId={spaceId}
                  showcaseId={showcaseId}
                  lastUpdated={lastUpdated}
                />
              )}
            </Colxx>
          </Colxx>
        </div>
      </ModalBody>
    </Modal>
  );
};

const GraphChanger = ({ setType, type }) => {
  return (
    <>
      <div className="d-flex flex-row" style={{ fontSize: '14px' }}>
        <Tab
          isLeft
          text="by-date"
          isActive={type === 'date'}
          onClick={() => setType('date')}
        />
        <Tab
          text="By City"
          isActive={type === 'city'}
          onClick={() => setType('city')}
        />
        <Tab
          text="By Country"
          isActive={type === 'country'}
          onClick={() => setType('country')}
        />
        <Tab
          isRight
          text="by-hour"
          isActive={type === 'hour'}
          onClick={() => setType('hour')}
        />
      </div>
      <div>
        <p className="text-primary  mt-2">
          <i className="simple-icon-info mr-2" />
          {type === 'date'
            ? 'Analytics for last 30 days'
            : type === 'hour'
            ? 'Analytics for last 7 days'
            : type === 'city'
            ? 'Top 5 Cities'
            : 'Top 5 Countries'}
        </p>
      </div>
    </>
  );
};

const Tab = ({ onClick, isActive, text, isLeft = false, isRight = false }) => {
  return (
    <div
      className="text-center c-pointer p-2"
      style={{
        width: '100px',
        borderTopRightRadius: isRight ? '50px' : '',
        borderBottomRightRadius: isRight ? '50px' : '',
        borderTopLeftRadius: isLeft ? '50px' : '',
        borderBottomLeftRadius: isLeft ? '50px' : '',
        border: '1px solid #992288',
        borderLeft: 'none',
        background: isActive && '#99228847'
      }}
      onClick={onClick}
    >
      <IntlMessages id={text} />
    </div>
  );
};

const ShowcaseByDate = ({
  impressions,
  lastUpdated,
  latestImpressionCount
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [labels, setLabels] = useState([]);
  const [values, setValues] = useState([]);
  const largestDate = new Date();

  useEffect(() => {
    const formatData = () => {
      const impressionData = impressions.reduce((acc, impression) => {
        const date = impression.eventdate;
        const index = acc.findIndex((obj) => obj.eventDate === date);
        if (index === -1) {
          acc.push({ eventDate: date, count: impression.eventCount });
        } else {
          acc[index].count += impression.eventCount;
        }
        return acc;
      }, []);

      if (impressionData?.length > 0) {
        const smallestDate = impressionData.reduce(
          (minDateObj, currentDateObj) => {
            const minDate = new Date(minDateObj.eventDate);
            const currentDate = new Date(currentDateObj.eventDate);

            return currentDate < minDate ? currentDateObj : minDateObj;
          },
          impressionData[0]
        ).eventDate;

        let newData = [];
        const start = new Date(smallestDate);
        for (
          let date = new Date(start);
          date <= largestDate;
          date.setDate(date.getDate() + 1)
        ) {
          const findObj = impressionData.find(
            (f) => formatDate(date) === formatDate(f.eventDate)
          );
          if (findObj) {
            newData.push({
              ...findObj,
              eventDate: formatDate(findObj.eventDate)
            });
          } else {
            newData.push({ eventDate: formatDate(date), count: 0 });
          }
        }
        newData = newData.map((f) => {
          if (formatDate(f.eventDate) === formatDate(largestDate)) {
            const imprData = newData.reduce((h, g) => g.count + h, 0);
            return {
              ...f,
              count:
                imprData === latestImpressionCount
                  ? f.count
                  : imprData < latestImpressionCount
                  ? latestImpressionCount - imprData
                  : f.count
            };
          }
          return f;
        });
        const imprLabel = newData.map((f) => f.eventDate);
        const imprData = newData.map((f) => f.count);
        // eslint-disable-next-line no-unused-vars
        setLabels(imprLabel);
        setValues(imprData);
      }
      setIsLoading(false);
    };
    if (impressions) {
      formatData();
      // fetchShowcaseByDate();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [impressions]);

  return (
    <>
      {isLoading ? (
        <div className="dataloading-overlay">
          <div className="dataloading" />
        </div>
      ) : (
        <>
          <NextUpdateTime time={lastUpdated} />
          {values?.length > 0 ? (
            <BarChart data={values} labels={labels} />
          ) : (
            <span style={{ fontSize: '13px' }}>
              <IntlMessages id="sm.no-views-by-date" />
            </span>
          )}
        </>
      )}
    </>
  );
};

const ShowcaseByHourDay = ({ hoursData, lastUpdated, latestViewCount }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [values, setValues] = useState([]);

  const options = {
    title: 'Showcase Insights for last 7 days',
    hAxis: {
      title: 'Day',
      ticks: [
        { v: 0, f: 'Sunday' },
        { v: 1, f: 'Monday' },
        { v: 2, f: 'Tuesday' },
        { v: 3, f: 'Wednesday' },
        { v: 4, f: 'Thursday' },
        { v: 5, f: 'Friday' },
        { v: 6, f: 'Saturday' }
      ]
    },
    vAxis: {
      title: 'Hour (24 Hrs format)',
      viewWindow: {
        min: 0,
        max: 23
      }
    },
    bubble: { textStyle: { fontSize: 11 } }
  };

  useEffect(() => {
    const formatData = () => {
      const eventMap = {
        showcase_view: 3,
        showcase_cta_click: 4
      };
      // Initialize the result with headers
      const result = [
        [
          'ID',
          'Day',
          'Hour (24 Hrs format)',
          'showcase_view',
          'showcase_cta_click'
        ]
      ];
      // Iterate over input_data and populate the result
      hoursData.forEach((item) => {
        const hour = Math.ceil(formatTime(item.eventdatetimeutc, true));
        const dayWords = getDay(item.eventdatetimeutc);
        const dayNumber = getDay(item.eventdatetimeutc, true);

        const check = result.findIndex(
          (f) => f[0] === dayWords && f[1] === dayNumber && f[2] === +hour
        );
        if (check && check !== -1) {
          const currentViews =
            item.eventName === 'showcase_view' ? +item.eventCount : 0;
          const currentClicks =
            item.eventName === 'showcase_cta_click' ? item.eventCount : 0;
          const earlierViews = result[check][3];
          const earlierClicks = result[check][4];
          result[check] = [
            dayWords,
            dayNumber,
            +hour,
            +earlierViews + +currentViews,
            +earlierClicks + +currentClicks
          ];
        } else {
          const row = [dayWords, dayNumber, +hour, 0, 0];

          if (eventMap[item.eventName]) {
            row[eventMap[item.eventName]] = item.eventCount;
          }

          result.push(row);
        }
      });
      const totalCount = hoursData
        .filter((obj) => obj.eventName === 'showcase_view')
        .reduce((total, obj) => total + obj.eventCount, 0);

      if (+latestViewCount > +totalCount) {
        const hour = Math.ceil(formatTime(new Date(), true));
        const dayWords = getDay(new Date());
        const dayNumber = getDay(new Date(), true);
        const diff = +latestViewCount - +totalCount;
        const row = [dayWords, dayNumber, +hour, diff, 0];
        result.push(row);
      }
      setValues(hoursData.length > 0 ? result : []);
      setIsLoading(false);
    };
    if (hoursData) {
      formatData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hoursData]);

  return (
    <>
      <GraphContainer
        loading={isLoading}
        // title="sm.top-country-map"
        lastUpdated={lastUpdated}
        dataExists={values?.length > 1}
        dataEmptyTitle="sm.no-views-by-hour"
        dataComponent={
          <GeoChartContainer className="p-2 p-md-4 d-flex align-items-center justify-content-center">
            {({ dimensions }) => (
              <Chart
                chartType="BubbleChart"
                height={dimensions.height}
                data={values}
                options={options}
              />
            )}
          </GeoChartContainer>
        }
      />
    </>
  );
};

const ByCity = ({ cities, lastUpdated }) => {
  const [isLoading, setLoading] = useState(true);
  const [data, setData] = useState([]);

  useEffect(() => {
    setLoading(true);
    const colors = generateColorArrays(cities.length);
    setData({
      labels: cities?.map((c) => c.city),
      data: cities?.map((c) => c.media_impressions),
      backgroundColor: colors?.lightColors,
      borderColor: colors?.darkColors
    });
    setLoading(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [cities]);

  return (
    <GraphContainer
      loading={isLoading}
      // title="sm.top-cities"
      lastUpdated={lastUpdated}
      dataExists={data?.data?.length > 0}
      dataEmptyTitle="sm.no-top-cities"
      dataComponent={
        <GeoChartContainer className="p-2 p-md-4 d-flex align-items-center justify-content-center">
          {({ dimensions }) => (
            <DoughnutChart
              dimensions={dimensions}
              data={{
                labels: data?.labels,
                datasets: [
                  {
                    backgroundColor: data?.backgroundColor,
                    borderColor: data?.borderColor,
                    data: data?.data,
                    borderWidth: 2
                  }
                ]
              }}
            />
          )}
        </GeoChartContainer>
      }
    />
  );
};

const ByCountry = ({ countries, lastUpdated }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState([]);

  useEffect(() => {
    setIsLoading(true);
    const countriesData =
      countries.length > 0
        ? countries.map((f) => [f.country, f.media_impressions])
        : [];
    setData([['City', 'Impressions'], ...countriesData]);
    setIsLoading(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [countries]);

  return (
    <>
      <GraphContainer
        loading={isLoading}
        // title="sm.top-country-map"
        lastUpdated={lastUpdated}
        dataExists={data?.length > 1}
        dataEmptyTitle="sm.no-top-country-map"
        dataComponent={
          <GeoChartContainer className="p-2 p-md-4 d-flex align-items-center justify-content-center">
            {({ dimensions }) => (
              <GeoChart
                dimensions={dimensions}
                data={data}
                options={{
                  colorAxis: { colors: ['#C198BD', '#922C88'] },
                  backgroundColor: 'transparent'
                }}
              />
            )}
          </GeoChartContainer>
        }
      />
    </>
  );
};

export default ShowcaseAnalyticsModal;
