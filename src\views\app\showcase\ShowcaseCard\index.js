/* eslint-disable no-unused-vars */
/* eslint-disable no-nested-ternary */
import useTimeAgo from 'hooks/useTimeAgo';
import React, { useEffect, useState } from 'react';
import useSubscription from '../useSubscription';
import { getTenantInfo, updateTenantProfile } from 'functions/api';
import { NotificationManager } from 'components/common/react-notifications';
import IntlMessages from 'helpers/IntlMessages';
import ShowcaseThumbnail from 'components/ShowcaseThumbnail';
import { handleShowcaseShare } from 'helpers/Utils';
import ShowcaseCardFooter from './ShowcaseCardFooter';
import ShowcaseModal from 'components/content/modals/ShowcaseModal';

const ShowcaseCard = ({
  data,
  isLoggedIn,
  showcases,
  setShowcases,
  isSubscribedTab,
  myShowcases,
  setSelectedContent,
  previewCache,
  history,
  themeColors,
  index,
  setThemeColors,
  showColorPicker,
  orgThemeColors,
  setOrgThemeColors,
  isDarkMode,
  expandFor,
  roleType,
  selectedTenantId,
  showcaseLikes,
  showcasedBgId,
  setOwnerData,
  localBgImg,
  setLocalBgImg,
  isShowcaseBgLoading,
  setIsShowcaseBgLoading,
  className,
  footerClassName
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isShowcased, setIsShowcased] = useState(false);
  const tenantDetails = JSON.parse(localStorage.getItem('tenantDetails'))?.data;
  const toggle = () => setIsOpen(!isOpen);
  const [open, setOpen] = useState(false);
  const [isHighlighting, setIsHighlighting] = useState(false);
  const [isPinned, setIsPinned] = useState(data.isPinned);
  const [showcasedBg, setShowcasedBg] = useState(false);
  const isStory = data?.entityKind === 2;
  const MediaType = data?.mediaType ?? null;

  useEffect(() => {
    setShowcasedBg(showcasedBgId === data.entityId);
    setIsPinned(data.isPinned);
  }, [data, showcasedBgId]);

  const time = useTimeAgo(data.creationDateTime);
  const { handleSubscription, isSubscribed, subscribing } = useSubscription({
    isAlreadySubscribed: data?.isSubscribed,
    showcases,
    setShowcases,
    isSubscribedTab,
    tenantId: data.tenantId,
    onComplete: () => {
      setOpen(false);
    }
  });

  const handleShowcaseClick = () => {
    history.push(`/app/showcase/detail/${data.id}?direct=false`);
    setSelectedContent({
      spaceId: data.spaceId,
      showcaseId: data.id,
      tenantId: data.tenantId
    });
  };

  const highlightShowcase = async () => {
    try {
      const showcasedId = data.id;
      setIsHighlighting(true);
      const payload = {
        PinShowcaseId: showcasedId,
        id: data.tenantId
      };
      const res = await updateTenantProfile(payload);
      const { isError } = res.data;
      if (!isError) {
        if (showcases.length > 0) {
          const updatedShowcases = showcases
            .map((s) => ({
              ...s,
              isPinned: s.id === showcasedId
            }))
            .sort((a, b) =>
              a.id === showcasedId ? -1 : b.id === showcasedId ? 1 : 0
            );
          setShowcases(updatedShowcases);
          NotificationManager.success(<IntlMessages id="success-highlight" />);
        }
      } else {
        NotificationManager.warning(<IntlMessages id="failed-highlight" />);
      }
      setIsHighlighting(false);
    } catch (error) {
      console.error('Something went wrong in highlightShowcase due to ', error);
      setIsHighlighting(false);
      NotificationManager.warning(<IntlMessages id="failed-highlight" />);
    }
  };

  const makeShowcaseContentBg = async (isRemove = false) => {
    const errorMessage = isRemove
      ? 'video-bg-remove-failed'
      : 'video-bg-applied-failed';
    try {
      const contentId = data.entityId;
      if (!contentId) {
        NotificationManager.error(
          <IntlMessages id="Something went wrong. Please try again." />
        );
        return;
      }
      setIsShowcaseBgLoading(true);
      const payload = {
        contentId: isRemove ? '' : contentId,
        id: data.tenantId
      };
      const res = await updateTenantProfile(payload);
      const { isError } = res.data;
      if (!isError) {
        const { data: ownerInfo, isError: ownerError } = await getTenantInfo(
          data.tenantId,
          true,
          true
        );
        if (ownerInfo) {
          setOwnerData(ownerInfo);
          setLocalBgImg(null);
          NotificationManager.success(
            <IntlMessages
              id={isRemove ? 'video-bg-removed-succ' : 'video-bg-applied-succ'}
            />
          );
        } else {
          NotificationManager.error(
            <IntlMessages id="video-bg-applied-but-no-user-info" />
          );
        }
      } else {
        NotificationManager.error(<IntlMessages id={errorMessage} />);
      }
      setIsShowcaseBgLoading(false);
    } catch (error) {
      NotificationManager.error(<IntlMessages id={errorMessage} />);
      setIsShowcaseBgLoading(false);
    }
  };

  const userIdentifier = data?.username ? `@${data?.username}` : data.tenantId;

  return (
    <>
      <div className={className}>
        <div className="">
          <ShowcaseThumbnail
            MediaType={MediaType}
            data={data}
            history={history}
            isStory={isStory}
            onClick={handleShowcaseClick}
            previewCache={previewCache}
            showcases={showcases}
            setShowcases={setShowcases}
            showcolorChanger={index === 0 && showColorPicker}
            themeColors={themeColors}
            setThemeColors={setThemeColors}
            orgThemeColors={orgThemeColors}
            setOrgThemeColors={setOrgThemeColors}
            isDarkMode={isDarkMode}
            roleType={roleType}
            isLoggedIn={isLoggedIn}
            showPinned={!!expandFor}
            myShowcases={myShowcases}
            showcasedBgId={showcasedBgId}
          />
        </div>
        <ShowcaseCardFooter
          data={data}
          time={time}
          isLoggedIn={isLoggedIn}
          showcaseLikes={showcaseLikes}
          selectedTenantId={selectedTenantId}
          isDarkMode={isDarkMode}
          isSubscribed={isSubscribed}
          subscribing={subscribing}
          myShowcases={myShowcases}
          roleType={roleType}
          setOpen={setOpen}
          open={open}
          isPinned={isPinned}
          highlightShowcase={highlightShowcase}
          isHighlighting={isHighlighting}
          expandFor={expandFor}
          isShowcaseBgLoading={isShowcaseBgLoading}
          showcasedBg={showcasedBg}
          makeShowcaseContentBg={makeShowcaseContentBg}
          toggle={toggle}
          MediaType={MediaType}
          handleSubscription={handleSubscription}
          userIdentifier={userIdentifier}
          handleShowcaseShare={handleShowcaseShare}
          footerClassName={footerClassName}
        />
      </div>
      {isOpen && (
        <ShowcaseModal
          isOpenShowCaseModal={isOpen}
          toggleShowCaseModal={toggle}
          setRefreshAfterShowcaseUpdate={() => {}}
          refreshAfterShowcaseUpdate={false}
          isContent={data.entityKind === 1}
          spaceId={data.spaceId}
          product={{ showcaseId: data.id }}
          tenantDetails={tenantDetails}
          fromShowcases
          setIsShowcased={setIsShowcased}
          isShowcased={isShowcased}
          showcases={showcases}
          setShowcases={setShowcases}
        />
      )}
    </>
  );
};

export default ShowcaseCard;
