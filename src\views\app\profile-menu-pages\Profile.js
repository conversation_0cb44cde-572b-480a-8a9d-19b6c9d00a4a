/* eslint-disable no-nested-ternary */
import React, { useEffect, useState } from 'react';
import { Button, Col, Form, FormGroup, Input, Label } from 'reactstrap';
import Avtar from 'components/common/Avtar';
import {
  validateInput,
  valiateTenantName,
  RoleTypes,
  getCurrentUserDetails,
  getCurrentColor
} from 'helpers/Utils';
import IntlMessages from 'helpers/IntlMessages';
import { fetchUser, handelProfileUpdate } from 'functions/api';
import { NotificationManager } from 'components/common/react-notifications';
import { Countries as ct } from '../../../data/countries';
import { Industries as indst } from '../../../data/Industries';
import VerifedIcon from 'constants/VerifedIcon';
import useWindowDimensions from 'hooks/useWindowDimensions';
import Copy from 'components/copy';

const Profile = ({ roleType }) => {
  const user = JSON.parse(localStorage.getItem('tenantDetails'))?.data;
  const [name, setName] = useState(user?.fullName);
  const [email, setEmail] = useState(user?.email);
  const [dataResidency, setDataResidency] = useState(
    user?.selectedTenant?.regionName
  );
  const [formChanged, setFormChanged] = useState(false);
  const [country, setCountry] = useState(user?.selectedTenant?.countryCode);
  const [industry, setIndustry] = useState(user?.selectedTenant?.industry);
  // console.log({
  //   countryCode: user?.selectedTenant?.countryCode,
  //   industry: user?.selectedTenant?.industry,
  //   stateIndustry: industry,
  //   condition: indst.find((d) => d.value === industry)
  // });
  const [loading, setLoading] = useState(false);
  const [account, setAccount] = useState(user?.selectedTenant?.name);
  const [error, setError] = useState({});
  const [selectedTenanrId] = useState(getCurrentUserDetails().selectedTenantId);
  const [isEditable, setIsEditable] = useState(false);
  const currentColor = getCurrentColor().includes('dark');
  const { width } = useWindowDimensions();

  const handelReset = () => {
    const resettedUser = getCurrentUserDetails();
    setName(resettedUser?.fullName);
    setEmail(resettedUser?.email);
    setAccount(resettedUser.selectedTenantName);
    setCountry(resettedUser.countryCode);
    setDataResidency(resettedUser.regionName);
    setIndustry(resettedUser.industry);
    setFormChanged(false);
    setIsEditable(false);
    // console.log({
    //   type: 'after restting',
    //   countryCode: resettedUser.countryCode,
    //   industry: resettedUser?.industry
    // });
  };

  const handelSubmit = async (e) => {
    e.preventDefault();
    if (
      error.name ||
      error.email ||
      error.country ||
      error.industry ||
      error.account
    ) {
      if (error?.name) {
        NotificationManager.warning(error?.name);
      }
      if (error?.email) {
        NotificationManager.warning(error?.email);
      }
      if (error?.country) {
        NotificationManager.warning(error?.country);
      }
      if (error?.industry) {
        NotificationManager.warning(error?.industry);
      }
      if (error?.account) {
        NotificationManager.warning(error?.account);
      }
      return;
    }
    // this checks if the data region is changed from form only and not from local storage
    // this do not requires loading so it is  fast.
    if (dataResidency !== user?.selectedTenant?.regionName) {
      NotificationManager.error(
        <IntlMessages id="Data Region can not be changed." />,
        <IntlMessages id="req.failed" />,
        3000,
        null,
        null,
        ''
      );
      return;
    }
    setLoading(true);
    const result = await handelProfileUpdate({
      FullName: name,
      CountryCode: country,
      Industry: industry,
      tenantName: account
    });
    if (!result || result.status !== 200) {
      setLoading(false);
      NotificationManager.error(
        <IntlMessages id="team-invitation-error" />,
        <IntlMessages id="req.failed" />,
        3000,
        null,
        null,
        ''
      );
      return;
    }

    await fetchUser();
    setFormChanged(false);
    setIsEditable(false);
    setLoading(false);
    NotificationManager.success(
      <IntlMessages id="user.profile-update" />,
      <IntlMessages id="req.success" />,
      3000,
      null,
      null,
      ''
    );
  };

  useEffect(() => {
    if (name.length >= 3 && error?.name) {
      NotificationManager.warning(
        error?.name,
        <IntlMessages id="name-too-long" />
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [error?.name, name]);

  useEffect(() => {
    if (account.length >= 3 && error?.account) {
      NotificationManager.warning(
        error?.account,
        <IntlMessages id="name-too-long" />
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [account, error?.account]);

  useEffect(() => {
    valiateTenantName(account);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const unchangableStyle = {
    backgroundColor: 'transparent',
    border: 'none',
    cursor: 'default',
    width: ''
  };

  const selectInputStyles = {
    border: !isEditable || roleType !== RoleTypes.OWNER ? 'none' : '',
    backgroundColor: 'transparent',
    cursor: isEditable ? '' : 'default',
    padding: isEditable ? '' : '10px 0 0 16px'
  };

  const avatarSize = width < 400 ? 60 : width < 700 ? 100 : 150;
  const SELECT_COUNTRY_DISABLED = isEditable
    ? roleType !== RoleTypes.OWNER
    : true;
  const SELECT_INDUSTRY_DISABLED = isEditable
    ? roleType > RoleTypes.OWNER
    : true;

  return (
    <div className="ProfileCont">
      <h2>
        <IntlMessages id="profile-title" />
      </h2>
      <div className="avatarNameCont">
        <Avtar size={avatarSize} />
        <div className=" flex-grow-1 d-flex flex-column justify-content-center">
          <span className="accName">{name}</span>
          {isEditable ? (
            <span style={{ marginTop: '2px' }}>&nbsp;</span>
          ) : (
            <>
              <button
                type="button"
                style={{
                  color: '#922c88',
                  borderBlockColor: '#922c88',
                  cursor: 'pointer',
                  background: 'none',
                  border: '1px solid',
                  width: 'fit-content',
                  borderRadius: '6px'
                }}
                onClick={() => setIsEditable(true)}
              >
                <IntlMessages id="profile-edit" />
                &nbsp;{' '}
                <i
                  className="simple-icon-pencil"
                  style={{ fontSize: '12px' }}
                />
              </button>
            </>
          )}
        </div>
      </div>
      <div
        className="d-flex flex-column "
        style={{
          marginTop: 60,
          width: '100%',
          height: '100%'
        }}
      >
        <Form
          className="av-tooltip tooltip-label-bottom"
          onChange={() => {
            setFormChanged(true);
          }}
          noValidate
          onSubmit={handelSubmit}
          onReset={handelReset}
        >
          <div
            className={`d-flex ${
              width < '900' ? 'flex-column' : 'flex-row'
            } flex-wrap justify-content-between`}
          >
            <FormGroup
              className={`form-group has-float-label ${
                currentColor ? ' ' : 'has-float-label2'
              } position-relative`}
              style={{ width: width < '900' ? '100%' : '30%', display: 'flex' }}
            >
              <Label
                for="name"
                className={!isEditable && 'font-weight-bold'}
                style={{ cursor: 'default' }}
              >
                <IntlMessages id="profile-acc-name" />
                <span
                  style={{
                    color: '#922c88',
                    backgroundColor: isEditable ? 'transparent' : '',
                    display: isEditable ? '' : 'none'
                  }}
                >
                  {' '}
                  *
                </span>
              </Label>
              <Input
                id="account"
                name="account"
                placeholder="Account name"
                type="text"
                value={account}
                style={{
                  border:
                    !isEditable || roleType !== RoleTypes.OWNER ? 'none' : '',
                  backgroundColor: 'transparent',
                  cursor: isEditable ? '' : 'default'
                }}
                onChange={(e) => {
                  const val = e.target.value;
                  const tooLarge = val.length > 70;
                  if (tooLarge) {
                    NotificationManager.warning('Too large Account Name');
                  } else {
                    setError({
                      ...error,
                      account: valiateTenantName(e.target.value)
                    });
                    setAccount(e.target.value);
                  }
                }}
                disabled={isEditable ? roleType > RoleTypes.OWNER : true}
              />
              <button
                style={{
                  border: 'none',
                  background: 'none',
                  display: isEditable ? 'none' : 'block'
                }}
                className="clip-button"
                type="button"
              >
                <Copy copyText={account} />
              </button>
              <VerifedIcon
                isVerfied={user?.selectedTenant?.isVerified}
                style={{ right: -30, top: 5 }}
                className="position-absolute"
              />
            </FormGroup>
            <FormGroup
              className={`form-group has-float-label  ${
                currentColor ? ' ' : 'has-float-label2'
              }`}
              style={{ width: width < '900' ? '100%' : '30%', display: 'flex' }}
            >
              <Label
                for="name"
                className={!isEditable && 'font-weight-bold'}
                style={{ cursor: 'default' }}
              >
                <IntlMessages id="profile-usr-name" />
                &nbsp;
                <span
                  style={{
                    color: '#922c88',
                    display: isEditable ? '' : 'none'
                  }}
                >
                  {' '}
                  *
                </span>
              </Label>
              <Input
                className="form-control"
                style={{
                  border: isEditable ? '' : 'none',
                  backgroundColor: 'transparent',
                  cursor: isEditable ? '' : 'default'
                }}
                type="text"
                name="name"
                disabled={!isEditable}
                placeholder="Full Name"
                value={name}
                onChange={(e) => {
                  const val = e.target.value;
                  const tooLarge = val.length > 30;
                  if (tooLarge) {
                    NotificationManager.warning('User Name is too Large');
                  } else {
                    setError({
                      ...error,
                      name: validateInput('name', e.target.value)
                    });
                    setName(e.target.value);
                  }
                }}
              />
            </FormGroup>
            <FormGroup
              className={`form-group has-float-label ${
                currentColor ? ' ' : 'has-float-label2'
              }`}
              style={{ width: width < '900' ? '100%' : '30%', display: 'flex' }}
            >
              <Label
                for="Industry"
                className={!isEditable && 'font-weight-bold'}
                style={{ cursor: 'default' }}
              >
                <IntlMessages id="profile-selected-industry" />
                &nbsp;
                <span
                  style={{
                    color: '#922c88',
                    display: isEditable ? '' : 'none'
                  }}
                >
                  {' '}
                  *
                </span>
              </Label>
              <Input
                id="Industry"
                name="Industry"
                className={SELECT_COUNTRY_DISABLED ? 'hide-drop-icon' : ''}
                type={isEditable ? 'select' : 'text'}
                style={selectInputStyles}
                disabled={SELECT_INDUSTRY_DISABLED}
                onChange={(e) => setIndustry(e.target.value)}
                defaultValue={industry}
                value={industry}
              >
                {isEditable ? (
                  indst?.map((indstr) => (
                    <option
                      key={indstr?.value}
                      value={indstr?.value}
                      selected={industry === indstr?.value}
                    >
                      {indstr?.name}
                    </option>
                  ))
                ) : (
                  <option key={industry} value={industry} selected>
                    {industry}
                  </option>
                )}
              </Input>
            </FormGroup>
            <FormGroup
              className={`form-group has-float-label  ${
                currentColor ? ' ' : 'has-float-label2'
              }`}
              style={{ width: width < '900' ? '100%' : '30%', display: 'flex' }}
            >
              <Label
                className={!isEditable && 'font-weight-bold '}
                style={{ cursor: 'default' }}
              >
                <IntlMessages id="profile-country" />
                <span
                  style={{
                    color: '#922c88',
                    display: isEditable ? '' : 'none'
                  }}
                >
                  {' '}
                  *
                </span>
              </Label>
              <Input
                id="country"
                name="country"
                className={SELECT_COUNTRY_DISABLED ? 'hide-drop-icon' : ''}
                type={isEditable ? 'select' : 'text'}
                onChange={(e) => setCountry(e.target.value.toLowerCase())}
                disabled={SELECT_COUNTRY_DISABLED}
                defaultValue={country.toUpperCase()}
                style={selectInputStyles}
              >
                {isEditable ? (
                  ct?.map((cntry, ind) => (
                    <option
                      // eslint-disable-next-line react/no-array-index-key
                      key={ind}
                      value={cntry?.cca2}
                      selected={
                        country.toUpperCase() === cntry?.cca2?.toUpperCase()
                      }
                    >
                      {cntry?.name?.common}
                    </option>
                  ))
                ) : (
                  <option selected value={country}>
                    {
                      ct?.find(
                        (c) => country.toUpperCase() === c?.cca2?.toUpperCase()
                      )?.name?.common
                    }
                  </option>
                )}
              </Input>
            </FormGroup>
            <FormInput
              name="email"
              placeholder="email"
              style={unchangableStyle}
              currentColor={currentColor}
              disabled
              label="profile-email"
              onChange={(e) => {
                setEmail(e.target.value);
                setFormChanged(true);
              }}
              isEditable={isEditable}
              type="email"
              value={email}
              readOnly
              width={width}
            />
            <FormInput
              name="data_residency"
              placeholder="data_residency"
              style={unchangableStyle}
              currentColor={currentColor}
              disabled
              label="profile-data-region"
              onChange={(e) => setDataResidency(e.target.value)}
              isEditable={isEditable}
              type="text"
              value={dataResidency}
              width={width}
            />
            <FormInput
              currentColor={currentColor}
              disabled={false}
              isEditable={isEditable}
              name="account"
              onChange={() => {}}
              type="text"
              label="profile-acc-id"
              placeholder="Account name"
              style={unchangableStyle}
              value={selectedTenanrId}
              width={width}
            />
          </div>
          {isEditable && (
            <Col
              className="d-flex align-items-center mt-4 mb-4 w-100"
              style={{ justifyContent: 'center' }}
            >
              <Button
                type="reset"
                style={{
                  backgroundColor: 'unset',
                  border: '1px solid #922c88',
                  color: '#922c88'
                }}
                disabled={loading}
              >
                <IntlMessages id="profile-reset" />
              </Button>
              <Button
                type="submit"
                size="md"
                disabled={!formChanged || loading}
                className={`ml-2 btn-shadow 
                ${loading ? 'show-spinner btn-multiple-state' : ''}`}
                color="primary"
              >
                <span className="spinner d-inline-block">
                  <span className="bounce1" />
                  <span className="bounce2" />
                  <span className="bounce3" />
                </span>
                <span className="label">
                  <IntlMessages id="profile-save" />
                </span>
              </Button>
            </Col>
          )}
        </Form>
      </div>
    </div>
  );
};

export default Profile;

const FormInput = ({
  currentColor,
  isEditable,
  name,
  placeholder,
  style,
  value,
  onChange,
  disabled,
  type,
  label,
  readOnly = false,
  width
}) => {
  return (
    <FormGroup
      className={`form-group has-float-label  ${
        currentColor ? ' ' : 'has-float-label2'
      }`}
      style={{ width: width < '900' ? '100%' : '30%', display: 'flex' }}
    >
      <Label
        for={name}
        className={!isEditable && 'font-weight-bold'}
        style={{ cursor: 'default' }}
      >
        <IntlMessages id={label} />
        &nbsp;
      </Label>
      <Input
        id={name}
        name={name}
        placeholder={placeholder}
        type={type}
        style={style}
        value={value}
        onChange={onChange}
        disabled={disabled}
        readOnly={readOnly}
      />

      {name !== 'data_residency' && (
        <button
          style={{
            border: 'none',
            background: 'none',
            display: isEditable ? 'none' : 'block'
          }}
          className="clip-button"
          type="button"
        >
          <Copy copyText={value} />
        </button>
      )}
    </FormGroup>
  );
};
