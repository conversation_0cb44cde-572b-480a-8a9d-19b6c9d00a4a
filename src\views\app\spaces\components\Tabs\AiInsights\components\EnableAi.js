/* eslint-disable no-unused-vars */
import AiButton from 'components/buttons/AiButton';
import RefreshButton from 'components/buttons/RefreshButton';
import InfoMessage from 'components/infoMessage';
import useEnableAi from 'hooks/useEnableAi';
import React, { useEffect } from 'react';

const EnableAi = ({ content, isAiEnabled, setIsAiEnabled, type }) => {
  const {
    handleCheckAiTriggerStatus,
    handleTrigger,
    showCheckTrggerStatusStatusButton,
    isInitiallyChecking,
    isCheckingTriggerStatus,
    isEnablingAi
  } = useEnableAi({
    content,
    isAiEnabled,
    setIsAiEnabled,
    initialRender: true,
    type
  });

  return (
    <div>
      <div className="d-flex flex-row gap-2 align-items-center justify-content-end ">
        {isInitiallyChecking ? (
          <RefreshButton
            handleRefresh={() => {
              handleCheckAiTriggerStatus(true);
            }}
            text="Checking Status"
            isLoading
          />
        ) : (
          <>
            {isEnablingAi && showCheckTrggerStatusStatusButton && (
              <RefreshButton
                handleRefresh={() => {
                  handleCheckAiTriggerStatus(true);
                }}
                text="Check Status"
                isLoading={isCheckingTriggerStatus}
              />
            )}
            <AiButton
              isLoading={isEnablingAi}
              disabled={isEnablingAi}
              text="enable-ai-btn"
              onClick={() => {
                handleTrigger();
              }}
            />
          </>
        )}
      </div>
      {isEnablingAi && <InfoMessage message="enabling-ai-info" />}
    </div>
  );
};

export default EnableAi;
