/* eslint-disable jsx-a11y/media-has-caption */
import React from 'react';
import data from 'data/video-test';
import { Colxx } from './CustomBootstrap';
import VideoPlayer from './VideoPlayer';

const MediaRenderer = ({ src, type, mediaScore }) => {
  // , poster=''
  return (
    <>
      {type?.includes('video') ? (
        <VideoPlayer
          autoplay={false}
          controls
          className="video-js card-img video-content mb-1"
          poster={data.image}
          sources={[
            {
              src: data.video,
              type,
            },
          ]}
        />
      ) : (
        <video
          className="video-js card-img content-audio-preview mb-1 p-2"
          controls
          src={src}
          poster="/assets/img/audio/headpho.jpeg"
        />
      )}
      <Colxx className="mb-5 mt-2 d-flex justify-content-end  h6">{` Quality Score: ${mediaScore}`}</Colxx>
    </>
  );
};
export default MediaRenderer;
