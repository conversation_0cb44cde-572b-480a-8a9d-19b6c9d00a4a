/* eslint-disable no-unused-vars */
import VerifedIcon from 'constants/VerifedIcon';
import React, { useEffect, useState } from 'react';
import { Card } from 'reactstrap';
import { Link } from 'react-router-dom';
import useTenantProfileImg from './useTenantProfileImg';

const SACard = ({ data, handleShowcaseAccountClick = () => {} }) => {
  const [tenantData, setTenantData] = useState(data);
  const { isLoading, isValid, profileImgUrl } = useTenantProfileImg({
    profileImgId: tenantData?.profileImageId,
    profileTenantId: tenantData?.id
  });

  useEffect(() => {
    if (data) {
      setTenantData(data);
    }
  }, [data]);

  const username = tenantData?.username ? `@${tenantData?.username}` : null;
  const tenantId = tenantData?.id;
  const userIdentifier = username ?? tenantId;

  const showImgUrl = !isLoading && isValid && profileImgUrl;

  return (
    <Link
      onClick={() => {
        handleShowcaseAccountClick();
      }}
      to={`/app/showcase/${userIdentifier}`}
    >
      <div className="SACard">
        <Card className="SACardImg">
          {showImgUrl ? (
            <img src={showImgUrl} alt="alt" />
          ) : (
            <span className="dummy-avatar" />
          )}
        </Card>
        <div className="SACName">
          <span className="name">
            <Link to={`/app/showcase/${userIdentifier}`}>
              {tenantData?.name}
            </Link>
          </span>
          <VerifedIcon isVerfied={tenantData?.isVerified} />
        </div>
        <Link
          style={{
            fontSize: 12,
            color: '#848484',
            textAlign: 'center',
            height: 20
          }}
          to={`/app/showcase/${userIdentifier}`}
        >
          {username}
        </Link>
      </div>
    </Link>
  );
};

export default SACard;
