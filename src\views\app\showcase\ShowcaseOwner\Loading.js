import LoadingSkeleton from 'components/LoadingSkeleton';
import React from 'react';

const ShowcaseOwnerLoading = () => {
  return (
    <div>
      <div className="position-relative">
        <LoadingSkeleton style={{ height: '150px', width: '100%' }} />
        <LoadingSkeleton
          className="position-absolute"
          circle
          style={{
            height: '120px',
            width: '120px',
            bottom: '-40px',
            alignSelf: 'center',
            left: '45%'
          }}
        />
      </div>
      <div>
        <LoadingSkeleton
          style={{ height: '40px', width: '30%', marginTop: '50px' }}
          containerClassName="d-flex align-items-center justify-content-center"
        />
        <LoadingSkeleton
          style={{ height: '20px', width: '70%' }}
          containerClassName="d-flex align-items-center justify-content-center mt-2"
        />
        <LoadingSkeleton
          style={{ height: '20px', width: '70%' }}
          containerClassName="d-flex align-items-center justify-content-center"
        />
      </div>
    </div>
  );
};

export default ShowcaseOwnerLoading;
