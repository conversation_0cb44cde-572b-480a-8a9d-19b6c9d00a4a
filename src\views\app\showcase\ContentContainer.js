import React from 'react';
import { Row } from 'reactstrap';
import ShowcaseCard from './ShowcaseCard';

function ContentContainer({
  showcases,
  setShowcases,
  isLoggedIn,
  isSubscribedTab,
  myShowcases,
  setSelectedContent,
  previewCache,
  history,
  themeColors,
  setThemeColors,
  showColorPicker,
  orgThemeColors,
  setOrgThemeColors,
  isDarkMode,
  expandFor,
  roleType,
  selectedTenantId,
  showcaseLikes,
  showcasedBgId,
  setOwnerData,
  localBgImg,
  setLocalBgImg,
  isShowcaseBgLoading,
  setIsShowcaseBgLoading
}) {
  return (
    <div className="ContentContainer mt-4 mb-3 w-full d-flex flex-column position-relative">
      <Row className={`ContentCardParent${isLoggedIn ? '' : 'Anonymous'} mb-3`}>
        {showcases
          .filter((f) => !f.isPinned)
          .map((a, i) => (
            <ShowcaseCard
              index={i}
              key={a.id}
              data={a}
              isLoggedIn={isLoggedIn}
              showcases={showcases}
              setShowcases={setShowcases}
              isSubscribedTab={isSubscribedTab}
              myShowcases={myShowcases}
              setSelectedContent={setSelectedContent}
              previewCache={previewCache}
              history={history}
              themeColors={themeColors}
              showColorPicker={showColorPicker}
              setThemeColors={setThemeColors}
              orgThemeColors={orgThemeColors}
              setOrgThemeColors={setOrgThemeColors}
              isDarkMode={isDarkMode}
              expandFor={expandFor}
              roleType={roleType}
              showcaseLikes={showcaseLikes}
              selectedTenantId={selectedTenantId}
              showcasedBgId={showcasedBgId}
              setOwnerData={setOwnerData}
              localBgImg={localBgImg}
              setLocalBgImg={setLocalBgImg}
              isShowcaseBgLoading={isShowcaseBgLoading}
              setIsShowcaseBgLoading={setIsShowcaseBgLoading}
              className="d-flex flex-column ContentCard"
            />
          ))}
      </Row>
    </div>
  );
}

export default ContentContainer;
