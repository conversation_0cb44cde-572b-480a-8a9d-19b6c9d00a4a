@media screen and (max-width: 860px) {
  .wrap-for-mobile {
    display: flex;
    flex-wrap: wrap;
  }
}
@media screen and (min-width: 860px) {
  .wrap-for-mobile {
    display: flex;
    justify-content: space-around;
    align-items: start;
    margin-bottom: 2rem;
  }
}

.Home {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
  gap: 2%;
}
@media (max-width: 860px) {
  .Home {
    display: flex;
    flex-direction: column;
  }
}
.HomeAbout {
  display: flex;
  flex-direction: column;
  width: 39%;
}
@media (max-width: 860px) {
  .HomeAbout {
    width: 100%;
  }
}
.HomeAbout h3 {
  font-size: 15px;
  text-align: center;
}
.HomeAboutButtons {
  display: flex;
  flex-direction: row;
  margin-top: 20px;
  align-items: center;
  justify-content: flex-end;
}
.HomeAboutButtons button {
  padding: 5px 10px;
  font-size: 14px;
}
@media (max-width: 860px) {
  .HomeAboutButtons {
    justify-content: center;
  }
}
.HomeAbout .Tuts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.HomeAbout .Tuts > div {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-top: 3%;
}
.HomeAbout .Tuts > div video {
  height: 150px;
  border-radius: 7px;
  width: 50%;
  background: rgb(225, 223, 223);
}
.HomeAbout .Tuts > div span {
  font-size: 12px;
}
.HomeUpdate {
  display: flex;
  flex-direction: column;
  width: 59%;
}
@media (min-width: 860px) {
  .HomeUpdate {
    padding-left: 10px;
    border-left: 1px solid silver;
  }
}
@media (max-width: 860px) {
  .HomeUpdate {
    width: 100%;
    margin-top: 20px;
  }
}
.HomeUpdate h3 {
  text-align: center;
  font-size: 17px;
}

.HomeCard {
  display: flex;
  flex-direction: column;
  width: 100%;
  border-radius: 10px;
  margin-top: 10px;
  padding: 10px;
}
.HomeCard div {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 2% 5%;
  font-size: 12px;
}
.HomeCard div .HomeCardOne {
  font-size: 12px;
}
.HomeCard div .HomeCardThree {
  text-transform: capitalize;
  background: silver;
  color: #fff;
  border-radius: 10px;
  padding: 3px 7px;
}
.HomeCard p {
  margin-top: 5px;
  font-size: 12px;
  margin-bottom: 0px;
}

.HomeTabContent {
  display: grid;
  grid-template-columns: auto auto auto auto auto;
  row-gap: 3%;
  -moz-column-gap: 15px;
       column-gap: 15px;
}
@media (max-width: 1400px) {
  .HomeTabContent {
    grid-template-columns: auto auto auto auto;
  }
}
@media (max-width: 1150px) {
  .HomeTabContent {
    grid-template-columns: auto auto auto;
  }
}
@media (max-width: 800px) {
  .HomeTabContent {
    grid-template-columns: auto auto;
  }
}
@media (max-width: 490px) {
  .HomeTabContent {
    padding: 3%;
    grid-template-columns: auto;
  }
}
.HomeTabContent video {
  height: 200px;
  border-radius: 10px;
  background: #e3e3e3;
}

.HomeDot {
  position: absolute;
  top: -12px;
  left: 55%;
  font-size: 10px;
  text-align: center;
  color: #992288;
  border-radius: 50%;
  border: 1px solid #992288;
  padding: 1px;
  padding-left: 7px;
  padding-right: 7px;
}

.content-vtt-editor {
  border: 1px solid silver;
}

.content-vtt-editor:active,
.content-vtt-editor:focus,
.content-vtt-editor:hover {
  border: 1px solid #992288;
  outline: none;
}

.home-mid-cards {
  justify-content: space-between !important;
}
@media (max-width: 1200px) {
  .home-mid-cards {
    justify-content: center !important;
  }
}

.notification-card {
  padding: 1.5rem;
}
@media (max-width: 1000px) {
  .notification-card {
    padding: 1rem;
  }
}
.notification-card .header {
  text-align: center;
  font-size: 20px;
  margin-bottom: 0.5rem;
}
.notification-card .para {
  text-align: center;
  margin-bottom: 1.5rem;
  margin-top: 0.5rem;
}
.notification-card .date-cont {
  height: 15px;
}
.notification-card img,
.notification-card iframe {
  min-height: 315px;
  min-width: 560px;
  max-height: 315px;
  max-width: 560px;
}
@media (max-width: 900px) {
  .notification-card .header {
    text-align: left;
    font-size: 13px;
    font-weight: 600;
  }
  .notification-card .para {
    text-align: left;
    font-size: 11px;
    margin-bottom: 1rem;
    margin-top: 0.5rem;
  }
}

.home-class {
  min-height: 315px;
  min-width: 560px;
  max-height: 315px;
  max-width: 560px;
}
@media (max-width: 560px) {
  .home-class {
    min-width: 100%;
  }
}

.all-cont {
  margin-top: 6rem;
}
@media (max-width: 560px) {
  .all-cont {
    margin-top: 30px;
  }
}

.featrure-card {
  min-width: 250px;
  max-width: 250px;
  height: 150px;
  border-radius: 15;
  font-size: 20;
  margin: 1rem;
  padding: 1.5rem;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}
.featrure-card .icon-cont {
  height: 50%;
}
.featrure-card .text-cont {
  height: 50%;
  width: 100%;
}
.featrure-card .text-cont p {
  text-align: center;
}
@media (max-width: 560px) {
  .featrure-card {
    width: 100%;
    height: 80px;
    margin: 0;
    margin-top: 10px;
    flex-direction: row;
    max-width: 100%;
    justify-content: start;
    padding: 0.5rem;
  }
  .featrure-card .icon-cont {
    width: 20%;
    height: 100%;
  }
  .featrure-card .text-cont {
    width: 80%;
    height: 100%;
    display: flex;
    align-items: center;
  }
  .featrure-card .text-cont p {
    text-align: start;
  }
}

.top-info .left {
  position: absolute;
  top: 15px;
  right: 20px;
}
.top-info .right {
  position: absolute;
  top: 15px;
  left: 0px;
}
@media (max-width: 750px) {
  .top-info {
    display: flex;
    flex-direction: column-reverse;
    width: 100%;
  }
  .top-info .left,
  .top-info .right {
    position: relative;
    width: 100%;
    left: 0;
    right: 0;
    margin-left: auto;
    margin-right: auto;
  }
  .top-info #user-role {
    margin-left: 5%;
    text-align: left;
    margin-top: 10px;
    margin-bottom: 30px;
    font-size: 14px;
  }
}

.show-smaller {
  display: none;
}
@media (max-width: 750px) {
  .show-smaller {
    display: block;
  }
}

@media (max-width: 750px) {
  .hide-after {
    display: none;
  }
}

.acc-directory-cont {
  display: flex;
  flex-direction: row;
  width: 90%;
  border-radius: 70px;
  height: 50px;
  align-items: center;
  padding-right: 10px;
  padding-left: 10px;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 10px;
}
.acc-directory-cont .btn {
  cursor: pointer;
  background: #fdecfb;
  padding: 10px 15px;
  border-radius: 60px;
}
.acc-directory-cont .btn p {
  text-wrap: nowrap;
}
@media (max-width: 560px) {
  .acc-directory-cont {
    width: 100%;
    gap: 2px;
  }
  .acc-directory-cont p {
    font-size: 12px;
  }
  .acc-directory-cont h5 {
    font-size: 12px;
    text-align: left;
  }
}
@media (min-width: 760px) {
  .acc-directory-cont {
    display: none;
  }
}

.home-card {
  min-width: 500px;
}
@media (max-width: 500px) {
  .home-card {
    min-width: 100%;
  }
}/*# sourceMappingURL=home.css.map */