import ShowcaseIcon from 'containers/navs/ShowcaseIcon';
import React from 'react';
import { NavLink } from 'react-router-dom';

const SIZE = 12;

const BrowseShowcase = () => {
  return (
    <div
      style={{
        fontSize: SIZE + 2,
        // bottom: '2%',
        // left: '0%',
        // right: '0%',
        width: '100%',
        color: '#fff'
      }}
      className="d-flex text-align-center flex-row align-items-center justify-content-center flex-wrap"
    >
      Browse community creations on our &nbsp;
      <span>
        <NavLink
          className="text-white text-align-center d-flex flex-row align-items-center"
          to="/app/showcase"
        >
          <ShowcaseIcon color="#fff" noHover h={SIZE + 4} w={SIZE + 4} />
          &nbsp;
          <b>Showcase</b>
        </NavLink>
      </span>
      &nbsp; page
    </div>
  );
};

export default BrowseShowcase;
