/* eslint-disable jsx-a11y/no-static-element-interactions */
import React, { useEffect } from 'react';
import IntlMessages from 'helpers/IntlMessages';
import BillingDetail from './Billing/BillingDetail';
import InvoiceDetails from './Billing/InvoiceDetails';
import SubscriptionDetail from './Billing/SubscriptionDetail';
import useQuery from 'hooks/useQuery';
import { NavLink } from 'react-router-dom';
import { RoleTypes } from 'helpers/Utils';

const Billing = ({ roleType, history }) => {
  const [detailPage, setDetailPage] = React.useState(1);
  const pathname = useQuery().get('tabIndex');

  useEffect(() => {
    if (roleType) {
      if (roleType > RoleTypes.BILLING_ADMIN) {
        history?.push('/error');
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [roleType]);

  useEffect(() => {
    if (
      pathname &&
      (pathname === '1' || pathname === '2' || pathname === '3')
    ) {
      setDetailPage(+pathname);
    }
  }, [pathname]);

  return (
    <div>
      <Tabs detailPage={detailPage} setDetailPage={setDetailPage} />
      {detailPage === 1 && <SubscriptionDetail />}
      {detailPage === 2 && <BillingDetail />}
      {detailPage === 3 && <InvoiceDetails />}
    </div>
  );
};

const tabs = [
  { id: 1, name: 'subs.subs' },
  { id: 2, name: 'subs.billing' },
  { id: 3, name: 'subs.inv' }
];

const Tabs = ({ detailPage, setDetailPage }) => {
  return (
    <div
      className="d-flex justify-content-around bb-2"
      style={{
        fontSize: '1.2rem'
      }}
    >
      {tabs.map((t) => (
        <NavLink
          to={`billing?tabIndex=${t.id}`}
          location={{}}
          key={t.id}
          className="TeamTabHeader"
          style={{
            borderBottom: `${
              detailPage === t.id ? '5px solid #922c88' : 'none'
            }`
          }}
          onClick={() => {
            // eslint-disable-next-line no-unused-expressions
            detailPage !== t.id && setDetailPage(t.id);
          }}
        >
          <IntlMessages id={t.name} />
        </NavLink>
      ))}
    </div>
  );
};

export default Billing;
