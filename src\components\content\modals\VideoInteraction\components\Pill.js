/* eslint-disable no-nested-ternary */
import { INTERACTION_TYPE } from 'constants/VideoInteraction';
import React from 'react';

const Pill = ({ type = INTERACTION_TYPE.IMAGE }) => {
  return (
    <span className="InteractionPill">
      <span
        style={{
          height: 10,
          width: 10,
          borderRadius: '50%',
          background: '#fff'
        }}
      />
      <span style={{ textWrap: 'nowrap' }}>
        {type === INTERACTION_TYPE.RICH_TEXT
          ? 'Rich Text'
          : type === INTERACTION_TYPE.IMAGE
          ? 'Image'
          : type === INTERACTION_TYPE.POLL
          ? 'Poll'
          : 'Survey'}
      </span>
    </span>
  );
};

export default Pill;
