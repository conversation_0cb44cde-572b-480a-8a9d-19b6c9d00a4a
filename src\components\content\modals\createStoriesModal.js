/* eslint-disable no-unused-expressions */
/* eslint-disable no-unused-vars */
import LoadingButton from 'components/buttons/LoadingButton';
import { NotificationManager } from 'components/common/react-notifications';
import IntlMessages from 'helpers/IntlMessages';
import { validateStoryTitle } from 'helpers/Utils';
import useWindowDimensions from 'hooks/useWindowDimensions';
import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { Form, FormGroup, Input, Label, Modal, ModalBody } from 'reactstrap';
import { storyName } from 'redux/stories/action';

const CreateStoriesModal = ({
  modalOpen,
  toggleModal,
  onSubmit = () => {},
  setName,
  name
}) => {
  const [loading, setLoading] = useState(false);
  const { width } = useWindowDimensions();
  // const dispatch = useDispatch();

  const CloseButton = () => {
    toggleModal();
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const errorMsgId = validateStoryTitle(name);
    if (errorMsgId) {
      NotificationManager.warning(<IntlMessages id={errorMsgId} />, '');
      return;
    }
    setLoading(true);
    // dispatch(storyName(name));
    onSubmit();
    CloseButton();
    setLoading(false);
  };
  return (
    <Modal
      isOpen={modalOpen}
      toggle={toggleModal}
      centered
      style={{ minWidth: '40%' }}
    >
      <ModalBody>
        <div className="d-flex justify-content-between  mb-xs-4">
          <p
            className="font-weight-lightbold "
            style={{ fontSize: '17px', color: 'black' }}
          >
            New Story
          </p>
          <span
            role="button"
            tabIndex={0}
            onClick={() => CloseButton()}
            className="w-5"
          >
            <i
              style={{ color: 'black' }}
              className="simple-icon-close close-btn"
            />
          </span>
        </div>
        <Form onSubmit={handleSubmit}>
          <FormGroup className={`form-group has-float-label  `}>
            <Label for="name" style={{ cursor: 'default' }}>
              <IntlMessages id="story.title" />
              &nbsp;
              <span
                style={{
                  color: '#922c88'
                }}
              >
                {' '}
                *
              </span>
            </Label>
            <Input
              className="w-300 my-4"
              id="storyTitle"
              name="Story Title"
              placeholder="Enter Story Title"
              type="text"
              value={name}
              onChange={(e) => {
                const text = e.target.value;
                const values = text.slice(0, 100);
                setName(values);
                if (text.length > 100) {
                  NotificationManager.warning(
                    <IntlMessages id="story.title-less-100" />
                  );
                }
              }}
            />
          </FormGroup>
        </Form>

        <div className="d-flex justify-content-center my-5">
          <LoadingButton
            style={{ minWidth: width < '600' ? '60%' : '30%' }}
            text="Proceed"
            loading={loading}
            disabled={loading}
            onClick={handleSubmit}
          />
        </div>
      </ModalBody>
    </Modal>
  );
};

export default CreateStoriesModal;
