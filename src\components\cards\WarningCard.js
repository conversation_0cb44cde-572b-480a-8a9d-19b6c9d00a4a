import InfoIcon from 'components/icon/InfoIcon';
import IntlMessages from 'helpers/IntlMessages';
import React from 'react';

const WarningCard = ({ text, className = '' }) => {
  return (
    <div className={`centered ${className}`}>
      <div className="warning-info">
        <p className="text-center">
          <InfoIcon iconName="iconsminds-danger" />
          <IntlMessages id={text} />
        </p>
      </div>
    </div>
  );
};

export default WarningCard;
