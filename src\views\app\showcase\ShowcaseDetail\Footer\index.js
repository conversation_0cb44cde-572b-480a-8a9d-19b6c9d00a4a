/* eslint-disable no-nested-ternary */
import VerifedIcon from 'constants/VerifedIcon';
import React from 'react';
import Like from 'components/ShowcaseThumbnail/Like';
import SubscribeButton from './SubscribeButton';
import ReloadButton from './ReloadButton';
import TotalViews from './TotalViews';
import StoryCount from './StoryCount';
import ShareButton from '../ShareButton';
import CTA from './CTA';

const Footer = ({
  subscribing,
  handleSubscription,
  data,
  isLoggedIn,
  isSubscribed,
  history,
  handleRefresh,
  windowWidth,
  handleShareClick,
  showcaseLikes,
  selectedTenantId,
  handleCallToActionClick
}) => {
  const userIdentifier = data?.username ? `@${data?.username}` : data?.tenantId;

  if (windowWidth < 700) {
    return (
      <SmallFooter
        data={data}
        handleCallToActionClick={handleCallToActionClick}
        userIdentifier={userIdentifier}
        history={history}
        subscribing={subscribing}
        handleSubscription={handleSubscription}
        isLoggedIn={isLoggedIn}
        isSubscribed={isSubscribed}
        showcaseLikes={showcaseLikes}
        selectedTenantId={selectedTenantId}
        handleRefresh={handleRefresh}
        windowWidth={windowWidth}
        handleShareClick={handleShareClick}
      />
    );
  }

  return (
    <div className="scm-footer mt-2">
      <div className="left-foot">
        <span
          className="c-pointer tenantName"
          onClick={() => history.push(`/app/showcase/${userIdentifier}`)}
        >
          <strong>{data?.tenantName}</strong>
          <VerifedIcon isVerfied={data?.isVerified} />
        </span>
        <SubscribeButton
          onClick={() => {
            handleSubscription({
              tenantId: data?.tenantId,
              showcaseId: data.id
            });
          }}
          subscribing={subscribing}
          isLoggedIn={isLoggedIn}
          isSubscribed={isSubscribed}
        />
        <div className="d-flex flex-row gap-1 align-items-center">
          <TotalViews
            count={data?.totalImpressionsCount}
            className="hide-left-top-views"
          />
          {data?.entityKind === 2 && (
            <StoryCount
              count={data?.contentsCount}
              className="hide-left-top-views"
            />
          )}
        </div>
      </div>
      <div className="d-flex flex-column align-items-end right-foot">
        <div className="d-flex flex-row align-items-center ">
          {data?.entityKind === 2 && (
            <StoryCount
              count={data?.contentsCount}
              className="hide-right-top-views"
            />
          )}
          <TotalViews
            count={data?.totalImpressionsCount}
            className="hide-right-top-views"
          />
          <ReloadButton windowWidth={windowWidth} onClick={handleRefresh} />
          {/* <Button
            color="primary"
            className="btn ExpButton"
            style={{ textWrap: 'nowrap' }}
            onClick={handleCallToActionClick}
          >
            {data?.purpose?.name ?? 'Call To Action'}
          </Button> */}
          <CTA onClick={handleCallToActionClick} data={data} />
        </div>
        <div className="d-flex flex-row w-full justify-content-between align-items-center">
          <Like
            isPublic={!isLoggedIn}
            showcaseId={data?.id}
            showcaseLikes={showcaseLikes}
            selectedTenantId={selectedTenantId}
            className="d-flex flex-row align-items-center justify-content-center c-pointer like-btn-footer"
          />
          <ShareButton
            className="show-share-in-footer mt-2"
            onClick={handleShareClick}
          />
        </div>
      </div>
    </div>
  );
};

const SmallFooter = ({
  data,
  handleCallToActionClick,
  userIdentifier,
  history,
  subscribing,
  handleSubscription,
  isLoggedIn,
  isSubscribed,
  showcaseLikes,
  selectedTenantId,
  handleRefresh,
  windowWidth,
  handleShareClick
}) => {
  return (
    <div className="smallFooter">
      <div className="topStats">
        <Like
          isPublic={!isLoggedIn}
          showcaseId={data?.id}
          showcaseLikes={showcaseLikes}
          selectedTenantId={selectedTenantId}
          className="d-flex flex-row align-items-center justify-content-center c-pointer like-btn-footer"
        />
        <TotalViews
          count={data?.totalImpressionsCount}
          className="hide-right-top-views"
        />
        <ReloadButton windowWidth={windowWidth} onClick={handleRefresh} />
        <ShareButton
          className="show-share-in-footer "
          onClick={handleShareClick}
        />
      </div>

      <CTA
        onClick={handleCallToActionClick}
        data={data}
        className="btn w-full my-4 call-to-action"
      />

      <div className="nameSubscribe">
        <p
          className="tenantName m-0"
          onClick={() => history.push(`/app/showcase/${userIdentifier}`)}
        >
          {data?.tenantName}
          <VerifedIcon isVerfied={data?.isVerified} />
        </p>

        <SubscribeButton
          onClick={() => {
            handleSubscription({
              tenantId: data?.tenantId,
              showcaseId: data.id
            });
          }}
          subscribing={subscribing}
          isLoggedIn={isLoggedIn}
          isSubscribed={isSubscribed}
        />
      </div>
    </div>
  );
};

export default Footer;
