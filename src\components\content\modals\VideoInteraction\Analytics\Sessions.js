/* eslint-disable react/no-array-index-key */
import { formatDateNTime } from 'helpers/Utils';
import React, { useMemo } from 'react';
import SessionHeader from 'views/app/spaces/components/ChatAnalytics/SessionList/SessionHeader';

const formatCityCountry = (place) => {
  if (!place) {
    return '-';
  }
  return place;
};

const Sessions = ({
  list,
  setSelectedSessionDetails,
  showSelectedSession = false
}) => {
  const sortedList = useMemo(() => {
    return list.sort((a, b) => {
      const dateA = new Date(a?.datetimestamp?.value);
      const dateB = new Date(b?.datetimestamp?.value);
      return dateB - dateA;
    });
  }, [list]);

  return (
    <div className="sessions">
      <SessionHeader isQna />
      <div>
        {sortedList.map((m, i) => (
          <SessionCard
            key={i}
            isQna
            data={m}
            onClick={() => {
              if (showSelectedSession) {
                setSelectedSessionDetails(m);
              }
            }}
          />
        ))}
      </div>
    </div>
  );
};

const SessionCard = ({ data, onClick, isQna }) => {
  const date = data?.datetimestamp?.value;
  return (
    <div className="SessionH session-card p-3 c-pointer" onClick={onClick}>
      <div
        style={{
          width: isQna ? '40%' : '20%'
        }}
        className="SessionH-Id text-ellipsis sc-text"
      >
        {data.sessionid}
      </div>
      <div className="SessionH-City sc-text">
        {formatCityCountry(data?.city)}
      </div>
      <div className="SessionH-Country sc-text">
        {formatCityCountry(data?.country)}
      </div>
      {date && (
        <div className="SessionH-Date sc-text">{formatDateNTime(date)}</div>
      )}
    </div>
  );
};

export default Sessions;
