/* eslint-disable no-unneeded-ternary */
import IntlMessages from 'helpers/IntlMessages';
import React from 'react';
import {
  UncontrolledDropdown,
  DropdownToggle,
  DropdownItem,
  DropdownMenu
} from 'reactstrap';

const SelectCategory = ({
  selectedCategories,
  optionsForCategory,
  setSelectedCategories,
  className = '',
  history
}) => {
  const selectedValue = selectedCategories
    ? selectedCategories
    : 'Select Category';

  const onSelectCategory = (dropdown) => {
    const selected = dropdown.value;
    setSelectedCategories(selected);
    history.push(`/app/showcase/explore/${selected}`);
  };

  const onSelectEmptyCategory = () => {
    setSelectedCategories('');
    history.push('/app/showcase');
  };

  return (
    <UncontrolledDropdown className={`category-showcase ${className}`}>
      <DropdownToggle
        caret
        color="primary"
        outline
        style={{ textAlign: 'center', width: 150, fontSize: '0.8rem' }}
      >
        <IntlMessages id={selectedValue} />
      </DropdownToggle>
      <DropdownMenu right className="w-full showcase-dropdown">
        <DropdownItem onClick={onSelectEmptyCategory}>
          <IntlMessages id="No Category" />
        </DropdownItem>
        {optionsForCategory?.length > 0 &&
          optionsForCategory?.map((dropdown) => (
            <DropdownItem
              key={dropdown.value}
              onClick={() => {
                onSelectCategory(dropdown);
              }}
            >
              {dropdown.label}
            </DropdownItem>
          ))}
      </DropdownMenu>
    </UncontrolledDropdown>
  );
};

export default SelectCategory;
