/* eslint-disable jsx-a11y/no-static-element-interactions */
import React from 'react';
import { Card, FormGroup, Label } from 'reactstrap';
import ReactQuill from 'react-quill';
import { RoleTypes } from 'helpers/Utils';
import Loader from 'react-loader-spinner';
import { NavLink } from 'react-router-dom';
import IntlMessages from 'helpers/IntlMessages';

function Description({
  disabledDesc,
  contributerOrAbove,
  tempDesc = '',
  setTempDesc,
  roleType,
  clickToEditDesc,
  updatingDesc,
  limit = 1000,
  title = 'space.description',
  mandatory = true,
  showLimit = true,
  className = ''
}) {
  const exceedLimit = tempDesc?.replace(/<[^>]*>?/gm, '').length <= limit;

  return (
    <Card className={`story-media-desc-editor mb-5 w-full ${className}`}>
      <div className="d-flex w-full ">
        <FormGroup
          className="form-group has-float-label mr-2 mb-0"
          style={{ flex: 1, width: '85%' }}
        >
          <Label for="Industry">
            <IntlMessages id={title} />
            {mandatory && <span style={{ color: '#922c88' }}> *</span>}
          </Label>
          <ReactQuill
            className={`react-quill ${
              !disabledDesc &&
              'disabled-ql SpaceDetailDescription DescriptionStyleInActive InputStyleActive '
            }`}
            readOnly={disabledDesc || !contributerOrAbove}
            theme="bubble"
            placeholder="Enter your description here"
            value={tempDesc}
            onChange={(value) => setTempDesc(value)}
          />
        </FormGroup>
        {roleType <= RoleTypes.CONTRIBUTOR && (
          <NavLink to="#" onClick={clickToEditDesc} className="mt-1">
            {updatingDesc ? (
              <Loader
                className="ml-3"
                type="TailSpin"
                color="#922c88"
                height={15}
                width={15}
              />
            ) : (
              <i
                className={`ml-3 ${
                  disabledDesc ? 'simple-icon-pencil' : 'simple-icon-check'
                } ins-no-bg-btn`}
              />
            )}
          </NavLink>
        )}
      </div>
      {showLimit && (
        <p
          className={`m-2 ml-auto mr-5 text-${
            !exceedLimit ? 'error' : 'primary'
          }`}
        >
          <strong>
            <i className="simple-icon-info font-weight-bold "> </i>
            <IntlMessages
              id={exceedLimit ? 'story.max-desc-limit' : 'story.desc-limit'}
            />
          </strong>
        </p>
      )}
    </Card>
  );
}

export default Description;
