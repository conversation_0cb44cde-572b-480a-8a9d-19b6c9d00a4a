import React from 'react';

const Header = ({ name, close }) => {
  return (
    <div
      className="d-flex align-items-center justify-content-between w-full card "
      style={{
        position: 'sticky',
        top: 0,
        right: 0,
        left: 0,
        zIndex: 100,
        paddingTop: '0.75rem',
        paddingRight: '0.75rem',
        paddingLeft: '0.75rem'
      }}
    >
      <div
        className="d-flex w-full flex-row align-items-center justify-content-between"
        style={{
          borderBottom: '1px solid rgba(206, 206, 206, 1)'
        }}
      >
        <div className="d-flex align-items-center w-95 my-2">
          <h2
            style={{
              fontSize: '1.2rem'
            }}
            className="mb-0"
          >
            <span className="font-bolder">Add product :&nbsp;</span>
            {name}
          </h2>
        </div>
        <span onClick={close} className="w-5 d-flex centered">
          <i className="simple-icon-close close-btn" />
        </span>
      </div>
    </div>
  );
};

export default Header;
