/* eslint-disable jsx-a11y/label-has-associated-control */
import { Colxx } from 'components/common/CustomBootstrap';
import React from 'react';
import { Row } from 'reactstrap';
import TimeInput from './TimeInput';

const TimeControl = ({
  className = '',
  style = {},
  startTime,
  setStartTime,
  endTime,
  setEndTime,
  pauseVideo,
  setPauseVideo
}) => {
  return (
    <Colxx style={style} className={`p-0 ${className}`}>
      <div className="d-flex flex-row align-items-center justify-content-start">
        <input
          style={{
            borderWidth: 2,
            accentColor: '#992288',
            transform: 'scale(1.25)',
            marginRight: '8px'
          }}
          size={20}
          aria-label="Checkbox for following text input"
          type="checkbox"
          id="pause-duration"
          checked={pauseVideo}
          onChange={(e) => {
            setPauseVideo(e.target.checked);
          }}
        />
        <label
          name="pause-duration"
          htmlFor="pause-duration"
          className="mb-0 font-bolder"
        >
          Pause video during interaction
        </label>
      </div>

      <Row className="startEndTime m-0 mt-2 p-0 gap-1">
        <TimeInput
          label="Start Time"
          required
          className="tymInput"
          value={startTime}
          onChange={(val) => {
            setStartTime(val);
          }}
        />
        {!pauseVideo && (
          <TimeInput
            label="End Time"
            required
            className="tymInput"
            value={endTime}
            onChange={(val) => {
              setEndTime(val);
            }}
          />
        )}
      </Row>
    </Colxx>
  );
};

export default TimeControl;
