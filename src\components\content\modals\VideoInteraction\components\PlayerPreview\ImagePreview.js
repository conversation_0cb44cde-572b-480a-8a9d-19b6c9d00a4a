/* eslint-disable no-unused-vars */
import { positionData } from 'constants/VideoInteraction';
import { getImage } from 'helpers/Utils';
import React from 'react';
import { Card } from 'reactstrap';

const ImagePreview = ({ position, img, width, height }) => {
  const positionClass = positionData.find((f) => f.id === +position)?.title;

  return (
    <div className={`preview-cont-${positionClass} position-absolute p-2`}>
      <Card
        className="d-flex centered p-0"
        style={{
          height: `${height}%`,
          width: `${width}%`,
          borderRadius: 10,
          background: '#D9D9D9',
          overflow: 'hidden'
        }}
      >
        {img ? (
          <img height="100%" width="100%" alt="preview" src={getImage(img)} />
        ) : (
          <i className="simple-icon-picture" style={{ fontSize: 50 }} />
        )}
      </Card>
    </div>
  );
};

export default ImagePreview;
