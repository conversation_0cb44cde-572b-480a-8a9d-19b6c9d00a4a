/* eslint-disable no-underscore-dangle */
import { ThemeColors } from 'helpers/ThemeColors';

export const chartTooltip = {
  backgroundColor: ThemeColors().foregroundColor,
  titleFontColor: ThemeColors().primaryColor,
  borderColor: ThemeColors().separatorColor,
  borderWidth: 0.5,
  bodyFontColor: ThemeColors().primaryColor,
  bodySpacing: 10,
  xPadding: 15,
  yPadding: 15,
  cornerRadius: 0.15
};

export const centerTextPlugin = (small = false) => {
  return {
    afterDatasetsUpdate() {},
    beforeDraw(chart) {
      const width = chart.chartArea.right;
      const height = chart.chartArea.bottom;
      const { ctx } = chart;
      ctx.restore();

      let activeLabel = chart.data.labels[0];
      let activeValue = chart.data.datasets[0].data[0];
      let dataset = chart.data.datasets[0];
      let total = dataset.data.reduce((a, b) => a + b, 0);

      let activePercentage = parseFloat(
        ((activeValue / total) * 100).toFixed(1)
      );
      activePercentage = chart?.legend?.legendItems[0]?.hidden
        ? 0
        : activePercentage;

      if (chart.pointAvailable) {
        activeLabel = chart.data.labels[chart.pointIndex];
        activeValue =
          chart.data.datasets[chart.pointDataIndex].data[chart.pointIndex];

        dataset = chart.data.datasets[chart.pointDataIndex];
        total = dataset.data.reduce((a, b) => a + b, 0);
        activePercentage = parseFloat(((activeValue / total) * 100).toFixed(1));
        activePercentage = chart.legend.legendItems[chart.pointIndex].hidden
          ? 0
          : activePercentage;
      }

      ctx.clearRect(0, 0, width, height); // Clear the canvas
      ctx.font = `${small ? '25' : '36'}px Nunito, sans-serif`;
      ctx.fillStyle = ThemeColors().primaryColor;
      ctx.textBaseline = 'middle';

      const text = `${activePercentage}%`;
      const textX = Math.round((width - ctx.measureText(text).width) / 2);
      const textY = small ? height / 1.65 : height / 1.75;

      ctx.fillText(text, textX, textY);

      ctx.font = '14px Nunito, sans-serif';
      ctx.textBaseline = 'middle';

      const text2 = activeLabel;
      const textX2 = Math.round((width - ctx.measureText(text2).width) / 2);
      const textY2 = small ? height / 1.5 : height / 2 - 10;

      ctx.fillText(text2, textX2, textY2);

      ctx.save();
    }
    // eslint-disable-next-line no-unused-vars
  };
};
