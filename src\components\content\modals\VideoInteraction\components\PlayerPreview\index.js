import React from 'react';
import ImagePreview from './ImagePreview';
import RichTextPreview from './RichTextPreview';
import { PLAYER_PREVIEW_TYPE, positionData } from 'constants/VideoInteraction';
import SinglePositionCard from 'components/SinglePositionCard';

const PlayerPreview = ({
  setPosition,
  position,
  type = PLAYER_PREVIEW_TYPE.IMAGE,
  img,
  text,
  bgColor,
  isTransparent,
  width,
  height
}) => {
  return (
    <div className="player-preview">
      <h4 className="text-center my-4">Preview</h4>

      <span className="plyr-img">
        {type === PLAYER_PREVIEW_TYPE.IMAGE ? (
          <ImagePreview
            position={position}
            img={img}
            width={width}
            height={height}
          />
        ) : (
          <RichTextPreview
            position={position}
            text={text}
            bgColor={bgColor}
            isTransparent={isTransparent}
          />
        )}
        <img
          src="/assets/player.webp"
          height="100%"
          width="100%"
          alt="player preview"
        />
      </span>

      <div className="positions">
        {positionData.map((m) => (
          <SinglePositionCard
            key={m.id}
            id={m.id}
            img={m.img}
            description={m.description}
            setPosition={setPosition}
            title={m.title}
            active={position === m.id}
          />
        ))}
      </div>
    </div>
  );
};


export default PlayerPreview;
