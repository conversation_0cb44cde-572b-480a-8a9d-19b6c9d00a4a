import { NotificationManager } from 'components/common/react-notifications';
import CopyIcon from 'components/svg/CopyIcon';
import IntlMessages from 'helpers/IntlMessages';
import { copyStringToClipboard } from 'helpers/Utils';
import React from 'react';

const CopyId = ({ id }) => {
  function copyToClipboard(copy) {
    copyStringToClipboard(copy);
    NotificationManager.success(
      <IntlMessages id="share.copy-clip" />,
      <IntlMessages id="req.success" />,
      3000,
      null,
      null,
      ''
    );
  }

  return (
    <div
      className="w-full c-pointer d-flex flex-row align-items-center "
      onClick={() => copyToClipboard(id)}
    >
      <span
        style={{ fontSize: 12, color: '#838383' }}
        className="text-ellipsis mr-1"
      >
        {id}
      </span>
      <CopyIcon color="#838383" height={15} width={15} />
    </div>
  );
};

export default CopyId;
