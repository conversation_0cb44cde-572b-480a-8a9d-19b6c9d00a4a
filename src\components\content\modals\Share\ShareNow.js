/* eslint-disable no-unused-vars */
import SelectPlayer from 'components/SelectPlayer';
import LoadingButton from 'components/buttons/LoadingButton';
import SwitchButton from 'components/buttons/SwitchButton';
import IntlMessages from 'helpers/IntlMessages';
import React, { useEffect, useState, memo } from 'react';
import { Button, Col, Row } from 'reactstrap';
import KeyValue from './components/KeyValue';
import Tab from './components/Tab';
import { formatDateNTime, getRandomChars } from 'helpers/Utils';
import { createShare, updateShare } from 'functions/api/spacesApi';
import { NotificationManager } from 'components/common/react-notifications';

function ShareNow({
  spaceId,
  sharingData,
  isShared,
  title,
  handleRemove,
  entityId,
  entityKind,
  handleSchedule,
  isRemoving,
  isEditing,
  setIsEditing,
  handleUpdateEntity,
  showSelectPlayer,
  isDRM
}) {
  const [players, setPlayers] = useState([]);
  const [selectedPlayer, setSelectedPlayer] = useState({
    name: null,
    id: null
  });
  // const [isEditing, setIsEditing] = useState(false);
  const [isSharing, setIsSharing] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  const showEdit = !isEditing && isShared;

  console.log({
    showEdit,
    isEditing,
    isShared,
    sharingData,
    showSelectPlayer
  });

  useEffect(() => {
    if (sharingData) {
      setSelectedPlayer(sharingData.basicPlayerInfo);
    }
  }, [sharingData]);

  const handleShare = async () => {
    try {
      setIsSharing(true);
      const data = {
        Id: getRandomChars(21),
        PlayerId: selectedPlayer.id,
        EntityKind: entityKind,
        EntityId: entityId,
        ShareAlways: true
      };
      const result = await createShare(data, spaceId);
      const isLimitError = result.errorMsg === 'SHARE_LIMIT_REACHED';
      if (!result.isError) {
        setIsEditing(false);
        const shareId = result?.data?.id;
        if (shareId) {
          handleUpdateEntity(shareId, true);
        }
        NotificationManager.success(
          <IntlMessages id="share.sucess" />,
          <IntlMessages id="share.share-success" />,
          3000,
          null,
          null,
          ''
        );
      } else {
        NotificationManager.error(
          <IntlMessages id={isLimitError ? 'share.limit' : 'share.error'} />,
          <IntlMessages id="req.failed" />,
          3000,
          null,
          null,
          ''
        );
      }
      setIsSharing(false);
    } catch (error) {
      setIsSharing(false);
      console.log('erropr', error);
    }
  };

  const handleUpdate = async () => {
    try {
      setIsUpdating(true);
      const data = {
        Id: sharingData.id,
        PlayerId: selectedPlayer?.id,
        EntityKind: entityKind,
        EntityId: entityId,
        ShareAlways: true,
        StartDateTime: 0,
        EndDateTime: 0,
        TimeZone: '',
        EventName: '',
        EventDescription: '',
        EventActionButton: '',
        EventActionButtonLink: ''
      };
      const result = await updateShare(data, spaceId);
      if (result.isError) {
        NotificationManager.error(
          <IntlMessages id="share.error" />,
          <IntlMessages id="req.failed" />
        );
      } else {
        setIsEditing(false);
        const shareId = result?.data?.id;
        if (shareId) {
          handleUpdateEntity(shareId, true);
        }
        NotificationManager.success(
          <IntlMessages id="share.sucess" />,
          <IntlMessages id="share.update-success" />
        );
      }
      setIsUpdating(false);
    } catch (error) {
      setIsUpdating(false);
      console.log('error', error);
    }
  };

  const disableCreateUpdateButton = (() => {
    if (isEditing && isShared && sharingData?.endDateTime && showSelectPlayer) {
      return false;
    }
    if (showSelectPlayer) {
      // incase of image/youtube vimeo
      if (!selectedPlayer?.id || selectedPlayer?.id === sharingData?.playerId) {
        return true;
      }
      return false;
      // eslint-disable-next-line no-else-return
    } else {
      // make the button disable when we edit the shared with (Share now)
      if (sharingData?.shareAlways && !sharingData?.endDateTime) {
        return true;
      }
      return false;
    }
  })();

  return (
    <div className="w-full p-2" aria-disabled={isUpdating || isSharing}>
      {!showEdit ? (
        <>
          {isEditing && (
            <div className="d-flex flex-row align-items-center justify-content-end gap-1">
              <SwitchButton name="Schedule an Event" onClick={handleSchedule} />
              {sharingData?.id && !sharingData?.endDateTime && (
                <SwitchButton
                  name="View Details"
                  showIcon={false}
                  onClick={() => setIsEditing(false)}
                />
              )}
            </div>
          )}
          <h5 className="font-bolder mt-5">Share Now</h5>
          {showSelectPlayer && (
            <SelectPlayer
              spaceId={spaceId}
              players={players}
              selectedPlayer={selectedPlayer}
              setPlayers={setPlayers}
              infoAbove={false}
              className="mt-4 mb-5"
              handleItemClick={(playerInfo) => {
                setSelectedPlayer(playerInfo);
              }}
            />
          )}
          <div className="d-flex flex-row align-items-center justify-content-center gap-1">
            {/* {isEditing && (
              <Button
                color="primary"
                outline
                onClick={() => setIsEditing(false)}
              >
                View Details
              </Button>
            )} */}
            <LoadingButton
              disabled={disableCreateUpdateButton}
              loading={isUpdating || isSharing}
              text={isEditing ? 'Update' : 'Share'}
              onClick={isEditing ? handleUpdate : handleShare}
            />
          </div>
        </>
      ) : (
        <>
          <Tab
            entityKind={entityKind}
            shareId={sharingData?.id}
            title={title}
            isDRM={isDRM}
          />
          <Shared
            setIsEditing={setIsEditing}
            sharingData={sharingData}
            handleRemove={handleRemove}
            isRemoving={isRemoving}
            showSelectPlayer={showSelectPlayer}
          />
        </>
      )}
    </div>
  );
}

const Shared = ({
  sharingData,
  handleRemove,
  setIsEditing,
  isRemoving,
  showSelectPlayer
}) => {
  let data = [
    {
      id: 1,
      name: 'Share Type :',
      value: 'Share Always'
    },
    {
      id: 2,
      name: 'Start Date & Time :',
      value: formatDateNTime(sharingData?.creationDateTime)
    },
    {
      id: 3,
      name: 'Time Zone :',
      value: sharingData?.timeZone
    },
    {
      id: 4,
      name: 'Player :',
      value: sharingData?.basicPlayerInfo?.name ?? ''
    }
  ];

  if (!sharingData?.timeZone) {
    data = data.filter((f) => f.id !== 3);
  }
  if (!showSelectPlayer) {
    data = data.filter((f) => f.id !== 4);
  }

  return (
    <div>
      <div className="w-full">
        <div className="d-flex flex-row align-items-center justify-content-between p-0 w-full">
          <h5 className="font-bolder">Detail</h5>
          <Button color="primary" onClick={() => setIsEditing(true)}>
            Edit Sharing
          </Button>
        </div>
        <div className="d-flex flex-column mt-2">
          {data.map((d) => (
            <KeyValue key={d.id} name={d.name} value={d.value} />
          ))}
        </div>
        <Row className="align-items-center justify-content-center mt-4 mb-2">
          <LoadingButton
            disabled={isRemoving}
            loading={isRemoving}
            width={175}
            text={isRemoving ? 'Removing...' : 'Remove Sharing'}
            onClick={handleRemove}
          />
        </Row>
      </div>
    </div>
  );
};

export default ShareNow;
