import React from 'react';
import { Card, CardHeader, CardBody } from 'reactstrap';
import SubscriptionType from '../SubscriptionType';
import { formatDateNTime } from 'helpers/Utils';
import LabelValue from './LabelValue';

const Header = ({ data, loading, subsCheck }) => {
  return (
    <Card className="p-2" style={{ borderRadius: 10 }}>
      <CardHeader className="headerBorder p-0">
        <LabelValue
          isLarge
          label="subs.current-plan"
          className="m-0"
          data={
            data?.subscriptionName === 'None' || !data?.subscriptionName
              ? 'Free Trial'
              : data?.subscriptionName
          }
        />
      </CardHeader>
      <CardBody className="mx-3 p-0 mt-2">
        <div className="subHeaderRow">
          <LabelValue
            isLarge
            className="w-70"
            label="subs.id"
            data={data?.id || 'NONE'}
          />
          <LabelValue
            isLarge
            className="w-30"
            label="subs.expiry-date"
            smData={6}
            smLabel={6}
            data={
              data?.endDateTime
                ? formatDateNTime(data?.endDateTime)
                : 'Not Defined'
            }
          />
        </div>
        {!data?.id ? (
          !loading && <LabelValue label="subs.status" data={'Deactivated'} />
        ) : (
          <SubscriptionType
            label="subs.status"
            data={subsCheck === true ? 'Expired' : 'Active'}
          />
        )}
      </CardBody>
    </Card>
  );
};

export default Header;
