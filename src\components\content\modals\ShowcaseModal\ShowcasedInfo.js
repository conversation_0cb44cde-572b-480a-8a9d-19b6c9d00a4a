/* eslint-disable no-nested-ternary */
import Copy from 'components/copy';
import IntlMessages from 'helpers/IntlMessages';
import { formatDateNTime } from 'helpers/Utils';
import React from 'react';
import { Row } from 'reactstrap';
import ShareButton from './ShareButton';

const MODES = {
  DRAFT: 1,
  PUBLISH: 2
};

const ShowcasedInfo = ({
  showcaseData,
  mode,
  conmputedDuration,
  showcasedUrl,
  showcaseAlways
}) => {
  return (
    <div>
      <div className="position-relative">
        <div className=" d-flex flex-row  my-2">
          <span
            className=" font-weight-bolder text-align-left"
            style={{ width: '154px', fontSize: '13px' }}
          >
            <IntlMessages id="sm.creation-date-time" />
          </span>
          <span className=" w-auto" style={{ fontSize: '13px' }}>
            {showcaseData && formatDateNTime(showcaseData?.creationDateTime)}
          </span>
        </div>
        <ShareButton data={showcaseData} href={showcasedUrl} />
        {mode !== MODES.DRAFT && (
          <div className=" d-flex flex-row mb-4">
            <span
              className=" font-weight-bolder text-align-left"
              style={{ width: '154px', fontSize: '13px' }}
            >
              <IntlMessages id="sm.expiry-date-time" />
            </span>
            <span className=" w-auto" style={{ fontSize: '13px' }}>
              {!showcaseAlways ? (
                <IntlMessages id="sm.no-expiry" />
              ) : conmputedDuration ? (
                formatDateNTime(new Date(conmputedDuration))
              ) : null}
            </span>
          </div>
        )}
      </div>

      <Row className="m-0 p-0 w-full">
        <div className="position-relative w-full">
          <input
            className="custom-txtarea w-full h-full"
            style={{
              fontSize: 13,
              height: 30,
              paddingLeft: 10
            }}
            disabled
            value={showcasedUrl}
            onChange={() => {}}
          />
          <div
            className="position-absolute d-flex centered"
            style={{
              right: 0,
              top: 0,
              bottom: 0,
              cursor: 'pointer',
              background: '#992288',
              width: 30,
              height: 30
            }}
          >
            <Copy
              height={15}
              width={15}
              useSvg
              copyText={showcasedUrl}
              isTransparentBg={false}
            />
          </div>
          <br />
        </div>
      </Row>
    </div>
  );
};

export default ShowcasedInfo;
