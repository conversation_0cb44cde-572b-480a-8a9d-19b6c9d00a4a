/* eslint-disable no-else-return */
/* eslint-disable no-unreachable */
/* eslint-disable no-lonely-if */
/* eslint-disable no-unused-vars */
/* eslint-disable consistent-return */
/* eslint-disable array-callback-return */
/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/label-has-associated-control */
import { Colxx } from 'components/common/CustomBootstrap';
import {
  INITIAL_STATE_SURVEY,
  INTERACTION_POSITION,
  INTERACTION_TRIGGER,
  INTERACTION_TYPE,
  positionData,
  RESPONSE_TYPES
} from 'constants/VideoInteraction';
import React, { useEffect, useMemo, useState } from 'react';
import { Button, Card, Row } from 'reactstrap';
import InteractiveIcon from './InteractiveIcon';
import ImageTab from './tabs/Image';
import RichTextTab from './tabs/RichText';
import SurveyTab from './tabs/Survey';
import PollTab from './tabs/Poll';
import SectionHeader from './SectionHeader';
import {
  createInteraction,
  getInteractionById,
  updateInteraction,
  uploadImageInteractionComplete
} from 'functions/api/interactionApi';
import { NotificationManager } from 'components/common/react-notifications';
import { nanoid } from 'nanoid';
import uploadFileToBlob from 'helpers/Azure';
import LoadingSkeleton from 'components/LoadingSkeleton';
import {
  extractLinks,
  removeHtmlTags,
  timecodeToSeconds,
  validateTimeCode,
  validateUrl
} from 'helpers/Utils';
import useInteraction from './useInteraction';

const getPollOptions = (op) => {
  return op
    .filter((f) => f.length > 0)
    .map((m, i) => {
      return {
        id: i,
        text: m
      };
    });
};
const getSurveys = (s) => {
  return s.map((m, i) => ({
    id: i,
    question: m.question,
    type: m.responseType,
    options: m.values.map((m) => m.value),
    isRequired: true
  }));
};

const InteractionDetail = ({
  isParticular = false,
  toggle,
  content,
  isCreateInteraction,
  updateInteractionId,
  setUpdateInteractionId,
  handlePlayerChange,
  showAtTimecodeList
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [active, setActive] = useState(INTERACTION_TYPE.RICH_TEXT);
  const [data, setData] = useState(null);
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  const [surveys, setSurveys] = useState([INITIAL_STATE_SURVEY]);
  const [startTime, setStartTime] = useState('00:00:00');
  const [endTime, setEndTime] = useState('00:00:00');
  const [pollQuestion, setPollQuestion] = useState('');
  const [pollOptions, setPollOptions] = useState(['', '']);
  const [text, setText] = useState('');
  const [position, setPosition] = useState(positionData[0].id);
  const [url, setUrl] = useState('');
  const [img, setImg] = useState(null);
  const [pauseVideo, setPauseVideo] = useState(false);
  const [height, setHeight] = useState(40);
  const [width, setWidth] = useState(40);
  const [bgColor, setBgColor] = useState('#FFFFFF');
  const [isTransparent, setIsTransparent] = useState(false);

  console.log('showAtTimecodeList', showAtTimecodeList);

  const { isDeleting, handleDelete: handleDeleteMutation } = useInteraction({
    id: data?.id,
    spaceId: content?.spaceId,
    contentId: content?.id,
    onDelete: () => {
      toggle();
    }
  });

  const title = (() => {
    if (active === INTERACTION_TYPE.RICH_TEXT) {
      return text;
    }
    if (active === INTERACTION_TYPE.IMAGE) {
      return url;
    }
    if (active === INTERACTION_TYPE.POLL) {
      return pollQuestion;
    }
    return '';
  })();

  const backgroundColor = (() => {
    if (active === INTERACTION_TYPE.RICH_TEXT) {
      return bgColor;
    }
    return '';
  })();

  const showAtTimecode = (() => {
    if (isParticular) {
      return startTime;
    }
    return '';
  })();

  const endAtTimecode = (() => {
    if (
      active === INTERACTION_TYPE.RICH_TEXT ||
      active === INTERACTION_TYPE.IMAGE
    ) {
      return endTime;
    }
    return '';
  })();

  const pauseOnShow = (() => {
    if (
      active === INTERACTION_TYPE.RICH_TEXT ||
      active === INTERACTION_TYPE.IMAGE
    ) {
      return pauseVideo;
    }
    return false;
  })();

  const handleUploadImage = async ({ sasUrl, interactionId }) => {
    try {
      const fileItems = [];
      fileItems.push(sasUrl);
      const fileId = sasUrl?.fileName.split('/input/')[1];
      const fileSasObj = {
        items: fileItems,
        sasUrl: sasUrl?.sasUrl
      };
      const { isError: isUploadingError } = await uploadFileToBlob(
        img,
        () => {},
        () => {},
        fileSasObj
      );
      if (isUploadingError) {
        NotificationManager.error('Failed to upload Image');
        return;
      }
      const { data: uploadCompleteData, isError: uploadCompleteError } =
        await uploadImageInteractionComplete({
          spaceId: content.spaceId,
          contentId: content.id,
          contentInteractionId: interactionId,
          fileName: fileId
        });

      if (uploadCompleteError) {
        // NotificationManager.error('Something went wrong while uploading brand');
      } else {
        NotificationManager.success('Created Interaction successfully');
        toggle();
      }
    } catch (error) {
      console.error('Something went wrong in handleUploadImage due to ', error);
    }
  };

  const validateInteraction = () => {
    try {
      const contentDuration = content.duration;
      const startTimeInSeconds = timecodeToSeconds(startTime);
      const endTimeInSeconds = timecodeToSeconds(endAtTimecode);

      if (isParticular) {
        const isSameStartTimeCodeExists = (() => {
          try {
            if (data?.id) {
              return showAtTimecodeList.some(
                (s) => s.showAtTimecode === startTime && s.id !== data.id
              );
            }
            return showAtTimecodeList.some(
              (s) => s.showAtTimecode === startTime
            );
          } catch (error) {
            console.error(
              'Somethinfg went wrong in isSameStartTimeCodeExists',
              error
            );
            return false;
          }
        })();
        if (isSameStartTimeCodeExists) {
          return 'Start Time cannot be duplicate.Kindly change start time for this interaction';
        }

        const doTimeRangesOverlap = (
          existingStartTime,
          existingEndTime,
          newStartTime,
          newEndTime
        ) => {
          if (existingStartTime && existingEndTime) {
            const existingStart = timecodeToSeconds(existingStartTime);
            const existingEnd = timecodeToSeconds(existingEndTime);
            const newStart = timecodeToSeconds(newStartTime);
            const newEnd = timecodeToSeconds(newEndTime);
            return existingStart <= newEnd && newStart <= existingEnd;
          }
          return false;
        };

        const isStartTimeInBetweenOfEarlierTimeStamps = (() => {
          try {
            const filteredTimeStamps = (() => {
              if (data?.id) {
                return showAtTimecodeList.filter((f) => f.id !== data.id);
              } else {
                return showAtTimecodeList;
              }
            })();
            if (filteredTimeStamps.length === 0) {
              return false;
            }
            return filteredTimeStamps.some((f) =>
              doTimeRangesOverlap(
                f.showAtTimecode,
                f.endAtTimecode,
                startTime,
                endAtTimecode
              )
            );
          } catch (error) {
            console.error(
              'Something went wrong in startTimeInBetweenOfEarlierTimeStamps',
              error
            );
            return false;
          }
        })();
        if (isStartTimeInBetweenOfEarlierTimeStamps) {
          return 'Interactions cannot overlap. Please change the start and end time';
        }

        // start time is in every type of trigger
        const startTimeError = validateTimeCode(startTime, contentDuration);
        if (startTimeError) {
          return startTimeError;
        }

        const endTimeAllowed =
          active === INTERACTION_TYPE.RICH_TEXT ||
          active === INTERACTION_TYPE.IMAGE;

        // end time is only for rich text and image
        if (endTimeAllowed) {
          if (!pauseOnShow) {
            const endTimeError = validateTimeCode(endTime, contentDuration);
            if (endTimeError) {
              return endTimeError;
            }
            if (startTime && endTime) {
              if (startTime === endTime) {
                return 'Start time and End time cannot be same';
              }

              if (endTimeInSeconds === 0) {
                return 'EndTime cannot be 0';
              }

              if (startTimeInSeconds > endTimeInSeconds) {
                return 'Start Time cannot be greater than End Time';
              }
            }
          }
        }
      }

      if (active === INTERACTION_TYPE.RICH_TEXT) {
        const links = extractLinks(text);
        const isLinkEmpty = links.filter((f) => f.length === 0);

        if (isLinkEmpty.length > 0) {
          return `URL cannot be empty`;
        }

        const isLinkInValid = links.find((s) => validateUrl(s));
        if (isLinkInValid) {
          return `${isLinkInValid} is invalid url`;
        }

        const richText = removeHtmlTags(text, true);
        if (richText.length === 0) {
          return `Rich Text cannot be empty `;
        }
      }
      if (active === INTERACTION_TYPE.IMAGE) {
        if (url.length > 0) {
          const validatedUrl = validateUrl(url);
          if (validatedUrl) {
            return validatedUrl;
          }
        }
        if (height < 40) {
          return 'Height should be greater than 40';
        }
        if (height > 150) {
          return 'Height should be less than 150 ';
        }
        if (width < 40) {
          return 'Width should be greater than 40';
        }
        if (width > 150) {
          return 'Width should be less than 150 ';
        }
        if (!img) {
          return 'Please select image';
        }
      }
      if (active === INTERACTION_TYPE.POLL) {
        if (pollQuestion.length <= 2) {
          return 'Poll Question is too small';
        }
        const isPollOptionEmpty = pollOptions.some((s) => s.length === 0);
        if (isPollOptionEmpty) {
          return 'Poll Option cannot be empty';
        }
        return '';
      }
      if (active === INTERACTION_TYPE.SURVEY) {
        const isSurveyQuestionTooSmall = surveys.some(
          (s) => s.question.length <= 2
        );
        if (isSurveyQuestionTooSmall) {
          return 'Survey Question is too small';
        }
        const isSurveyOptionEmpty = (() => {
          const surveyWithoutTextAndDate = surveys.filter(
            (f) =>
              f.responseType === RESPONSE_TYPES.CHECKBOX ||
              f.responseType === RESPONSE_TYPES.MCQ
          );
          const isOptionsEmpty = surveyWithoutTextAndDate.some((s) =>
            s.values.find((f) => f.value.length === 0)
          );
          if (isOptionsEmpty) {
            return 'Survey Option cannot be empty';
          }
          return '';
        })();

        return isSurveyOptionEmpty;
      }
    } catch (error) {
      console.error(
        'Something went wrong in validateInteraction due to ',
        error
      );
      return error;
    }
  };

  console.log({
    interactionError: validateInteraction()
  });

  const handleCreate = async () => {
    const handleError = (error = '') => {
      NotificationManager.error(
        'Something went wrong while creating the Interaction'
      );
      console.error('Something went wrong in the handleCreate due to', error);
      setIsCreating(false);
    };

    const checkError = validateInteraction();
    if (checkError) {
      NotificationManager.error(checkError);
      return;
    }

    try {
      setIsCreating(true);
      const payload = (() => {
        return {
          id: nanoid(),
          contentId: content.id,
          spaceId: content.spaceId,
          type: active,
          trigger: isParticular
            ? INTERACTION_TRIGGER.AtTimestamp
            : INTERACTION_TRIGGER.OnPause,
          showAtTimecode,
          endAtTimecode,
          pauseOnShow,
          isEnabled: true,
          isTransparent,
          bgColor: backgroundColor,
          data: {
            title,
            description: '',
            position,
            pollOptions: getPollOptions(pollOptions),
            surveyQuestions: getSurveys(surveys),
            fileName: img?.name ?? '',
            height,
            width
          }
        };
      })();
      const { data, isError } = await createInteraction(payload);
      console.log({
        img,
        active,
        data,
        isError
      });
      if (isError) {
        handleError();
      } else {
        if (img && active === INTERACTION_TYPE.IMAGE) {
          await handleUploadImage({
            interactionId: data?.id,
            sasUrl: data?.sasUrl
          });
        } else {
          NotificationManager.success('Created the interaction successfull');
          toggle();
        }
        handlePlayerChange();
      }
      setIsCreating(false);
    } catch (error) {
      handleError();
    }
  };

  const handleUpdate = async () => {
    const handleError = (error = '') => {
      NotificationManager.error(
        'Something went wrong while updating the Interaction'
      );
      console.error('Something went wrong in the handleUpdate due to', error);
      setIsUpdating(false);
    };

    const checkError = validateInteraction();
    if (checkError) {
      NotificationManager.error(checkError);
      return;
    }

    try {
      setIsUpdating(true);
      const payload = (() => {
        const dataPayload = {
          title,
          description: '',
          position,
          pollOptions: getPollOptions(pollOptions),
          surveyQuestions: getSurveys(surveys),
          fileName: img?.name,
          height,
          width
        };

        if (!img?.name) {
          delete dataPayload.fileName;
        }

        return {
          id: data.id,
          contentId: content.id,
          spaceId: content.spaceId,
          type: active,
          trigger: isParticular
            ? INTERACTION_TRIGGER.AtTimestamp
            : INTERACTION_TRIGGER.OnPause,
          showAtTimecode,
          endAtTimecode,
          pauseOnShow,
          isEnabled: true,
          data: dataPayload,
          isTransparent,
          bgColor: backgroundColor
        };
      })();
      const { data: updateData, isError } = await updateInteraction(payload);
      if (isError) {
        handleError();
      } else {
        if (img && active === INTERACTION_TYPE.IMAGE && updateData?.sasUrl) {
          await handleUploadImage({
            interactionId: updateData?.id,
            sasUrl: updateData?.sasUrl
          });
        } else {
          NotificationManager.success('Updated the Interaction successfully');
          toggle();
        }
        handlePlayerChange();
      }
      setIsUpdating(false);
    } catch (error) {
      handleError();
    }
  };

  const interactionList = useMemo(() => {
    const commonList = [
      {
        id: INTERACTION_TYPE.RICH_TEXT,
        name: 'Rich Text'
      },
      {
        id: INTERACTION_TYPE.IMAGE,
        name: 'Image'
      }
    ];
    if (isParticular) {
      return [
        ...commonList,
        {
          id: INTERACTION_TYPE.POLL,
          name: 'Poll'
        },
        {
          id: INTERACTION_TYPE.SURVEY,
          name: 'Survey'
        }
      ];
    }
    return commonList;
  }, [isParticular]);

  useEffect(() => {
    const fetchInteractionDetails = async () => {
      try {
        setIsLoading(true);
        const { data, isError } = await getInteractionById({
          spaceId: content?.spaceId,
          contentId: content?.id,
          id: updateInteractionId
        });
        if (isError) {
          toggle();
          NotificationManager.error('Something went wrong.Please try again');
        } else {
          setData(data);
          setActive(data.type);
        }
        setIsLoading(false);
      } catch (error) {
        setIsLoading(false);
        toggle();
        NotificationManager.error('Something went wrong.Please try again');
        console.error(
          'Something went wrong in fetchInteractionDetails due to ',
          error
        );
      }
    };

    if (updateInteractionId) {
      fetchInteractionDetails();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updateInteractionId, content?.spaceId, content?.id]);

  if (isLoading) {
    return <Loading />;
  }

  const handleDelete = () => {
    if (isCreateInteraction) {
      toggle();
    } else {
      handleDeleteMutation();
    }
  };

  return (
    <div
      className="InteractionDetail"
      aria-disabled={isCreating || isUpdating || isDeleting}
    >
      <div className="IDHeader">
        <h1>Interaction Detail</h1>
        <span className="ID-Btn" onClick={handleDelete}>
          <i className="simple-icon-trash" />
        </span>
      </div>

      <Colxx className="m-0 p-0">
        <SectionHeader
          style={{
            marginTop: '1rem',
            marginBottom: '0.5rem'
          }}
        >
          Display
        </SectionHeader>
        <div className="IDList">
          {interactionList.map((m) => {
            return (
              <InteractionSelect
                key={m.id}
                data={m}
                active={active === m.id}
                onClick={() => {
                  setActive(m.id);
                }}
              />
            );
          })}
        </div>
      </Colxx>

      {active === INTERACTION_TYPE.IMAGE ? (
        <ImageTab
          isParticular={isParticular}
          position={position}
          setPosition={setPosition}
          img={img}
          setImg={setImg}
          url={url}
          setUrl={setUrl}
          interactiondetails={data}
          startTime={startTime}
          setStartTime={setStartTime}
          endTime={endTime}
          setEndTime={setEndTime}
          pauseVideo={pauseVideo}
          setPauseVideo={setPauseVideo}
          height={height}
          setHeight={setHeight}
          width={width}
          setWidth={setWidth}
        />
      ) : active === INTERACTION_TYPE.POLL ? (
        <PollTab
          pollQuestion={pollQuestion}
          setPollQuestion={setPollQuestion}
          pollOptions={pollOptions}
          setPollOptions={setPollOptions}
          startTime={startTime}
          setStartTime={setStartTime}
          interactiondetails={data}
        />
      ) : active === INTERACTION_TYPE.RICH_TEXT ? (
        <RichTextTab
          isParticular={isParticular}
          text={text}
          setText={setText}
          position={position}
          setPosition={setPosition}
          startTime={startTime}
          setStartTime={setStartTime}
          endTime={endTime}
          setEndTime={setEndTime}
          interactiondetails={data}
          pauseVideo={pauseVideo}
          setPauseVideo={setPauseVideo}
          setBgColor={setBgColor}
          bgColor={bgColor}
          isTransparent={isTransparent}
          setIsTransparent={setIsTransparent}
        />
      ) : (
        <SurveyTab
          surveys={surveys}
          setSurveys={setSurveys}
          startTime={startTime}
          setStartTime={setStartTime}
          interactiondetails={data}
        />
      )}

      <Row className="d-flex flex-row align-items-center justify-content-center gap-1">
        <Button
          disabled={isCreating || isUpdating}
          style={{ minWidth: 125 }}
          color="primary"
          outline
          className="mb-2"
          onClick={toggle}
        >
          Cancel
        </Button>
        <Button
          disabled={isCreating || isUpdating}
          style={{ minWidth: 125 }}
          color="primary"
          className="mb-2"
          onClick={() => {
            if (isCreateInteraction) {
              handleCreate();
            } else {
              handleUpdate();
            }
          }}
        >
          {isCreateInteraction ? 'Save' : 'Update'}
        </Button>
      </Row>
    </div>
  );
};

const InteractionSelect = ({ data, active, onClick }) => {
  const color = active ? '#992288' : '#B6B6B6';
  return (
    <Card
      className="InteractionSelect"
      style={{ border: active ? '1px solid #992288' : '1px solid silver' }}
      onClick={onClick}
    >
      <span
        className="h-full w-full d-flex"
        style={{
          background: active ? '#992288' : '',
          height: 10,
          width: 10,
          borderRadius: 10,
          outline: `2px solid ${color}`,
          outlineOffset: '2px'
        }}
      />
      <span className="d-flex flex-row align-items-center justify-content-center">
        <InteractiveIcon iconType={data.id} active={active} />
        <p className="m-0 ml-1">{data.name}</p>
      </span>
    </Card>
  );
};

const Loading = () => {
  return (
    <div className="InteractionDetail">
      <LoadingSkeleton
        style={{ height: '30px', width: '150px', marginBottom: 10 }}
      />
      <LoadingSkeleton
        style={{ height: '10px', width: '150px', marginBottom: 5 }}
      />
      <LoadingSkeleton
        style={{ height: '10px', width: '100%', marginBottom: 2 }}
      />

      <LoadingSkeleton
        style={{ height: '360px', width: '100%', marginBottom: 10 }}
      />
      <div className="d-flex flex-row w-full centered gap-2">
        <LoadingSkeleton style={{ height: '50px', width: '150px' }} />
        <LoadingSkeleton style={{ height: '50px', width: '150px' }} />
      </div>
    </div>
  );
};

export default InteractionDetail;
