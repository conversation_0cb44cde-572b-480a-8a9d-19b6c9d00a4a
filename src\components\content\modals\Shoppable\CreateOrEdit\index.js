/* eslint-disable consistent-return */
/* eslint-disable no-unused-vars */
import React, { useEffect, useMemo, useState } from 'react';
import ProductDetails from './ProductDetails';
import InteractionDetails from './InteractionDetails';
import SectionHeader from '../../SectionHeader';
import IntlMessages from 'helpers/IntlMessages';
import InfoIcon from 'components/icon/InfoIcon';
import FormInput from 'components/input/FormInput';
import Preview from './Preview';
import {
  createShoppableProduct,
  deleteShoppableImage,
  deleteShoppableProduct,
  shoppableImageUploadComplete,
  updateShoppableProduct
} from 'functions/api/shoppableApi';
import { positionData } from 'constants/VideoInteraction';
import { NotificationManager } from 'components/common/react-notifications';
import uploadFileToBlob from 'helpers/Azure';
import { Button } from 'reactstrap';
import {
  timecodeToSeconds,
  validateTimeCode,
  validateUrl
} from 'helpers/Utils';
import SinglePositionCard from 'components/SinglePositionCard';

const headerStyle = {
  fontSize: '1.1rem'
};

const CreateOrEdit = ({
  data,
  content,
  handleCancel,
  fetchShoppableProduct,
  handlePlayerChange,
  setContent
}) => {
  const [originalPrice, setOriginalPrice] = useState(0);
  const [sellingPrice, setSellingPrice] = useState(0);
  const [currency, setCurrency] = useState('');
  const [buttonText, setButtonText] = useState('Buy Now');
  const [title, setTitle] = useState('');
  const [url, setUrl] = useState('');
  const [showThroughOut, setShowThroughout] = useState(false);
  const [startTime, setStartTime] = useState('00:00:00');
  const [endTime, setEndTime] = useState('00:00:00');
  const [img, setImg] = useState(null);
  const [position, setPosition] = useState(positionData[3].id);

  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isDeletingImage, setIsDeletingImage] = useState(false);

  const getCurrencyPayload = (cur) => {
    if (!cur) {
      return '';
    }
    if (typeof cur === 'string') {
      return cur;
    }
    if (currency?.symbol) {
      return currency?.symbol;
    }
    return '';
  };

  useEffect(() => {
    if (data) {
      setOriginalPrice(data.originalPrice);
      setSellingPrice(data.sellingPrice);
      setCurrency(data.currency);
      setButtonText(data.ctaText);
      setShowThroughout(data.displayThroughout);
      setStartTime(data.startTime);
      setEndTime(data.endTime);
      setUrl(data.ctaValue);
      setImg(data.imageUrl);
      setTitle(data.title);
      setPosition(data.position);
    }
  }, [data]);

  const validateErrors = () => {
    if (!originalPrice) {
      return 'Original price is required';
    }
    if (!sellingPrice) {
      return 'Selling price is required';
    }
    if (!currency) {
      return 'Currency is required';
    }
    if (buttonText.length === 0) {
      return 'Button Text cannot be empty';
    }
    if (buttonText.length <= 2) {
      return 'Button Text is too small';
    }
    if (!title) {
      return 'Title cannot be empty';
    }
    if (title.length < 2) {
      return 'Title is too small';
    }
    const urlError = validateUrl(url, false);
    if (urlError) {
      return urlError;
    }
    if (!showThroughOut) {
      const contentDuration = content.duration;
      const startTimeInSeconds = timecodeToSeconds(startTime);
      const endTimeInSeconds = timecodeToSeconds(endTime);

      const startTimeError = validateTimeCode(startTime, contentDuration);
      if (startTimeError) {
        return startTimeError;
      }

      const endTimeError = validateTimeCode(startTime, contentDuration);
      if (endTimeError) {
        return endTimeError;
      }

      if (startTime === endTime) {
        return 'Start time and End time cannot be same';
      }

      if (endTimeInSeconds === 0) {
        return 'EndTime cannot be 0';
      }

      if (startTimeInSeconds > endTimeInSeconds) {
        return 'Start Time cannot be greater than End Time';
      }
    }

    return '';
  };

  const handleDeleteImage = async () => {
    if (img?.name) {
      setImg(null);
      return;
    }
    const handleError = () => {
      setIsDeletingImage(false);
      NotificationManager.error(
        'Something went wrong while deleting the shoppable product image'
      );
    };
    try {
      setIsDeletingImage(true);
      const { data: respdata, isError } = await deleteShoppableImage({
        contentId: content.id,
        spaceId: content.spaceId
      });
      if (isError) {
        handleError();
      } else {
        setImg(null);
        handlePlayerChange();
        NotificationManager.success(
          'Deleted the shoppable product image successfully'
        );
      }
      setIsDeletingImage(false);
    } catch (error) {
      console.error('Something went wrong in handleDeleteImage due to ', error);
      handleError();
    }
  };

  const handleUploadImage = async ({ sasUrl, isUpdate = false }) => {
    try {
      const fileItems = [];
      fileItems.push(sasUrl);
      const fileId = sasUrl?.fileName.split('/input/')[1];
      const fileSasObj = {
        items: fileItems,
        sasUrl: sasUrl?.sasUrl
      };
      const { isError: isUploadingError } = await uploadFileToBlob(
        img,
        () => {},
        () => {},
        fileSasObj
      );
      if (isUploadingError) {
        NotificationManager.error('Failed to upload Image');
        return;
      }
      const { data: uploadCompleteData, isError: uploadCompleteError } =
        await shoppableImageUploadComplete({
          SpaceId: content.spaceId,
          ContentId: content.id,
          fileName: fileId
        });

      if (uploadCompleteError) {
        // NotificationManager.error('Something went wrong while uploading brand');
      } else {
        NotificationManager.success(
          isUpdate
            ? 'Updated shoppable product successfully'
            : 'Created shoppable product successfully'
        );
      }
    } catch (error) {
      console.error('Something went wrong in handleUploadImage due to ', error);
    }
  };

  const handleCreate = async () => {
    const handleError = () => {
      setIsCreating(false);
      NotificationManager.error(
        'Something went wrong while creating the shoppable product'
      );
    };

    const error = validateErrors();
    if (error) {
      return NotificationManager.error(error);
    }

    try {
      setIsCreating(true);
      const payload = {
        title,
        ContentId: content.id,
        SpaceId: content.spaceId,
        FileName: img?.name ?? '',
        Currency: getCurrencyPayload(currency),
        SellingPrice: +sellingPrice,
        OriginalPrice: +originalPrice,
        CtaText: buttonText,
        CtaValue: url,
        DisplayThroughout: showThroughOut,
        StartTime: startTime,
        EndTime: endTime,
        Position: position
      };
      const { data, isError } = await createShoppableProduct(payload);
      if (isError) {
        handleError();
      } else {
        const sasUrl = data?.sasUrl;
        if (sasUrl) {
          await handleUploadImage({ sasUrl });
        }
        await fetchShoppableProduct();
        handlePlayerChange();
        setContent((prev) => ({
          ...prev,
          shoppableProductExists: true
        }));
      }
      setIsCreating(false);
    } catch (error) {
      console.log('Something went wrong in handleCreate due to', error);
      handleError();
    }
  };

  const handleUpdate = async () => {
    const handleError = () => {
      setIsUpdating(false);
      NotificationManager.error(
        'Something went wrong while updating the shoppable product'
      );
    };

    const error = validateErrors();
    if (error) {
      return NotificationManager.error(error);
    }

    try {
      setIsUpdating(true);
      const payload = {
        title,
        ContentId: content.id,
        SpaceId: content.spaceId,
        FileName: img?.name ?? '',
        Currency: getCurrencyPayload(currency),
        SellingPrice: +sellingPrice,
        OriginalPrice: +originalPrice,
        CtaText: buttonText,
        CtaValue: url,
        DisplayThroughout: showThroughOut,
        StartTime: startTime,
        EndTime: endTime,
        Position: position
      };
      const { data: respData, isError } = await updateShoppableProduct(payload);
      console.log('data', respData);
      if (isError) {
        handleError();
      } else {
        const sasUrl = respData?.sasUrl;
        if (sasUrl && img?.name) {
          await handleUploadImage({ sasUrl, isUpdate: true });
          handlePlayerChange();
        } else {
          NotificationManager.success(
            'Created the shoppable experience successfully'
          );
        }
      }
      setIsUpdating(false);
    } catch (error) {
      console.error('something went wrong in handleUpdate due to ', error);
      handleError();
    }
  };

  const handleDelete = async () => {
    const handleError = () => {
      setIsDeleting(false);
      NotificationManager.error(
        'Something went wrong while deleting the shoppable product'
      );
    };

    try {
      setIsDeleting(true);
      const { data, isError } = await deleteShoppableProduct({
        spaceId: content.spaceId,
        contentId: content.id
      });
      if (isError) {
        handleError();
      } else {
        NotificationManager.success(
          'Deleted the shoppable experience successfully'
        );
        handlePlayerChange();
        handleCancel();
        setContent((prev) => ({
          ...prev,
          shoppableProductExists: false
        }));
      }
      setIsDeleting(false);
    } catch (error) {
      handleError();
    }
  };

  const imgSrc = useMemo(() => {
    if (!img) {
      return null;
    }
    if (img?.name) {
      return URL.createObjectURL(img);
    }
    if (typeof img === 'string') {
      return img;
    }
    return null;
  }, [img]);

  const disabled = isCreating || isUpdating || isDeleting || isDeletingImage;

  return (
    <div className="CreateOrEditSP" aria-disabled={disabled}>
      <SectionHeader number={1}>
        <p className="text-align-left mb-0" style={headerStyle}>
          <strong className="mr-2">
            <IntlMessages id="Product Details" />
          </strong>
          <InfoIcon
            name="product-details"
            message="Add product name, image, and pricing to highlight your offering."
          />
        </p>
      </SectionHeader>
      <ProductDetails
        originalPrice={originalPrice}
        setOriginalPrice={setOriginalPrice}
        sellingPrice={sellingPrice}
        setSellingPrice={setSellingPrice}
        currency={currency}
        setCurrency={setCurrency}
        setImg={setImg}
        img={img}
        imgSrc={imgSrc}
        title={title}
        setTitle={setTitle}
        handleDeleteImage={handleDeleteImage}
      />

      <SectionHeader number={2}>
        <p className="text-align-left mb-0" style={headerStyle}>
          <strong className="mr-2">
            <IntlMessages id="Call to Action button (CTA) " />
          </strong>
          <InfoIcon
            name="call-to-action"
            message="Define the button text and link to guide users to shop or learn more."
          />
        </p>
      </SectionHeader>
      <div className="w-full">
        <FormInput
          label="Button text"
          placeholder="Add button text"
          value={buttonText}
          onChangeText={(e) => setButtonText(e)}
          isImportant
        />
        <FormInput
          label="Add Url"
          placeholder="Add url"
          value={url}
          onChangeText={(e) => setUrl(e)}
          isImportant
        />
      </div>

      <SectionHeader number={3}>
        <p className="text-align-left mb-0" style={headerStyle}>
          <strong className="mr-2">
            <IntlMessages id="Interactions details" />
          </strong>
          <InfoIcon
            name="Interactions-details-info"
            message="Set when your interaction should show up during playback."
          />
        </p>
      </SectionHeader>
      <InteractionDetails
        showThroughOut={showThroughOut}
        setShowThroughout={setShowThroughout}
        startTime={startTime}
        setStartTime={setStartTime}
        endTime={endTime}
        setEndTime={setEndTime}
      />
      {title?.length > 0 && (
        <>
          <Preview
            title={title}
            sellingPrice={+sellingPrice}
            originalPrice={+originalPrice}
            currency={getCurrencyPayload(currency)}
            imgSrc={imgSrc}
            position={position}
          />
          <div className="d-flex flex-row w-full align-items-center justify-content-center gap-1 mt-4 flex-wrap">
            {positionData.map((m) => (
              <SinglePositionCard
                key={m.id}
                id={m.id}
                img={m.img}
                description={m.description}
                setPosition={setPosition}
                title={m.title}
                active={position === m.id}
              />
            ))}
          </div>
        </>
      )}
      <div className="d-flex flex-row gap-2 align-items-center mt-4">
        {data && (
          <Button
            style={{
              textWrap: 'nowrap'
            }}
            outline
            color="primary"
            disabled={isDeleting}
            type="button"
            onClick={() => {
              handleDelete();
            }}
          >
            Delete
          </Button>
        )}
        <Button
          style={{
            textWrap: 'nowrap'
          }}
          color="primary"
          disabled={disabled}
          type="button"
          onClick={() => {
            if (data) {
              handleUpdate();
            } else {
              handleCreate();
            }
          }}
        >
          {data ? 'Update' : 'Add'} Product
        </Button>
      </div>
    </div>
  );
};

export default CreateOrEdit;
