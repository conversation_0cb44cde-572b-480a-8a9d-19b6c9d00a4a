/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-vars */
/* eslint-disable jsx-a11y/media-has-caption */
/* eslint-disable react/button-has-type */
/* eslint-disable consistent-return */
import IntlMessages from 'helpers/IntlMessages';
import React, { useEffect, useState } from 'react';
import { Button, ModalBody } from 'reactstrap';
import { localFIleLimit } from 'constants/defaultValues';
import { NotificationManager } from 'components/common/react-notifications';
import { nanoid } from 'nanoid';
import { getRecordingSpaceId, returnFileSize, RoleTypes } from 'helpers/Utils';
import {
  handleContentEncoding,
  handleCreateSasUrl,
  handleUpdateContentStatus,
  updateContentUploadStatus
} from 'functions/api/spacesApi';
import uploadFileToBlob from 'helpers/Azure';
import PortalModal from 'components/Portal/PortalModal';
import PreviewContent from '../components/PreviewContent';
import ProgressBar from '../components/ProgressBar';
import AudioRecording from './AudioRecording';
import VideoRecording from './VideoRecording';
import ScreenRecording from './ScreenRecording';
import usePermission from 'hooks/usePermission';
import ConfirmationModal from 'components/common/ConfirmationModal';
import { useSelector } from 'react-redux';
import AutoApprove from '../components/AutoApprove';

const tabs = [
  { id: 1, name: 'Audio Recording' },
  { id: 2, name: 'Video Recording' },
  { id: 3, name: 'Screen Recording' }
];

function RecNUploadModal({
  modalOpen,
  toggleModal,
  type,
  setType,
  //   screen recording props
  blob,
  blobUrl,
  isCameraStarted,
  screenRecordingDuration,
  resetScreenRecorderData,
  isRecordingStopped,
  isStarted,
  stopCamera,
  startCamera,
  audio,
  setAudio,
  startRecording,
  stopRecording,
  setSelectedCamera,
  setSelectedMicrophone,
  selectedCamera,
  selectedMicrophone,
  cameraPos,
  setCameraPos,
  roleType
}) {
  const permissionProps = usePermission({ condition: modalOpen });
  const isAudioRecordingModal = type === 1;
  const isVideoRecordingModal = type === 2;
  const isScreenRecordingModal = type === 3;
  const MediaType = isAudioRecordingModal ? 2 : 1;

  const [controller, setController] = useState();
  const [category, setCategory] = useState('');
  const [previewLink, setPreviewLink] = useState(blobUrl);
  const [mediaFile, setMediaFile] = useState(blob);
  const [tags, setTags] = useState([]);
  const [newTitle, setNewTitle] = useState('');
  const [duration, setDuration] = useState(0);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgess] = useState(0);
  const [sizeInGB, setSizeInGB] = useState(0);
  const [cancelEncoding, setCancelEncoding] = useState(null);
  const checkedSpaceId = getRecordingSpaceId();
  const [openConfirmationModal, setOpenConfirmationModal] = useState(false);
  const [isAnyTabRecording, setIsAnyTabRecording] = useState(false);
  const [autoApprove, setAutoApprove] = useState(false);
  const { workFlowEnabled } = useSelector((state) => state.subscription);

  useEffect(() => {
    if (modalOpen && (isVideoRecordingModal || isAudioRecordingModal)) {
      setMediaFile(null);
      setIsAnyTabRecording(false);
    }
  }, [isVideoRecordingModal, modalOpen, isAudioRecordingModal]);

  const toggleConfirmationModal = () => {
    setOpenConfirmationModal(!openConfirmationModal);
  };

  useEffect(() => {
    if (blob && blobUrl) {
      setPreviewLink(blobUrl);
      setMediaFile(blob);
      setSizeInGB(returnFileSize(blob?.size).sizeInGB);
    }
  }, [blob, blobUrl]);

  const removeSelectedItem = () => {
    setNewTitle('');
    setCategory('');
    setPreviewLink();
    setMediaFile(null);
    resetScreenRecorderData();
    setTags([]);
    setDuration(0);
    setSizeInGB(0);
    setSelectedCamera(null);
    setSelectedMicrophone(null);
  };

  const validateTitle = (value) => {
    let error = '';
    if (!value || value.trim().length === 0) {
      setNewTitle(value);
      error = 'Please enter a content title';
    } else if (value.length > 100) {
      error = 'Maximum character limit for title is 100';
    } else if (value.trim().length < 2) {
      error = 'Title should have atleast 2 characters';
      setNewTitle(value);
    } else {
      setNewTitle(value);
    }
    return error;
  };

  const handleUploadError = async (contentId) => {
    setUploadProgess(-1);
    setUploading(false);
    resetScreenRecorderData();
    toggleModal();
    await handleUpdateContentStatus(
      { contentId, ContentStatus: 4 },
      checkedSpaceId
    );
  };

  useEffect(() => {
    // When we upload video via screen recording
    // after pressing continue and redirecting
    // if we press open screen recording button
    // the modals uploading condition would be true
    // thats y we have added below condition
    if (!modalOpen) {
      setUploading(false);
    }
  }, [modalOpen]);

  // eslint-disable-next-line no-unused-vars
  const handleUpload = async () => {
    if (tags.length === 0) {
      NotificationManager.error(<IntlMessages id="min-one-tag" />);
      return;
    }
    if (category.length === 0) {
      NotificationManager.error(<IntlMessages id="min-one-category" />);
      return;
    }
    const titleError = validateTitle(newTitle);
    if (titleError) {
      NotificationManager.error(titleError);
      return;
    }
    setUploading(true);

    if (sizeInGB > localFIleLimit) {
      NotificationManager.warning(
        `${mediaFile.name} exceeds accepted file size`,
        <IntlMessages id="req.success" />,
        3000,
        null,
        null,
        ''
      );
      return;
    }
    if (sizeInGB < localFIleLimit) {
      const payload = [
        {
          id: nanoid(),
          Title: newTitle,
          Filename: mediaFile.name,
          ContentType: mediaFile.type,
          Size: mediaFile.size,
          duration: isScreenRecordingModal ? screenRecordingDuration : duration,
          MediaType,
          AutoApprove: autoApprove,
          tags,
          category,
          isDRM: false,
          isAES: false,
          optimization: true,
          streaming: false,
          adaptiveBitRate: false
        }
      ];

      let sasObj;
      try {
        const createdSas = await handleCreateSasUrl(
          payload,
          checkedSpaceId,
          true
        );

        if (createdSas?.isError || typeof createdSas === 'string') {
          setUploading(false);
          console.log('Error==>', createdSas?.data);
          toggleModal();
          return;
        }
        const { data } = createdSas;
        sasObj = data;
        const { sasUrl } = sasObj;

        if (sasObj) {
          const fileItems = [];
          fileItems.push(sasObj.items[0]);

          const fileSasObj = {
            items: fileItems,
            sasUrl
          };

          const { isError: isUploadingError } = await uploadFileToBlob(
            mediaFile,
            (e) => setUploadProgess((e.loadedBytes / mediaFile.size) * 90),
            setController,
            fileSasObj
          );
          if (!isUploadingError) {
            const { data, isError } = await updateContentUploadStatus(
              {
                ContentIds: [fileItems[0].contentId],
                UploadStatus: 3,
                WatermarkOptions: null
              },
              checkedSpaceId
            );
            if (!isError) {
              const handledData = {
                spaceId: checkedSpaceId,
                contentId: fileItems[0].contentId,
                MediaType,
                isDRM: false,
                isAES: false,
                optimize: true,
                adaptiveBitRate: false
              };
              const encodedresp = await handleContentEncoding(
                handledData,
                false,
                cancelEncoding,
                setCancelEncoding
              );

              if (!encodedresp?.isError && encodedresp?.data) {
                if (handledData.optimize === false) {
                  await handleUpdateContentStatus(
                    { contentId: fileItems[0].contentId, ContentStatus: 2 },
                    checkedSpaceId
                  );
                }
                setUploadProgess(100);
                resetScreenRecorderData();
                removeSelectedItem();
                // setUploading(false);
                // setUploadProgess(-1);
              } else {
                NotificationManager.error(
                  <IntlMessages id="content-opt-failed" />,
                  <IntlMessages id="req.failed" />,
                  3000,
                  null,
                  null,
                  ''
                );
                handleUploadError(fileItems[0].contentId);
              }
            } else {
              NotificationManager.error(
                <IntlMessages id="failed-upload" />,
                <IntlMessages id="req.failed" />,
                3000,
                null,
                null,
                ''
              );
              handleUploadError(fileItems[0].contentId);
            }
          } else {
            NotificationManager.error(
              <IntlMessages id="failed-upload" />,
              <IntlMessages id="req.failed" />,
              3000,
              null,
              null,
              ''
            );
            handleUploadError(fileItems[0].contentId);
          }
        }
      } catch (error) {
        console.log(error);
      }
    }
  };

  const AbortUpload = () => {
    if (controller) {
      controller.abort();
    }
  };

  useEffect(() => {
    if ((isAudioRecordingModal || isVideoRecordingModal) && mediaFile) {
      setSizeInGB(returnFileSize(mediaFile?.size).sizeInGB);
      const previewAudio = URL.createObjectURL(mediaFile);
      setPreviewLink(previewAudio);
    }
  }, [isAudioRecordingModal, mediaFile, isVideoRecordingModal]);

  useEffect(() => {
    // when closing modal we are setting everything to
    // null or empty (only in the case if its of audio
    // or video recortding modal)
    if (!modalOpen && (isAudioRecordingModal || isVideoRecordingModal)) {
      removeSelectedItem();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [modalOpen, isAudioRecordingModal, isVideoRecordingModal]);

  const showPreview =
    isAudioRecordingModal || isVideoRecordingModal
      ? !mediaFile
      : !blob && !blobUrl;

  React.useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        toggleConfirmationModal();
      }
    };
    window.addEventListener('keydown', handleEscape);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [modalOpen, openConfirmationModal]);

  if (modalOpen) {
    return (
      <>
        <ConfirmationModal
          openConfirmationModal={openConfirmationModal}
          toggleConfirmationModal={toggleConfirmationModal}
          handleConfirm={() => {
            toggleModal();
            toggleConfirmationModal();
            removeSelectedItem();
          }}
          handleClose={toggleConfirmationModal}
        />
        <PortalModal
          isOpen={modalOpen}
          toggle={toggleConfirmationModal}
          audioSmallWidth={MediaType === 2 && !showPreview}
        >
          {uploading ? (
            <ModalBody className="d-flex flex-column pt-0  m-4 p-3">
              <ProgressBar
                progress={uploadProgress}
                setProgress={setUploadProgess}
                setUploading={setUploading}
                AbortUpload={AbortUpload}
                isRecording
                optimize
                reload={() => {}}
                spaceId={checkedSpaceId}
                toggleModal={toggleModal}
                MediaType={MediaType}
                message={
                  isAudioRecordingModal
                    ? 'Audio Upload Successfully!'
                    : 'Video Upload Successfully!'
                }
              />
            </ModalBody>
          ) : (
            <>
              <ModalBody
                className="d-flex flex-column pt-0 dashboard-media-upload-container m-4 p-3"
                style={{ minHeight: '350px !importnant' }}
              >
                <div className="d-flex flex-row w-full align-content-center  dashbaord-upload-title-container justify-content-between mb-3">
                  <span> </span>
                  <div className="tabupload dashboard-upload-heading">
                    <span className="font-bolder">
                      {isScreenRecordingModal && (
                        <IntlMessages id="Screen Recording" />
                      )}
                      {isVideoRecordingModal && (
                        <IntlMessages id="Video Messaging" />
                      )}
                      {isAudioRecordingModal && (
                        <IntlMessages id="Voice Note" />
                      )}
                    </span>
                  </div>
                  <Button
                    color="white"
                    type="button"
                    className="d-flex justify-content-end p-0 mb-2"
                    onClick={() => {
                      if (isRecordingStopped) {
                        removeSelectedItem();
                      }
                      toggleConfirmationModal();
                    }}
                  >
                    <i className="simple-icon-close close-btn" />
                  </Button>
                </div>
                {/* {showPreview && (
                  <div className="d-flex mb-2">
                    {tabs.map((t) => (
                      <div
                        key={t.id}
                        className="tabupload"
                        aria-disabled={isAnyTabRecording}
                        style={{
                          borderBottom: `${
                            type === t.id
                              ? '3px solid #922c88'
                              : '0.7px solid #922c88'
                          }`
                        }}
                        onClick={() => {
                          setType(t.id);
                        }}
                      >
                        <IntlMessages id={t.name} />
                      </div>
                    ))}
                  </div>
                )} */}
                {showPreview ? (
                  <div className="centered">
                    <div className="d-flex justify-content-center align-items-center flex-column mb-4  w-full">
                      {isAudioRecordingModal ? (
                        <AudioRecording
                          setMediaFile={setMediaFile}
                          setDuration={setDuration}
                          setIsAnyTabRecording={setIsAnyTabRecording}
                        />
                      ) : isVideoRecordingModal ? (
                        <VideoRecording
                          setDuration={setDuration}
                          setMediaFile={setMediaFile}
                          setIsAnyTabRecording={setIsAnyTabRecording}
                          {...permissionProps}
                        />
                      ) : (
                        isScreenRecordingModal && (
                          <ScreenRecording
                            isCameraStarted={isCameraStarted}
                            isStarted={isStarted}
                            stopCamera={stopCamera}
                            startCamera={startCamera}
                            audio={audio}
                            setAudio={setAudio}
                            isRecordingStopped={isRecordingStopped}
                            startRecording={startRecording}
                            stopRecording={stopRecording}
                            setSelectedCamera={setSelectedCamera}
                            setSelectedMicrophone={setSelectedMicrophone}
                            selectedMicrophone={selectedMicrophone}
                            selectedCamera={selectedCamera}
                            cameraPos={cameraPos}
                            setCameraPos={setCameraPos}
                            {...permissionProps}
                          />
                        )
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="centered">
                    <PreviewContent
                      previewLink={previewLink}
                      mediaFile={mediaFile}
                      showPreviewAndTitle
                      setDuration={setDuration}
                      removeSelectedItem={removeSelectedItem}
                      validateTitle={validateTitle}
                      newTitle={newTitle}
                      tags={tags}
                      setTags={setTags}
                      category={category}
                      setCategory={setCategory}
                      recordingDuration={duration || screenRecordingDuration}
                    />
                    {workFlowEnabled &&
                      roleType <= RoleTypes.CONTENT_MANAGER && (
                        <AutoApprove
                          autoApprove={autoApprove}
                          setAutoApprove={setAutoApprove}
                        />
                      )}
                    <div className="mt-5 mb-2">
                      <Button
                        type="button"
                        color="primary"
                        onClick={handleUpload}
                      >
                        Process
                      </Button>
                    </div>
                  </div>
                )}
              </ModalBody>
            </>
          )}
        </PortalModal>
      </>
    );
  }
  return null;
}

export default RecNUploadModal;
