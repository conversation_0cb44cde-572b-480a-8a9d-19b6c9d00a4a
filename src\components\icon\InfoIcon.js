/* eslint-disable no-unused-vars */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import IntlMessages from 'helpers/IntlMessages';
import useOnClickOutside from 'hooks/useOnClickOutSide';
import React, { useRef, useState } from 'react';
import { Popover, PopoverBody } from 'reactstrap';

const InfoIcon = ({ message = "", iconName = "simple-icon-info", name, className, iconStyle = {}, customBody, textPrimary = false }) => {
  const [toggled, setToggled] = useState(false);
  const key = `Popover-${name}`;

  const ref = useRef(null);
  useOnClickOutside(ref, () => setToggled(false));
  return (
    <span className={`mr-2 ${className}`}>
      <i
        className={`${iconName} font-weight-bold ${textPrimary ? 'text-primary' : ''} c-pointer`}
        style={iconStyle}
        onClick={(e) => {
          setToggled(true);
          e.stopPropagation();
        }
        }
        id={key}
      >
        <span >
          <Popover
            placement="top"
            isOpen={toggled}
            target={key}
            toggle={() => setToggled(!toggled)}
          >
            <PopoverBody >
              <span ref={ref} >
                {message ? <IntlMessages id={message} /> : customBody}
              </span>
            </PopoverBody>
          </Popover>
        </span>
      </i>
    </span>
  );
};

export default InfoIcon;
