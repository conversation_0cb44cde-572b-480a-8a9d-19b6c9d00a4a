/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import GraphContainer from '../../GraphContainer';
import GeoChartContainer from 'components/charts/GeoChartContainer';
import Chart from 'react-google-charts';
import { formatTime, getDay } from 'helpers/Utils';

const HourGraph = ({ data, nextUpdateTime }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [values, setValues] = useState([]);

  const options = {
    title: 'Showcase Insights for last 7 days',
    hAxis: {
      title: 'Day',
      ticks: [
        { v: 0, f: 'Sunday' },
        { v: 1, f: 'Monday' },
        { v: 2, f: 'Tuesday' },
        { v: 3, f: 'Wednesday' },
        { v: 4, f: 'Thursday' },
        { v: 5, f: 'Friday' },
        { v: 6, f: 'Saturday' }
      ]
    },
    vAxis: {
      title: 'Hour (24 Hrs format)',
      viewWindow: {
        min: 0,
        max: 23
      }
    },
    bubble: { textStyle: { fontSize: 11 } }
  };

  console.log('values', values);

  useEffect(() => {
    const formatData = () => {
      const result = [['ID', 'Day', 'Hour (24 Hrs format)', 'engagement']];

      data.forEach((item) => {
        const newdate = item.eventdate;
        const hour = Math.ceil(formatTime(item.eventdate, true));
        const dayWords = getDay(newdate);
        const dayNumber = getDay(newdate, true);
        const count = item?.engagements ?? 0;

        const check = result.findIndex(
          (f) => f[0] === dayWords && f[1] === dayNumber && f[2] === +hour
        );
        if (check && check !== -1) {
          const currentEngagement = count;
          const earlierEngagement = result[check][3];
          result[check] = [
            dayWords,
            dayNumber,
            +hour,
            +earlierEngagement + +currentEngagement
          ];
        } else {
          const row = [dayWords, dayNumber, +hour, count];
          result.push(row);
        }
      });

      setValues(data.length > 0 ? result : []);
      setIsLoading(false);
    };

    if (data) {
      formatData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  return (
    <>
      <GraphContainer
        loading={isLoading}
        // title="sm.top-country-map"
        lastUpdated={nextUpdateTime}
        dataExists={values?.length > 1}
        dataEmptyTitle="sm.no-views-by-hour"
        className="w-full-imp h-full-imp"
        dataComponent={
          <GeoChartContainer>
            {({ dimensions }) => {
              return (
                <Chart
                  chartType="BubbleChart"
                  height={dimensions.height}
                  width={dimensions.width}
                  data={values}
                  options={options}
                />
              );
            }}
          </GeoChartContainer>
        }
      />
    </>
  );
};

export default HourGraph;
