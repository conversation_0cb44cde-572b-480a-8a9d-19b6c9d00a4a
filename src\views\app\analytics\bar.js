/* eslint-disable prefer-rest-params */
import React from 'react';
// eslint-disable-next-line no-unused-vars
import { Chart as ChartJS } from 'chart.js/auto';
import { Bar } from 'react-chartjs-2';

const BarChart = ({
  label,
  labels,
  data,
  title,
  className,
  height = 100,
  width = 600
}) => {
  return (
    <Bar
      className={className}
      data={{
        labels,
        datasets: [
          {
            label,
            data,
            backgroundColor: '#F4E9F3',
            borderColor: '#94308A',
            borderWidth: 1
          }
        ]
      }}
      options={{
        plugins: {
          legend: {
            display: false
          },
          title: {
            display: true,
            text: title
          }
        },
        layout: {
          padding: 20
        },
        responsive: true,
        maintainAspectRatio: false
      }}
      height={height}
      width={width}
    />
  );
};

export default BarChart;
