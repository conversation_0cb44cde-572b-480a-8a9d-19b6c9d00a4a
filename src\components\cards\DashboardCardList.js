import React from 'react';
import { Row } from 'reactstrap';
import { Colxx } from 'components/common/CustomBootstrap';
import DashboardCard from './DashboardCard';
import NextUpdateTime from 'components/nextUpdateTime';

const DashboardCardList = ({ className, data, lastUpdated }) => {
  return (
    <Row className={className}>
      {data.map((item, index) => {
        return (
          // eslint-disable-next-line react/no-array-index-key
          <Colxx key={`icon_card_${index}`} style={{ width: '20%' }}>
            <DashboardCard {...item} index={index} className="dashboard-card" />
          </Colxx>
        );
      })}
      <div className="position-absolute mb-2 w-full" style={{ bottom: 0 }}>
        <NextUpdateTime time={lastUpdated} />
      </div>
    </Row>
  );
};

export default DashboardCardList;
