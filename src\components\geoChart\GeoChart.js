/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable import/no-extraneous-dependencies */
import React, { useEffect, useRef, useState } from 'react';
import { Chart } from 'react-google-charts';

function Geo<PERSON>hart({ data, options, small, dimensions }) {
  return (
    <div style={{ minHeight: '100%', width: '100%' }}>
      <Chart
        width={dimensions?.width}
        height={dimensions?.height}
        chartType="GeoChart"
        loader={<div>Loading Chart</div>}
        // data={[
        //   ['Country', 'Population'],
        //   ['India', 1088],
        // ]}
        // options={{
        //   colorAxis: { colors: ['#00853f', 'black', '#e31b23'] },
        // }}
        data={data}
        options={options}
        rootProps={{ 'data-testid': '1' }}
        style={{
          overflowX: 'auto'
        }}
      />
    </div>
  );
}

export default GeoChart;
