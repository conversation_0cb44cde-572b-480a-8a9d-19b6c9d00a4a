/* eslint-disable react/no-array-index-key */
import React from 'react';
import ShowcaseExpiry from '../ShowcaseExpiry';
import { removeHtmlTags } from 'helpers/Utils';
import DynamicQuill from 'components/DynamicQuill';

const Info = ({ data }) => {
  const metaData = (() => {
    const newData = data?.metadata;
    delete newData?.TagsAuto;
    delete newData?.TitleAuto;
    delete newData?.SummaryAuto;
    delete newData?.ChaptersVtt;
    delete newData?.VidAiDesc;
    delete newData?.DescribeVidAiDesc;
    return newData;
  })();

  return (
    <div className="d-flex flex-column position-relative">
      {data?.tags?.length !== 0 && (
        <div className="my-2">
          {data?.tags?.map((t) => (
            <span key={t} className="badge badge-pill mr-2 Tag-Expanded mb-2">
              {t}
            </span>
          ))}
        </div>
      )}
      <ShowcaseExpiry
        date={data?.endDateTime}
        isCol={false}
        style={{ position: 'absolute', right: 5, bottom: -10 }}
      />
      {data?.description && removeHtmlTags(data?.description)?.length > 0 && (
        <DynamicQuill description={data?.description} />
      )}
      {metaData && (
        <div>
          {Object.entries(metaData).map(([key, values]) => {
            return (
              <div
                key={key}
                className="d-flex flex-row align-items-center justify-content-start mb-2"
              >
                <div className="mr-2" style={{ fontSize: '15px' }}>
                  {key}
                </div>
                <div
                  className="d-flex flex-row align-items-center justify-content-start"
                  style={{ flexWrap: 'wrap' }}
                >
                  {Array.isArray(values)
                    ? values.map((value, index) => (
                        <Tag value={value} key={index} />
                      ))
                    : typeof values === 'string' && (
                        <Tag value={JSON.stringify(values)} />
                      )}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

const Tag = ({ value }) => {
  return (
    <span className="badge mt-1 badge-pill mr-2 Tag-Expanded">{value}</span>
  );
};

export default Info;
