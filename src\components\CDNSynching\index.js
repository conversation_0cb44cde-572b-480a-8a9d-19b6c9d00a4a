import IntlMessages from 'helpers/IntlMessages';
import useOnClickOutside from 'hooks/useOnClickOutSide';
import React, { useRef, useState } from 'react';
import { Button, Popover, PopoverBody } from 'reactstrap';

function CDNsynching({ id, style = {}, condition, className }) {
  const ref = useRef(null);
  const [toggled, setToggled] = useState(false);
  useOnClickOutside(ref, () => setToggled(false));

  const key = `Popover-${id}`;

  if (condition) {
    return (
      <span
        id={key}
        onMouseEnter={() => setToggled(true)}
        onMouseLeave={() => setToggled(false)}
        className={`d-flex flex-row align-items-center ${className}`}
        style={{ width: '60px', color: '#992288', ...style }}
      >
        <i className="simple-icon-globe" />
        <Button
          style={{
            padding: 0,
            margin: 0,
            background: 'transparent',
            border: 'none'
          }}
          className="btn-multiple-state show-spinner"
        >
          <span className="spinner d-inline-block">
            <span style={{ backgroundColor: '#992288' }} className="bounce1" />
            <span style={{ backgroundColor: '#992288' }} className="bounce2" />
            <span style={{ backgroundColor: '#992288' }} className="bounce3" />
          </span>
        </Button>
        <span ref={ref}>
          <Popover
            placement="bottom"
            isOpen={toggled}
            target={key}
            toggle={() => setToggled(!toggled)}
          >
            <PopoverBody>
              <IntlMessages id="cdn-info" />
            </PopoverBody>
          </Popover>
        </span>
      </span>
    );
  }
  return null;
}

export default CDNsynching;
