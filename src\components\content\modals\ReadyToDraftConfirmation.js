import React from 'react';
import { NavLink } from 'react-router-dom';
import { Button, Modal, ModalBody } from 'reactstrap';

const ReadyToDraftConfirmation = ({
  modalOpen,
  toggleModal,
  handleDraft,
  message,
}) => {
  return (
    <Modal isOpen={modalOpen} toggle={toggleModal} centered>
      <NavLink
        className="btn-link text-decoration-none d-flex align-items-center justify-content-end mt-2 mr-2"
        to="#"
        onClick={toggleModal}
      >
        <i className="simple-icon-close close-btn" />
      </NavLink>
      <ModalBody>
        <p>
          {message ||
            'Your Content will not be available externally if it is already shared / showcased individually or with Story.'}
        </p>
        <div className="d-flex justify-content-center">
          <Button
            color="primary"
            onClick={() => {
              handleDraft(true);
            }}
          >
            Confirm
          </Button>
          <Button
            color
            onClick={toggleModal}
            className="ml-2 badge-outline-primary"
          >
            Cancel
          </Button>
        </div>
      </ModalBody>
    </Modal>
  );
};

export default ReadyToDraftConfirmation;
