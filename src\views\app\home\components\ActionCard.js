import ShowcaseIcon from 'containers/navs/ShowcaseIcon';
import IntlMessages from 'helpers/IntlMessages';
import React from 'react';
import { Button, Card } from 'reactstrap';

const Actioncard = ({
  description,
  id,
  className,
  title,
  btnName,
  btnDisabled,
  onClick,
  singleCard,
  ...props
}) => {
  return (
    <Card {...props} className={`${className} action-card`}>
      <div
        className="pb-2 px-3 pt-3 "
        style={{ borderBottom: '1px solid silver' }}
      >
        <h5 className="text-align-left px-2">
          <span className="mr-1">
            {id === 'Invite' && <i className="simple-icon-people" />}
            {id === 'Showcase' && <ShowcaseIcon h={22} />}
          </span>
          <b>
            <IntlMessages id={title} />
          </b>
        </h5>
      </div>
      <div className="p-3 h-full bottom justify-content-between d-flex flex-column">
        <p
          style={{
            textAlign: 'left',
            marginBottom: 0,
            fontWeight: 600
          }}
        >
          <IntlMessages id={description} />
        </p>
        <Button
          color="primary"
          onClick={onClick}
          outline
          className="btn"
          btnDisabled={btnDisabled}
        >
          <IntlMessages id={btnName} />
        </Button>
      </div>
    </Card>
  );
};

export default Actioncard;
