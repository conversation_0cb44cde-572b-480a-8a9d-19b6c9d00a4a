/* eslint-disable no-unused-vars */
/* eslint-disable no-nested-ternary */
import React, { useEffect, useState } from 'react';
import IntlMessages from 'helpers/IntlMessages';
import { Colxx } from 'components/common/CustomBootstrap';
import ReactQuill from 'react-quill';
import {
  Row,
  Card,
  FormGroup,
  Label,
  UncontrolledDropdown,
  DropdownItem,
  DropdownToggle,
  DropdownMenu
} from 'reactstrap';
import 'react-quill/dist/quill.bubble.css';
import { NotificationManager } from 'components/common/react-notifications';
import { getCurrentUserDetails, removeHtmlTags } from 'helpers/Utils';
import { getSubscriptionDetails } from 'functions/api/billingApi';
import useQuery from 'hooks/useQuery';
import { sendSupportEmail } from 'functions/api/supportApi';
import LoadingButton from 'components/buttons/LoadingButton';
import { useLocation } from 'react-router-dom/cjs/react-router-dom.min';
// import MainLoader from 'components/loaders/MainLoader';

const types = [
  { id: 1, value: 'Inquiry' },
  { id: 2, value: 'Issue' },
  { id: 3, value: 'Billing' }
];

const HelpSupport = () => {
  const location = useLocation();
  const queryString = location.search;
  const { subscriptionId } = getCurrentUserDetails();
  const [submitting, setSubmitting] = useState(false);
  const queryId = useQuery().get('id');
  const queryTitle = useQuery().get('title');
  const queryExpiry = useQuery().get('expiry');
  const subs = useQuery().get('subs');
  const renewal = useQuery().get('renewal');

  console.log({
    queryId,
    queryTitle,
    queryExpiry,
    subs,
    renewal
  });

  const handleDescription = () => {
    if (queryId) {
      return `I need help with ${queryTitle} (${queryId})`;
    }
    if (subs) {
      return `Hi, kindly upgrade my current subscription to enable ${subs}.`;
    }
    if (queryExpiry) {
      return `<p>I would like to renew my subscription. Here is my <br/> <strong>Tenant ID:</strong> ${queryExpiry}</p>)`;
    }
    if (renewal) {
      return `<p>I would like to request an extension for my subscription. Here is my <br/> <strong>Subscription ID:</strong> ${subscriptionId}</p>`;
    }
    if (!renewal && queryString.length > 0) {
      return `<strong>Content ID:</strong> ${queryString.slice(1)}`;
    }
    return '';
  };

  const [description, setDescription] = useState(handleDescription());

  const handleSelect = () => {
    if (queryId) {
      return 'Issue';
    }
    if (subs || queryExpiry || renewal) {
      return 'Billing';
    }
    return 'Inquiry';
  };
  const [select, setSelect] = useState(handleSelect());

  useEffect(() => {
    setDescription(handleDescription());
    setSelect(handleSelect());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [queryId, subs, queryExpiry, subscriptionId, queryTitle, renewal]);

  const handleSubmit = async () => {
    setSubmitting(true);
    const resp = await sendSupportEmail({
      message: removeHtmlTags(description),
      reqType: select
    });
    if (resp.isError) {
      NotificationManager.error(
        <IntlMessages id="help-error" />,
        <IntlMessages id="req.failed" />,
        3000,
        null,
        null,
        ''
      );
    } else {
      NotificationManager.success(
        <IntlMessages id="help-success" />,
        <IntlMessages id="req.success" />,
        3000,
        null,
        null,
        ''
      );
      setDescription('');
    }
    setSubmitting(false);
  };

  return (
    <Row>
      <Colxx xxs="12" className="mb-4">
        <form>
          <div className="d-flex justify-content-center">
            <h1>
              <IntlMessages id="menu.help-support" />
            </h1>
          </div>
          <div className="d-flex justify-content-center mb-4 mt-5">
            <UncontrolledDropdown>
              <DropdownToggle caret color="primary" size="sm" outline>
                <span className="name">{select}</span>
              </DropdownToggle>
              <DropdownMenu className="mt-3 center-help">
                {types.map((d) => (
                  <DropdownItem
                    className="text-center"
                    key={d.id}
                    onClick={() => setSelect(d.value)}
                  >
                    {d.value}
                  </DropdownItem>
                ))}
              </DropdownMenu>
            </UncontrolledDropdown>
          </div>
          <div className="d-flex justify-content-center ">
            <Card className="space-media-desc-editor mb-5">
              <div className="d-flex">
                <FormGroup
                  className="form-group has-float-label mb-0"
                  style={{ flex: 1, width: '100%' }}
                >
                  <Label for="Industry">
                    <IntlMessages id="help.desc" />
                    <span style={{ color: '#922c88' }}> *</span>
                  </Label>
                  <ReactQuill
                    className="react-quill w-full-imp"
                    theme="bubble"
                    value={description}
                    onChange={(value) => setDescription(value)}
                  />
                </FormGroup>
              </div>
              <p className="m-2 ml-auto mr-5">
                {description.replace(/<[^>]*>?/gm, '').length <= 200 ? (
                  <IntlMessages id="help-max-char-lim" />
                ) : (
                  <IntlMessages id="help-description-max" />
                )}
              </p>
            </Card>
          </div>

          <Colxx className="d-flex justify-content-center mb-3 ">
            <LoadingButton
              loading={submitting}
              text="help.submit"
              onClick={() => {
                if (removeHtmlTags(description).length < 5) {
                  NotificationManager.warning(
                    <IntlMessages id="help-description" />,
                    '',
                    3000,
                    null,
                    null,
                    ''
                  );
                } else if (removeHtmlTags(description).length > 1000) {
                  NotificationManager.warning(
                    <IntlMessages id="help-description-max" />,
                    '',
                    3000,
                    null,
                    null,
                    ''
                  );
                } else {
                  handleSubmit();
                }
              }}
            />
          </Colxx>
        </form>
      </Colxx>
    </Row>
  );
};

export default HelpSupport;
