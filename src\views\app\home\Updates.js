/* eslint-disable react/no-danger */
/* eslint-disable no-unused-vars */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-nested-ternary */
import { ListAllSeen, ListLoading } from 'components/list';
// import { fetchSystemNotifications } from 'functions/api';
import { formatDate } from 'helpers/Utils';
import useQuery from 'hooks/useQuery';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Card } from 'reactstrap';
import InfiniteScroll from 'react-infinite-scroll-component';
import { connect } from 'react-redux';
import { fetchNotifications } from 'redux/actions';
import DynamicQuill from 'components/DynamicQuill';

function UpdateTab({
  notifications,
  isInitialFetching,
  hasMore,
  skipCount,
  maxResultCount,
  fetchNotificationsAction
}) {
  const pathname = useQuery().get('tabIndex');
  return (
    <div className="d-flex flex-column w-full">
      {isInitialFetching ? (
        <div className="notificationLoading">
          <div className="icon" />
        </div>
      ) : (
        <InfiniteScroll
          dataLength={notifications?.length}
          next={() => {
            fetchNotificationsAction({
              skipCount,
              maxResultCount
            });
            // fetchAllNotification(false);
          }}
          hasMore={hasMore}
          loader={<ListLoading />}
          endMessage={<ListAllSeen />}
        >
          {notifications?.map((d) => (
            <NotificationCard
              key={d.creationTime}
              title={d.title}
              description={d.desc}
              creationTime={d.creationTime}
              pathname={pathname}
              embedcode={d.embedcode}
              mediatype={d.mediatype}
            />
          ))}
          {!notifications && (
            <p style={{ textAlign: 'center' }}>No Notifications</p>
          )}
        </InfiniteScroll>
      )}
    </div>
  );
}

const mapStateToProps = ({ settings }) => {
  const {
    notifications,
    isInitialFetching,
    isFetchingNotification,
    hasMore,
    skipCount,
    maxResultCount
  } = settings;
  return {
    notifications,
    isInitialFetching,
    isFetchingNotification,
    hasMore,
    skipCount,
    maxResultCount
  };
};

export default connect(mapStateToProps, {
  fetchNotificationsAction: fetchNotifications
})(UpdateTab);

const NotificationCard = ({
  title,
  description,
  pathname,
  creationTime,
  embedcode,
  mediatype
}) => {
  const isImage = useMemo(() => +mediatype === 3, [mediatype]);
  const [isLoading, setIsLoading] = useState(true);
  const [src, setSrc] = useState(null);
  const embedRef = useRef(null);

  // eslint-disable-next-line consistent-return
  useEffect(() => {
    if (!isImage && embedRef) {
      const iframeElement = embedRef?.current?.querySelector('iframe');
      setSrc(iframeElement?.src);
    }
  }, [embedcode, isImage]);

  return (
    <Card
      className="notification-card border-radius border-10 mb-3"
      style={{
        border: Number(pathname) === Number(creationTime) && '1px solid #922c88'
      }}
    >
      <div className="header">
        <div className="left">
          <b>{title}</b>
        </div>
        <div className="right">
          <p
            className="mb-0 text-right"
            style={{ right: 20, fontSize: '12px' }}
          >
            {formatDate(new Date(creationTime))}
          </p>
        </div>
      </div>
      {description && (
        <div className="para">
          <DynamicQuill description={description} />
        </div>
      )}
      {embedcode && (
        <div>
          {isLoading ? (
            <div className="dataloading-overlay detailed-content">
              <div className="dataloading" />
            </div>
          ) : null}
          {+mediatype === 3 ? (
            <>
              <div className="d-flex align-items-center justify-content-center">
                <img
                  src={embedcode}
                  alt="embed-content"
                  style={{ objectFit: 'cover' }}
                  className={isLoading ? 'd-none' : 'd-block'}
                  onLoad={() => setIsLoading(false)}
                />
              </div>
            </>
          ) : (
            <>
              {src ? (
                <iframe
                  src={src}
                  title={title}
                  allowFullScreen
                  className={isLoading && 'd-none'}
                  onLoad={() => {
                    setIsLoading(false);
                  }}
                />
              ) : (
                <div
                  ref={embedRef}
                  className="iframe-border-0"
                  // eslint-disable-next-line react/no-danger
                  dangerouslySetInnerHTML={{
                    __html: embedcode
                  }}
                />
              )}
            </>
          )}
        </div>
      )}
    </Card>
  );
};
