/* eslint-disable react/no-access-state-in-setstate */
/* eslint-disable no-unused-vars */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable react/no-array-index-key */
import React, { Component, useState } from 'react';
import { connect } from 'react-redux';
import ReactDOM from 'react-dom';
import { Card, Nav, NavItem } from 'reactstrap';
import { NavLink, withRouter } from 'react-router-dom';
import classnames from 'classnames';
import PerfectScrollbar from 'react-perfect-scrollbar';
import {
  setContainerClassnames,
  addContainerClassname,
  changeDefaultClassnames,
  changeSelectedMenuHasSubItems,
  logoutUser
} from 'redux/actions';
import { adminRoot, SLACK_LINK } from 'constants/defaultValues';
import {
  formatDateNTime,
  getCurrentUserDetails,
  getExpiryMessage,
  getLastVisitedSpaceId,
  getTeamUserRoleType,
  RoleTypes
} from 'helpers/Utils';
import <PERSON>barIconComp from './SidebarIconComp';
import TopnavDarkSwitch from '../Topnav.DarkSwitch';
import { subscriptionChecker } from 'helpers/subscriptionChecker';
import { NotificationManager } from 'components/common/react-notifications';
import IntlMessages from 'helpers/IntlMessages';
import UserMenu from './UserMenu';
import ItemContainer from './ItemContainer';
import NotificationMenu from './NotificationMenu';
import QuickUpload from './QuickUpload';
import VoiceNote from './VoiceNote';
import VideoMessaging from './VideoMessaging';
import RecordingMenu from './RecordingMenu';

const getAllMenuItems = () => {
  const defaultSpaceId = getLastVisitedSpaceId();
  return [
    // {
    //   id: 'Showcase',
    //   label: 'menu.showcase',
    //   icon: null,
    //   to: `${adminRoot}/showcase`,
    //   maxRole: RoleTypes.VIEWER
    // },
    {
      id: 'home',
      label: 'menu.home',
      icon: 'iconsminds-shop-4',
      to: `${adminRoot}/home`,
      maxRole: RoleTypes.CONSUMER
    },
    {
      id: 'dashboard',
      icon: 'iconsminds-monitor-analytics',
      label: 'menu.dashboard',
      to: `${adminRoot}/dashboard`,
      maxRole: RoleTypes.ADMIN
    },
    {
      id: 'spaces',
      icon: 'iconsminds-air-balloon-1',
      label: 'menu.spaces',
      to: `${adminRoot}/spaces`,
      maxRole: RoleTypes.VIEWER
    },
    {
      id: 'review',
      icon: null,
      label: 'menu.review',
      to: `${adminRoot}/review`,
      maxRole: RoleTypes.CONTENT_MANAGER,
      subscription: {
        workFlowEnabled: true
      }
    },
    {
      id: 'videos',
      icon: 'simple-icon-camrecorder',
      label: 'menu.videos',
      to: `${adminRoot}/spaces/space/${defaultSpaceId}/manage-content?tabIndex=1`,
      maxRole: RoleTypes.VIEWER
    },
    {
      id: 'audios',
      icon: 'simple-icon-earphones',
      label: 'menu.audios',
      to: `${adminRoot}/spaces/space/${defaultSpaceId}/manage-content?tabIndex=2`,
      maxRole: RoleTypes.VIEWER
    },
    {
      id: 'images',
      icon: 'simple-icon-picture',
      label: 'menu.images',
      to: `${adminRoot}/spaces/space/${defaultSpaceId}/manage-content?tabIndex=3`,
      maxRole: RoleTypes.VIEWER
    },
    {
      id: 'media-player',
      icon: null,
      label: 'menu.player',
      to: `${adminRoot}/spaces/space/${defaultSpaceId}/media-player`,
      maxRole: RoleTypes.CONTENT_MANAGER
    },
    {
      id: 'Stories',
      icon: null,
      label: 'menu.story',
      to: `${adminRoot}/spaces/space/${defaultSpaceId}/stories`,
      maxRole: RoleTypes.VIEWER
    }
    // {
    //   id: 'feedback',
    //   icon: 'iconsminds-speach-bubble-asking',
    //   label: 'menu.feature',
    //   to: `https://innerloop.canny.io/`,
    //   maxRole: RoleTypes.VIEWER,
    //   newWindow: true
    // },
    // {
    //   id: 'help-support',
    //   icon: 'iconsminds-support',
    //   label: 'menu.help-support',
    //   to: `${adminRoot}/help-support`,
    //   maxRole: RoleTypes.VIEWER
    // }
  ];
};

class Sidebar extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedParentMenu: '',
      viewingParentMenu: '',
      collapsedMenus: [],
      hovered: false,
      useWiderSidebar: true
      // useWiderSidebar: window?.innerWidth < 768
    };
  }

  // eslint-disable-next-line react/sort-comp
  handleWindowResize = (event) => {
    // this.setState((prev) => {
    //   return {
    //     ...prev,
    //     useWiderSidebar: window?.innerWidth < 768
    //   };
    // });
    if (event && !event.isTrusted) {
      return;
    }
    const { containerClassnames } = this.props;
    const nextClasses = this.getMenuClassesForResize(containerClassnames);
    // eslint-disable-next-line react/destructuring-assignment
    this.props.setContainerClassnames(
      0,
      nextClasses.join(' '),
      // eslint-disable-next-line react/destructuring-assignment
      this.props.selectedMenuHasSubItems
    );
  };

  handleDocumentClick = (e) => {
    const container = this.getContainer();
    let isMenuClick = false;
    if (
      e.target &&
      e.target.classList &&
      (e.target.classList.contains('menu-button') ||
        e.target.classList.contains('menu-button-mobile'))
    ) {
      isMenuClick = true;
    } else if (
      e.target.parentElement &&
      e.target.parentElement.classList &&
      (e.target.parentElement.classList.contains('menu-button') ||
        e.target.parentElement.classList.contains('menu-button-mobile'))
    ) {
      isMenuClick = true;
    } else if (
      e.target.parentElement &&
      e.target.parentElement.parentElement &&
      e.target.parentElement.parentElement.classList &&
      (e.target.parentElement.parentElement.classList.contains('menu-button') ||
        e.target.parentElement.parentElement.classList.contains(
          'menu-button-mobile'
        ))
    ) {
      isMenuClick = true;
    }
    if (container.contains(e.target) || container === e.target || isMenuClick) {
      return;
    }
    this.setState({
      viewingParentMenu: ''
    });
    this.toggle();
  };

  getMenuClassesForResize = (classes) => {
    const { menuHiddenBreakpoint, subHiddenBreakpoint } = this.props;
    let nextClasses = classes.split(' ').filter((x) => x !== '');
    const windowWidth = window.innerWidth;
    if (windowWidth < menuHiddenBreakpoint) {
      nextClasses.push('menu-mobile');
    } else if (windowWidth < subHiddenBreakpoint) {
      nextClasses = nextClasses.filter((x) => x !== 'menu-mobile');
      if (
        nextClasses.includes('menu-default') &&
        !nextClasses.includes('menu-sub-hidden')
      ) {
        nextClasses.push('menu-sub-hidden');
      }
    } else {
      nextClasses = nextClasses.filter((x) => x !== 'menu-mobile');
      if (
        nextClasses.includes('menu-default') &&
        nextClasses.includes('menu-sub-hidden')
      ) {
        nextClasses = nextClasses.filter((x) => x !== 'menu-sub-hidden');
      }
    }
    return nextClasses;
  };

  getContainer = () => {
    // eslint-disable-next-line react/no-find-dom-node
    return ReactDOM.findDOMNode(this);
  };

  toggle = () => {
    const hasSubItems = this.getIsHasSubItem();
    // eslint-disable-next-line react/destructuring-assignment
    this.props.changeSelectedMenuHasSubItems(hasSubItems);
    const { containerClassnames, menuClickCount } = this.props;
    const currentClasses = containerClassnames
      ? containerClassnames.split(' ').filter((x) => x !== '')
      : '';
    let clickIndex = -1;

    if (!hasSubItems) {
      if (
        currentClasses.includes('menu-default') &&
        (menuClickCount % 4 === 0 || menuClickCount % 4 === 3)
      ) {
        clickIndex = 1;
      } else if (
        currentClasses.includes('menu-sub-hidden') &&
        (menuClickCount === 2 || menuClickCount === 3)
      ) {
        clickIndex = 0;
      } else if (
        currentClasses.includes('menu-hidden') ||
        currentClasses.includes('menu-mobile')
      ) {
        clickIndex = 0;
      }
    } else if (
      currentClasses.includes('menu-sub-hidden') &&
      menuClickCount === 3
    ) {
      clickIndex = 2;
    } else if (
      currentClasses.includes('menu-hidden') ||
      currentClasses.includes('menu-mobile')
    ) {
      clickIndex = 0;
    }
    if (clickIndex >= 0) {
      // eslint-disable-next-line react/destructuring-assignment
      this.props.setContainerClassnames(
        clickIndex,
        containerClassnames,
        hasSubItems
      );
    }
  };

  handleProps = () => {
    this.addEvents();
  };

  addEvents = () => {
    ['click', 'touchstart', 'touchend'].forEach((event) =>
      document.addEventListener(event, this.handleDocumentClick, true)
    );
  };

  removeEvents = () => {
    ['click', 'touchstart', 'touchend'].forEach((event) =>
      document.removeEventListener(event, this.handleDocumentClick, true)
    );
  };

  setSelectedLiActive = (callback) => {
    const oldli = document.querySelector('.sub-menu  li.active');
    if (oldli != null) {
      oldli.classList.remove('active');
    }

    const oldliSub = document.querySelector('.third-level-menu  li.active');
    if (oldliSub != null) {
      oldliSub.classList.remove('active');
    }

    /* set selected parent menu */
    const selectedSublink = document.querySelector(
      '.third-level-menu  a.active'
    );
    if (selectedSublink != null) {
      selectedSublink.parentElement.classList.add('active');
    }

    const selectedlink = document.querySelector('.sub-menu  a.active');
    if (selectedlink != null) {
      selectedlink.parentElement.classList.add('active');
      this.setState(
        {
          selectedParentMenu:
            selectedlink.parentElement.parentElement.getAttribute('data-parent')
        },
        callback
      );
    } else {
      const selectedParentNoSubItem = document.querySelector(
        '.main-menu  li a.active'
      );
      if (selectedParentNoSubItem != null) {
        this.setState(
          {
            selectedParentMenu:
              selectedParentNoSubItem.getAttribute('data-flag')
          },
          callback
        );
      } else if (this.state.selectedParentMenu === '') {
        const menuItems = getAllMenuItems();
        this.setState(
          {
            selectedParentMenu: menuItems[0].id
          },
          callback
        );
      }
    }
  };

  setHasSubItemStatus = () => {
    const hasSubmenu = this.getIsHasSubItem();
    // eslint-disable-next-line react/destructuring-assignment
    this.props.changeSelectedMenuHasSubItems(hasSubmenu);
    this.toggle();
  };

  getIsHasSubItem = () => {
    const { selectedParentMenu } = this.state;
    const menuItems = getAllMenuItems();
    const menuItem = menuItems.find((x) => x.id === selectedParentMenu);
    if (menuItem)
      return !!(menuItem && menuItem.subs && menuItem.subs.length > 0);
    return false;
  };

  handleNewVersion = () => {
    const version = JSON.parse(localStorage.getItem('version'));
    if (version?.refreshNeeded) {
      localStorage.setItem(
        'version',
        JSON.stringify({
          ...version,
          refreshNeeded: false
        })
      );
      NotificationManager.warning(<IntlMessages id="new-version" />, '', 10000);
    }
  };

  // eslint-disable-next-line react/sort-comp
  componentDidUpdate(prevProps) {
    // eslint-disable-next-line react/destructuring-assignment
    if (this.props.location.pathname !== prevProps.location.pathname) {
      this.setSelectedLiActive(this.setHasSubItemStatus);

      window.scrollTo(0, 0);
    }
    this.handleProps();
  }

  componentDidMount() {
    window.addEventListener('resize', this.handleWindowResize);
    this.handleWindowResize();
    this.handleNewVersion();
    this.handleProps();
    this.setSelectedLiActive(this.setHasSubItemStatus);
  }

  componentWillUnmount() {
    this.removeEvents();
    window.removeEventListener('resize', this.handleWindowResize);
  }

  openSubMenu = (e, menuItem) => {
    const selectedParent = menuItem.id;
    const hasSubMenu = menuItem.subs && menuItem.subs.length > 0;
    // eslint-disable-next-line react/destructuring-assignment
    this.props.changeSelectedMenuHasSubItems(hasSubMenu);
    if (!hasSubMenu) {
      this.setState({
        viewingParentMenu: selectedParent,
        selectedParentMenu: selectedParent
      });
      this.toggle();
    } else {
      e.preventDefault();

      const { containerClassnames, menuClickCount } = this.props;
      const currentClasses = containerClassnames
        ? containerClassnames.split(' ').filter((x) => x !== '')
        : '';

      if (!currentClasses.includes('menu-mobile')) {
        if (
          currentClasses.includes('menu-sub-hidden') &&
          (menuClickCount === 2 || menuClickCount === 0)
        ) {
          // eslint-disable-next-line react/destructuring-assignment
          this.props.setContainerClassnames(3, containerClassnames, hasSubMenu);
        } else if (
          currentClasses.includes('menu-hidden') &&
          (menuClickCount === 1 || menuClickCount === 3)
        ) {
          // eslint-disable-next-line react/destructuring-assignment
          this.props.setContainerClassnames(2, containerClassnames, hasSubMenu);
        } else if (
          currentClasses.includes('menu-default') &&
          !currentClasses.includes('menu-sub-hidden') &&
          (menuClickCount === 1 || menuClickCount === 3)
        ) {
          // eslint-disable-next-line react/destructuring-assignment
          this.props.setContainerClassnames(0, containerClassnames, hasSubMenu);
        }
      } else {
        // eslint-disable-next-line react/destructuring-assignment
        this.props.addContainerClassname(
          'sub-show-temporary',
          containerClassnames
        );
      }
      this.setState({
        viewingParentMenu: selectedParent
      });
    }
  };

  toggleMenuCollapse = (e, menuKey) => {
    e.preventDefault();

    const { collapsedMenus } = this.state;
    if (collapsedMenus.indexOf(menuKey) > -1) {
      this.setState({
        collapsedMenus: collapsedMenus.filter((x) => x !== menuKey)
      });
    } else {
      collapsedMenus.push(menuKey);
      this.setState({
        collapsedMenus
      });
    }
    return false;
  };

  // eslint-disable-next-line no-shadow
  filteredList = (menuItems, useWiderSidebar) => {
    const { currentUser } = this.props;
    if (currentUser) {
      return menuItems.filter(
        (x) =>
          ((x.roles && x.roles.includes(currentUser.role)) || !x.roles) &&
          (x.id !== 'Showcase' || useWiderSidebar)
      );
    }
    return menuItems;
  };

  render() {
    const {
      selectedParentMenu,
      viewingParentMenu,
      collapsedMenus,
      hovered,
      // menuItems,
      useWiderSidebar
    } = this.state;
    const {
      expiredSubscription,
      subscriptionDetails,
      roleType: localUserRoleType,
      daysUntillExpiry,
      history,
      notifications,
      isEvent,
      isSideBarVisible,
      subscriptionExpiryDate,
      recordingProps,
      recordingEnabled,
      logoutUserAction
    } = this.props;

    const menuItems = getAllMenuItems();

    return (
      <div
        className="sidebar"
        // style={{ marginTop: isEvent ? 35 : 0 }}
      >
        <div
          className="main-menu"
          style={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between'
          }}
        >
          <ItemContainer
            first={
              <div className="scroll">
                <PerfectScrollbar
                  options={{ suppressScrollX: true, wheelPropagation: false }}
                >
                  {!expiredSubscription &&
                    localUserRoleType <= RoleTypes.CONTRIBUTOR && (
                      <QuickUpload />
                    )}
                  {!expiredSubscription &&
                    recordingEnabled &&
                    localUserRoleType <= RoleTypes.CONTRIBUTOR && (
                      <RecordingMenu recordingProps={recordingProps} />
                    )}
                  <Nav vertical className="list-unstyled">
                    {menuItems &&
                      this.filteredList(menuItems, useWiderSidebar).map(
                        (item) => {
                          return (
                            <>
                              <NavItem
                                key={item.id}
                                className={classnames({
                                  active:
                                    (selectedParentMenu === item.id &&
                                      viewingParentMenu === '') ||
                                    viewingParentMenu === item.id
                                })}
                                aria-disabled={
                                  expiredSubscription &&
                                  item.id !== 'help-support' &&
                                  item.id !== 'home'
                                }
                              >
                                {item.newWindow
                                  ? subscriptionChecker(
                                      subscriptionDetails,
                                      item?.subscription
                                    ) && (
                                      <a
                                        href={item.to}
                                        rel="noopener noreferrer"
                                        target="_blank"
                                      >
                                        <SidebarIconComp
                                          item={item}
                                          active={
                                            selectedParentMenu === item.id ||
                                            hovered === item.id
                                          }
                                        />
                                      </a>
                                    )
                                  : item?.maxRole >= localUserRoleType
                                  ? subscriptionChecker(
                                      subscriptionDetails,
                                      item?.subscription
                                    ) && (
                                      <NavLink
                                        to={item.to}
                                        onClick={(e) =>
                                          this.openSubMenu(e, item)
                                        }
                                        data-flag={item.id}
                                        style={{ tranform: 'scale(0.7)' }}
                                        onMouseEnter={() =>
                                          this.setState((prev) => {
                                            return {
                                              ...prev,
                                              hovered: item.id
                                            };
                                          })
                                        }
                                        onMouseLeave={() =>
                                          this.setState((prev) => {
                                            return {
                                              ...prev,
                                              hovered: null
                                            };
                                          })
                                        }
                                      >
                                        <SidebarIconComp
                                          item={item}
                                          active={
                                            selectedParentMenu === item.id ||
                                            hovered === item.id
                                          }
                                        />
                                      </NavLink>
                                    )
                                  : null}
                              </NavItem>
                              {(item.id === 'Stories' ||
                                item.id === 'review') && (
                                <div
                                  className="d-flex align-items-center justify-content-center separate-border-cont"
                                  style={{ height: '30px' }}
                                />
                              )}
                            </>
                          );
                        }
                      )}
                  </Nav>
                </PerfectScrollbar>
              </div>
            }
            second={
              <div className="p-2">
                <RoleDecidedOption
                  currentRole={localUserRoleType}
                  maxRole={RoleTypes.VIEWER}
                >
                  <Slack
                    item={{
                      id: 'slack',
                      label: 'menu.slack',
                      to: SLACK_LINK,
                      newWindow: true
                    }}
                  />
                </RoleDecidedOption>
                <RoleDecidedOption
                  currentRole={localUserRoleType}
                  maxRole={RoleTypes.VIEWER}
                >
                  <TalkToAvi
                    item={{
                      id: 'meet',
                      label: 'menu.meet',
                      to: `/meet`,
                      newWindow: true
                    }}
                  />
                </RoleDecidedOption>
                <RoleDecidedOption
                  currentRole={localUserRoleType}
                  maxRole={RoleTypes.CONSUMER}
                >
                  <Feedback
                    item={{
                      id: 'feedback',
                      icon: 'iconsminds-speach-bubble-asking',
                      label: 'menu.feature',
                      to: `https://innerloop.canny.io/`,
                      maxRole: RoleTypes.CONSUMER,
                      newWindow: true
                    }}
                  />
                </RoleDecidedOption>
                <RoleDecidedOption
                  currentRole={localUserRoleType}
                  maxRole={RoleTypes.CONSUMER}
                >
                  <Help
                    item={{
                      id: 'help-support',
                      icon: 'iconsminds-support',
                      label: 'menu.help-support',
                      to: `${adminRoot}/help-support`
                    }}
                  />
                </RoleDecidedOption>
                <RoleDecidedOption
                  currentRole={localUserRoleType}
                  maxRole={RoleTypes.CONSUMER}
                >
                  <NotificationMenu
                    history={history}
                    notifications={notifications}
                    item={{
                      id: 'notification',
                      icon: 'simple-icon-bell',
                      label: 'Notification',
                      to: `${adminRoot}/home`
                    }}
                  />
                </RoleDecidedOption>

                <UserMenu
                  roleType={localUserRoleType}
                  handleLogout={() => {
                    logoutUserAction(history);
                  }}
                />
              </div>
            }
          />
        </div>
        <Ribbon
          daysUntillExpiry={daysUntillExpiry}
          history={history}
          roleType={localUserRoleType}
          isSideBarVisible={isSideBarVisible}
          subscriptionExpiryDate={subscriptionExpiryDate}
        />
      </div>
    );
  }
}

const RoleDecidedOption = ({ children, currentRole, maxRole }) => {
  if (currentRole <= maxRole) {
    return children;
  }
  return null;
};

const Feedback = ({ item }) => {
  return (
    <div className="p-2">
      <a href={item.to} rel="noopener noreferrer" target="_blank">
        <SidebarIconComp item={item} small={false} />
      </a>
    </div>
  );
};

const Slack = ({ item }) => {
  return (
    <div className="p-2">
      <a href={item.to} rel="noopener noreferrer" target="_blank">
        <SidebarIconComp item={item} small={false} />
      </a>
    </div>
  );
};

const TalkToAvi = ({ item }) => {
  return (
    <div className="p-2">
      <a href={item.to} rel="noopener noreferrer" target="_blank">
        <SidebarIconComp item={item} small={false} />
      </a>
    </div>
  );
};

const Help = ({ item }) => {
  return (
    <div className="p-2">
      <NavLink to={item.to} data-flag={item.id}>
        <SidebarIconComp item={item} small={false} />
      </NavLink>
    </div>
  );
};

const RenewNow = ({ history }) => {
  return (
    <span
      style={{ fontWeight: 600 }}
      className="c-pointer text-primary "
      onClick={() => {
        history.push('/app/help-support?renewal=true');
      }}
    >
      Renew Now
    </span>
  );
};

const Ribbon = ({
  daysUntillExpiry,
  history,
  isSideBarVisible,
  subscriptionExpiryDate,
  roleType
}) => {
  const message = (() => {
    const m = getExpiryMessage(daysUntillExpiry);
    if (m) {
      return m;
    }
    return `Your subscription will be expired on ${formatDateNTime(
      subscriptionExpiryDate
    )}`;
  })();

  return (
    <Card
      style={{
        paddingLeft: !isSideBarVisible ? '175px' : '0px'
      }}
      className="ribbon d-flex flex-row align-items-center justify-content-between text-align-center"
    >
      <div className="px-2">
        {message} {daysUntillExpiry.days <= 7 && <RenewNow history={history} />}
      </div>
      <div className="px-2">
        Your Highest Role for this Account is :{' '}
        <span>
          <i className="simple-icon-user" />{' '}
          <b>{getTeamUserRoleType(roleType)}</b>
        </span>
      </div>
    </Card>
  );
};

const mapStateToProps = ({ menu, authUser, subscription }) => {
  const {
    containerClassnames,
    subHiddenBreakpoint,
    menuHiddenBreakpoint,
    menuClickCount,
    selectedMenuHasSubItems
  } = menu;

  const { currentUser } = authUser;
  return {
    containerClassnames,
    subHiddenBreakpoint,
    menuHiddenBreakpoint,
    menuClickCount,
    selectedMenuHasSubItems,
    daysUntillExpiry: subscription?.daysUntillExpiry,
    subscriptionExpiryDate: subscription?.allData?.endDateTime,
    currentUser
  };
};

export default withRouter(
  connect(mapStateToProps, {
    setContainerClassnames,
    addContainerClassname,
    changeDefaultClassnames,
    changeSelectedMenuHasSubItems,
    logoutUserAction: logoutUser
  })(Sidebar)
);
