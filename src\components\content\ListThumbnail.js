/* eslint-disable no-unused-vars */
import React, { useState } from 'react';
import { CardImg } from 'reactstrap';
import { NavLink } from 'react-router-dom';
import { getDownloadUrl } from 'functions/api/spacesApi';
import {
  getStatusText,
  isDownloadAllowed,
  isYoutubeORVimeoSource
} from 'helpers/Utils';
import { useSelector } from 'react-redux';

const ListThumbnail = ({
  isCard = false,
  handleEdit,
  isPreviewValid,
  previewSrc,
  icon,
  // fileName,
  // id,
  // spaceId,
  isReady,
  content,
  roleType
}) => {
  const { id, spaceId, fileName } = content;
  const previewExists = previewSrc && isPreviewValid;
  const [isImgLoading, setImgLoading] = useState(true);
  const [isDownloading, setIsDownloading] = useState(false);
  const { allowDownload } = useSelector((state) => state.subscription);

  const handleDownload = async () => {
    try {
      setIsDownloading(true);
      const data = await getDownloadUrl(spaceId, id);
      if (!data.isError && data.data) {
        window.open(data.data);
      }
      setIsDownloading(false);
    } catch (error) {
      setIsDownloading(false);
    }
  };

  const showDownloadButton = isDownloadAllowed({
    allowDownload,
    isYtVimeoVideo: isYoutubeORVimeoSource(content?.contentSource),
    roleType,
    status: content.status
  });

  if (isCard) {
    return (
      <span className="overflow-hidden border-5 mt-2 w-full position-relative">
        <NavLink to="#" className="d-flex h-full w-full" onClick={handleEdit}>
          {previewExists ? (
            <CardImg
              top
              alt={fileName || 'media-thumbnail'}
              src={previewSrc}
              onLoad={() => setImgLoading(true)}
              onError={() => setImgLoading(false)}
              className="img-thumbnail overflow-hidden w-full-imp"
            />
          ) : (
            <i className={`${icon} card-default-icon-style`} />
          )}
        </NavLink>
        {showDownloadButton && (
          <DownloadIcon
            previewExists={isImgLoading ? false : previewExists}
            isDownloading={isDownloading}
            disabled={isDownloading}
            onClick={handleDownload}
            condition={isReady}
          />
        )}
      </span>
    );
  }
  return (
    <>
      <span onClick={handleEdit} className=" d-flex centered ">
        {previewExists ? (
          <img
            alt={fileName || 'media-thumbnail'}
            src={previewSrc}
            onLoad={() => setImgLoading(true)}
            onError={() => setImgLoading(false)}
            height={80}
            width="100%"
            style={{
              objectFit: 'contain'
            }}
          />
        ) : (
          <i
            style={{ height: 80, fontSize: 40 }}
            className={`${icon} d-flex centered`}
          />
        )}
      </span>
      {showDownloadButton && (
        <DownloadIcon
          disabled={isDownloading}
          condition={isReady}
          isDownloading={isDownloading}
          previewExists={isImgLoading ? false : previewExists}
          onClick={handleDownload}
        />
      )}
    </>
  );
};

const DownloadIcon = ({
  previewExists = true,
  disabled,
  onClick,
  isDownloading,
  condition,
  ...props
}) => {
  if (condition) {
    return (
      <span
        {...props}
        onClick={(e) => {
          e.stopPropagation();
          onClick();
        }}
        aria-disabled={disabled}
        className={`position-absolute ${
          isDownloading ? '' : 'centered'
        }  p-1 cursor-pointer`}
        style={{
          background: '#ffffff36',
          borderRadius: 4,
          bottom: 5,
          right: 5,
          width: 40,
          height: 40
        }}
      >
        {isDownloading ? (
          <span className="btn-multiple-state show-spinner d-flex align-items-center justify-content-center h-full">
            <span className="spinner d-inline-block">
              <span
                style={{ backgroundColor: '#992288' }}
                className="bounce1"
              />
              <span
                style={{ backgroundColor: '#992288' }}
                className="bounce2"
              />
              <span
                style={{ backgroundColor: '#992288' }}
                className="bounce3"
              />
            </span>
          </span>
        ) : (
          <svg width="26" height="26" viewBox="0 0 24 24" fill="none">
            <path
              d="M12 2a1 1 0 0 1 1 1v10.586l2.293-2.293a1 1 0 0 1 1.414 1.414l-4 4a1 1 0 0 1-1.414 0l-4-4a1 1 0 1 1 1.414-1.414L11 13.586V3a1 1 0 0 1 1-1M5 17a1 1 0 0 1 1 1v2h12v-2a1 1 0 1 1 2 0v2a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2v-2a1 1 0 0 1 1-1"
              fill={previewExists ? '#fff' : '#000'}
            />
          </svg>
        )}
      </span>
    );
  }
  return null;
};

export default ListThumbnail;
