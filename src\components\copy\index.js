/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable no-undef */
import { NotificationManager } from 'components/common/react-notifications';
import IntlMessages from 'helpers/IntlMessages';
import { copyStringToClipboard } from 'helpers/Utils';
import React from 'react';

function Copy({
  height,
  width,
  copyText,
  isTransparentBg,
  useSvg = false,
  className = ''
}) {
  function copyToClipboard(copy) {
    copyStringToClipboard(copy);
    NotificationManager.success(
      <IntlMessages id="share.copy-clip" />,
      <IntlMessages id="req.success" />,
      3000,
      null,
      null,
      ''
    );
  }
  if (useSvg) {
    return (
      <svg
        height={height}
        width={width}
        className={className}
        onClick={() => copyToClipboard(copyText)}
        viewBox="0 0 15 15"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M1 9.5A1.5 1.5 0 0 0 2.5 11H4v-1H2.5a.5.5 0 0 1-.5-.5v-7a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 .5.5V4H5.5A1.5 1.5 0 0 0 4 5.5v7A1.5 1.5 0 0 0 5.5 14h7a1.5 1.5 0 0 0 1.5-1.5v-7A1.5 1.5 0 0 0 12.5 4H11V2.5A1.5 1.5 0 0 0 9.5 1h-7A1.5 1.5 0 0 0 1 2.5zm4-4a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-.5.5h-7a.5.5 0 0 1-.5-.5z"
          fill="#fff"
        />
      </svg>
    );
  }
  return (
    <img
      height={height}
      width={width}
      title="copy to clipboard"
      onClick={() => copyToClipboard(copyText)}
      src="/assets/icons/copy.svg"
      className={`${
        isTransparentBg ? 'copyToClip-trans' : 'copyToClip'
      } c-pointer`}
      alt="copy"
    />
  );
}

export default Copy;
