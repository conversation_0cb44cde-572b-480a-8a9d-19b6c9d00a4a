/* eslint-disable no-unused-vars */
/* eslint-disable react/no-array-index-key */
import React from 'react';

const ShoppableCard = ({ data }) => {
  return (
    <div className="ShoppableCard">
      <div className="ShoppableCardTop">
        <img height={50} width={50} alt="" />
        <div className="ShoppableCardTopDescription">
          <h2>Product title</h2>
          <div className="priceCont">
            <span className="sellingPrice">74,990.00</span>
            <span className="orgPrice">74,990.00</span>
          </div>
          <div className="ShoppableCardTopDescriptionBtn">
            <button type="button">Buy now</button>
          </div>
        </div>
      </div>
      <div className="ShoppableCardBottom">
        <TimeRow />
        <TimeRow />
        <div className="iconBtns">
          <IconButton icon={<i className="simple-icon-pencil" />} />
          <IconButton
            icon={
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M7 4a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v2h4a1 1 0 1 1 0 2h-1.069l-.867 12.142A2 2 0 0 1 17.069 22H6.93a2 2 0 0 1-1.995-1.858L4.07 8H3a1 1 0 0 1 0-2h4zm2 2h6V4H9zM6.074 8l.857 12H17.07l.857-12zM10 10a1 1 0 0 1 1 1v6a1 1 0 1 1-2 0v-6a1 1 0 0 1 1-1m4 0a1 1 0 0 1 1 1v6a1 1 0 1 1-2 0v-6a1 1 0 0 1 1-1"
                  fill="#0D0D0D"
                />
              </svg>
            }
          />
        </div>
      </div>
    </div>
  );
};

const TimeRow = () => {
  return (
    <div className="timeRow">
      <i className="simple-icon-clock" />
      <p>Start Time</p>
      <p>00.01.00</p>
    </div>
  );
};

const IconButton = ({ icon }) => {
  return <span className="SCIconButton">{icon}</span>;
};

export default ShoppableCard;
