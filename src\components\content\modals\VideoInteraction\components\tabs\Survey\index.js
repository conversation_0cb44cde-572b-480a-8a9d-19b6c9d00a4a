/* eslint-disable react/no-array-index-key */
/* eslint-disable no-unused-vars */
import React, { useEffect } from 'react';
import QuesAnsCard from './QuesAnsCard';
import {
  INITIAL_STATE_SURVEY,
  INTERACTION_TYPE
} from 'constants/VideoInteraction';
import SurveyPreview from './SurveyPreview';
import TimeInput from '../../TimeInput';

const SurveyTab = ({
  surveys,
  setSurveys,
  startTime,
  setStartTime,
  interactiondetails
}) => {
  console.log({
    surveys,
    interactiondetails
  });

  useEffect(() => {
    if (
      interactiondetails &&
      interactiondetails?.type === INTERACTION_TYPE.SURVEY
    ) {
      setStartTime(interactiondetails?.showAtTimecode);
      const formattedSurveys = interactiondetails.data.surveyQuestions.map(
        (m) => {
          return {
            question: m.question,
            responseType: m.type,
            values: m.options.map((j) => ({
              value: j
            }))
          };
        }
      );
      console.log('formattedSurveys', formattedSurveys);
      setSurveys(formattedSurveys);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [interactiondetails]);

  return (
    <>
      <div className="survey">
        <TimeInput
          value={startTime}
          onChange={(v) => {
            setStartTime(v);
          }}
          label="Start Time"
          required
          className="w-30 mt-4"
        />

        <div>
          {surveys.map((m, i) => (
            <QuesAnsCard
              key={i}
              index={i + 1}
              data={m}
              setPolls={setSurveys}
              polls={surveys}
            />
          ))}
        </div>
        <div className="d-flex flex-row align-items-center justify-content-end">
          <AddQuestionButton
            onClick={() => {
              setSurveys((prev) => [...prev, INITIAL_STATE_SURVEY]);
            }}
          />
        </div>
        <SurveyPreview polls={surveys} />
      </div>
    </>
  );
};

const AddQuestionButton = (props) => {
  return (
    <span {...props} className="addQuestion">
      <svg
        width="20"
        height="20"
        viewBox="0 0 24 25"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12 23C6.21 23 1.5 18.29 1.5 12.5C1.5 6.71 6.21 2 12 2C17.79 2 22.5 6.71 22.5 12.5C22.5 18.29 17.79 23 12 23ZM12 3.5C7.035 3.5 3 7.535 3 12.5C3 17.465 7.035 21.5 12 21.5C16.965 21.5 21 17.465 21 12.5C21 7.535 16.965 3.5 12 3.5Z"
          fill="black"
        />
        <path
          d="M12 17.75C11.58 17.75 11.25 17.42 11.25 17V8C11.25 7.58 11.58 7.25 12 7.25C12.42 7.25 12.75 7.58 12.75 8V17C12.75 17.42 12.42 17.75 12 17.75Z"
          fill="black"
        />
        <path
          d="M16.5 13.25H7.5C7.08 13.25 6.75 12.92 6.75 12.5C6.75 12.08 7.08 11.75 7.5 11.75H16.5C16.92 11.75 17.25 12.08 17.25 12.5C17.25 12.92 16.92 13.25 16.5 13.25Z"
          fill="black"
        />
      </svg>

      <span className="text">Add Question</span>
    </span>
  );
};

export default SurveyTab;
