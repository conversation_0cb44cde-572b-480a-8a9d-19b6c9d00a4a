/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-vars */
/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable react/no-array-index-key */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/media-has-caption */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { getTenantInfo } from 'functions/api';
import SEO from 'components/SEO';
import {
  DEFAULT_SUBSCRIBE_BUTTON_COLOR,
  DEFUALT_COLOR
} from 'constants/showcase';
import { RoleTypes, getCurrentColor } from 'helpers/Utils';
import ShowcaseOwnerLoading from './Loading';
import EditOwnerModal from './EditOwnerModal';
import ShowcaseImageModal from './ShowcaseImageModal';
import VideoOwner from './VideoOwner';
import ImageOwner from './ImageOwner';
import ShowcaseOwnerBottom from './ShowcaseOwnerBottom';
import useSubscription from '../useSubscription';
import { NotificationManager } from 'components/common/react-notifications';
import IntlMessages from 'helpers/IntlMessages';

function ShowcaseOwner({
  expandFor,
  selectedTenantId,
  showcases,
  setShowcases,
  isLoggedIn,
  setExpandFor,
  setOwnerLoading,
  history,
  selectedTenantUserName,
  themeColors,
  setThemeColors,
  setIsEdit,
  isEdit,
  myShowcases,
  orgThemeColors,
  setOrgThemeColors,
  roleType,
  setTenantDetails,
  expandForUserName,
  setExpandForUserName,
  ownerData,
  setOwnerData,
  isVideoFile,
  setIsVideoFile,
  localBgImg,
  setLocalBgImg,
  isShowcaseBgLoading,
  showcaseCardProps = {}
}) {
  const isMyShowcase =
    selectedTenantId === expandFor ||
    expandFor === `@${selectedTenantUserName}`;
  const [isLoading, setIsLoading] = useState(true);
  const [isEditOwnerModalOpen, setIsEditOwnerModalOpen] = useState(false);
  const [isThemePicker, setIsThemePicker] = useState(false);
  const [editImageModalOpen, setEditImageModalOpen] = useState(null);
  const [localProfileImg, setLocalProfileImg] = useState(null);
  const currentColor = getCurrentColor().includes('dark');
  const showEditOptions = roleType <= RoleTypes.ADMIN;
  const accountInfo = JSON.parse(localStorage.getItem('tenantDetails'))?.data;

  const { handleSubscription, isSubscribed, subscribing } = useSubscription({
    isAlreadySubscribed: ownerData?.isSubscribed,
    showcases,
    setShowcases,
    isSubscribedTab: false,
    tenantId: ownerData?.id
  });

  const handleError = () => {
    setExpandFor(null);
    setOwnerData(null);
    history.push('/app/showcase');
  };

  console.log('expandFor', expandFor);

  const saveOwnerColos = () => {
    const newColors = {
      subscribeButtonColor:
        ownerData?.subscribeButtonColor ?? DEFAULT_SUBSCRIBE_BUTTON_COLOR,
      tenantTagsColor: ownerData?.tenantTagsColor ?? DEFUALT_COLOR,
      contentBorderColor: ownerData?.contentBorderColor ?? DEFUALT_COLOR,
      reloadButtonColor: ownerData?.reloadButtonColor ?? DEFUALT_COLOR,
      shareButtonColor: ownerData?.shareButtonColor ?? DEFUALT_COLOR
    };
    console.log('saveOwnerColos newColors', newColors);
    setThemeColors(newColors);
    setOrgThemeColors(newColors);
    // setExpandFor(ownerData?.id ?? null);
  };

  const fetchTenantInfo = async () => {
    const fetch = (() => {
      const isExpandUsername = expandFor.includes('@');
      if (isExpandUsername) {
        return `@${ownerData?.username}` !== expandFor;
      }
      return ownerData?.id !== expandFor;
    })();

    if (fetch) {
      console.log('fetching biatch');
      try {
        setOwnerLoading(true);
        setShowcases([]);
        const { data, isError } = await getTenantInfo(
          expandFor,
          isLoggedIn,
          isMyShowcase
        );
        if (!isError && data) {
          setExpandForUserName({
            id: data?.id ?? null,
            username: data?.username ? `@${data?.username}` : null
          });
          if (!myShowcases) {
            setExpandFor(data?.id ?? null);
          }
          setExpandFor(data?.id ?? null);
          setOwnerData(data);
          const newColors = {
            subscribeButtonColor:
              data.subscribeButtonColor ?? DEFAULT_SUBSCRIBE_BUTTON_COLOR,
            tenantTagsColor: data.tenantTagsColor ?? DEFUALT_COLOR,
            contentBorderColor: data.contentBorderColor ?? DEFUALT_COLOR,
            reloadButtonColor: data.reloadButtonColor ?? DEFUALT_COLOR,
            shareButtonColor: data.shareButtonColor ?? DEFUALT_COLOR
          };
          setThemeColors(newColors);
          setOrgThemeColors(newColors);
        }
        if (isError) {
          handleError();
        }
        setIsLoading(false);
        setOwnerLoading(false);
      } catch (error) {
        console.error(error);
        handleError();
      }
    } else {
      saveOwnerColos();
      setIsLoading(false);
      setOwnerLoading(false);
    }
  };

  useEffect(() => {
    if (expandFor) {
      fetchTenantInfo();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [expandFor, isLoggedIn]);

  const toggleEditOwnerModal = () => {
    setIsEditOwnerModalOpen(!isEditOwnerModalOpen);
  };

  const toggleEditImageModal = () => {
    setEditImageModalOpen(null);
  };

  const bgImg = localBgImg ?? ownerData?.coverImageUrl;

  useEffect(() => {
    if (bgImg) {
      const isString = typeof bgImg === 'string';
      if (isString) {
        const url = new URL(bgImg);
        url.search = '';
        const cleanUrl = url.toString();
        const lastDotIndex = cleanUrl.lastIndexOf('.');
        const extension = cleanUrl.slice(lastDotIndex + 1);
        const itIsVideo = extension === 'mp4';
        console.log({
          cleanUrl,
          itIsVideo
        });
        setIsVideoFile(itIsVideo);
      }
    }
  }, [bgImg, ownerData?.coverImageUrl]);

  if (isLoading) {
    return <ShowcaseOwnerLoading />;
  }

  const profileImg = localProfileImg ?? ownerData?.profileImageUrl;
  const userIdentifier = ownerData?.username
    ? `@${ownerData?.username}`
    : ownerData?.tenantId;

  const accountName = (() => {
    if (ownerData?.profileTitle) {
      return ownerData.profileTitle;
    }
    const extrcatedName = ownerData?.name?.split('Innerloop Account')[0] ?? '';
    return `${extrcatedName} Showcase Homepage`;
  })();

  const handleEditBg = () => {
    if (ownerData?.contentId) {
      NotificationManager.warning(<IntlMessages id="prevent-editing" />);
    } else {
      setEditImageModalOpen('Background');
    }
  };

  const VideoImageBgProps = {
    isVideoFile,
    bgImg,
    showEditOptions,
    isMyShowcase,
    setEditImageModalOpen,
    isEdit,
    profileImg,
    setIsEdit,
    isThemePicker,
    setIsThemePicker,
    setThemeColors,
    themeColors,
    orgThemeColors,
    setOrgThemeColors,
    toggleEditOwnerModal,
    handleSubscription,
    ownerData,
    expandFor,
    userIdentifier,
    isSubscribed,
    isLoggedIn,
    accountName,
    currentColor,
    subscribing,
    handleEditBg,
    isShowcaseBgLoading
  };

  // eslint-disable-next-line no-unreachable
  return (
    <>
      {isLoggedIn && showEditOptions && (
        <EditOwnerModal
          isOpen={isEditOwnerModalOpen}
          toggle={toggleEditOwnerModal}
          tenantId={expandFor}
          ownerData={ownerData}
          setOwnerData={setOwnerData}
          history={history}
          setTenantDetails={setTenantDetails}
        />
      )}
      {isLoggedIn && (
        <ShowcaseImageModal
          key={`${editImageModalOpen}`}
          isOpen={editImageModalOpen}
          toggle={toggleEditImageModal}
          tenantId={expandFor}
          ownerData={ownerData}
          setLocalProfileImg={setLocalProfileImg}
          setLocalBgImg={setLocalBgImg}
          bgImg={bgImg}
          setOwnerData={setOwnerData}
          profileImg={profileImg}
          isVideoFile={isVideoFile}
          setIsVideoFile={setIsVideoFile}
        />
      )}
      {ownerData?.profileTitle && (
        <SEO
          description={ownerData?.profileDescription ?? ''}
          title={ownerData?.profileTitle ?? ''}
          keywords={ownerData?.tags?.join(',') ?? ''}
          imgUrl={ownerData?.coverImageUrl ?? ownerData?.profileImageUrl}
          url={window.location.href}
        />
      )}
      <div className="d-flex flex-column w-full">
        {isVideoFile ? (
          <VideoOwner {...VideoImageBgProps} />
        ) : (
          <ImageOwner {...VideoImageBgProps} />
        )}
        <ShowcaseOwnerBottom
          ownerData={ownerData}
          themeColors={themeColors}
          setThemeColors={setThemeColors}
          orgThemeColors={orgThemeColors}
          setOrgThemeColors={setOrgThemeColors}
          isEdit={isEdit}
          expandFor={expandFor}
          isMyShowcase={isMyShowcase}
          handleSubscription={handleSubscription}
          subscribing={subscribing}
          isLoggedIn={isLoggedIn}
          isSubscribed={isSubscribed}
          userIdentifier={userIdentifier}
          isVideoFile={isVideoFile}
          showcaseCardProps={{
            showcases,
            setShowcases,
            myShowcases,
            history,
            roleType,
            selectedTenantId,
            setOwnerData,
            localBgImg,
            setLocalBgImg,
            isShowcaseBgLoading,
            ...showcaseCardProps
          }}
        />
      </div>
    </>
  );
}

export default ShowcaseOwner;
