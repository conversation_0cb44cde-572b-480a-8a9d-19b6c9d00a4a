import { ColorInput } from 'components/input';
import React, { useEffect, useState } from 'react';
import { Button } from 'reactstrap';

const RefreshButton = ({
  handleRefresh,
  isText = true,
  color = '#922C88',
  textColor = 'white',
  themeColors,
  setThemeColors,
  showColorPicker,
  orgThemeColors,
  setOrgThemeColors,
  isLoading,
  text,
  ...props
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [mouseHover, setMouseHover] = React.useState(false);
  const [btnClicked, setBtnClicked] = React.useState(color);

  useEffect(() => {
    setTimeout(() => {
      setBtnClicked(color);
    }, 100);
  }, [btnClicked, color]);

  return (
    <div {...props} className="d-flex justify-content-end position-relative">
      <Button
        type="button"
        disabled={isLoading}
        className="border-0 text-primary d-flex align-items-center"
        onClick={() => {
          if (!isOpen) {
            handleRefresh();
            setBtnClicked('#fff');
          }
        }}
        onMouseEnter={() => setMouseHover(true)}
        onMouseLeave={() => setMouseHover(false)}
        style={{
          backgroundColor: mouseHover ? btnClicked : 'transparent',
          paddingTop: '10px',
          textWrap: 'nowrap'
        }}
      >
        <i
          className="iconsminds-repeat-6 mr-2"
          style={{
            color: mouseHover ? textColor : btnClicked,
            fontSize: '17px'
          }}
        />
        {isText && (
          <span
            style={{
              color: mouseHover ? textColor : btnClicked,
              fontSize: '17px'
            }}
          >
            {text ?? 'Reload'}
          </span>
        )}
        {isLoading && (
          <span className="show-spinner btn-multiple-state">
            <span className="spinner d-inline-block">
              <span
                style={{ backgroundColor: '#992288' }}
                className="bounce1"
              />
              <span
                style={{ backgroundColor: '#992288' }}
                className="bounce2"
              />
              <span
                style={{ backgroundColor: '#992288' }}
                className="bounce3"
              />
            </span>
          </span>
        )}
        {themeColors && showColorPicker && (
          <ColorInput
            key={showColorPicker}
            open={isOpen}
            setOpen={setIsOpen}
            themeColors={themeColors}
            setThemeColors={setThemeColors}
            orgThemeColors={orgThemeColors}
            setOrgThemeColors={setOrgThemeColors}
            color={themeColors.reloadButtonColor}
            setColor={(c) => {
              setThemeColors({
                ...themeColors,
                reloadButtonColor: c
              });
            }}
            style={{ top: -15, right: 0, position: 'absolute' }}
          />
        )}
      </Button>
    </div>
  );
};

export default RefreshButton;
