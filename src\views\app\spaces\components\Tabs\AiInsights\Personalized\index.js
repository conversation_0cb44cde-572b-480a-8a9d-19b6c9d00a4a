/* eslint-disable no-restricted-globals */
/* eslint-disable no-undef */
/* eslint-disable no-unused-vars */
/* eslint-disable yoda */
/* eslint-disable consistent-return */
import React, { useEffect, useState } from 'react';
import PurposeSelector from './PurposeSelector';
import PreferenceSelector from './PreferenceSelector';
import { CHANNELS, getObjectives, PREFERENCE_TYPE, PREFERENCES } from './data';
import useCounter from 'hooks/useCounter';
import Generate from './GenerateButton';
import { isYoutube } from 'helpers/Utils';
import {
  getContentPersonalizedInsights,
  getPersonalizedInsights,
  getPersonalizedInsightsById
} from 'functions/api/analyze';
import Result from './Result';
import { NotificationManager } from 'components/common/react-notifications';
import SearchResults from './SearchResult';
import WarningCard from 'components/cards/WarningCard';
import { AI_OPERATION_TYPE, ENTITY_TYPES } from 'constants/defaultValues';
import PIHistory from './history';
import IntlMessages from 'helpers/IntlMessages';
import EnableAi from '../components/EnableAi';

const INITIAL_PURPOSE_STATE = {
  objective: '',
  channel: '',
  businessDomain: '',
  languageVibe: '',
  brandName: '',
  companySize: '',
  industry: '',
  targetAudience: '',
  internet: false,
  location: false,
  brandGuideline: '',
  format: ''
};

const Insights = ({
  content,
  isStory,
  contentIds,
  isAiEnabled,
  setIsAiEnabled,
  showEnableAiOption
  // persoanlizedInsights,
  // setPersonalizedInsights
}) => {
  const { mediaType, contentSource, metadata, id, spaceId } = content;
  const isImage = 3 === +mediaType;
  const isAudio = 2 === +mediaType;
  const isVideo = 1 === +mediaType;
  const description = metadata?.SummaryAuto;
  const isYoutubeContent = isYoutube(contentSource);
  const { count, reset, start, stop } = useCounter();
  const [selectedPreference, setSelectedPreference] = useState(null);
  const [selectedPurpose, setSelectedPurpose] = useState(INITIAL_PURPOSE_STATE);
  const [isTranscribing, setIsTranscribing] = useState(false);
  const [data, setData] = useState(null);

  useEffect(() => {
    const selectedObjective = getObjectives(+selectedPreference?.id)[0].value;
    setSelectedPurpose({
      ...selectedPurpose,
      objective: selectedObjective
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedPreference]);

  const handlePersonalizedInsights = async () => {
    try {
      const preferenceType = selectedPreference?.id ?? PREFERENCE_TYPE.GENERAL;
      setIsTranscribing(true);
      setData(null);
      reset();
      start();
      const { data, isError } = await getPersonalizedInsights({
        purpose: selectedPurpose.objective,
        channel: selectedPurpose.channel,
        brandName: selectedPurpose.brandName,
        languageVibe: selectedPurpose.languageVibe,
        businessDomain: selectedPurpose.businessDomain,
        companySize: selectedPurpose.companySize,
        targetAudience: selectedPurpose.targetAudience,
        industry: selectedPurpose.industry,
        internet: selectedPurpose.internet,
        location: selectedPurpose.location,
        brandtone: selectedPurpose.brandGuideline,
        purposeType: preferenceType,
        format: selectedPurpose.format,
        isYoutube: isYoutubeContent,
        isVideo,
        isImage,
        contentId: content?.id,
        spaceId: content?.spaceId,
        duration: content?.duration,
        mediaType: content?.mediaType,
        isStory,
        contentIds
      });
      console.log({
        data,
        isError
      });
      if (!isError && data?.limitExceed) {
        reset();
        NotificationManager.error(<IntlMessages id="limit-ai-exceed" />);
      } else if (!isError && data) {
        setData({
          response: data,
          payload: {
            ...selectedPurpose,
            isYoutube: isYoutubeContent,
            preferenceType
          }
        });
        stop();
      } else {
        reset();
        NotificationManager.error(
          'Something went wrong while generating insights'
        );
      }
      setIsTranscribing(false);
    } catch (error) {
      reset();
      console.error('errror', error);
      setIsTranscribing(false);
      return NotificationManager.error(
        'Something went wrong while generating insights'
      );
    }
  };

  if (!isAiEnabled && showEnableAiOption) {
    return (
      <EnableAi
        content={content}
        isAiEnabled={isAiEnabled}
        setIsAiEnabled={setIsAiEnabled}
        type={AI_OPERATION_TYPE.Narrative}
      />
    );
  }
  // eslint-disable-next-line no-constant-condition
  if (
    !description &&
    !showEnableAiOption &&
    !isImage &&
    !isYoutubeContent &&
    !isStory &&
    !isAudio
  ) {
    return (
      <WarningCard text="To use personalized Insights, you need to first create Detailed Insights for the content " />
    );
  }

  return (
    <>
      <div className="Personalized">
        <div className="trigger-card ">
          <div className="bottom">
            <div className="btns">
              <PreferenceSelector
                selectedPreference={selectedPreference}
                setSelectedPreference={setSelectedPreference}
              />
              <PurposeSelector
                selectedPurpose={selectedPurpose}
                setSelectedPurpose={setSelectedPurpose}
                selectedPreference={selectedPreference?.id}
              />
            </div>
            <Generate
              count={count}
              isLoading={isTranscribing}
              onClick={() => {
                console.log('pressing...');
                handlePersonalizedInsights();
              }}
            />
          </div>
          <Strip selectedPurpose={selectedPurpose} />
        </div>
      </div>
      {isTranscribing && (
        <p
          style={{
            fontSize: 14,
            marginTop: 10,
            color: '#992288',
            textAlign: 'center'
          }}
        >
          {isImage
            ? `Hang tight...⏳ we are analyzing and preparing insights for you.`
            : `Hang tight...⏳ This may take up to 60 seconds. Depending on media duration and multimodality (including audio, video, and text), it can take a bit longer. Thanks for your patience! 🙏`}
        </p>
      )}
      {data?.response?.text && (
        <>
          <h1 className="res-title">AI Generated Insights</h1>
          <Result data={data} />
          {typeof data?.response !== 'string' &&
            data?.response?.search?.length > 0 && (
              <SearchResults searchResults={data?.response?.search ?? []} />
            )}
        </>
      )}
      <PIHistory spaceId={spaceId} entityId={id} isStory={isStory} />
    </>
  );
};

const Strip = ({ selectedPurpose }) => {
  const showStrip =
    selectedPurpose.channel ||
    selectedPurpose.industry ||
    selectedPurpose.brandName ||
    selectedPurpose.languageVibe ||
    selectedPurpose.businessDomain ||
    selectedPurpose.brandGuideline ||
    selectedPurpose.targetAudience ||
    selectedPurpose.internet ||
    selectedPurpose.format ||
    selectedPurpose.companySize;

  if (showStrip) {
    return (
      <div className="strip">
        <>
          {selectedPurpose.channel && (
            <KeyValue
              name="Channel"
              value={
                CHANNELS.find((f) => f.value === selectedPurpose.channel)?.label
              }
            />
          )}
          {selectedPurpose.industry && (
            <KeyValue name="Industry" value={selectedPurpose.industry} />
          )}

          {selectedPurpose.brandName && (
            <KeyValue
              name="Company / Brand name"
              value={selectedPurpose.brandName}
            />
          )}

          {selectedPurpose.businessDomain && (
            <KeyValue
              name="Brand / Company website"
              value={selectedPurpose.businessDomain}
            />
          )}

          {selectedPurpose.companySize && (
            <KeyValue name="Company size" value={selectedPurpose.companySize} />
          )}

          {selectedPurpose.languageVibe && (
            <KeyValue
              name="Language Vibe"
              value={selectedPurpose.languageVibe}
            />
          )}

          {selectedPurpose.brandGuideline && (
            <KeyValue name="Brand Guidelines" value={<>&nbsp;Selected</>} />
          )}

          {selectedPurpose.targetAudience && (
            <KeyValue name="Target Audience" value={<>&nbsp;Selected</>} />
          )}
          {selectedPurpose.internet && (
            <KeyValue name="Live Search" value={<>&nbsp;Enabled</>} />
          )}
          {selectedPurpose.format && (
            <KeyValue name="Output Format" value={<>&nbsp;HTML Selected</>} />
          )}
        </>
      </div>
    );
  }
  return null;
};

const KeyValue = ({ name, value }) => {
  return (
    <div>
      {name}: &nbsp;&nbsp;
      <span style={{ fontWeight: '700' }}>{value}</span>
    </div>
  );
};

export default Insights;
