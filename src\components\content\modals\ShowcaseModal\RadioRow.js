/* eslint-disable no-unused-vars */
import { RadioInput } from 'components/input';
import React from 'react';

const RadioRow = ({
  mode,
  setMode,
  info,
  title,
  value,
  isShowcased,
  setButtonDisabled,
  disabled = false
}) => {
  const isDraftActive = mode === value;
  return (
    <div
      className="mb-1 d-flex flex-row w-full gap-2"
      style={{ cursor: disabled && 'not-allowed' }}
      aria-disabled={disabled}
    >
      <div style={{ alignSelf: 'baseline' }}>
        {/* <div style={{ width: '10%', alignSelf: 'baseline' }}> */}
        <RadioInput
          id={title}
          value={value}
          checked={mode === value}
          name="mode"
          disabled={disabled}
          onChange={() => {
            setMode(value);
            // if (isShowcased) {
            //   setButtonDisabled(false);
            // }
          }}
        />
      </div>
      <div style={{ width: '95%' }}>
        <p
          className={`mb-0 text-align-left ${isDraftActive && 'text-primary'}`}
        >
          <b>
            <label className="m-0" htmlFor={title}>
              {title}
            </label>
          </b>
        </p>
        {info && (
          <p
            className={`m-0 text-align-left ${isDraftActive && 'text-primary'}`}
          >
            {info}
          </p>
        )}
      </div>
    </div>
  );
};

export default RadioRow;
