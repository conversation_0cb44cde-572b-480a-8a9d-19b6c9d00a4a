import InfoMessage from 'components/infoMessage';
import IntlMessages from 'helpers/IntlMessages';
import React from 'react';
import { FormGroup, Input, Label } from 'reactstrap';

const SingleInput = () => {
  return (
    <div className="SingleInput">
      <FormGroup className="form-group has-float-label mb-0">
        <Label for="name" style={{ cursor: 'default' }}>
          <IntlMessages id="profile-usr-name" />
          &nbsp;
        </Label>
        <Input
          className="form-control"
          type="text"
          name="name"
          placeholder="Full Name"
          value=""
        />
      </FormGroup>
      <InfoMessage
        textAlign="right"
        message="Max character limit 30"
        className="mt-0"
      />
    </div>
  );
};

export default SingleInput;
