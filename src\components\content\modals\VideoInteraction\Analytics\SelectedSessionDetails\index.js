import React from 'react';
import SelectedSessionHeader from './SelectedSessionHeader';
import PollDetails from './PollDetails';
import SurveyDetails from './SurveyDetails';
import { getInteractionTypes } from 'helpers/Utils';

const SelectedSessionDetails = ({
  setSelectedSessionDetails,
  selectedSessionDetails,
  selectedInteractions
}) => {
  const { isPoll, isSurvey } = getInteractionTypes(selectedSessionDetails);
  const { pollOptions, surveyQuestions } = selectedInteractions.data;

  return (
    <div className="InteractionAnalytics">
      <div className="IALeft">
        <svg
          onClick={() => {
            setSelectedSessionDetails(null);
          }}
          width="12"
          height="23"
          viewBox="0 0 12 23"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="c-pointer"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M11.5003 20.7975L10.194 22.125L0.611547 12.3875C0.380923 12.1496 0.251953 11.8313 0.251953 11.5C0.251953 11.1687 0.380923 10.8504 0.611547 10.6125L10.194 0.875L11.5003 2.20375L2.3528 11.5L11.5003 20.7975Z"
            fill="black"
          />
        </svg>
      </div>
      <div className="IARight sessionDetails">
        <h1>Session Details</h1>
        <SelectedSessionHeader
          selectedSessionDetails={selectedSessionDetails}
        />
        {isPoll && (
          <PollDetails
            pollOptions={pollOptions}
            data={selectedSessionDetails}
          />
        )}
        {isSurvey && (
          <SurveyDetails
            surveyQuestions={surveyQuestions}
            data={selectedSessionDetails}
          />
        )}
      </div>
    </div>
  );
};

export default SelectedSessionDetails;
