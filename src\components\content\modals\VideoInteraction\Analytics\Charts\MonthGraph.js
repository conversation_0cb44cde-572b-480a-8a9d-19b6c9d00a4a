/* eslint-disable prefer-const */
/* eslint-disable array-callback-return */
import GeoChartContainer from 'components/charts/GeoChartContainer';
import React, { useEffect, useState } from 'react';
import Bar<PERSON>hart from 'views/app/analytics/bar';
import GraphContainer from 'views/app/spaces/components/GraphContainer';

const MonthGraph = ({ data, nextUpdateTime }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [labels, setLabels] = useState([]);
  const [values, setValues] = useState([]);

  useEffect(() => {
    const formatData = () => {
      try {
        setLabels([]);
        setValues([]);
        setIsLoading(true);
        let newLabels = [];
        let newValues = [];

        data.map((m) => {
          newLabels.push(`${m?.month}-${m?.year}`);
          newValues.push(m.interactionId);
        });

        setLabels(newLabels);
        setValues(newValues);
        setIsLoading(false);
      } catch (error) {
        console.error(
          'Something went wrong in formatData in Dategraph component',
          error
        );
      }
    };
    if (data) {
      formatData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  return (
    <GraphContainer
      loading={isLoading}
      // title="sm.top-country-map"
      lastUpdated={nextUpdateTime}
      dataExists={!!labels[0]}
      dataEmptyTitle="sm.no-views-by-months"
      className="w-full-imp h-full-imp"
      dataComponent={
        <GeoChartContainer className="p-2 p-md-4 d-flex align-items-center justify-content-center w-full h-full">
          {({ dimensions }) => (
            <BarChart
              data={values}
              labels={labels}
              height={dimensions.height}
              width={dimensions.width}
            />
          )}
        </GeoChartContainer>
      }
    />
  );
};

export default MonthGraph;
