import GeoChartContainer from 'components/charts/GeoChartContainer';
import React, { useEffect, useState } from 'react';
import Bar<PERSON>hart from 'views/app/analytics/bar';
import GraphContainer from '../../GraphContainer';

const Dategraph = ({ data, nextUpdateTime }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [labels, setLabels] = useState([]);
  const [values, setValues] = useState([]);

  useEffect(() => {
    const formatData = () => {
      try {
        setIsLoading(true);
        const now = new Date();
        const last30Days = Array.from({ length: 30 }, (_, i) => {
          const date = new Date();
          date.setDate(now.getDate() - (29 - i));
          return date;
        });

        const formattedAnalytics = data.reduce((acc, item) => {
          const key = `${item.year}-${item.monthno.padStart(
            2,
            '0'
          )}-${item.day.padStart(2, '0')}`;
          acc[key] = item.media_impressions;
          return acc;
        }, {});

        const labels = last30Days.map(
          (date) =>
            `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
              2,
              '0'
            )}-${String(date.getDate()).padStart(2, '0')}`
        );

        const values = labels.map((label) => formattedAnalytics[label] || 0);

        setLabels(labels);
        setValues(values);
        setIsLoading(false);
      } catch (error) {
        console.error(
          'Something went wrong in formatData in Dategraph component',
          error
        );
      }
    };

    if (data) {
      formatData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  console.log({
    labels,
    values,
    data
  });

  return (
    <GraphContainer
      loading={isLoading}
      // title="sm.top-country-map"
      lastUpdated={nextUpdateTime}
      dataExists={data?.length > 0}
      dataEmptyTitle="sm.no-views-by-date"
      className="w-full-imp h-full-imp"
      dataComponent={
        <GeoChartContainer className="p-2 p-md-4 d-flex align-items-center justify-content-center w-full h-full">
          {({ dimensions }) => (
            <BarChart
              data={values}
              labels={labels}
              height={dimensions.height}
              width={dimensions.width}
            />
          )}
        </GeoChartContainer>
      }
    />
  );
};

export default Dategraph;
