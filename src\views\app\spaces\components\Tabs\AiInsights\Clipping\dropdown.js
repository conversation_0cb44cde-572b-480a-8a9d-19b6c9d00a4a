import IntlMessages from 'helpers/IntlMessages';
import React from 'react';
// eslint-disable-next-line import/no-extraneous-dependencies, import/no-unresolved
import * as DropdownMenu from '@radix-ui/react-dropdown-menu';
import { adminRoot } from 'constants/defaultValues';

export default function ClippingDropdown({ data, history }) {
  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger asChild>
        <i
          className="simple-icon-options-vertical cursor-pointer"
          style={{ fontSize: '1rem' }}
        />
      </DropdownMenu.Trigger>
      <DropdownMenu.Portal>
        <DropdownMenu.Content
          className="DropdownMenuContent dropdown-radix"
          sideOffset={5}
        >
          {/* <DropdownMenu.Item className="dot-dropitem-radix">
            <i className="simple-icon-minus mr-2" />
            <span>
              <IntlMessages id="dub-delete" />
            </span>
          </DropdownMenu.Item> */}

          <DropdownMenu.Item
            className="dot-dropitem-radix"
            onClick={() =>
              history.push(
                `${adminRoot}/help-support?id=${data.parentId}&title=clipping ${data?.id} with parent contentId`
              )
            }
          >
            <i
              style={{ fontSize: '1em' }}
              className="iconsminds-support mr-1"
            />
            <span>
              <IntlMessages id="menu.support" />
            </span>
          </DropdownMenu.Item>
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
}
