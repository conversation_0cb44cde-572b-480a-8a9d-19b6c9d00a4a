/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import { Row } from 'reactstrap';
import BodyInputs from './BodyInputs';
import {
  checkAiDescStatus,
  checkAiTriggerStatus,
  triggerAiDescribe
} from 'functions/api/analyze';
import { NotificationManager } from 'components/common/react-notifications';
import IntlMessages from 'helpers/IntlMessages';
import AiButton from 'components/buttons/AiButton';
import RefreshButton from 'components/buttons/RefreshButton';
import useCounter from 'hooks/useCounter';
import { Colxx } from 'components/common/CustomBootstrap';
import useEnableAi from 'hooks/useEnableAi';
import ConfirmationModal from 'components/common/ConfirmationModal';
import { AI_OPERATION_TYPE } from 'constants/defaultValues';
import InfoMessage from 'components/infoMessage';

const VidAudDescription = ({
  isStory,
  content,
  roleType,
  selectedTenantId,
  getContent,
  isAiEnabled,
  setIsAiEnabled
}) => {
  const { id: contentId, mediaType, duration, spaceId } = content;
  const { count, reset, start, stop } = useCounter();
  const title = content?.metadata?.TitleAuto;
  const description = content?.metadata?.SummaryAuto;
  const tags = content?.metadata?.TagsAuto;
  const [showCheckGenerateInsightsButton, setShowCheckGenerateInsightsButton] =
    useState(false);

  const [isGeneratingInsights, setIsGeneratingInsights] = useState(false);
  const [isCheckingGeneratingInsights, setIsCheckingGeneratingInsights] =
    useState(false);
  const [openconfirm, setOpenConfirm] = useState(false);
  const toggle = () => setOpenConfirm(!openconfirm);
  const [
    wasLoadingBecauseItWasProcessing,
    setWasLoadingBecauseItWasProcessing
  ] = useState(false);

  const {
    handleCheckAiTriggerStatus,
    handleTrigger,
    showCheckTrggerStatusStatusButton,
    isInitiallyChecking,
    isCheckingTriggerStatus,
    isEnablingAi
  } = useEnableAi({
    content,
    isAiEnabled,
    setIsAiEnabled,
    type: AI_OPERATION_TYPE.Description
  });

  console.log({
    isInitiallyChecking,
    isCheckingTriggerStatus,
    isEnablingAi,
    isGeneratingInsights,
    isCheckingGeneratingInsights,
    wasLoadingBecauseItWasProcessing,
    showCheckTrggerStatusStatusButton,
    showCheckGenerateInsightsButton
  });

  const VID_AUD_SIEVE_PROCESSING = 'vid-aud-sieve-processing';

  const generateDescription = async (isChecking = false) => {
    const check = (val) => {
      if (isChecking) {
        setIsCheckingGeneratingInsights(val);
      } else {
        setIsGeneratingInsights(val);
      }
    };

    try {
      if (!isChecking) {
        reset();
      }
      check(true);
      if (!isChecking) {
        start();
      }
      const res = await checkAiDescStatus({
        contentId,
        mediaType,
        spaceId,
        duration,
        wasLoadingBecauseItWasProcessing
      });
      if (typeof res === 'string') {
        check(false);
        if (!isChecking) {
          reset();
        }
        return;
      }
      const { data, isError } = res;
      if (isError) {
        check(false);
        NotificationManager.error(<IntlMessages id="something-went-wrong" />);
        if (!isChecking) {
          reset();
        }
      } else {
        if (data === 'SUCCESS') {
          await getContent(true);
          localStorage.removeItem(VID_AUD_SIEVE_PROCESSING);
          check(false);
          if (!isChecking) {
            reset();
          }
          return;
        }
        setTimeout(() => {
          setShowCheckGenerateInsightsButton(true);
        }, 25000);
        if (data === 'SIEVE_PROCESSING') {
          localStorage.setItem(VID_AUD_SIEVE_PROCESSING, contentId);
          NotificationManager.warning(<IntlMessages id="refresh-processing" />);
          return;
        }
        if (data === 'OTHER_PROCESSING') {
          setShowCheckGenerateInsightsButton(true);
          setWasLoadingBecauseItWasProcessing(true);
          check(false);
          NotificationManager.warning(<IntlMessages id="refresh-processing" />);
          return;
        }
        if (data === 'RELOAD') {
          await getContent(true);
          localStorage.removeItem(VID_AUD_SIEVE_PROCESSING);
          check(false);
          setIsGeneratingInsights(false);
          setIsCheckingGeneratingInsights(false);
          setShowCheckGenerateInsightsButton(false);
          setWasLoadingBecauseItWasProcessing(false);
          reset();
          return;
        }
        if (data === 'NEVER_TRIGGERED') {
          if (isEnablingAi) {
            NotificationManager.warning(
              <IntlMessages id="refresh-processing" />
            );
          }
        }
      }
    } catch (error) {
      check(false);
      console.error(
        'Something went wrong in generateDescription due to ',
        error
      );
      if (!isChecking) {
        reset();
      }
    }
  };

  useEffect(() => {
    const localValue = localStorage.getItem(VID_AUD_SIEVE_PROCESSING);
    if (localValue === contentId) {
      start();
      setIsAiEnabled(true);
      generateDescription();
      setIsGeneratingInsights(true);
      setShowCheckGenerateInsightsButton(true);
    }
    handleCheckAiTriggerStatus();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [contentId]);

  const disabled = description && isGeneratingInsights && isAiEnabled;

  console.log({
    isAiEnabled,
    wasLoadingBecauseItWasProcessing,
    isCheckingGeneratingInsights
  });

  return (
    <>
      <Row
        // aria-disabled={disabled}
        className="m-0 d-flex flex-row w-full align-items-center justify-content-between"
      >
        <p className="font-bolder mb-0" style={{ fontSize: 16 }}>
          AI Description
        </p>
        <>
          {!isAiEnabled ? (
            <div className="d-flex flex-row gap-2 align-items-center justify-content-between ">
              {isInitiallyChecking ? (
                <RefreshButton
                  handleRefresh={() => {}}
                  text="Checking Status"
                  isLoading
                />
              ) : (
                <>
                  <div>
                    <div className="d-flex flex-row gap-2 align-items-center">
                      {isEnablingAi && showCheckTrggerStatusStatusButton && (
                        <RefreshButton
                          handleRefresh={() => {
                            handleCheckAiTriggerStatus(true);
                          }}
                          text="Check Status"
                          isLoading={isCheckingTriggerStatus}
                        />
                      )}
                      <AiButton
                        isLoading={isEnablingAi}
                        disabled={isEnablingAi}
                        text="enable-ai-btn"
                        onClick={() => {
                          handleTrigger();
                        }}
                      />
                    </div>
                    {isEnablingAi && <InfoMessage message="enabling-ai-info" />}
                  </div>
                </>
              )}
            </div>
          ) : (
            <div className="d-flex flex-row gap-2 align-items-center justify-content-between ">
              {wasLoadingBecauseItWasProcessing ? (
                <>
                  {showCheckGenerateInsightsButton && (
                    <RefreshButton
                      handleRefresh={() => {
                        generateDescription(true);
                      }}
                      text="Check Status"
                      isLoading={isCheckingGeneratingInsights}
                    />
                  )}
                  <AiButton
                    showCount
                    disabled
                    count={count}
                    isLoading
                    text="Generate Insights"
                    onClick={() => {}}
                  />
                </>
              ) : (
                <>
                  {isGeneratingInsights &&
                    showCheckGenerateInsightsButton &&
                    wasLoadingBecauseItWasProcessing && (
                      <RefreshButton
                        handleRefresh={() => {
                          generateDescription(true);
                        }}
                        text="Check Status"
                        isLoading={isCheckingGeneratingInsights}
                      />
                    )}
                  <AiButton
                    showCount
                    disabled={disabled}
                    count={count}
                    isLoading={isGeneratingInsights}
                    text="Generate Insights"
                    onClick={() => {
                      if (description) {
                        toggle();
                      } else {
                        generateDescription();
                      }
                    }}
                  />
                </>
              )}
            </div>
          )}
        </>
      </Row>
      {(isGeneratingInsights || isCheckingGeneratingInsights) && (
        <Colxx className="mt-2">
          <p className="text-primary text-align-center mb-0">
            <IntlMessages id="hang-tight" />
          </p>
        </Colxx>
      )}
      {description && !disabled && (
        <BodyInputs
          isStory={isStory}
          content={content}
          roleType={roleType}
          selectedTenantId={selectedTenantId}
        />
      )}
      <ConfirmationModal
        openConfirmationModal={openconfirm}
        toggleConfirmationModal={toggle}
        handleConfirm={() => {
          generateDescription();
          toggle();
        }}
        handleClose={toggle}
        type="confirmDetailedInsights"
      />
    </>
  );
};

export default VidAudDescription;
