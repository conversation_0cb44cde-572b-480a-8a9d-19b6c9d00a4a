/* eslint-disable no-nested-ternary */
import IntlMessages from 'helpers/IntlMessages';
import React, { useState } from 'react';
import { Button } from 'reactstrap';

const SubscribeButton = ({
  onClick,
  subscribing,
  isLoggedIn,
  isSubscribed
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  const handleTouchStart = () => {
    setIsHovered(true);
  };

  const handleTouchEnd = () => {
    setTimeout(() => setIsHovered(false), 300);
  };

  return (
    <Button
      color="primary"
      outline
      active={false}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      disabled={subscribing}
      style={{
        background: isHovered ? '#992288' : 'transparent',
        color: isHovered ? '#fff' : '#992288'
      }}
      className={`ns-btn ${
        subscribing ? 'btn-multiple-state show-spinner' : ''
      }`}
      onClick={onClick}
    >
      <span className="spinner d-inline-block">
        <span style={{ background: '#992288' }} className="bounce1" />
        <span style={{ background: '#992288' }} className="bounce2" />
        <span style={{ background: '#992288' }} className="bounce3" />
      </span>
      <span className="label">
        <span className="label px-2">
          <IntlMessages
            id={`showcase-${
              !isLoggedIn
                ? 'subscribe'
                : isSubscribed
                ? 'unsubscribe'
                : 'subscribe'
            }`}
          />
        </span>
      </span>
    </Button>
  );
};

export default SubscribeButton;
