/* eslint-disable jsx-a11y/anchor-is-valid */
import React from 'react';
import { Table } from 'reactstrap';

const InvoiceDetails = () => {
  //   const data = [{

  //   }]
  return (
    <div
      style={{
        display: 'block',
      }}
    >
      {/* eslint-disable-next-line jsx-a11y/no-static-element-interactions */}
      {/* <div
        onClick={() => {
          setIsOpen(!isOpen);
        }}
        style={{ cursor: 'pointer', fontWeight: 'bold' }}
      >
        Invoice{' '}
        {isOpen ? (
          <i
            className="simple-icon-arrow-down"
            style={{ fontSize: '.5rem', fontWeight: 'bolder' }}
          />
        ) : (
          <i
            className="simple-icon-arrow-right"
            style={{ fontSize: '.5rem', fontWeight: 'bolder' }}
          />
        )}
      </div>
      <hr /> */}
      <Table striped>
        <thead>
          <tr>
            <th>S.NO</th>
            <th>Date</th>
            <th>Title</th>
            <th>Download</th>
          </tr>
        </thead>
        {/* <tbody>
          <tr>
            <td>1</td>
            <td>01/01/2020</td>
            <td>Here goes the title </td>
            <td>
              <i
                className="simple-icon-cloud-download"
                style={{ width: 30, height: 30 }}
              />
            </td>
          </tr>
          <tr>
            <td>2</td>
            <td>01/01/2020</td>
            <td>Here goes the title </td>
            <td>
              <i
                className="simple-icon-cloud-download"
                style={{ width: 30, height: 30 }}
              />
            </td>
          </tr>
        </tbody> */}
      </Table>
    </div>
  );
};

export default InvoiceDetails;
