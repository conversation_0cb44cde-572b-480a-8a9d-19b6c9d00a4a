/* eslint-disable no-unused-vars */
import LoadingButton from 'components/buttons/LoadingButton';
import { NotificationManager } from 'components/common/react-notifications';
import { handleCreatePlayer } from 'functions/api';
import IntlMessages from 'helpers/IntlMessages';
import useWindowDimensions from 'hooks/useWindowDimensions';
import React, { useState } from 'react';
import { useHistory } from 'react-router-dom/cjs/react-router-dom.min';
import { Form, FormGroup, Input, Label, Modal, ModalBody } from 'reactstrap';

const CreatePlayer = ({ modalOpen, toggleModal, handleRefresh }) => {
  const history = useHistory();
  const spaceId = history.location.pathname.split('/')[4];
  const [selectedContent, setSelectedContent] = useState({
    title: 'Tears of Steel (HLS)',
    url: 'id_tears_of_steel_hls'
  });
  const [playerSettings, setPlayerSettings] = useState({
    autoplay: false,
    customCss: '',
    loop: false,
    hideProgressControl: false,
    subtitle: false,
    skipping: false,
    whitelabel: false,
    brandId: null,
    enableChapters: false,
    accessControl: {
      minAllowedAge: null,
      allowedCode: null,
      expiryTimestamp: null
    }
  });
  const [name, setName] = useState(null);
  const [loading, setLoading] = useState(false);
  const { width } = useWindowDimensions();

  /// will create a new player based on the new settings
  const getPlayerDetails = () => {
    const playerDetails = {
      ...playerSettings,
      accessControl: {
        ...playerSettings.accessControl
      },
      name
    };
    return playerDetails;
  };

  const createPlayer = async () => {
    if (!name) {
      NotificationManager.warning(
        <IntlMessages id="mediaPlayer.enter-title" />,
        <IntlMessages id="mediaPlayer.title" />
      );
      return;
    }
    if (name.trim().length < 3) {
      NotificationManager.warning(<IntlMessages id="title.atleast" />);
      return;
    }
    if (name.length > 100) {
      NotificationManager.warning(<IntlMessages id="story.title-less-100" />);
      return;
    }
    setLoading(true);
    const playerDetails = getPlayerDetails();
    playerDetails.spaceId = spaceId;
    playerDetails.isEnabled = true;

    const result = await handleCreatePlayer(playerDetails);
    if (!result) {
      setLoading(false);
      return;
    }

    const { data } = result?.data;
    const newPlayerId = data.id;

    if (newPlayerId) {
      setPlayerSettings({ ...playerSettings, id: data.id });
      setSelectedContent(selectedContent);
      NotificationManager.success(
        <IntlMessages id="mediaPlayer.create-sucess" />,
        <IntlMessages id="req.success" />
      );
      history.push(
        `/app/spaces/space/${spaceId}/media-player/${newPlayerId}/edit`
      );
      toggleModal();
      handleRefresh();
    }
    setLoading(false);
  };

  const CloseButton = () => {
    setName(null);
    toggleModal();
  };

  const handleFormSubmit = (event) => {
    event.preventDefault();
    createPlayer();
  };

  return (
    <Modal
      isOpen={modalOpen}
      toggle={toggleModal}
      centered
      style={{ minWidth: '40%' }}
    >
      <ModalBody>
        <div className="d-flex justify-content-between  mb-xs-4">
          <p
            className="font-weight-lightbold "
            style={{ fontSize: '17px', color: 'black' }}
          >
            New Media Player
          </p>
          <span
            role="button"
            tabIndex={0}
            onClick={() => CloseButton()}
            className="w-5"
          >
            <i
              style={{ color: 'black' }}
              className="simple-icon-close close-btn"
            />
          </span>
        </div>
        <Form onSubmit={handleFormSubmit}>
          <FormGroup className={`form-group has-float-label  `}>
            <Label for="name" style={{ cursor: 'default' }}>
              <IntlMessages id="player-title" />
              &nbsp;
              <span
                style={{
                  color: '#922c88'
                }}
              >
                {' '}
                *
              </span>
            </Label>
            <Input
              className="w-300 my-4"
              id="playerTitle"
              name="title"
              value={name}
              placeholder="Enter Player Title"
              type="text"
              onChange={(e) => {
                const text = e.target.value;
                const values = text.slice(0, 100);
                setName(values);
                if (text.length > 100) {
                  NotificationManager.warning(
                    <IntlMessages id="story.title-less-100" />
                  );
                }
              }}
            />
          </FormGroup>
        </Form>

        <div className="d-flex justify-content-center my-5">
          <LoadingButton
            style={{ minWidth: width < '600' ? '60%' : '30%' }}
            text="Create Player"
            loading={loading}
            disabled={loading}
            onClick={handleFormSubmit}
          />
        </div>
      </ModalBody>
    </Modal>
  );
};

export default CreatePlayer;
