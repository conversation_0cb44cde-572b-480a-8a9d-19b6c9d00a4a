import Like from 'components/ShowcaseThumbnail/Like';
import { adminRoot } from 'constants/defaultValues';
import React from 'react';
import { Row } from 'reactstrap';
import ShareButton from './ShareButton';

const Header = ({
  data,
  isStory,
  toggleExpanded,
  history,
  showcaseCreatedByOurTenant,
  handleShareClick,
  isLoggedIn,
  selectedTenantId,
  showcaseLikes
}) => {
  return (
    <Row className="showcase-header m-0">
      <div className="sh-left">
        <h2
          className={`pb-0 font-weight-bolder mb-0 ${
            showcaseCreatedByOurTenant && 'c-pointer'
          }`}
          onClick={() => {
            if (showcaseCreatedByOurTenant) {
              if (isStory) {
                history.push(
                  `${adminRoot}/spaces/space/${data?.spaceId}/stories/${data?.entityId}/detail?fromShowcase=${data?.id}`
                );
              } else {
                history.push(
                  `${adminRoot}/spaces/space/${data?.spaceId}/manage-content/${data?.entityId}/edit?fromShowcase=${data?.id}`
                );
              }
            }
          }}
        >
          <span style={{ color: showcaseCreatedByOurTenant && '#992288' }}>
            {data?.title}
          </span>
        </h2>
        {data?.category && (
          <p className="text-primary mt-0 sh-category">{data?.category}</p>
        )}
      </div>
      <div
        className="d-flex flex-row align-items-center sh-right"
        style={{ gap: '10px' }}
      >
        <div className="d-flex flex-row header-top-btn" style={{ gap: '10px' }}>
          <Like
            isPublic={!isLoggedIn}
            showcaseId={data?.id}
            showcaseLikes={showcaseLikes}
            selectedTenantId={selectedTenantId}
            className="d-flex flex-row align-items-center justify-content-center c-pointer"
          />
          <ShareButton onClick={handleShareClick} />
        </div>
        <span className="showcaseCircleButton" onClick={toggleExpanded}>
          <svg width="20" height="20" viewBox="0 0 24 24">
            <path
              fill="#992288"
              d="m13.41 12 6.3-6.29a1 1 0 1 0-1.42-1.42L12 10.59l-6.29-6.3a1 1 0 0 0-1.42 1.42l6.3 6.29-6.3 6.29a1 1 0 0 0 0 1.42 1 1 0 0 0 1.42 0l6.29-6.3 6.29 6.3a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42Z"
            />
          </svg>
        </span>
      </div>
    </Row>
  );
};

export default Header;
