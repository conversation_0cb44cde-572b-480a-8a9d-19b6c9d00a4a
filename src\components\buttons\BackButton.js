import React, { useState } from 'react';

function BackButton({ onClick, color = '#992288' }) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <button
      type="button"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{
        width: '40px',
        height: '40px',
        color: isHovered ? 'white' : color,
        border: isHovered ? '1px solid white' : `1px solid ${color}`,
        background: isHovered ? color : 'transparent'
      }}
      onClick={onClick}
      className="btn btn-outline-primary d-flex align-items-center justify-content-center mr-2"
    >
      <i className="iconsminds-left" />
    </button>
  );
}

export default BackButton;
