/* eslint-disable no-unused-vars */
/* eslint-disable no-nested-ternary */
import React from 'react';
import { CHAT_ANALYTICS_TYPE } from 'constants/defaultValues';
import Dategraph from '../Tabs/Dategraph';
import HourGraph from '../Tabs/HourGraph';
import MonthGraph from '../Tabs/MonthGraph';

const ChartSection = ({ active, data }) => {
  return (
    <div className="charts">
      {+active === CHAT_ANALYTICS_TYPE.DATE ? (
        <Dategraph
          data={data?.date?.counts}
          nextUpdateTime={data?.date.lastUpdated}
        />
      ) : +active === CHAT_ANALYTICS_TYPE.HOUR ? (
        <HourGraph
          data={data?.hour?.counts}
          nextUpdateTime={data?.hour.lastUpdated}
        />
      ) : (
        <MonthGraph
          data={data?.month?.counts}
          nextUpdateTime={data?.month.lastUpdated}
        />
      )}
    </div>
  );
};

export default ChartSection;
