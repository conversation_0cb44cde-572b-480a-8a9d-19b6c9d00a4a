/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable no-lonely-if */
/* eslint-disable no-else-return */
/* eslint-disable no-unused-vars */
import React, { useState, useEffect, useRef } from 'react';
import { NavLink, useHistory } from 'react-router-dom';
import { Row } from 'reactstrap';
import { Colxx } from 'components/common/CustomBootstrap';
import {
  getTopCountriesCities,
  topMediaContentsByImpressions,
  viewImpressionsByMonth
} from 'functions/api/analyticsApi';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';
import IntlMessages from 'helpers/IntlMessages';
import MainLoader from 'components/loaders/MainLoader';
import usePreview from 'hooks/usePreview';
import {
  convertNumberToShort,
  formatDate,
  generateColorArrays,
  isDarkModeActive,
  isYoutubeORVimeoSource
} from 'helpers/Utils';
import InfiniteScroll from 'react-infinite-scroll-component';
import ShowcaseThumbnail from 'components/ShowcaseThumbnail';
import {
  getShowcaseViewsClicks,
  getShowcases
} from 'functions/api/showcaseApi';
import { DoughnutChart } from 'components/charts';
import GeoChart from 'components/geoChart/GeoChart';
import Bar from '../../spaces/components/bar';
import ShowcaseAnalyticsModal from 'components/content/modals/ShowcaseAnalyticsModal';
import useQuery from 'hooks/useQuery';
import NextUpdateTime from 'components/nextUpdateTime';
import GraphContainer from 'views/app/spaces/components/GraphContainer';
import StoryIcon from 'constants/StoryIcon';
import GeoChartContainer from 'components/charts/GeoChartContainer';

ChartJS.register(ArcElement, Tooltip, Legend);

const tabs = [
  { id: 1, name: 'Media Analytics' },
  { id: 2, name: 'Showcase Analytics' }
];

const DashboardCharts = ({ previewCache, roleType }) => {
  const [activeTab, setActiveTab] = useState(1);
  const search = useQuery().get('tabIndex');

  useEffect(() => {
    if (+search !== activeTab) {
      setActiveTab(+search);
    }
  }, [activeTab, search]);

  useEffect(() => {
    setActiveTab(1);
  }, []);

  return (
    <>
      <div
        className="d-flex justify-content-around bb-2"
        style={{
          fontSize: '1.2rem'
        }}
      >
        {tabs.map((t) => (
          <NavLink
            to={`dashboard?tabIndex=${t.id}`}
            location={{}}
            key={t.id}
            className="TeamTabHeader"
            style={{
              borderBottom: `${
                activeTab === t.id ? '5px solid #922c88' : 'none'
              }`
            }}
            onClick={() => {
              setActiveTab(t.id);
            }}
          >
            <IntlMessages id={t.name} />
          </NavLink>
        ))}
      </div>
      <Colxx>
        {activeTab === 2 && (
          <div className="d-flex flex-column flex-wrap mb-5 pb-5">
            <ActiveShowcaseInsights
              previewCache={previewCache}
              roleType={roleType}
            />
          </div>
        )}
        {activeTab === 1 && (
          <>
            <Row className="d-flex flex-row flex-wrap align-items-initial dash-chart">
              <TopFiveMedia previewCache={previewCache} />
              <TopFiveMedia previewCache={previewCache} audioVideo />
            </Row>
            <Row className="d-flex flex-row flex-wrap align-items-initial dash-chart">
              <TopCitiesDoughnutChart />
              <TopCountriesMap />
            </Row>
            <Row className="d-flex flex-row align-items-center justify-content-center">
              <div className=" my-3 w-full-imp">
                <MonthsImpressions />
              </div>
            </Row>
          </>
        )}
      </Colxx>
    </>
  );
};

const TopFiveMedia = ({ previewCache, audioVideo }) => {
  const [data, setData] = useState(null);
  const [lastUpdated, setLastUpdated] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const getTopFiveMedia = async () => {
    setIsLoading(true);
    const gotData = await topMediaContentsByImpressions(
      new Date().getUTCFullYear(),
      audioVideo
    );
    if (audioVideo) {
      gotData?.data?.sort((a, b) => {
        if (a.watchTime > b.watchTime) {
          return -1;
        } else if (a.watchTime < b.watchTime) {
          return 1;
        } else {
          return 0;
        }
      });
    } else {
      gotData?.data?.sort((a, b) => {
        if (a.views > b.views) {
          return -1;
        } else if (a.views < b.views) {
          return 1;
        } else {
          return 0;
        }
      });
    }
    setData(gotData?.data?.filter((f) => f.title || f.name) || []);
    setLastUpdated(gotData.nextUpdateTime);
    setIsLoading(false);
  };

  useEffect(() => {
    getTopFiveMedia();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <GraphContainer
        loading={isLoading}
        title={
          audioVideo ? 'dashboard.top5contents-av' : 'dashboard.top5contents'
        }
        lastUpdated={lastUpdated}
        dataExists={data?.length > 0}
        dataEmptyTitle="dashboard-t5m-no-record"
        dataComponent={
          <Colxx className="card p-2 p-md-4 ">
            <div className="d-flex flex-row w-full justify-content-end align-items-center mt-3">
              <div className="w-40" />
              <p className="list-item-heading w-20">
                <IntlMessages id="dashboard-t5m-title" />
              </p>
              <p className="w-30 text-center">
                <IntlMessages
                  id={
                    audioVideo
                      ? 'dashboard-t5m-avg-watchtime'
                      : 'dashboard-t5m-views'
                  }
                />
              </p>
            </div>
            {data?.map((item) => (
              <TopMediaRow
                key={item.id}
                item={item}
                previewCache={previewCache}
                audioVideo={audioVideo}
              />
            ))}
          </Colxx>
        }
        isDashboard
      />
    </>
  );
};

const MonthsImpressions = () => {
  const [chartData, setChartData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState('');

  const getMonthsImpression = async () => {
    setIsLoading(true);
    const result = await viewImpressionsByMonth();
    const labels = [];
    const data = [];
    result.data?.forEach((item) => {
      labels.push(`${item?.month}-${item?.year}`);
      data.push(item.media_impressions);
    });
    setChartData({ ...chartData, labels, data });
    setLastUpdated(result?.nextUpdateTime);
    setIsLoading(false);
  };

  useEffect(() => {
    getMonthsImpression();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (isLoading) {
    return (
      <div className="dataloading-overlay">
        <div className="dataloading" />
      </div>
    );
  }

  return (
    <Colxx className="d-flex flex-column col mt-3 mt-lg-0 h-full">
      <>
        <p className="d-flex justify-content-center align-items-center font-weight-lightbold list-item-heading mb-0">
          <IntlMessages id="dashboard.impressionsByMonth" />
        </p>
        <NextUpdateTime time={lastUpdated} />
        {chartData?.data?.length > 0 && chartData?.labels?.length > 0 ? (
          <Colxx className="card p-1 p-md-3" style={{ minHeight: '600px' }}>
            <div className="w-100 h-100 mt-5 chart-container">
              <Bar {...chartData} />
            </div>
          </Colxx>
        ) : (
          <div className="d-flex flex-wrap justify-content-center align-items-center">
            <span style={{ fontSize: '13px' }}>
              <IntlMessages id="dashboard-mi-no-record" />
            </span>
          </div>
        )}
      </>
    </Colxx>
  );
};

const TopCitiesDoughnutChart = () => {
  const [loading, setLoading] = useState(false);
  const [lastUpdated, setlastUpdated] = useState(null);
  const [labels, setLabels] = useState([]);
  const [labelsData, setLabelsData] = useState([]);
  const [colors, setColors] = useState(null);

  useEffect(() => {
    const getData = async () => {
      setLoading(true);
      const result = await getTopCountriesCities(undefined, undefined, true);
      // const result = await topAllCountries(true);
      if (result?.data?.data?.length > 0) {
        // eslint-disable-next-line prefer-destructuring
        const data = result?.data?.data;
        const lbls = data?.map((f) => f.city);
        const lblsData = data?.map((f) => f.media_impressions);
        setLabels(lbls);
        setLabelsData(lblsData);
        setlastUpdated(result?.data?.nextUpdateTime);
        setColors(generateColorArrays(lblsData?.length));
      }
      setLoading(false);
    };
    getData();
  }, []);

  const dataExists = labels?.length > 0;

  return (
    <GraphContainer
      loading={loading}
      title="sm.top-cities-dash"
      lastUpdated={lastUpdated}
      dataExists={dataExists}
      dataEmptyTitle="sm.no-top-cities"
      isDashboard
      dataComponent={
        dataExists && (
          <Colxx className="card p-2 p-md-4 d-flex align-items-center justify-content-center">
            <div style={{ minHeight: '600px', width: '400px' }}>
              <DoughnutChart
                data={{
                  labels,
                  datasets: [
                    {
                      backgroundColor: colors?.lightColors,
                      borderColor: colors?.darkColors,
                      data: labelsData,
                      borderWidth: 2
                    }
                  ]
                }}
              />
            </div>
          </Colxx>
        )
      }
    />
  );
};

const TopCountriesMap = () => {
  const [loading, setLoading] = useState(false);
  const [lastUpdated, setlastUpdated] = useState(null);
  const [mappedData, setMappedData] = useState([]);

  useEffect(() => {
    const getTopCountries = async () => {
      setLoading(true);
      const result = await getTopCountriesCities(undefined, undefined);
      if (result?.data?.data?.length > 0) {
        const mapped = result?.data?.data?.map((f) => [
          f.country,
          f.media_impressions
        ]);
        setMappedData([['Country', 'Impressions'], ...mapped]);
      }
      setlastUpdated(result?.data?.nextUpdateTime);
      setLoading(false);
    };
    getTopCountries();
  }, []);

  const dataExists = mappedData.length > 0;

  return (
    <>
      <GraphContainer
        loading={loading}
        title="sm.top-country-dash"
        lastUpdated={lastUpdated}
        dataExists={dataExists}
        dataEmptyTitle="sm.no-top-country-map"
        isDashboard
        dataComponent={
          dataExists && (
            <GeoChartContainer
              className="card p-2 p-md-4 d-flex align-items-center justify-content-center"
              style={{ minHeight: '600px', maxHeight: '600px' }}
            >
              {({ dimensions }) => (
                <GeoChart
                  dimensions={dimensions}
                  data={
                    mappedData || [
                      ['Country', 'Impressions'],
                      ['Germany', 200]
                    ]
                  }
                  options={{
                    colorAxis: { colors: ['#C198BD', '#922C88'] }
                  }}
                />
              )}
            </GeoChartContainer>
          )
        }
      />
    </>
  );
};

const TopMediaRow = ({ item, previewCache, audioVideo }) => {
  const [buttonIsHovered, setButtonHovered] = useState(false);
  const [loading, setLoading] = useState(false);
  const history = useHistory();

  let itemIcon = '';
  if (item.mediaType === 1) itemIcon = 'simple-icon-camrecorder';
  else if (item.mediaType === 2) itemIcon = 'simple-icon-earphones';
  else if (item.mediaType === 3) itemIcon = 'simple-icon-picture';

  const { previewSrc: preview, previewValid: validPreview } = usePreview({
    spaceId: item.spaceId,
    contentId: item.contentId,
    isMulti: false,
    MediaType: item.mediaType === 1 ? 2 : 1,
    waitFor: item,
    previewCache,
    roleType: item?.roleType,
    roleReqd: true
  });

  const handleEdit = async () => {
    if (item?.roleType > 999 || item?.roleType < 1) {
      return;
    }
    setLoading(true);
    setLoading(false);
    history.push(
      `/app/spaces/space/${item.spaceId}/manage-content/${item?.contentId}/edit`
    );
  };

  if (loading) {
    return (
      <div className="position-relative">
        <div className="Overlapping">
          <MainLoader
            text="&nbsp; &nbsp;&nbsp; &nbsp;&nbsp;&nbsp; Please Wait. &nbsp; &nbsp;&nbsp; &nbsp;
          Preparing Data Insights."
            dataLoad
          />
        </div>
      </div>
    );
  }

  return (
    <div className="card card-body ins-player-list-item list-view-row col p-0">
      <NavLink
        to="#"
        className="responsive-thumbnail w-40"
        aria-disabled={item?.roleType > 999 || item?.roleType < 1}
      >
        {validPreview && preview ? (
          <img
            width="150"
            src={preview}
            alt="story"
            className="responsive-img responsive-md-story"
          />
        ) : itemIcon ? (
          <i
            className={`${itemIcon} list-default-icon-style list-thumbnail 
            border-0 card-img-left mr-5`}
          />
        ) : (
          <StoryIcon width={25} />
        )}
      </NavLink>
      <p
        className="list-item-heading mb-1 truncate w-20 "
        style={{
          cursor: 'pointer',
          color:
            buttonIsHovered && (item?.roleType <= 999 || item?.roleType > 0)
              ? '#922c88'
              : '#696969',
          fontWeight: buttonIsHovered ? 'bold' : 'normal',
          width: '10%'
        }}
        onMouseEnter={() => setButtonHovered(true)}
        onMouseLeave={() => setButtonHovered(false)}
        onClick={handleEdit}
        aria-hidden="true"
        aria-disabled={item?.roleType > 999 || item?.roleType < 1}
      >
        {item?.title || `Content Title`}
      </p>
      <p className="mb-1 w-30 text-center">
        {audioVideo ? item?.avg_watchtime?.toFixed(2) : item.views}
      </p>
    </div>
  );
};

const ActiveShowcaseInsights = ({ previewCache, roleType }) => {
  const history = useHistory();
  const [maxResultCount] = useState(10);
  const [skipCount, setSkipCount] = useState(0);
  const tenantId = localStorage.getItem('selectedTenantId');
  const [showcases, setShowcases] = useState([]);
  const [hasMore, setHasMore] = useState(true);
  const [analytics, setAnalytics] = useState([]);
  const [list, setList] = useState([]);
  const [lastUpdated, setlastUpdated] = useState(null);
  const [expandShowcase, setExpandShowcase] = useState(null);
  const isDarkMode = isDarkModeActive();

  const fetchActiveInsights = async () => {
    const result = await Promise.allSettled([
      getShowcases({
        AccountShowcaseOnly: true,
        maxResultCount,
        skipCount
      }),
      getShowcaseViewsClicks(tenantId)
    ]);
    if (result[0].status === 'fulfilled' && result[1].status === 'fulfilled') {
      setAnalytics(result[1].value.data.data ?? []);
      setShowcases(result[0].value?.data?.items ?? []);
      setlastUpdated(result[1].value?.data?.nextUpdateTime ?? null);
      setHasMore(true);
      if (result[0].value?.data?.items?.length < maxResultCount) {
        setHasMore(false);
      }
    }
  };

  useEffect(() => {
    fetchActiveInsights();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getAllShowcases = async () => {
    try {
      const { data, isError } = await getShowcases({
        maxResultCount,
        skipCount: skipCount + maxResultCount,
        AccountShowcaseOnly: true
      });
      setSkipCount(skipCount + maxResultCount);
      setHasMore(true);
      if (data.items.length < maxResultCount) {
        setHasMore(false);
      }
      if (!isError && data) {
        setShowcases([...showcases, ...data?.items]);
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    if (showcases.length > 0) {
      const newList = showcases.map((s) => {
        let showcase = {};
        let views = 0;
        let clicks = 0;

        analytics.forEach((a) => {
          if (s.id === a.showcaseId) {
            if (a.eventName === 'showcase_view') {
              views += a?.eventCount || 0;
            } else if (a.eventName === 'showcase_cta_click') {
              clicks += a?.eventCount || 0;
            }
          }
        });

        showcase = {
          ...s,
          views,
          clicks
        };

        return showcase;
      });
      setList(newList);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [showcases]);

  return (
    <>
      {expandShowcase && (
        <ShowcaseAnalyticsModal
          isOpen={!!expandShowcase}
          spaceId={expandShowcase?.spaceId}
          expandShowcase={expandShowcase}
          tenantId={tenantId}
          toggleModal={() => setExpandShowcase(null)}
        />
      )}
      <Colxx
        className="d-flex flex-column col mt-4 mb-5"
        style={{ height: '98%' }}
      >
        <p
          className="text-center"
          style={{ fontSize: '17px', lineHeight: '1.5rem' }}
        >
          <b>
            <IntlMessages id="dashboard-showcase-title" />
          </b>
        </p>
        <p className="text-center" style={{ fontSize: '15px' }}>
          <IntlMessages id="dashboard-showcase-subtitle" />
        </p>
        <NextUpdateTime time={lastUpdated} />
        <Colxx className="card p-1 p-md-3">
          <div
            className="d-flex flex-row mb-3 align-align-items-center"
            style={{ fontSize: '14px' }}
          >
            <div className="text-center" style={{ width: '13%' }}>
              &nbsp;
            </div>
            <div className="text-center" style={{ width: '20%' }}>
              <IntlMessages id="dashboard-si-title" />
            </div>
            <div className="text-center" style={{ width: '10%' }}>
              <IntlMessages id="dashboard-si-views" />
            </div>
            <div className="text-center" style={{ width: '17%' }}>
              <IntlMessages id="dashboard-si-cta" />
            </div>
            <div className="text-center" style={{ width: '20%' }}>
              <IntlMessages id="dashboard-si-createdat" />
            </div>
            <div className="text-center" style={{ width: '20%' }}>
              <IntlMessages id="dashboard-si-expiry" />
            </div>
            <div className="text-center" style={{ width: '5%' }} />
          </div>
          <InfiniteScroll
            dataLength={list.length}
            next={getAllShowcases}
            hasMore={hasMore}
            className="overflow-auto scrollbar-hidden"
            style={{ maxHeight: '1170px', fontSize: '13px' }}
            loader={
              <p style={{ textAlign: 'center', color: '#94308A' }}>
                <b>Loading...</b>
              </p>
            }
            endMessage={
              <p style={{ textAlign: 'center', color: '#94308A' }}>
                <IntlMessages id="seen-all" />
              </p>
            }
          >
            {list.map((d) => (
              <div
                className="card mb-3 d-flex flex-row p-2 py-3 align-items-center"
                key={d.id}
              >
                <div
                  style={{ width: '13%' }}
                  className="text-center d-flex align-items-center justify-content-center"
                >
                  <div className="d-flex flex-column ContentCard m-0">
                    <ShowcaseThumbnail
                      isDarkMode={isDarkMode}
                      MediaType={d.mediaType}
                      isStory={d.entityKind === 2}
                      data={d}
                      roleType={roleType}
                      history={history}
                      size="small"
                      onClick={() =>
                        history.push(`/app/showcase/detail/${d.id}`)
                      }
                      previewCache={previewCache}
                    />
                  </div>
                </div>
                <div style={{ width: '20%', fontSize: '16px' }}>
                  <NavLink to={`/app/showcase/detail/${d.id}`}>
                    <b style={{ color: '#992288' }}>{d.title}</b>
                  </NavLink>
                </div>

                <div style={{ width: '10%' }} className="text-center">
                  {d?.totalImpressionsCount
                    ? convertNumberToShort(d?.totalImpressionsCount)
                    : 0}
                </div>
                <div style={{ width: '17%' }} className="text-center">
                  {d.clicks ?? 0}
                </div>
                <div className="d-flex flex-column" style={{ width: '20%' }}>
                  <span className="text-center mb-2">
                    {formatDate(d.creationDateTime)}
                  </span>
                  <span className="text-center">
                    {new Date(d.creationDateTime).toLocaleTimeString()}
                  </span>
                </div>
                <div className="d-flex flex-column" style={{ width: '20%' }}>
                  {d.endDateTime === 1 ? (
                    <span className="text-center">No Expiry</span>
                  ) : (
                    <>
                      <span className="text-center mb-2">
                        {formatDate(new Date(d.endDateTime))}
                      </span>
                      <span className="text-center">
                        {new Date(d.endDateTime).toLocaleTimeString()}
                      </span>
                    </>
                  )}
                </div>
                <div
                  style={{ width: '5%' }}
                  className="text-center"
                  onClick={() =>
                    setExpandShowcase({
                      viewCount: d?.totalImpressionsCount,
                      clickCount: d?.clicks,
                      endDateTime: d.endDateTime,
                      creationDateTime: d.creationDateTime,
                      showcaseId: d?.id,
                      spaceId: d?.spaceId
                    })
                  }
                >
                  <i className="simple-icon-chart text-primary c-pointer" />
                </div>
              </div>
            ))}
          </InfiniteScroll>
        </Colxx>
      </Colxx>
    </>
  );
};

export default DashboardCharts;
