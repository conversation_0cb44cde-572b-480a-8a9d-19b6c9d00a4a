import IntlMessages from 'helpers/IntlMessages';
import useOnClickOutside from 'hooks/useOnClickOutSide';
import React, { useRef, useState } from 'react';
import { Popover, PopoverBody } from 'reactstrap';

const InfoPopover = ({
  infoId,
  message,
  className = '',
  iconClassName = '',
  style = {}
}) => {
  const ref = useRef();
  const [popoverOpen, setPopoverOpen] = useState(false);
  useOnClickOutside(ref, () => setPopoverOpen(false));

  return (
    <span ref={ref} className={className}>
      <span id={infoId} onClick={() => setPopoverOpen(true)} style={style}>
        <i className={`simple-icon-info c-pointer ${iconClassName}`} />
      </span>
      <Popover
        placement="top"
        isOpen={popoverOpen}
        target={infoId}
        toggle={() => setPopoverOpen(!popoverOpen)}
      >
        <PopoverBody>
          <IntlMessages id={message} />
        </PopoverBody>
      </Popover>
    </span>
  );
};

export default InfoPopover;
