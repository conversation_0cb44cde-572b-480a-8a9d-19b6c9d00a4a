/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-vars */
import ConfirmationModal from 'components/common/ConfirmationModal';
import { Colxx } from 'components/common/CustomBootstrap';
import { NotificationManager } from 'components/common/react-notifications';
import { BG_DIMENSIONS, PROFILE_DIMENSIONS } from 'constants/showcase';
import { createTenantProfileSasUrl, updateTenantProfile } from 'functions/api';
import uploadFileToBlob from 'helpers/Azure';
import IntlMessages from 'helpers/IntlMessages';
import { resizeImageFromFile } from 'helpers/Utils';
import React, { useEffect, useRef, useState } from 'react';
import { Button, Modal, ModalBody, Row } from 'reactstrap';

function ShowcaseImageModal({
  isOpen,
  toggle,
  tenantId,
  bgImg,
  profileImg,
  setLocalProfileImg,
  setLocalBgImg,
  ownerData,
  setOwnerData,
  isVideoFile
}) {
  const isProfile = isOpen === 'Profile';
  const image = isProfile ? profileImg : bgImg;
  const [selectedImg, setSelectedImg] = useState(image);
  const [isApplying, setIsApplying] = useState(false);
  const [isConfirmationOpen, setIsConfirmationOpen] = useState(false);
  const [isImgAlreadyExists, setIsImgAlreadyExists] = useState(true);
  const fileInputRef = useRef(null);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    if (isProfile) {
      setIsImgAlreadyExists(!!profileImg);
    } else {
      setIsImgAlreadyExists(!!bgImg);
    }
  }, [profileImg, bgImg, isProfile]);

  useEffect(() => {
    if (isProfile) {
      setSelectedImg(profileImg);
    } else {
      setSelectedImg(bgImg);
    }
  }, [isProfile, profileImg, bgImg]);

  const handleButtonClick = () => {
    fileInputRef.current.click();
  };

  const handleFileInputChange = async (event) => {
    const selectedFile = event.target.files[0];
    const width = isProfile ? PROFILE_DIMENSIONS.width : BG_DIMENSIONS.width;
    const height = isProfile ? PROFILE_DIMENSIONS.height : BG_DIMENSIONS.height;
    const resizedImg = await resizeImageFromFile({
      height,
      width,
      imageFile: selectedFile
    });
    setSelectedImg(resizedImg);
    setIsImgAlreadyExists(null);
  };

  const avatarStyles = (() => {
    return {
      height: '250px',
      width: isProfile ? '250px' : '100%',
      borderRadius: isProfile ? '125px' : '0px'
    };
  })();

  const handleToggle = ({ updating = false, closing = false }) => {
    if (updating) {
      setSelectedImg(URL.createObjectURL(selectedImg));
      if (isProfile) {
        setLocalProfileImg(URL.createObjectURL(selectedImg));
      } else {
        setLocalBgImg(URL.createObjectURL(selectedImg));
      }
    } else if (closing) {
      setSelectedImg(image);
      if (isProfile) {
        setLocalProfileImg(profileImg);
      } else {
        setLocalBgImg(bgImg);
      }
    }
    toggle();
    setIsApplying(false);
  };

  const handleApplyImage = async () => {
    try {
      setIsApplying(true);
      const payload = [selectedImg?.name];
      const { data, isError } = await createTenantProfileSasUrl(payload);
      if (!isError && data) {
        const fileItems = [];
        fileItems.push(data.items[0]);

        const fileSasObj = {
          items: fileItems,
          sasUrl: data?.sasUrl
        };
        console.log('data?.sasUrl', data?.sasUrl);
        const { isError: isUploadingError } = await uploadFileToBlob(
          selectedImg,
          () => {},
          () => {},
          fileSasObj
        );

        if (!isUploadingError) {
          const fileId = data?.items[0]?.fileName.split('profile/input/')[1];
          const tenantPayload = (() => {
            if (isProfile) {
              return {
                id: tenantId,
                profileImageId: fileId
              };
            }
            return {
              id: tenantId,
              coverImageId: fileId
            };
          })();
          const { data: updatedData } = await updateTenantProfile(
            tenantPayload
          );
          if (!updatedData.isError) {
            setIsImgAlreadyExists(true);
            if (isProfile) {
              setLocalProfileImg(URL.createObjectURL(selectedImg));
            } else {
              setLocalBgImg(URL.createObjectURL(selectedImg));
            }
            NotificationManager.warning(
              <IntlMessages id="Updated successfully" />
            );
            handleToggle({ updating: true });
          } else {
            NotificationManager.warning(
              <IntlMessages id="Failed to update image" />
            );
          }
        }
      }
      setIsApplying(false);
    } catch (error) {
      setIsApplying(false);
    }
  };

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      const tenantPayload = (() => {
        if (isProfile) {
          return {
            id: tenantId,
            profileImageId: ''
          };
        }
        return {
          id: tenantId,
          coverImageId: ''
        };
      })();
      const { data: updatedData } = await updateTenantProfile(tenantPayload);
      if (updatedData.isError) {
        NotificationManager.error(
          <IntlMessages
            id={
              isProfile
                ? 'Failed to delete Profile Image'
                : 'Failed to delete Background Image'
            }
          />,
          <IntlMessages id="req.failed" />
        );
      } else {
        setSelectedImg(null);
        if (isProfile) {
          setLocalProfileImg(null);
        } else {
          setLocalBgImg(null);
        }
        setIsImgAlreadyExists(null);
        const newOwnerInfo = (() => {
          if (isProfile) {
            return {
              ...ownerData,
              profileImageUrl: ''
            };
          }
          return {
            ...ownerData,
            coverImageUrl: ''
          };
        })();
        console.log('newOwnerInfo', newOwnerInfo);
        setOwnerData(newOwnerInfo);
        const localTenantDetails = JSON.parse(
          localStorage.getItem('tenantDetails')
        );
        const newTenantInfo = (() => {
          if (isProfile) {
            return {
              ...localTenantDetails.data,
              profileImageUrl: ''
            };
          }
          return {
            ...localTenantDetails.data,
            coverImageUrl: ''
          };
        })();
        localStorage.setItem(
          'tenantDetails',
          JSON.stringify({
            data: newTenantInfo,
            isError: false
          })
        );
        NotificationManager.success(
          <IntlMessages
            id={
              isProfile
                ? 'Deleted Profile Image successfully'
                : `Deleted Background Image successfully`
            }
          />,
          <IntlMessages id="req.success" />
        );
      }
      setIsDeleting(false);
    } catch (error) {
      setIsDeleting(false);
      console.log('error', error);
      NotificationManager.error(
        <IntlMessages
          id={
            isProfile
              ? 'Failed to delete Profile Image'
              : `Failed to delete Background Image`
          }
        />,
        <IntlMessages id="req.failed" />
      );
    }
  };

  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        toggle();
      }
    };
    window.addEventListener('keydown', handleEscape);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <Modal
        isOpen={!!isOpen}
        toggle={() => {
          handleToggle({ closing: true });
        }}
        centered
        keyboard={false}
        style={{ minWidth: isProfile ? '50%' : '70%' }}
        backdrop="static"
      >
        <ModalBody
          className="dotted-border m-4 py-4 px-5"
          aria-disabled={isDeleting}
        >
          <Row className="px-2">
            <p
              className="ml-1 font-weight-lightbold"
              style={{
                fontSize: '20px'
              }}
            >
              Showcase {isProfile ? 'Profile Image' : 'Background'}
            </p>
            <Button
              color="white"
              type="button"
              className="d-flex justify-content-end p-3"
              onClick={() => {
                handleToggle({ closing: true });
              }}
            >
              <i className="simple-icon-close close-btn" />
            </Button>
          </Row>
          <Colxx className="d-flex flex-column align-items-center w-full justify-content-center mt-2">
            <div
              style={{
                ...avatarStyles,
                backgroundColor: '#C4C4C4'
              }}
            >
              {selectedImg && (
                <>
                  {isVideoFile && !isProfile ? (
                    <video
                      muted
                      controls
                      height="100%"
                      width="100%"
                      style={{
                        ...avatarStyles
                      }}
                      src={
                        selectedImg?.name
                          ? URL.createObjectURL(selectedImg)
                          : selectedImg
                      }
                    />
                  ) : (
                    <img
                      height="100%"
                      width="100%"
                      style={{
                        ...avatarStyles
                      }}
                      src={
                        isImgAlreadyExists
                          ? image
                          : typeof selectedImg === 'string' &&
                            selectedImg.includes('blob')
                          ? selectedImg
                          : URL.createObjectURL(selectedImg)
                      }
                      alt={isProfile ? 'Profile' : 'Background'}
                    />
                  )}
                </>
              )}
            </div>
            <ImageSizeRecommendation
              isProfile={isProfile}
              selectedImg={selectedImg}
            />
          </Colxx>
          <div className="centered mt-4 gap-2 d-flex flex-row my-4">
            <input
              type="file"
              accept="image/*"
              style={{ display: 'none' }}
              ref={fileInputRef}
              onChange={handleFileInputChange}
            />
            {!selectedImg ? (
              <Button
                color="primary"
                size="md"
                onClick={handleButtonClick}
                style={{ alignSelf: 'center', width: '175px' }}
              >
                <span className="label">
                  <i className="simple-icon-picture mr-2" />
                  <IntlMessages id="Add Image" />
                </span>
              </Button>
            ) : isImgAlreadyExists ? (
              <>
                <Button
                  color="primary"
                  onClick={handleDelete}
                  outline
                  style={{ width: '175px' }}
                >
                  Delete
                </Button>
                <Button
                  color="primary"
                  style={{ width: '175px' }}
                  onClick={handleButtonClick}
                >
                  Change
                </Button>
              </>
            ) : (
              <>
                <Button
                  color="primary"
                  outline
                  style={{ width: '175px' }}
                  onClick={handleButtonClick}
                >
                  Change
                </Button>
                <Button
                  color="primary"
                  style={{ width: '175px' }}
                  disabled={isApplying}
                  onClick={handleApplyImage}
                  className={`btn-shadow auth-btn ${
                    isApplying ? 'btn-multiple-state show-spinner' : ''
                  }`}
                >
                  <span className="spinner d-inline-block">
                    <span className="bounce1" />
                    <span className="bounce2" />
                    <span className="bounce3" />
                  </span>
                  <span className="label">Apply</span>
                </Button>
              </>
            )}
          </div>
        </ModalBody>
      </Modal>
      <ConfirmationModal
        openConfirmationModal={isConfirmationOpen}
        toggleConfirmationModal={() =>
          setIsConfirmationOpen(!isConfirmationOpen)
        }
        type={isProfile ? 'deleteProfile' : 'deleteBg'}
        handleClose={() => {}}
        handleConfirm={() => {}}
        confirmText="Delete"
        cancelText="Cancel"
      />
    </>
  );
}

const ImageSizeRecommendation = ({ isProfile, selectedImg }) => {
  return (
    <p
      className={`text-primary mt-2 ${
        !isProfile ? 'w-full text-align-right' : ''
      }`}
    >
      <i className="simple-icon-info mr-2" />
      Recommended size:&nbsp;
      {isProfile ? PROFILE_DIMENSIONS.width : BG_DIMENSIONS.width} x{' '}
      {isProfile ? PROFILE_DIMENSIONS.height : BG_DIMENSIONS.height} pixels.
    </p>
  );
};

export default ShowcaseImageModal;
