/* eslint-disable no-unused-vars */
/* eslint-disable react/no-array-index-key */
import React, { useEffect, useState } from 'react';
import SACard from './SACard';
import { ShowcaseLoading } from './loading';
import IntlMessages from 'helpers/IntlMessages';
import InfiniteScroll from 'react-infinite-scroll-component';
import { getTenantProfiles } from 'functions/api/tenantApi';

const LIMIT = 20;

const ShowcaseAccounts = ({ isLoggedIn, setShowAccountList }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState([]);
  const [hasMore, setHasMore] = useState(false);
  const [skip, setSkip] = useState(0);

  const fetchList = async ({ skip = 0 }) => {
    try {
      if (skip === 0) {
        setIsLoading(true);
      }
      const { data, isError } = await getTenantProfiles({
        isPublic: !isLoggedIn,
        skip,
        limit: LIMIT
      });
      const totalItems = data?.items ?? [];
      const itemsLength = totalItems?.length;
      setHasMore(!(itemsLength < LIMIT));
      if (!isError) {
        setSkip(skip + LIMIT);
        setData((prev) => [...prev, ...totalItems]);
      }
      setIsLoading(false);
    } catch (error) {
      console.error('Sopmething went wrong in the fetchList due to ', error);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchList({ skip: 0 });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (isLoading) {
    return <ShowcaseLoading />;
  }

  return (
    <InfiniteScroll
      dataLength={data?.length}
      next={() => fetchList({ skip })}
      hasMore={hasMore}
      loader={
        <p style={{ textAlign: 'center', color: '#94308A' }}>
          <b>Loading...</b>
        </p>
      }
      endMessage={
        <p style={{ textAlign: 'center', color: '#94308A' }}>
          <IntlMessages id="seen-all" />
        </p>
      }
    >
      <div className="SAAllList">
        {data.map((m, i) => (
          <SACard
            key={i}
            data={m}
            handleShowcaseAccountClick={() => {
              setShowAccountList(false);
            }}
          />
        ))}
      </div>
    </InfiniteScroll>
  );
};

export default ShowcaseAccounts;
