/* eslint-disable no-unused-vars */
import BackButton from 'components/buttons/BackButton';
import InfoMessage from 'components/infoMessage';
import React, { useMemo } from 'react';
import { Card } from 'reactstrap';
import Messages from '../../Tabs/AiInsights/components/Messages';
import { formatDateNTime } from 'helpers/Utils';
import InfoPopover from 'components/InfoPopover';
import CopyIcon from 'components/svg/CopyIcon';

const SessionDetail = ({ data, isQna }) => {
  const sessionId = data?.sessionId;
  const chats = useMemo(() => {
    return data?.event_details.map((m) => {
      return {
        question: m.question,
        createdAt: m?.datetimestamp?.value,
        answer: m.answer
      };
    });
  }, [data?.event_details]);

  const date = useMemo(() => {
    const firstChat = data.event_details[0];
    const dateOfChat = firstChat.datetimestamp.value;
    return formatDateNTime(dateOfChat);
  }, [data?.event_details]);

  return (
    <div className="SessionDetail aichat">
      <div className="SDMid">
        <LabelValue label="Session ID" value={sessionId} copy />
        {!isQna && (
          <LabelValue
            label="Engagement Level"
            value={data.engagements}
            infoMessage
          />
        )}
        <LabelValue label="Date" value={date} />
      </div>

      <h5>{isQna ? 'Question ' : 'Questions'} Asked</h5>

      <div
        className="messages scrollbar-hidden p-2"
        style={{
          border: '1px solid rgba(236, 236, 236, 1)',
          borderRadius: 10,
          overflowY: 'scroll',
          maxHeight: '100%'
        }}
      >
        <Messages isQna={isQna} chat={chats} isWaiting={false} />
      </div>
    </div>
  );
};

const LabelValue = ({ label, value, infoMessage, copy }) => {
  return (
    <div className="lblvalue">
      <div className="lbl text-nowrap">
        {infoMessage && (
          <InfoPopover
            infoId="label-valye-detail"
            style={{
              fontSize: 14
            }}
            iconClassName="text-primary"
            message="Engagement Level reflects the number of questions asked by users"
            className="mr-2"
          />
        )}
        {label} :
      </div>
      <div className="text-ellipsis" style={{ fontWeight: '700' }}>
        &nbsp;{value}
      </div>
      {copy && (
        <CopyIcon
          dataToCopy={value}
          copyToClipboardOnClick
          color="#838383"
          height={15}
          width={15}
        />
      )}
    </div>
  );
};

export default SessionDetail;
