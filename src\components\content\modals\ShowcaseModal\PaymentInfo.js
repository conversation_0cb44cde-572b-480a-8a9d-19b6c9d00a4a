/* eslint-disable no-unused-vars */
// eslint-disable-next-line import/no-extraneous-dependencies
import { getAllISOCodes } from 'iso-country-currency';
import { getCurrentColor } from 'helpers/Utils';
import React, { useMemo, useState } from 'react';
import {
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  FormGroup,
  Input,
  Label,
  UncontrolledDropdown
} from 'reactstrap';
import FormInput from 'components/input/FormInput';
import { NotificationManager } from 'components/common/react-notifications';

const currentColor = getCurrentColor().includes('dark');

const PaymentInfo = ({
  originalPrice,
  setOriginalPrice,
  sellingPrice,
  setSellingPrice,
  currency,
  setCurrency,
  buttonText,
  setButtonText
}) => {
  const [search, setSearch] = useState('');
  const isoCodeList = useMemo(() => {
    return getAllISOCodes();
  }, []);

  const selectedCurrency = useMemo(() => {
    if (typeof currency === 'string' && currency.length > 0) {
      return currency;
    }
    if (currency?.currency && currency?.symbol) {
      return `${currency?.currency} (${currency?.symbol})`;
    }
    return 'Select Currency';
  }, [currency]);

  const filteredCurrency = useMemo(() => {
    return isoCodeList.filter((f) =>
      f.countryName.toLowerCase().includes(search.toLowerCase())
    );
  }, [search, isoCodeList]);

  return (
    <div>
      <div>
        <UncontrolledDropdown
          className="badge-outline-light mt-4"
          color="primary"
        >
          <DropdownToggle
            caret
            className="btn-sm w-full"
            style={{
              backgroundColor: 'transparent',
              border: 'none',
              color: currentColor ? 'silver' : '#000'
            }}
          >
            <span>{selectedCurrency}</span>
          </DropdownToggle>
          <DropdownMenu
            center
            className="dropmenuheightcont"
            style={{
              overflowY: 'scroll'
            }}
          >
            <div
              className="p-2"
              style={{
                position: 'sticky',
                top: 0,
                right: 0,
                left: 0
              }}
            >
              <Input
                type="search"
                placeholder="Please enter country name"
                value={search}
                onChange={(e) => {
                  const textValue = e?.target?.value;
                  setSearch(textValue);
                }}
              />
            </div>
            {filteredCurrency.map((m) => (
              <DropdownItem
                className="text-center"
                key={m.countryName}
                onClick={() => {
                  setCurrency(m);
                }}
              >
                {m.currency} ({m.symbol})
              </DropdownItem>
            ))}
          </DropdownMenu>
        </UncontrolledDropdown>
      </div>
      <div className="d-flex flex-row gap-2">
        <FormInput
          value={originalPrice}
          placeholder="Enter Original price"
          type="number"
          isImportant
          label="Original Price"
          style={{ width: '45%' }}
          onChangeText={(v) => {
            setOriginalPrice(v?.trim());
          }}
          errorMessage={(() => {
            if (+originalPrice === 0) {
              return 'Original price cannot be empty';
            }
            return '';
          })()}
        />

        <FormInput
          value={sellingPrice}
          placeholder="Enter selling price"
          type="number"
          isImportant
          label="Selling Price"
          style={{ width: '45%' }}
          onChangeText={(v) => {
            setSellingPrice(v?.trim());
          }}
          errorMessage={(() => {
            if (+sellingPrice === 0) {
              return 'Selling price cannot be empty';
            }
            if (+sellingPrice >= +originalPrice) {
              return 'Selling price cannot be same or more than original price';
            }
            return '';
          })()}
        />
      </div>
    </div>
  );
};

export default PaymentInfo;
