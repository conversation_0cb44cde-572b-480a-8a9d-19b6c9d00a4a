import { Field } from 'formik';
import IntlMessages from 'helpers/IntlMessages';
import React, { useState } from 'react';
import { FormGroup, InputGroupAddon, InputGroupText, Label } from 'reactstrap';

const PasswordInput = ({
  text = 'user.password',
  password,
  validate = () => {},
  errors,
  touched
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const handleTogglePassword = () => {
    setShowPassword(!showPassword);
  };
  return (
    <FormGroup className="form-group has-float-label">
      <Label>
        <IntlMessages id={text} />
        <span style={{ color: '#922c88' }}> *</span>
      </Label>
      <div style={{ display: 'flex' }}>
        <Field
          className="form-control"
          type={showPassword ? 'text' : 'password'}
          name="password"
          placeholder="password"
          value={password}
          validate={validate}
        />
        <InputGroupAddon style={{ display: 'inline-flex' }} addonType="append">
          <InputGroupText
            onClick={handleTogglePassword}
            className="cursor-pointer"
          >
            {' '}
            {showPassword ? (
              <i className="simple-icon-eye" />
            ) : (
              <img
                src="https://img.icons8.com/material-outlined/16/000000/hide.png"
                alt="hide-password"
                className="hide-password-icon"
              />
            )}
          </InputGroupText>
        </InputGroupAddon>
      </div>
      {errors?.password && touched?.password && (
        <div className="invalid-feedback d-block">{errors.password}</div>
      )}
    </FormGroup>
  );
};

export default PasswordInput;
