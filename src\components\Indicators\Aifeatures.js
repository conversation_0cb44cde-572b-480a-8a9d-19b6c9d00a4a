/* eslint-disable no-nested-ternary */
import AiIcon from 'constants/AiIcon';
import React from 'react';

const DUBBING = 2;
const CLIPPING = 3;
const REPURPOSING = 4;

const Aifeatures = ({
  status = 2,
  onClick = () => {},
  style = {},
  height = 12
}) => {
  if (status === DUBBING || status === CLIPPING || status === REPURPOSING) {
    return (
      <span
        className="c-pointer d-flex flex-row align-items-center justify-content-center"
        style={{ fontSize: 12, ...style }}
        onClick={onClick}
      >
        <AiIcon active height={height} color="#F4CA3E" />{' '}
        {status === DUBBING
          ? 'Auto Dubbed'
          : status === CLIPPING
          ? 'Auto Clipped'
          : status === REPURPOSING && 'Auto Cropped'}
      </span>
    );
  }
  return null;
};

export default Aifeatures;
