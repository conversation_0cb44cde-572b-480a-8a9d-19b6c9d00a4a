/* eslint-disable jsx-a11y/label-has-associated-control */
import TimeInput from 'components/content/modals/VideoInteraction/components/TimeInput';
import React from 'react';

const InteractionDetails = ({
  showThroughOut,
  setShowThroughout,
  startTime,
  setStartTime,
  endTime,
  setEndTime
}) => {
  return (
    <div className="w-full">
      <div className="d-flex flex-row align-items-center justify-content-start mt-2 mb-4">
        <input
          style={{
            borderWidth: 2,
            accentColor: '#992288',
            transform: 'scale(1.25)',
            marginRight: '8px'
          }}
          size={20}
          aria-label="Checkbox for following text input"
          type="checkbox"
          id="pause-duration"
          checked={showThroughOut}
          onChange={(e) => {
            setShowThroughout(e.target.checked);
          }}
        />
        <label
          name="pause-duration"
          htmlFor="pause-duration"
          className="mb-0 font-bolder"
        >
          Display throughout the video
        </label>
      </div>

      {!showThroughOut && (
        <div className="startEndTime gap-2">
          <TimeInput
            label="Start Time"
            required
            className="tymInput"
            labelStyle={{
              fontSize: '0.75rem',
              marginBottom: 0
            }}
            value={startTime}
            onChange={(val) => {
              setStartTime(val);
            }}
          />
          <TimeInput
            label="End Time"
            required
            className="tymInput"
            labelStyle={{
              fontSize: '0.75rem',
              marginBottom: 0
            }}
            value={endTime}
            onChange={(val) => {
              setEndTime(val);
            }}
          />
        </div>
      )}
    </div>
  );
};

export default InteractionDetails;
