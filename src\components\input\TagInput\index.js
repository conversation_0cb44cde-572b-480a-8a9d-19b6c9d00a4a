import IntlMessages from 'helpers/IntlMessages';
import React from 'react';
import TagsInput from 'react-tagsinput';

function TagInput(
  {
    value,
    inputValue,
    placeholder = 'Enter tags',
    onChange,
    onChangeInput,
    disabled = false,
    required,
    className = '',
    title="ins.tags"
  },
  props
) {
  return (
    <fieldset className={`tagsbox ${className}`}>
      <legend>
        <IntlMessages id={title} />
        {required && <span style={{ color: '#922c88' }}> *</span>}
      </legend>
      <TagsInput
        value={value ?? []}
        inputValue={inputValue}
        onChange={onChange}
        addOnBlur
        onlyUnique
        onChangeInput={onChangeInput}
        inputProps={{ placeholder }}
        autoComplete="off"
        className="w-full"
        disabled={disabled}
        {...props}
      />
    </fieldset>
  );
}

export default TagInput;
