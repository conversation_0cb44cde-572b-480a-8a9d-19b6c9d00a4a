import React from 'react';
import LoadingSkeleton from 'components/LoadingSkeleton';

const PollResultLoading = () => {
  return (
    <div className="d-flex flex-column gap-3 w-full">
      <LoadingSkeleton style={{ width: '50%', height: '50px' }} />
      <br />
      <br />
      <LoadingSkeleton
        style={{ width: '100%', height: '40px', marginBottom: 10 }}
      />
      <LoadingSkeleton
        style={{ width: '100%', height: '40px', marginBottom: 10 }}
      />
      <LoadingSkeleton
        style={{ width: '100%', height: '40px', marginBottom: 10 }}
      />
      <LoadingSkeleton
        style={{ width: '100%', height: '40px', marginBottom: 10 }}
      />
      <LoadingSkeleton
        style={{ width: '40px', height: '30px', marginBottom: 10 }}
      />
    </div>
  );
};

const InteractionLoading = () => {
  return (
    <div className="d-flex flex-column gap-3">
      <LoadingSkeleton style={{ width: '50%', height: '50px' }} />
      <LoadingSkeleton style={{ width: '100%', height: '300px' }} />
      <br />
      <br />
      <LoadingSkeleton
        style={{ width: '100%', height: '80px', marginBottom: 10 }}
      />
      <LoadingSkeleton
        style={{ width: '100%', height: '80px', marginBottom: 10 }}
      />
      <LoadingSkeleton
        style={{ width: '100%', height: '80px', marginBottom: 10 }}
      />
    </div>
  );
};

export { PollResultLoading, InteractionLoading };
