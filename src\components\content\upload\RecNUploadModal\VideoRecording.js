/* eslint-disable no-unused-expressions */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-vars */
/* eslint-disable jsx-a11y/media-has-caption */
import { NotificationManager } from 'components/common/react-notifications';
import InfoIcon from 'components/icon/InfoIcon';
import PauseIcon from 'constants/PauseIcon';
import PlayIcon from 'constants/PlayIcon';
import StopIcon from 'constants/StopIcon';
import VideoIcon from 'constants/VideoIcon';
import VideoMessaging from 'containers/navs/Sidebar/VideoMessaging';
import IntlMessages from 'helpers/IntlMessages';
import React, { useCallback, useEffect, useState } from 'react';
import {
  Button,
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  UncontrolledDropdown
} from 'reactstrap';
import {
  PauseRecordingButton,
  ResumeRecordingButton,
  StopRecordingButton
} from './CommonButtons';

const VideoRecording = ({
  setMediaFile,
  audioDevices: microphones,
  videoDevices: cameras,
  isError,
  isPermissionDenied,
  setDuration,
  setIsAnyTabRecording
}) => {
  const [selectedCamera, setSelectedCamera] = useState(null);
  const [selectedCameraName, setSelectedCameraName] = useState(null);
  const [selectedMicrophone, setSelectedMicrophone] = useState(null);
  const [recording, setRecording] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState(null);
  const somethingWrong = isError || isPermissionDenied;
  const isRecordingStarterd = recording && mediaRecorder?.stream;
  const [startTime, setStartTime] = useState(null);
  const [isResumed, setIsResumed] = useState(false);
  const [isPaused, setIsPaused] = useState(false);

  useEffect(() => {
    return () => {
      // making camera off when we change the tab
      // eslint-disable-next-line array-callback-return
      mediaRecorder?.stream?.getTracks()?.map((track) => {
        setDuration((Date.now() - startTime) / 1050);
        track?.stop();
      });
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mediaRecorder]);

  useEffect(() => {
    async function getVideoStream() {
      try {
        setDuration(0);
        const videoStream = await navigator.mediaDevices.getUserMedia({
          video:
            isError || !selectedCamera ? false : { deviceId: selectedCamera },
          audio:
            isError || !selectedMicrophone
              ? false
              : { deviceId: selectedMicrophone }
        });
        const recorder = new MediaRecorder(videoStream);
        recorder.onresume = () => {
          setIsResumed(true);
          setIsPaused(false);
        };
        recorder.onpause = () => {
          setIsResumed(false);
          setIsPaused(true);
        };
        setMediaRecorder(recorder);
        recorder.state === 'inactive' &&
          (recorder.ondataavailable = (event) => {
            const blobData = event.data;
            const file = new File([blobData], `${Date.now()}.webm`, {
              type: blobData?.type
            });
            setMediaFile(file);
            setRecording(false);
          });
      } catch (error) {
        console.error('Error getting video stream:', error);
      }
    }
    if (selectedCamera) {
      getVideoStream();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedCamera, selectedMicrophone, isError]);

  const toggleRecording = () => {
    // if (!selectedMicrophone) {
    //   NotificationManager.warning(
    //     <IntlMessages id="select-micro" />,
    //     <IntlMessages id="req.warning" />
    //   );
    //   return;
    // }
    if (recording) {
      mediaRecorder?.stop();
      setIsAnyTabRecording(false);
      setDuration((Date.now() - startTime) / 1050);
      // eslint-disable-next-line array-callback-return
      mediaRecorder?.stream?.getTracks()?.map((track) => {
        track.stop();
      });
    } else {
      setStartTime(Date.now());
      mediaRecorder?.start();
      setIsAnyTabRecording(true);
    }
    setRecording(!recording);
  };

  const pauseRecording = useCallback(() => {
    if (mediaRecorder.state === 'recording') {
      mediaRecorder.pause();
    }
  }, [mediaRecorder]);

  const resumeRecording = useCallback(() => {
    if (mediaRecorder.state === 'paused') {
      mediaRecorder.resume();
    }
  }, [mediaRecorder]);

  const showButtonsAndVideo =
    !somethingWrong && selectedCamera && mediaRecorder?.stream;

  return (
    <>
      {isPermissionDenied && (
        <div
          className="mb-4"
          style={{
            fontSize: '14px',
            padding: '20px',
            color: 'red',
            border: '1px solid red',
            borderRadius: '10px',
            fontWeight: 'bold'
          }}
        >
          <i className="simple-icon-info font-weight-bold"> </i>
          <IntlMessages id="no-mic-camera-permission" />
        </div>
      )}
      {!isRecordingStarterd && (
        <div
          className="d-flex flex-column mt-4"
          aria-disabled={isPermissionDenied}
        >
          <div className="d-flex flex-row justify-content-center">
            <div className=" d-flex align-items-center justify-content-center mr-3">
              <span className="media-label-title">
                <InfoIcon
                  message="enable camera while recordign your screen"
                  name=""
                />
              </span>
            </div>
            <UncontrolledDropdown style={{ width: '250px' }}>
              <DropdownToggle
                caret
                disabled={isRecordingStarterd}
                color="primary"
                className="btn-sm w-full overflow-hidden"
                outline
              >
                {selectedCamera && !isError
                  ? selectedCameraName
                  : 'Select camera'}
              </DropdownToggle>
              <DropdownMenu center>
                {isError && (
                  <DropdownItem
                    onClick={() => {
                      setSelectedCamera('');
                    }}
                  >
                    Default Camera
                  </DropdownItem>
                )}
                <DropdownItem
                  onClick={() => {
                    setSelectedCamera('');
                  }}
                >
                  Select Camera
                </DropdownItem>
                {cameras?.map((camera) => (
                  <DropdownItem
                    key={camera.deviceId}
                    onClick={() => {
                      setSelectedCameraName(camera.label);
                      setSelectedCamera(camera.deviceId);
                    }}
                  >
                    {camera.label || `Camera ${camera.deviceId}`}
                  </DropdownItem>
                ))}
              </DropdownMenu>
            </UncontrolledDropdown>
          </div>
          <div className="d-flex flex-row mt-4">
            <div className=" d-flex align-items-center justify-content-center mr-3">
              <span className="media-label-title">
                <InfoIcon
                  name="mic"
                  message="enable your microphone while recording the screen"
                />
              </span>
            </div>
            <div style={{ width: '250px' }}>
              <UncontrolledDropdown style={{ width: '250px' }}>
                <DropdownToggle
                  disabled={isRecordingStarterd}
                  caret
                  color="primary"
                  className="btn-sm w-full overflow-hidden"
                  outline
                >
                  {selectedMicrophone && !isError
                    ? selectedMicrophone
                    : 'Select microphone'}
                </DropdownToggle>
                <DropdownMenu center>
                  {isError && (
                    <DropdownItem
                      onClick={() => {
                        setSelectedMicrophone('');
                      }}
                    >
                      Default Microphone
                    </DropdownItem>
                  )}
                  <DropdownItem
                    onClick={() => {
                      setSelectedMicrophone('');
                    }}
                  >
                    Select Microphone
                  </DropdownItem>
                  {microphones?.map((microphone) => (
                    <DropdownItem
                      key={microphone.deviceId}
                      onClick={() => {
                        setSelectedMicrophone(microphone.deviceId);
                      }}
                    >
                      {microphone.label || `Microphone ${microphone.deviceId}`}
                    </DropdownItem>
                  ))}
                </DropdownMenu>
              </UncontrolledDropdown>
            </div>
          </div>
        </div>
      )}
      {showButtonsAndVideo && (
        <>
          <video
            className="mt-4"
            style={{ borderRadius: '10px' }}
            autoPlay
            height={275}
            width={350}
            muted
            playsInline
            ref={(videoElement) => {
              if (videoElement && mediaRecorder?.stream) {
                videoElement.srcObject = mediaRecorder?.stream;
              }
            }}
          />
        </>
      )}
      <div className="mt-3 bottom-rec-btns gap-2">
        {(recording || isResumed) && !isPaused && (
          <PauseRecordingButton onClick={pauseRecording} />
        )}
        {isPaused && <ResumeRecordingButton onClick={resumeRecording} />}
        <div aria-disabled={isPermissionDenied}>
          {recording ? (
            <StopRecordingButton
              disabled={!selectedCamera || !showButtonsAndVideo}
              onClick={toggleRecording}
            />
          ) : (
            <Button
              className="mt-4 align-self-center"
              type="button"
              style={{ minWidth: '200px', color: '#fff' }}
              disabled={!selectedCamera || !showButtonsAndVideo}
              color="primary"
              onClick={toggleRecording}
            >
              <VideoIcon color="#fff" />
              Start Recording
            </Button>
          )}
        </div>
      </div>
    </>
  );
};

export default VideoRecording;
