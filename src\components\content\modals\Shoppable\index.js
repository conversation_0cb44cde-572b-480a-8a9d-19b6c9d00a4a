/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import { Modal, ModalBody } from 'reactstrap';
import Header from './Header';
import CreateOrEdit from './CreateOrEdit';
import { getShoppableProduct } from 'functions/api/shoppableApi';
import Loading from './Loading';

const Shoppable = ({
  modalOpen,
  setModalOpen,
  content,
  handlePlayerChange,
  setContent
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState(null);

  const handleClose = () => {
    setModalOpen(false);
  };

  const handleCancel = () => {
    handleClose();
  };

  const fetchShoppableProduct = async () => {
    const handleError = () => {
      setIsLoading(false);
      setData(null);
    };
    try {
      setIsLoading(true);
      const { data, isError } = await getShoppableProduct({
        body: {
          spaceId: content.spaceId,
          contentId: content.id
        }
      });
      if (isError) {
        handleError();
      } else {
        setData(data);
      }
      setIsLoading(false);
    } catch (error) {
      handleError();
      console.log('Something went wrong in getShoppableProduct due to ', error);
    }
  };

  useEffect(() => {
    fetchShoppableProduct();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [content.spaceId, content.id]);

  return (
    <Modal
      isOpen={modalOpen}
      toggle={handleClose}
      contentClassName="border-radius-10"
      centered
      style={{
        minWidth: '50%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 20
      }}
      backdrop="static"
    >
      <ModalBody
        className="m-3 border-radius-10 d-flex align-items-center flex-column p-0"
        style={{
          border: '1px solid #992288',
          maxHeight: '90vh',
          overflowY: 'auto'
        }}
      >
        <Header name={content?.title} close={handleClose} />
        <div className="p-3 w-full">
          {isLoading ? (
            <Loading />
          ) : (
            <>
              <CreateOrEdit
                data={data}
                content={content}
                setContent={setContent}
                handleCancel={handleCancel}
                handlePlayerChange={handlePlayerChange}
                fetchShoppableProduct={fetchShoppableProduct}
              />
            </>
          )}
        </div>
      </ModalBody>
    </Modal>
  );
};

const AddNewProduct = (props) => {
  return (
    <div
      {...props}
      className="d-flex w-full flex-row align-items-center justify-content-center"
    >
      <span className="d-flex flex-row align-items-center justify-content-center gap-2 text-primary">
        <i className="simple-icon-plus" />
        <p className="m-0 text-nowrap">Add new product</p>
      </span>
    </div>
  );
};

export default Shoppable;
