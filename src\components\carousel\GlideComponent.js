/* eslint-disable no-plusplus */
/* eslint-disable prefer-destructuring */
/* eslint-disable react/forbid-prop-types */
// eslint-disable-next-line react/forbid-prop-types
import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import Glide from '@glidejs/glide';
import { getDirection } from 'helpers/Utils';
import useWindowDimensions from 'hooks/useWindowDimensions';
import '@glidejs/glide/dist/css/glide.core.min.css';

let resizeTimeOut = -1;
let mountTimeOut = -1;

function GlideComponent({ settings, itemsCount, children }) {
  const { width } = useWindowDimensions();
  const [slidesPerview, setSlidesPerview] = useState(3);
  const [disableLeft, setDisableLeft] = useState(true);
  const [disableRight, setDisableRight] = useState(false);

  const carousel = useRef(null);
  const glideCarousel = useRef(null);

  const getSlidesPerview = () => {
    if (width <= 575) setSlidesPerview(itemsCount);
    else if (width <= 675) setSlidesPerview(1);
    else if (width <= 775) setSlidesPerview(2);
    else if (width <= 975) setSlidesPerview(3);
    else if (width <= 1075) setSlidesPerview(4);
    else if (width <= 1175) setSlidesPerview(5);
    else setSlidesPerview(6);
  };

  useEffect(() => {
    getSlidesPerview();
    // eslint-disable-next-line
  }, [width, itemsCount]);

  const onResize = () => {
    clearTimeout(resizeTimeOut);
    resizeTimeOut = setTimeout(() => {
      glideCarousel.current.update();
      resizeTimeOut = -1;
    }, 500);
  };

  const updateButtonState = () => {
    const index = glideCarousel.current.index;
    setDisableLeft(index === 0);
    setDisableRight(index + slidesPerview >= itemsCount);
  };

  const initGlide = () => {
    glideCarousel.current = new Glide(carousel.current, {
      ...settings,
      perView: slidesPerview,
      direction: getDirection().direction
    });

    glideCarousel.current.mount();
    glideCarousel.current.on('resize', onResize);
    glideCarousel.current.on('run', updateButtonState);
    mountTimeOut = setTimeout(() => {
      const event = document.createEvent('HTMLEvents');
      event.initEvent('resize', false, false);
      window.dispatchEvent(event);
      updateButtonState();
    }, 500);
  };

  const destroyGlide = () => {
    clearTimeout(resizeTimeOut);
    clearTimeout(mountTimeOut);
    if (glideCarousel.current) {
      glideCarousel.current.destroy();
    }
  };

  useEffect(() => {
    initGlide();
    return () => {
      destroyGlide();
    };
    /* eslint-disable-next-line react-hooks/exhaustive-deps */
  }, [slidesPerview, itemsCount]);

  const renderDots = () => {
    const total = React.Children.count(children);
    const dots = [];
    for (let i = 0; i < total; i++) {
      dots.push(
        <button
          type="button"
          className="glide__bullet slider-dot"
          key={i}
          data-glide-dir={`=${i}`}
        />
      );
    }
    return dots;
  };

  return (
    <div>
      <div className="glide d-flex" ref={carousel}>
        {itemsCount >= slidesPerview && (
          <div className="glide__arrows slider-nav" data-glide-el="controls">
            <button
              type="button"
              className="glide__arrow glide__arrow--left left-arrow btn btn-link"
              data-glide-dir="<"
              disabled={disableLeft}
            >
              <i className="simple-icon-arrow-left" />
            </button>
          </div>
        )}

        <div data-glide-el="track" className="glide__track w-100">
          <div className="glide__slides">{children}</div>
        </div>

        {itemsCount >= slidesPerview && (
          <div className="glide__arrows slider-nav" data-glide-el="controls">
            <button
              type="button"
              className="glide__arrow glide__arrow--right right-arrow btn btn-link"
              data-glide-dir=">"
              disabled={disableRight}
            >
              <i className="simple-icon-arrow-right" />
            </button>
          </div>
        )}
        {!settings.hideNav && (
          <div className="glide__arrows slider-nav" data-glide-el="controls">
            <div
              className="glide__bullets slider-dot-container"
              data-glide-el="controls[nav]"
            >
              {renderDots()}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

GlideComponent.defaultProps = {
  settings: {}
};

GlideComponent.propTypes = {
  settings: PropTypes.shape({
    type: PropTypes.string,
    startAt: PropTypes.number,
    perView: PropTypes.number,
    focusAt: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    customSlideAnimation: PropTypes.object,
    gap: PropTypes.number,
    autoplay: PropTypes.bool,
    hoverpause: PropTypes.bool,
    keyboard: PropTypes.bool,
    bound: PropTypes.bool,
    swipeThreshold: PropTypes.oneOfType([PropTypes.number, PropTypes.bool]),
    dragThreshold: PropTypes.oneOfType([PropTypes.number, PropTypes.bool]),
    perTouch: PropTypes.oneOfType([PropTypes.number, PropTypes.bool]),
    touchRatio: PropTypes.number,
    touchAngle: PropTypes.number,
    animationDuration: PropTypes.number,
    rewind: PropTypes.bool,
    rewindDuration: PropTypes.number,
    animationTimingFunc: PropTypes.string,
    direction: PropTypes.string,
    peek: PropTypes.object,
    breakpoints: PropTypes.object,
    classes: PropTypes.object,
    throttle: PropTypes.number,
    className: PropTypes.string
  }),
  itemsCount: PropTypes.number.isRequired,
  children: PropTypes.node.isRequired
};

export default GlideComponent;
