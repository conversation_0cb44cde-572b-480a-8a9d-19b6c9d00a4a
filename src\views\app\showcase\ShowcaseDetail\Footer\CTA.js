/* eslint-disable no-unneeded-ternary */
import React from 'react';
import { Button } from 'reactstrap';

const CTA = ({ onClick, data, className }) => {
  const purposeName = data?.purpose?.name;
  const isBuyNow = purposeName === 'Buy Now';
  const originalPrice = data?.purpose?.originalPrice ?? '';
  const sellingPrice = data?.purpose?.sellingPrice ?? '';
  const currency = data?.purpose?.priceSymbol ?? '';
  const btnText = data?.purpose?.buttonText ?? '';
  const trimmedButtonText = btnText?.trim();

  const conditionForBuyNow =
    isBuyNow &&
    +originalPrice > 0 &&
    +sellingPrice > 0 &&
    currency &&
    trimmedButtonText?.length > 0;

  if (conditionForBuyNow) {
    return (
      <BuyNowButton
        data={data}
        onClick={onClick}
        originalPrice={originalPrice}
        currency={currency}
        sellingPrice={sellingPrice}
        btnText={trimmedButtonText}
      />
    );
  }
  return (
    <Button
      color="primary"
      className={className ? className : 'btn ExpButton'}
      style={{ textWrap: 'nowrap' }}
      onClick={onClick}
    >
      {trimmedButtonText?.length > 0 ? trimmedButtonText : purposeName}
    </Button>
  );
};

export default CTA;

const BuyNowButton = ({
  onClick,
  originalPrice,
  sellingPrice,
  currency,
  btnText
}) => {
  return (
    <div className="CTA-price">
      <p className="priceText">Price : </p>
      <p className="orgPrice">
        {currency}&nbsp;
        {sellingPrice}
      </p>
      <p className="sellingPrice">{originalPrice}</p>
      <Button
        color="primary"
        className="btn ExpButton"
        style={{
          textWrap: 'nowrap',
          width: 'unset !important',
          fontSize: '0.70rem !important'
        }}
        onClick={onClick}
      >
        {btnText}
      </Button>
    </div>
  );
};
