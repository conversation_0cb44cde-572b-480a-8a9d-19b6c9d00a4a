const express = require('express');
const { sendSupportMail } = require('../services/emailService');
const { getUser } = require('../services/profileService');
const { ruleNameFromType, getSubscriptionName } = require('../utils/rules');

const router = express.Router();
// const Response = require('../model/response');

router.post('/', async (req, res, next) => {
  try {
    const response = await getUser(req.user);

    await sendSupportMail({
      message: req.body.message,
      reqType: req.body.reqType,
      userId: req.user.user_id,
      name: response.fullName,
      email: req.user.email,
      userRole: ruleNameFromType(response.roleType),
      accountId: req.user.selectedTenantId,
      accountName: response.selectedTenant.name,
      subscriptionType: getSubscriptionName(
        response.subscription.subscriptionType
      ),
      subscriptionID: response.subscription.subscriptionId,
      accountOwnerName: response.selectedTenant.ownerName,
    });

    return res.send({ success: true });
  } catch (e) {
    console.error(e);
    return next(e);
  }
});

module.exports = router;
