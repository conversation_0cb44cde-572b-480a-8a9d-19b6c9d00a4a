import React, { useEffect, useState } from 'react';

const CalendlyModal = ({ open, toggle }) => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        toggle();
      }
    };
    window.addEventListener('keydown', handleEscape);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  if (open) {
    return (
      <>
        <div className="calendly-cont">
          <i
            onClick={toggle}
            style={{ right: 20, top: 20, color: '#fff' }}
            className="simple-icon-close close-btn position-absolute"
          />
          {isLoading ? <div className="loading" /> : null}
          <iframe
            src="https://calendly.com/innerloop-streaming/15min?embed_type=PopupWidget&amp;embed_domain=1"
            title="Calendly Scheduling Page"
            width="100%"
            height="100%"
            style={{ border: 'none' }}
            onLoad={() => setIsLoading(false)}
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          />
        </div>
      </>
    );
  }
  return null;
};

export default CalendlyModal;
