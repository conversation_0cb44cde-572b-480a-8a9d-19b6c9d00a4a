/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import IntlMessages from 'helpers/IntlMessages';
import { Row } from 'reactstrap';
import { Colxx } from 'components/common/CustomBootstrap';
import React, { useState } from 'react';
import { RadioInput } from 'components/input';
import InfoIcon from 'components/icon/InfoIcon';

function AdvanceOptions({
  AESStatus,
  DRMStatus,
  setAESStatus,
  setDRMStatus,
  aesEnabled,
  drmEnabled
}) {
  const [isOpen, setIsOpen] = useState(false);
  const bothEnabled = drmEnabled && aesEnabled;
  return (
    <Colxx className="d-flex flex-column align-items-center justify-content-center my-4">
      <span onClick={() => setIsOpen(!isOpen)} className="mb-2 c-pointer">
        <b className="text-primary">
          <IntlMessages id="ad-options" />
          <i className={`ml-2 simple-icon-arrow-${isOpen ? 'up' : 'down'}`} />
        </b>
      </span>
      {isOpen && (
        <Colxx
          className="mt-2 d-flex flex-column align-items-center justify-content-center"
          style={{ border: '1px solid silver', borderRadius: '10px' }}
        >
          <span className="my-3" style={{ borderBottom: '2px solid silver' }}>
            <IntlMessages id="ad-media-protection" />
          </span>
          <Row className="d-flex align-items-center justify-content-end w-80">
            <span
              className="text-primary c-pointer"
              onClick={() => {
                setDRMStatus(false);
                setAESStatus(false);
              }}
            >
              <i className="mr-2 simple-icon-reload" />
              <IntlMessages id="ad-clear-selection" />
            </span>
          </Row>
          <Row
            className="w-70 mt-4 pb-5 align-items-center"
            style={{
              fontSize: '15px',
              justifyContent: bothEnabled ? 'space-between' : 'center'
            }}
          >
            {aesEnabled && (
              <Row className="d-flex flex-row align-items-center justify-content-center">
                <RadioInput
                  id="AES"
                  name="protection"
                  value={AESStatus}
                  checked={AESStatus}
                  onChange={() => {
                    setAESStatus(true);
                    setDRMStatus(false);
                  }}
                />
                <label className="mx-2 mb-0" htmlFor="AES">
                  <IntlMessages id="ad-aes" />
                </label>
                <InfoIcon name="AES" message="ad-aes-info" />
              </Row>
            )}
            {drmEnabled && (
              <Row className="d-flex flex-row align-items-center justify-content-center">
                <RadioInput
                  name="protection"
                  id="DRM"
                  value={DRMStatus}
                  checked={DRMStatus}
                  onChange={() => {
                    setAESStatus(false);
                    setDRMStatus(true);
                  }}
                />
                <label className="mx-2 mb-0" htmlFor="DRM">
                  <IntlMessages id="ad-drm" />
                </label>
                <InfoIcon name="DRM" message="ad-drm-info" />
              </Row>
            )}
          </Row>
        </Colxx>
      )}
    </Colxx>
  );
}

export default AdvanceOptions;
