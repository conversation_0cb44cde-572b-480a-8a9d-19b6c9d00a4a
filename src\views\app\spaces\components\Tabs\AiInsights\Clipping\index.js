/* eslint-disable react/no-array-index-key */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable no-unused-vars */
/* eslint-disable no-else-return */
import AiIcon from 'constants/AiIcon';
import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Card } from 'reactstrap';
import ClippingDropdown from './dropdown';
import IntlMessages from 'helpers/IntlMessages';
import { useHistory } from 'react-router-dom';
import { getAiContents, handleSieveOperations } from 'functions/api/analyze';
import RefreshButton from 'components/buttons/RefreshButton';
import {
  isDarkModeActive,
  isNoAudioError,
  isSieveProcessing
} from 'helpers/Utils';
import usePreview from 'hooks/usePreview';
import LoadingSkeleton from 'components/LoadingSkeleton';
import Loader from 'react-loader-spinner';
import { NotificationManager } from 'components/common/react-notifications';
import InfoMessage from 'components/infoMessage';
import { AI_OPERATION_TYPE } from 'constants/defaultValues';
import WarningInfoCard from '../WarningInfoCard';

function Clipping({
  previewCache,
  content,
  isStartedClipping,
  setIsStartedClipping,
  isClipping,
  setIsClipping
}) {
  const {
    spaceId,
    id: contentId,
    duration: mediaDuration,
    mediaType
  } = content;

  const isVideo = +mediaType === 3;

  // const [isClipping, setIsClipping] = useState(false);
  const [isCheckingStatus, setIsCheckingStatus] = useState(false);
  const [clippedContents, setClippedContents] = useState({
    isFetching: true,
    list: [],
    jobDetails: null
  });
  const history = useHistory();

  const handleClipping = async () => {
    try {
      setIsStartedClipping(true);
      const res = await handleSieveOperations(
        {
          aiOperationType: 3,
          contentId,
          mediaDuration
        },
        spaceId
      );
      if (typeof res === 'string') {
        setIsStartedClipping(false);
        return;
      }
      if (res.isError) {
        NotificationManager.error(
          <IntlMessages id="clip-msg-error" />,
          <IntlMessages id="req.failed" />
        );
      } else {
        setIsClipping(true);
        setIsStartedClipping(false);
        NotificationManager.success(
          <IntlMessages id="clip-msg-success" />,
          <IntlMessages id="req.success" />
        );
      }
    } catch (error) {
      setIsStartedClipping(false);
      NotificationManager.error(
        <IntlMessages id="clip-msg-error" />,
        <IntlMessages id="req.failed" />
      );
      console.log('error', error);
    }
  };

  const fetchClippingContents = async () => {
    try {
      const { data, isError } = await getAiContents({
        spaceId,
        contentId,
        contentKind: AI_OPERATION_TYPE.AutoClipping
      });
      if (!isError) {
        setClippedContents({
          isFetching: false,
          list: data.items,
          jobDetails: data.jobDetails
        });
        const isStillProcessing = isSieveProcessing(
          data.jobDetails?.sieveJobStatus
        );

        if (isStillProcessing || (!data?.jobDetails && isClipping)) {
          setIsClipping(true);
          NotificationManager.success(
            <IntlMessages id="clip-generate-process-info" />
          );
        }
      } else {
        setClippedContents({
          list: [],
          isFetching: false
        });
      }
      setIsCheckingStatus(false);
    } catch (error) {
      console.error(error);
      setIsCheckingStatus(false);
      NotificationManager.error(
        <IntlMessages id="clip-list-error" />,
        <IntlMessages id="req.failed" />
      );
    }
  };

  const handleReload = () => {
    setIsCheckingStatus(true);
    setIsClipping(false);
    setClippedContents({
      isFetching: true,
      list: [],
      jobDetails: null
    });
    fetchClippingContents();
  };

  useEffect(() => {
    handleReload();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (isClipping) {
    return (
      <div className="w-full">
        <div className="d-flex flex-row align-items-center justify-content-between">
          <h3 className="mb-0">
            <IntlMessages id="clip-title" />
          </h3>
          <RefreshButton
            handleRefresh={handleReload}
            text={isCheckingStatus ? 'Checking' : 'Check Status'}
          />
        </div>
        <div
          className="w-full centered d-flex flex-row font-bolder"
          color="#787878"
        >
          <IntlMessages id="clip-generate-info" />
          <Loader
            className="ml-3"
            type="TailSpin"
            color="#787878"
            height={15}
            width={15}
          />
        </div>
      </div>
    );
  }

  if (clippedContents.isFetching) {
    return (
      <div className="d-flex flex-column w-full p-2">
        <div className="d-flex flex-row justify-content-end">
          <LoadingSkeleton
            isContainer
            className="border-10"
            style={{ height: '50px', width: '175px' }}
          />
        </div>
        <div className="d-flex flex-column">
          <ClipCardLoading />
          <ClipCardLoading />
          <ClipCardLoading />
        </div>
      </div>
    );
  }

  const isAudioError = isNoAudioError(clippedContents?.jobDetails?.error);

  return (
    <div className="d-flex flex-column w-full">
      <div className="d-flex flex-row align-items-center justify-content-end my-2">
        <RefreshButton handleRefresh={handleReload} />
      </div>
      {!clippedContents?.list?.length > 0 && (
        <div className="w-full centered mb-3 mt-1">
          <Button
            disabled={
              clippedContents?.list?.length > 0 ||
              isStartedClipping ||
              clippedContents.isFetching
            }
            color="primary"
            className="mb-2"
            onClick={handleClipping}
          >
            <AiIcon primary={false} active />{' '}
            <IntlMessages id="clip-generate" />
            {isStartedClipping && (
              <span className="ml-2 text-primary pr-4">
                <span className="show-spinner btn-multiple-state">
                  <span className="spinner d-inline-block">
                    <span
                      style={{ backgroundColor: '#fff' }}
                      className="bounce1"
                    />
                    <span
                      style={{ backgroundColor: '#fff' }}
                      className="bounce2"
                    />
                    <span
                      style={{ backgroundColor: '#fff' }}
                      className="bounce3"
                    />
                  </span>
                </span>
              </span>
            )}
          </Button>
          <InfoMessage style={{ fontSize: 14 }} message="clip-title-info" />
          <WarningInfoCard
            text={isVideo ? 'clipping-work-vid' : 'clipping-work-aud'}
          />
        </div>
      )}

      {clippedContents?.jobDetails?.error?.length > 0 && (
        <div className="w-full centered">
          <div className="mt-2 novtt">
            <i className="simple-icon-info mr-2" />
            <IntlMessages
              id={
                isAudioError ? 'clipping-no-audio' : 'clipping-something-error'
              }
            />
          </div>
        </div>
      )}
      <div className="clipsCon">
        {!isStartedClipping &&
          clippedContents?.list?.map((m) => (
            <ClipCard
              history={history}
              data={m}
              key={m.id}
              previewCache={previewCache}
            />
          ))}
      </div>
    </div>
  );
}

const ClipButton = ({ type }) => {
  // eslint-disable-next-line consistent-return
  const config = (() => {
    if (type === 4) {
      return {
        text: 'clip-failed',
        color: '#FF0000',
        bgColor: 'transparent',
        isLoading: false
      };
    } else if (type === 2 || type === 3) {
      return {
        text: 'clip-success',
        color: '#008000',
        bgColor: 'transparent',
        isLoading: false
      };
    } else if (type === 1 || type === 0) {
      return {
        text: 'clip-processing',
        color: '#fff',
        bgColor: '#00A3FF',
        isLoading: true
      };
    }
    return {
      text: 'clip-processing',
      color: '#fff',
      bgColor: '#00A3FF',
      isLoading: true
    };
  })();

  return (
    <Button
      color="primary"
      style={{
        color: config.color,
        background: config.bgColor,
        border: `1px solid ${config.color}`
      }}
      className="d-flex flex-row gap-3 align-items-center py-1"
    >
      <p className="m-0 ml-2 text-nowrap px-2 py-1">
        <IntlMessages id={config.text} />
        {config.isLoading && (
          <span className="show-spinner btn-multiple-state pr-3">
            <span className="spinner d-inline-block">
              <span style={{ backgroundColor: '#fff' }} className="bounce1" />
              <span style={{ backgroundColor: '#fff' }} className="bounce2" />
              <span style={{ backgroundColor: '#fff' }} className="bounce3" />
            </span>
          </span>
        )}
      </p>
    </Button>
  );
};

const getFormattedTimeStamp = (time) => {
  const [hours, minutes, seconds] = time.split('.')[0].split(':');
  return `${hours}:${minutes}:${seconds}`;
};

const ClipCard = ({ data, history, previewCache }) => {
  const contentReady = data?.status === 2 || data?.status === 3;
  const [buttonIsHovered, setButtonIsHovered] = useState(false);
  const spaceId = data?.spaceId;

  const handleEdit = () => {
    if (data?.status === 2 || data?.status === 3) {
      const link = document.createElement('a');
      link.href = `${window.location.origin}/app/spaces/space/${spaceId}/manage-content/${data.id}/edit`;
      link.target = '_blank';
      link.click();
    }
  };

  const { previewSrc, previewValid: isPreviewValid } = usePreview({
    spaceId,
    contentId: data.id,
    isMulti: false,
    MediaType: data.mediaType,
    waitFor: (() => {
      if (data?.status === 2 || data?.status === 3) {
        return true;
      }
      return false;
    })(),
    previewCache
  });

  return (
    <Card className="border-10 w-full p-3 px-4 mt-4 clipCard">
      <div
        className="d-flex flex-row align-items-center gap-1 preview"
        onClick={handleEdit}
      >
        {previewSrc && isPreviewValid ? (
          <img
            style={{ objectFit: 'contain' }}
            alt={data.title || 'media-thumbnail'}
            src={previewSrc}
          />
        ) : (
          <i
            className={`simple-icon-camrecorder list-default-icon-style list-thumbnail 
             border-0 card-img-left`}
          />
        )}
      </div>
      <div className="mid">
        <div className="mid-title">
          <p
            className="text-ellipsis mb-0 font-weight-bolder"
            onClick={handleEdit}
            style={{
              cursor: contentReady && 'pointer',
              // eslint-disable-next-line no-nested-ternary
              color: buttonIsHovered
                ? '#922c88'
                : isDarkModeActive()
                ? 'silver'
                : ''
            }}
            onMouseEnter={() => {
              if (contentReady) setButtonIsHovered(true);
            }}
            onMouseLeave={() => setButtonIsHovered(false)}
          >
            {data.title}
          </p>
        </div>
        <div className="mid-time">
          {data?.startTimecode && data?.endTimecode && (
            <div className="text-center" style={{ fontSize: 13 }}>
              <span>
                {getFormattedTimeStamp(data?.startTimecode)}
                <i className="simple-icon-clock text-primary" />
              </span>
              <span className="mx-2">-</span>
              <span>
                {getFormattedTimeStamp(data?.endTimecode)}
                <i className="simple-icon-clock text-primary" />
              </span>
            </div>
          )}
        </div>
      </div>
      <div
        style={{
          justifyContent: data?.status === 4 ? 'space-between' : 'center'
        }}
        className="d-flex flex-row gap-2  buttons"
      >
        <ClipButton type={data?.status} />
        {data?.status === 4 && (
          <ClippingDropdown data={data} history={history} />
        )}
      </div>
    </Card>
  );
};

const ClipCardLoading = () => {
  return (
    <div
      className="d-flex flex-row align-items-center justify-content-between p-2 mt-3 position-relative"
      style={{ height: 100 }}
    >
      <LoadingSkeleton
        className="border-10 w-full position-absolute"
        style={{ height: 100, top: 0, right: 0, left: 0, bottom: 0 }}
      />
      <div className="d-flex flex-row w-50 gap-2 ml-4 align-items-center">
        <div className="w-30">
          <LoadingSkeleton
            className="border-10 w-full"
            style={{ height: '80px' }}
          />
        </div>
        <div className="w-50">
          <LoadingSkeleton
            className="border-10 w-full"
            style={{ height: '30px' }}
          />
        </div>
      </div>
      <div className="w-20" />
      <div className="w-30">
        <LoadingSkeleton
          className="border-10 w-full"
          style={{ height: '50px' }}
        />
      </div>
    </div>
  );
};

export default Clipping;
