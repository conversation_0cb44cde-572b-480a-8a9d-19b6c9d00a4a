.aspectRatioBox {
  height: 100px;
  width: 100px;
  border: 1px solid #CBCBCB;
  border-radius: 5px;
}
.aspectRatioBox span {
  background: #CBCBCB;
}
.aspectRatioBox p {
  font-size: 12px;
}
.aspectRatioBox:hover, .aspectRatioBox-active {
  border: 1px solid #922c88 !important;
  background: #FFF2FE;
}
.aspectRatioBox:hover span, .aspectRatioBox-active span {
  background: #922c88;
}
.aspectRatioBox:hover p, .aspectRatioBox-active p {
  color: #992288;
  font-size: 12px;
}

.RepurposeGroup {
  display: flex;
  flex-direction: column;
  border: 1px solid silver;
  padding: 1.5rem;
  border-radius: 10px;
  position: relative;
}
.RepurposeGroup .topCol {
  display: flex;
  flex-direction: row;
  width: 100%;
  align-items: center;
  justify-content: space-between;
}
.RepurposeGroup .topCol .topLeft {
  width: 50%;
}
.RepurposeGroup .topCol .topRight {
  width: 50%;
  text-align: right;
}
@media (max-width: 1000px) {
  .RepurposeGroup .topCol {
    flex-direction: column;
  }
  .RepurposeGroup .topCol .topLeft {
    width: 100%;
  }
  .RepurposeGroup .topCol .topRight {
    text-align: left;
    margin-top: 5px;
    width: 100%;
  }
}

.repurposeCard {
  padding-top: 2rem;
  padding-bottom: 2rem;
  padding: 1rem;
  border: 1px solid silver;
  display: flex;
  flex-direction: row;
  border-radius: 10px;
  align-items: center;
  font-size: 14px;
}
.repurposeCard p {
  font-size: 14px;
}
@media (max-width: 1000px) {
  .repurposeCard {
    font-size: 12px;
  }
  .repurposeCard p {
    font-size: 12px;
  }
}

.repurposeBtn {
  padding: 0px;
}/*# sourceMappingURL=repurpose.css.map */