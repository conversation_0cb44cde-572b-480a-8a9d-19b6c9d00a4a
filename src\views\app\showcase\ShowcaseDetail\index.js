/* eslint-disable no-unused-vars */
import { NotificationManager } from 'components/common/react-notifications';
import {
  getPublicSingleShowcase,
  getPublicStreamingToken,
  updateLastViewdTT,
  updateShowcaseClicks,
  updateShowcaseImpression
} from 'functions/api/publicApi';
import IntlMessages from 'helpers/IntlMessages';
import { checkifLoggedIn, handleShowcaseShare } from 'helpers/Utils';
import React, { useEffect, useMemo, useState } from 'react';
import { Button, Card, CardBody } from 'reactstrap';
import SEO from 'components/SEO';
import useWindowDimensions from 'hooks/useWindowDimensions';
import usePreview from 'hooks/usePreview';
import useQuery from 'hooks/useQuery';
import { useSelector } from 'react-redux';
import useSubscription from '../useSubscription';
import DetailLoading from './DetailLoading';
import Middle from './Middle';
import Header from './Header';
import Footer from './Footer';
import Info from './Info';
import 'react-quill/dist/quill.bubble.css';

function ShowcaseDetail({
  hash,
  showcases,
  selectedContent,
  setShowcases,
  toggleExpanded,
  isSubscribedTab,
  history,
  selectedTenantId,
  usePlayerId,
  previewCache,
  showcaseLikes
}) {
  const userDetails = JSON.parse(
    localStorage.getItem('innerloop_current_user')
  );
  const isLoggedIn = checkifLoggedIn();
  const [data, setData] = useState(null);
  const [isImage, setIsImage] = useState(false);
  const [isIframeLoading, setIsIframeLoading] = useState(true);
  const [token, setToken] = useState(null);
  const [preview, setPreview] = useState(null);
  const [isSingleShowcaseLoading, setIsSingleShowcaseLoading] = useState(false);
  const [MediaType, setMediaType] = useState(null);
  const [isStory, setIsStory] = useState(false);
  const [Keywords, setKeywords] = useState('');
  const isContentUploadedBySameTenant = selectedTenantId === data?.tenantId;
  const [tokenError, setTokenError] = useState(false);
  const [lockedContent, setLockedContent] = useState(false);
  const { height: windowHeight, width: windowWidth } = useWindowDimensions();
  const openedDirectly = useQuery().get('direct');
  const isOpenedDirectly = useMemo(() => {
    if (openedDirectly === false || openedDirectly === 'false') {
      return false;
    }
    return true;
  }, [openedDirectly]);
  const query = useQuery();
  const isLPShowcase = query.get('lp') === 'true';
  const { showcases: cachedShowcases } = useSelector(
    (state) => state.showcases
  );

  const { previewSrc } = usePreview({
    waitFor: !!data,
    isMulti: false,
    contentId: data?.entityId,
    MediaType,
    spaceId: data?.spaceId,
    previewCache,
    isShowcase: true
  });

  const showcaseData = {
    purpose: data?.purpose,
    creator: data?.creator,
    creationDateTime: data?.creationDateTime,
    endDateTime: data?.endDateTime
  };

  const { handleSubscription, isSubscribed, subscribing, setIsSubscribed } =
    useSubscription({
      isAlreadySubscribed: data?.isSubscribed,
      showcases,
      setShowcases,
      isSubscribedTab,
      tenantId: data?.tenantId
    });

  const updateCount = () => {
    updateLastViewdTT(
      {
        id: data?.id,
        lastViewedTimestamp: new Date().toISOString()
      },
      data?.tenantId,
      userDetails?.uid
    );
    if (!isContentUploadedBySameTenant) {
      updateShowcaseImpression({
        tenantId: data?.tenantId,
        showcaseId: data?.id,
        playerId: data?.entityId,
        showcaseData
      });
    }
  };

  const getToken = async (mediaType, id) => {
    setIsIframeLoading(true);
    setIsImage(mediaType === 3);
    let res;
    if (data.entityKind === 2) {
      res = await getPublicStreamingToken(undefined, undefined, undefined, id);
    } else {
      res = await getPublicStreamingToken(mediaType, id);
    }
    if (res?.data?.isError) {
      setTokenError(true);
      setPreview(false);
      setIsIframeLoading(false);
      return;
    }
    setTokenError(false);
    if (mediaType === 3) {
      const imgUrl = res?.data?.data?.strmUrl;
      // const check if its locked
      const isLoceked = res?.data?.data?.fetchUrl && !res?.data?.data?.match;
      if (isLoceked) {
        setTokenError(false);
        setPreview(false);
        setIsIframeLoading(false);
        setLockedContent(true);
        return;
      }
      if (imgUrl) {
        updateCount(data);
        setPreview(imgUrl);
        setTokenError(false);
      } else {
        setTokenError(true);
        setPreview(null);
      }
      setIsIframeLoading(false);
    } else {
      updateCount(data);
      setToken(res?.data?.data);
    }
  };

  const handleRefresh = () => {
    setIsIframeLoading(true);
    setPreview(null);
    setTokenError(null);
    getToken(MediaType, data.entityId);
  };

  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && data) {
        toggleExpanded();
      }
    };
    window.addEventListener('keydown', handleEscape);

    return () => {
      window.removeEventListener('keydown', handleEscape);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  useEffect(() => {
    if (data) {
      const tags = data?.tags?.length > 0 ? data?.tags?.join(',') : '';
      const category = data?.category ?? '';
      let valueees = '';
      Object.entries(data?.metadata)?.forEach(([key, values]) => {
        if (Array.isArray(values)) {
          values.forEach((value, index) => {
            valueees += value;
            if (index !== values.length - 1) {
              valueees += ', ';
            }
          });
        } else if (typeof values === 'string') {
          valueees = `${valueees},${values}`;
        }
      });
      const categoory = `${tags},${category},${valueees}`;
      setKeywords(categoory);
      getToken(data.mediaType, data.entityId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  const handleError = () => {
    window.location.hash = '';
    NotificationManager.error(
      <IntlMessages id="showcase-nf" />,
      '',
      3000,
      null,
      null,
      ''
    );
    toggleExpanded();
    setIsSingleShowcaseLoading(false);
  };

  const handleSaveStates = (response) => {
    setData(response);
    setMediaType(response?.mediaType);
    setIsStory(response.entityKind === 2);
    setIsSubscribed(response?.isSubscribed);
  };

  useEffect(() => {
    const getSingleShowcaseData = async () => {
      setIsSingleShowcaseLoading(true);
      const { data: publicData, isError } = await getPublicSingleShowcase({
        showcaseId: hash,
        tenantId: selectedContent?.tenantId,
        userId: userDetails?.uid
      });
      if (publicData && !isError) {
        if (!selectedContent.tenantId && isLoggedIn) {
          const { data, isError: isSingleError } =
            await getPublicSingleShowcase({
              showcaseId: publicData.id,
              tenantId: publicData.tenantId,
              userId: userDetails?.uid
            });
          if (isSingleError || !data) {
            handleError();
          }
          if ((!isLoggedIn && data?.isPublic) || (isLoggedIn && data)) {
            handleSaveStates(data);
          }
        } else if (
          (!isLoggedIn && publicData?.isPublic) ||
          (isLoggedIn && publicData)
        ) {
          handleSaveStates(publicData);
        } else {
          handleError();
        }
      } else {
        handleError();
      }
      setIsSingleShowcaseLoading(false);
    };
    if (hash) {
      if (cachedShowcases && cachedShowcases?.has(hash)) {
        handleSaveStates(cachedShowcases?.get(hash));
      } else {
        getSingleShowcaseData();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hash]);

  const handleCallToActionClick = (e) => {
    const handleUpdateShowcaseClicks = async () => {
      await updateShowcaseClicks({
        tenantId: data?.tenantId,
        showcaseId: data.id,
        playerId: data?.entityId,
        showcaseData
      });
    };
    if (!isContentUploadedBySameTenant) {
      handleUpdateShowcaseClicks();
    }
    if (data?.purpose?.type === 2) {
      window.open(`mailto:${data?.purpose?.value}`);
    } else {
      // eslint-disable-next-line no-lonely-if
      if (!data?.purpose?.value?.includes('https')) {
        window.open(`https://${data?.purpose?.value}`);
      } else {
        window.open(data?.purpose?.value);
      }
    }
  };

  // eslint-disable-next-line consistent-return
  const getIframeSource = (() => {
    if (token) {
      const playerId = data?.playerId;
      const entityId = data?.entityId;
      const aiPart = `${!isContentUploadedBySameTenant ? '&ai=true' : ''}`;
      const useAccountPlayer = !!playerId;
      // const useAccountPlayer = playerId && (usePlayerId || isOpenedDirectly);
      if (isStory) {
        if (useAccountPlayer) {
          return `${process.env.REACT_APP_STORY_EMBED_URL}/mixexp/showcase?sId=${entityId}&plyrId=${playerId}&lp=${isLPShowcase}&tk=${token}${aiPart}`;
        }
        return `${process.env.REACT_APP_STORY_EMBED_URL}/mixexp/showcase?sId=${entityId}&tk=${token}&lp=${isLPShowcase}${aiPart}`;
      }
      if (useAccountPlayer) {
        return `${process.env.REACT_APP_EMBED_URL}/playersvc/ins-showcase/${playerId}$${entityId}?tk=${token}&lp=${isLPShowcase}${aiPart}`;
      }
      return `${process.env.REACT_APP_EMBED_URL}/playersvc/ins-showcase/${entityId}?tk=${token}&lp=${isLPShowcase}${aiPart}`;
    }
  })();

  const showcaseCreatedByOurTenant = data?.tenantId === selectedTenantId;

  const getShowcaseId = (showcaseId, isNext = true) => {
    if (showcases?.length === 0 || !showcases) {
      return null;
    }

    console.log({
      showcaseId,
      isNext,
      showcases
    });

    // eslint-disable-next-line no-plusplus
    for (let i = 0; i < showcases.length; i++) {
      if (showcases[i].id === showcaseId) {
        if (isNext) {
          const checkId = showcases[i + 1]?.id ?? null;
          if (checkId) {
            return checkId;
          }
          const newShowcasedId = showcases[0]?.id;
          return newShowcasedId;
        }
        const checkId = showcases[i - 1]?.id ?? null;
        if (checkId) {
          return checkId;
        }
        const newShowcasedId = showcases[showcases.length - 1]?.id;
        return newShowcasedId;
      }
    }
    return showcases.length > 0 ? showcases[0]?.id : null;
  };

  const handleShareClick = () => {
    handleShowcaseShare(window.location.href, data.title, data.tenantName);
  };

  const handleOnClickNext = () => {
    const showcaseId = getShowcaseId(hash, true);
    history.push(`/app/showcase/detail/${showcaseId}`);
  };
  const handleOnClickPrev = () => {
    const showcaseId = getShowcaseId(hash, false);
    history.push(`/app/showcase/detail/${showcaseId}`);
  };

  return (
    <div className="SelectedContent ">
      {showcases.length > 1 && (
        <>
          <NextPrevButton isNext onClick={handleOnClickNext} />
          <NextPrevButton isNext={false} onClick={handleOnClickPrev} />
        </>
      )}
      <Card className="SelectedContentCard">
        {isSingleShowcaseLoading ? (
          <DetailLoading />
        ) : (
          <CardBody className="SSbody">
            <SEO
              description={data?.description}
              keywords={Keywords}
              title={data?.title}
              url={window.location.href}
              imgUrl={previewSrc}
            />
            <Header
              data={data}
              handleShareClick={handleShareClick}
              isStory={isStory}
              handleCallToActionClick={handleCallToActionClick}
              toggleExpanded={toggleExpanded}
              history={history}
              showcaseLikes={showcaseLikes}
              isLoggedIn={isLoggedIn}
              selectedTenantId={selectedTenantId}
              showcaseCreatedByOurTenant={showcaseCreatedByOurTenant}
            />
            <Middle
              isStory={isStory}
              isIframeLoading={isIframeLoading}
              token={token}
              isImage={isImage}
              preview={preview}
              setIsIframeLoading={setIsIframeLoading}
              data={data}
              lockedContent={lockedContent}
              tokenError={tokenError}
              setPreview={setPreview}
              getIframeSource={getIframeSource}
              windowHeight={windowHeight}
              windowWidth={windowWidth}
            />
            <Footer
              subscribing={subscribing}
              windowWidth={windowWidth}
              handleSubscription={handleSubscription}
              data={data}
              isLoggedIn={isLoggedIn}
              isSubscribed={isSubscribed}
              history={history}
              handleRefresh={handleRefresh}
              handleShareClick={handleShareClick}
              handleCallToActionClick={handleCallToActionClick}
              showcaseLikes={showcaseLikes}
              selectedTenantId={selectedTenantId}
            />
            <Info data={data} />
          </CardBody>
        )}
      </Card>
    </div>
  );
}

const NextPrevButton = ({ isNext = true, onClick }) => {
  const style = (() => {
    if (isNext) {
      return {
        right: 5
      };
    }
    return {
      left: 5
    };
  })();
  return (
    <Card
      onClick={onClick}
      className="nextPrevButton"
      style={{
        ...style
      }}
    >
      {!isNext ? (
        <svg
          width="12"
          height="20"
          viewBox="0 0 12 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M0.74844 11.0016C0.482856 10.7359 0.333659 10.3756 0.333659 9.99999C0.333659 9.62434 0.482856 9.26407 0.74844 8.99841L8.76252 0.984322C8.89321 0.849016 9.04953 0.741091 9.22237 0.666843C9.39521 0.592598 9.5811 0.553518 9.7692 0.551884C9.95731 0.550249 10.1439 0.586094 10.318 0.657326C10.4921 0.728556 10.6502 0.83375 10.7832 0.966764C10.9163 1.09978 11.0215 1.25795 11.0927 1.43205C11.1639 1.60616 11.1998 1.7927 11.1981 1.98081C11.1965 2.16891 11.1574 2.35481 11.0832 2.52765C11.0089 2.70049 10.901 2.85681 10.7657 2.98749L3.75319 9.99999L10.7657 17.0125C11.0237 17.2797 11.1665 17.6375 11.1633 18.009C11.1601 18.3804 11.0111 18.7357 10.7484 18.9984C10.4858 19.2611 10.1305 19.41 9.75901 19.4133C9.38756 19.4165 9.02971 19.2737 8.76252 19.0157L0.74844 11.0016Z"
            fill="#922C88"
          />
        </svg>
      ) : (
        <svg
          width="12"
          height="20"
          viewBox="0 0 12 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M11.2516 8.99843C11.5172 9.26409 11.6664 9.62436 11.6664 10C11.6664 10.3757 11.5172 10.7359 11.2516 11.0016L3.23754 19.0157C3.10686 19.151 2.95053 19.2589 2.77769 19.3332C2.60486 19.4074 2.41896 19.4465 2.23086 19.4481C2.04275 19.4498 1.85621 19.4139 1.6821 19.3427C1.508 19.2714 1.34983 19.1663 1.21681 19.0332C1.0838 18.9002 0.978605 18.7421 0.907374 18.5679C0.836143 18.3938 0.800299 18.2073 0.801933 18.0192C0.803568 17.8311 0.842649 17.6452 0.916895 17.4724C0.991141 17.2995 1.09906 17.1432 1.23437 17.0125L8.24687 10L1.23437 2.98751C0.976314 2.72033 0.833522 2.36247 0.83675 1.99103C0.839977 1.61958 0.988966 1.26426 1.25163 1.0016C1.51429 0.73894 1.86961 0.589951 2.24105 0.586723C2.6125 0.583496 2.97035 0.726288 3.23754 0.984345L11.2516 8.99843Z"
            fill="#922C88"
          />
        </svg>
      )}
    </Card>
  );
};

export default ShowcaseDetail;
