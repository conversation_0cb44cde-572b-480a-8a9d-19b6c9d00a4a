/* eslint-disable no-unused-vars */
/* eslint-disable no-nested-ternary */
import { GRAPH_TYPES, graphData } from 'constants/VideoInteraction';
import IntlMessages from 'helpers/IntlMessages';
import React from 'react';
import Dategraph from './Dategraph';
import HourGraph from './HourGraph';
import MonthGraph from './MonthGraph';

const Charts = ({ active, setActive, data, title }) => {
  return (
    <div>
      {title && (
        <h2 className="mt-4" style={{ fontSize: 16, fontWeight: 700 }}>
          {title}
        </h2>
      )}
      <div
        className="d-flex flex-row w-full align-items-center justify-content-center"
        style={{ fontSize: '14px' }}
      >
        {graphData.map((m) => (
          <Tab
            key={m.id}
            isLeft={m.id === GRAPH_TYPES.DATE}
            isRight={m.id === GRAPH_TYPES.MONTH}
            text={m.text}
            isActive={active === m.id}
            onClick={() => setActive(m.id)}
          />
        ))}
      </div>

      <div className="charts">
        {+active === GRAPH_TYPES.DATE ? (
          <Dategraph
            data={data?.date?.counts}
            nextUpdateTime={data?.date.lastUpdated}
          />
        ) : +active === GRAPH_TYPES.HOUR ? (
          <HourGraph
            data={data?.hour?.counts}
            nextUpdateTime={data?.hour.lastUpdated}
          />
        ) : (
          <MonthGraph
            data={data?.month?.counts}
            nextUpdateTime={data?.month.lastUpdated}
          />
        )}
      </div>
    </div>
  );
};

const Tab = ({ onClick, isActive, text, isLeft = false, isRight = false }) => {
  return (
    <div
      className="text-center c-pointer p-2"
      style={{
        width: '100px',
        borderTopRightRadius: isRight ? '50px' : '',
        borderBottomRightRadius: isRight ? '50px' : '',
        borderTopLeftRadius: isLeft ? '50px' : '',
        borderBottomLeftRadius: isLeft ? '50px' : '',
        border: '1px solid #992288',
        borderLeft: 'none',
        background: isActive && '#99228847'
      }}
      onClick={onClick}
    >
      <IntlMessages id={text} />
    </div>
  );
};

export default Charts;
