import React from 'react';
import { FormGroup, Input, Label } from 'reactstrap';
import IntlMessages from 'helpers/IntlMessages';

const DateInput = ({ label, value, onChange, disabled = false, answer }) => {
  const dateValue = answer ? answer[0] : value;
  return (
    <div className="TextBoxInput" aria-disabled={answer ? 'view' : disabled}>
      <FormGroup className="form-group has-float-label mb-0">
        <Label for="name" style={{ cursor: 'default' }}>
          <IntlMessages id={label} />
          &nbsp;
        </Label>
        <Input
          className="form-control tiemInputStyle"
          type="date"
          name="name"
          placeholder="Full Name"
          value={dateValue}
          onChange={answer ? () => {} : onChange}
        />
      </FormGroup>
    </div>
  );
};

export default DateInput;
