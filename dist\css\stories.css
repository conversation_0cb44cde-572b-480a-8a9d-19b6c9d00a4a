.SLIcon {
  width: 10%;
}
@media (max-width: 1150px) {
  .SLIcon {
    width: 8%;
  }
  .SLIcon .responsive-thumbnail {
    width: auto;
  }
}
.SLTitle {
  width: 15%;
}
.SLFlag {
  width: 4%;
}
.SLCC {
  width: 10%;
}
.SLDate {
  width: 10%;
}
.SLShowcased {
  width: 12%;
}
.SLWorkflow {
  width: 16%;
}
.SLShared {
  width: 10%;
}
.SLStatus {
  width: 10%;
}
.SLMore {
  width: 3%;
}/*# sourceMappingURL=stories.css.map */