/* eslint-disable no-nested-ternary */
/* eslint-disable react/no-array-index-key */
import DynamicQuill from 'components/DynamicQuill';
import React, { useMemo } from 'react';
import Tag from './Tag';
import { ShareButton, SubcribeButton } from './Buttons';
import { handleOwnerShare, removeHtmlTags } from 'helpers/Utils';
import ShowcaseCard from '../ShowcaseCard';
import useWindowDimensions from 'hooks/useWindowDimensions';

const ShowcaseOwnerBottom = ({
  ownerData,
  themeColors,
  setThemeColors,
  orgThemeColors,
  setOrgThemeColors,
  isEdit,
  expandFor,
  isMyShowcase,
  handleSubscription,
  subscribing,
  isLoggedIn,
  isSubscribed,
  userIdentifier,
  isVideoFile,
  showcaseCardProps
}) => {
  const highlightData = useMemo(() => {
    const showcasesArray = showcaseCardProps?.showcases ?? [];
    return showcasesArray.find((s) => s?.isPinned);
  }, [showcaseCardProps?.showcases]);
  const { width } = useWindowDimensions();

  return (
    <div className="ShowcaseOwnerBottom">
      {highlightData && (
        <div className="highlight-container">
          <ShowcaseCard
            index={0}
            data={highlightData}
            isLoggedIn={isLoggedIn}
            setThemeColors={setThemeColors}
            orgThemeColors={orgThemeColors}
            setOrgThemeColors={setOrgThemeColors}
            expandFor={expandFor}
            themeColors={themeColors}
            {...showcaseCardProps}
            className={
              width < 1000
                ? 'd-flex flex-column ContentCard'
                : 'd-flex flex-row ContentCard'
            }
          />
        </div>
      )}
      <div className="owner-details">
        {ownerData?.profileDescription &&
          removeHtmlTags(ownerData?.profileDescription)?.length > 0 && (
            <DynamicQuill
              description={ownerData?.profileDescription}
              className={
                width < 1000
                  ? 'showcaseHomepageQuill'
                  : highlightData
                  ? 'showcaseHomepageQuillStart'
                  : 'showcaseHomepageQuill'
              }
            />
          )}
        <div
          className={`mt-2 d-flex flex-wrap align-items-center  ${
            width < 1000
              ? 'justify-content-center'
              : highlightData
              ? 'justify-content-start'
              : 'justify-content-center'
          }`}
        >
          {ownerData?.tags?.map((t, i) => (
            <Tag
              text={t}
              key={i}
              showColorChooser={i === 0 && expandFor && isMyShowcase && isEdit}
              themeColors={themeColors}
              setThemeColors={setThemeColors}
              orgThemeColors={orgThemeColors}
              setOrgThemeColors={setOrgThemeColors}
            />
          ))}
        </div>
        {!isVideoFile && (
          <div className="my-2 bottom-subscription">
            <SubcribeButton
              showColorPicker={isEdit && isMyShowcase}
              subscribing={subscribing}
              themeColors={themeColors}
              handleSubscription={handleSubscription}
              ownerData={ownerData}
              setThemeColors={setThemeColors}
              isLoggedIn={isLoggedIn}
              isSubscribed={isSubscribed}
              orgThemeColors={orgThemeColors}
              setOrgThemeColors={setOrgThemeColors}
              userIdentifier={userIdentifier}
            />
            <ShareButton
              isMyShowcase={isMyShowcase && isEdit}
              expandFor={expandFor}
              themeColors={themeColors}
              orgThemeColors={orgThemeColors}
              setOrgThemeColors={setOrgThemeColors}
              setThemeColors={setThemeColors}
              onClick={() =>
                handleOwnerShare(window.location.href, ownerData?.profileTitle)
              }
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default ShowcaseOwnerBottom;
