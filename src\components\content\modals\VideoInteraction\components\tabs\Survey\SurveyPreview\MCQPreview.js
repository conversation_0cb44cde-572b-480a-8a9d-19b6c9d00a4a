/* eslint-disable no-unneeded-ternary */
/* eslint-disable no-unused-vars */
/* eslint-disable react/no-array-index-key */
/* eslint-disable react/self-closing-comp */
import React from 'react';

const MCQPreview = ({ values, answer }) => {
  return (
    <div className="MCQPreview" aria-disabled={answer ? 'view' : false}>
      {values.map((m, i) => (
        <div key={i} className="MCQPreviewRow">
          <input
            id="juts"
            type="radio"
            checked={answer?.some((a) => a === m.value)}
            disabled={answer ? false : true}
          />
          <label htmlFor="juts">{m.value}</label>
        </div>
      ))}
    </div>
  );
};

export default MCQPreview;
