import { isDarkModeActive } from 'helpers/Utils';
import React from 'react';
import Skeleton from 'react-loading-skeleton';

function LoadingSkeleton({ ...props }) {
  const isDarkMode = isDarkModeActive();
  return (
    <Skeleton
      {...props}
      baseColor={isDarkMode ? '#242224' : '#ebebeb'}
      highlightColor={isDarkMode ? '#424242' : '#f5f5f5'}
    />
  );
}

export default LoadingSkeleton;
