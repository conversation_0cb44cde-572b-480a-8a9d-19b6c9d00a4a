.chapterCard {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.chapterCard input:focus,
.chapterCard input:active {
  outline: none;
}
.chapterCard .mid-cont {
  display: flex;
  flex-direction: row;
}
.chapterCard .mid-cont .top {
  width: 30%;
}
.chapterCard .mid-cont .bottom {
  width: 70%;
}
@media (max-width: 700px) {
  .chapterCard .mid-cont {
    flex-direction: column;
  }
  .chapterCard .mid-cont .top {
    width: 100%;
  }
  .chapterCard .mid-cont .bottom {
    width: 100%;
  }
}/*# sourceMappingURL=transcribe.css.map */