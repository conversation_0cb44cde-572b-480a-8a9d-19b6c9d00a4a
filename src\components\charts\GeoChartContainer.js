/* eslint-disable react-hooks/exhaustive-deps */
import { Colxx } from 'components/common/CustomBootstrap';
import React, { useEffect, useRef, useState } from 'react';

const GeoChartContainer = ({ className = '', style = {}, children }) => {
  const containerRef = useRef(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        const { offsetWidth, offsetHeight } = containerRef.current;
        setDimensions({ width: offsetWidth, height: offsetHeight });
      }
    };
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [containerRef.current]);

  return (
    <Colxx ref={containerRef} className={className} style={style}>
      {children({ dimensions })}
    </Colxx>
  );
};

export default GeoChartContainer;
