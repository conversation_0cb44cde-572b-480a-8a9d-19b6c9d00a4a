/* eslint-disable no-unused-vars */
/* eslint-disable react/button-has-type */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import { Colxx } from 'components/common/CustomBootstrap';
import InfoIcon from 'components/icon/InfoIcon';
import AiIcon from 'constants/AiIcon';
import IntlMessages from 'helpers/IntlMessages';
import React, { useState } from 'react';
import Switch from 'rc-switch';

function AdvancedOptions({
  AESStatus,
  DRMStatus,
  setAESStatus,
  setDRMStatus,
  aesEnabled,
  drmEnabled,
  showProtectionCard
}) {
  const [isOpen, setIsOpen] = useState(false);
  return (
    <Colxx className="d-flex flex-column align-items-center justify-content-center my-4">
      <span onClick={() => setIsOpen(!isOpen)} className="mb-2 c-pointer">
        <b className="text-primary">
          <IntlMessages id="ad-options" />
          <i className={`ml-2 simple-icon-arrow-${isOpen ? 'up' : 'down'}`} />
        </b>
      </span>
      {isOpen && (
        <div
          className="AdvanceOptions mt-3"
          style={{ justifyContent: 'center' }}
        >
          <ProtectionCard
            disabled={!showProtectionCard}
            AESStatus={AESStatus}
            DRMStatus={DRMStatus}
            setAESStatus={setAESStatus}
            setDRMStatus={setDRMStatus}
            aesEnabled={aesEnabled}
            drmEnabled={drmEnabled}
          />
          {/* <EnrichmentCard
            autoTranscibe={autoTranscibe}
            deNoise={deNoise}
            setDeNoise={setDeNoise}
            setAutoTranscibe={setAutoTranscibe}
            autoTranscribeEnabled={autoTranscribeEnabled}
          /> */}
        </div>
      )}
    </Colxx>
  );
}

const ProtectionCard = ({
  DRMStatus,
  AESStatus,
  setAESStatus,
  setDRMStatus,
  aesEnabled,
  drmEnabled,
  disabled
}) => {
  return (
    <div aria-disabled={disabled}>
      <h5 className="mt-3 text-center mb-4">
        <span style={{ borderBottom: '2px solid silver', fontSize: 16 }}>
          <IntlMessages id="ad-media-protection" />
        </span>
      </h5>
      <div className="Protection mb-2">
        <button
          className={
            !DRMStatus && !AESStatus ? 'activeButton' : 'inActiveButton'
          }
          onClick={() => {
            setDRMStatus(false);
            setAESStatus(false);
          }}
          style={{ borderTopLeftRadius: 20, borderBottomLeftRadius: 20 }}
        >
          <IntlMessages id="ad-media-protection-none" />
        </button>
        <button
          aria-disabled={!aesEnabled}
          className={AESStatus ? 'activeButton' : 'inActiveButton'}
          onClick={() => {
            setDRMStatus(false);
            setAESStatus(true);
          }}
        >
          <IntlMessages id="ad-media-protection-aes" />
        </button>
        <button
          aria-disabled={!drmEnabled}
          className={DRMStatus ? 'activeButton' : 'inActiveButton'}
          onClick={() => {
            setDRMStatus(true);
            setAESStatus(false);
          }}
          style={{ borderTopRightRadius: 20, borderBottomRightRadius: 20 }}
        >
          <IntlMessages id="ad-media-protection-drm" />
        </button>
      </div>
      {AESStatus && (
        <p
          className="text-primary text-center font-bolder"
          style={{ fontSize: 12 }}
        >
          <i className="simple-icon-info mr-2" />
          <IntlMessages id="ad-aes-info" />
        </p>
      )}
      {DRMStatus && (
        <p
          className="text-primary text-center font-bolder"
          style={{ fontSize: 12 }}
        >
          <i className="simple-icon-info mr-2" />
          <IntlMessages id="ad-drm-info" />
        </p>
      )}
    </div>
  );
};

const EnrichmentCard = ({
  autoTranscibe,
  setAutoTranscibe,
  autoTranscribeEnabled,
  deNoise,
  setDeNoise
}) => {
  console.log('autoTranscribeEnabled', autoTranscribeEnabled);
  return (
    <div>
      <h5 className="mt-3 text-center mb-4">
        <span style={{ borderBottom: '2px solid silver', fontSize: 16 }}>
          <IntlMessages id="ad-media-enrichment" />
        </span>
      </h5>
      <div className="d-flex flex-row align-items-center justify-content-center gap-2 mb-2">
        <AiIcon primary active height={16} />
        <span style={{ fontSize: 14 }}>
          <IntlMessages id="ad-media-analyser" />
        </span>
        <InfoIcon
          name="MES"
          message="ad-media-enrichment-info"
          iconStyle={{ fontSize: 14 }}
        />
        <Switch
          disabled={!autoTranscribeEnabled}
          className="custom-switch custom-switch-primary custom-switch-small ml-3"
          checked={autoTranscibe}
          onClick={() => {
            setAutoTranscibe(!autoTranscibe);
          }}
        />
      </div>
      {autoTranscibe && (
        <div className="d-flex flex-row align-items-center justify-content-center gap-2 mb-2">
          {/* <AiIcon primary active height={16} /> */}
          <span style={{ fontSize: 14 }}>
            <IntlMessages id="ad-rm-noise" />
          </span>
          <InfoIcon
            name="RemoveNoise"
            message="ad-rm-noise-info"
            iconStyle={{ fontSize: 14 }}
          />
          <Switch
            className="custom-switch custom-switch-primary custom-switch-small ml-3"
            checked={deNoise}
            onClick={() => {
              setDeNoise(!deNoise);
            }}
          />
        </div>
      )}

      {/* <p className="text-primary text-center mt-1 font-bolder" style={{ fontSize: 12 }}>
        <IntlMessages id="ad-media-analyser-info" />
      </p> */}
    </div>
  );
};

export default AdvancedOptions;
