import React from 'react';

const UserAvatar = ({
  setEditImageModalOpen,
  profileImageUrl,
  isMyShowcase,
  isEdit
}) => {
  return (
    <div className="centered position-relative">
      <div className="user-avatar-cont">
        {profileImageUrl && (
          <img height="100%" alt="profile" width="100%" src={profileImageUrl} />
        )}
      </div>
      {isMyShowcase && (
        <span
          aria-disabled={isEdit}
          onClick={() => setEditImageModalOpen('Profile')}
          className="position-absolute d-flex align-items-center justify-content-center box-shadow  c-pointer"
          style={{
            background: '#fff',
            height: '30px',
            width: '30px',
            borderRadius: '15px',
            bottom: 7,
            right: 7,
            color: '#949494'
          }}
        >
          <i className="simple-icon-pencil" />
        </span>
      )}
    </div>
  );
};

export default UserAvatar;
