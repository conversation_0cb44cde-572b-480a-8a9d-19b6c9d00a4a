import MainLoader from 'components/loaders/MainLoader';
import { getSubscriptionDetails } from 'functions/api/billingApi';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import NextUpdateTime from 'components/nextUpdateTime';
import { Colxx } from 'components/common/CustomBootstrap';
import Header from './Header';
import NewAdditionalDetails from './NewAdditionalDetails';
import NewUsageDetails from './NewUsageDetails';
import FeaturesAccessibilty from './FeaturesAccessibility';

const SubscriptionDetail = () => {
  const [data, setData] = useState();
  const [loading, setLoading] = useState(true);
  const { subscriptionExpired: subsCheck } = useSelector(
    (state) => state.subscription
  );

  useEffect(() => {
    const fetchSubScriptionDetails = async () => {
      setLoading(true);
      const result = await getSubscriptionDetails(true);
      if (result?.id) {
        setData(result);
      } else {
        setData({
          notFound: true
        });
      }
      setLoading(false);
    };
    fetchSubScriptionDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div
      style={{
        display: 'block'
      }}
    >
      {!loading ? (
        <>
          {data?.id ? (
            <Colxx className="pb-4">
              {data.nextUpdateTime && (
                <NextUpdateTime
                  time={data.nextUpdateTime}
                  className=" m-0 mt-0"
                />
              )}
              <Header data={data} loading={loading} subsCheck={subsCheck} />

              <FeaturesAccessibilty data={data} />
              <NewUsageDetails data={data} />
              <NewAdditionalDetails data={data} />
            </Colxx>
          ) : null}
        </>
      ) : (
        <div className="d-flex justify-content-center">
          <MainLoader dataLoad noBackdrop />
        </div>
      )}
    </div>
  );
};

export default SubscriptionDetail;
