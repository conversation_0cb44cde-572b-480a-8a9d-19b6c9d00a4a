/* eslint-disable jsx-a11y/no-static-element-interactions */
import ShareButton from 'components/buttons/ShareButton';
import Copy from 'components/copy';
import IntlMessages from 'helpers/IntlMessages';
import { isDarkModeActive } from 'helpers/Utils';
import React, { useState } from 'react';

function Tab({ entityKind, shareId, title, isDRM }) {
  const [isEmbedCodeActive, setIsEmbedCodeActive] = useState(false);

  const link = (() => {
    const storyShareLink = `${process.env.REACT_APP_STORY_EMBED_URL}/mixexp/render?sId=${shareId}`;

    const contentShareLink = `${
      process.env.REACT_APP_EMBED_URL
    }/playersvc/ins-share/${shareId}${isDRM ? '?ighdcp=true' : ''}`;

    return entityKind === 1 ? contentShareLink : storyShareLink;
  })();

  const embedcode = `<iframe allowfullscreen width="760" height="515" src="${link}"
  title = "${title}"
  frame-border="0"
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture;encrypted-media"
  ></iframe>`;

  const textValue = isEmbedCodeActive ? embedcode : link;

  const handleSharing = async () => {
    if (navigator.share) {
      try {
        await navigator
          .share({
            // url: link,
            // title: 'Please checkout the Content'
            text: textValue
          })
          .then(() =>
            console.log('Hooray! Your content was shared to tha world')
          );
      } catch (error) {
        console.error(`Oops! I couldn't share to the world because: ${error}`);
      }
    } else {
      // fallback code
      console.log(
        'Web share is currently not supported on this browser. Please provide a callback'
      );
    }
  };

  return (
    <div>
      <div className="my-2 d-flex flex-row align-items-center justify-content-between">
        <div className="d-flex flex-row gap-1 ">
          <TabButton
            icon={<i className="simple-icon-link" />}
            active={!isEmbedCodeActive}
            text="Share URL"
            onClick={() => setIsEmbedCodeActive(false)}
          />
          <TabButton
            icon={'</>'}
            text="Embed Code"
            active={isEmbedCodeActive}
            onClick={() => setIsEmbedCodeActive(true)}
          />
        </div>
        <ShareButton onClick={handleSharing} />
      </div>
      <div className="position-relative my-2">
        <textarea
          className="custom-txtarea w-full"
          style={{
            // color: currentColor ? 'Silver' : ''
            fontSize: 13
          }}
          value={textValue}
          rows={isEmbedCodeActive ? 7 : 1}
          cols={isEmbedCodeActive ? 7 : 1}
          onChange={() => {}}
        />
        <div
          className="position-absolute"
          style={{
            right: 5,
            top: 5,
            cursor: 'pointer'
          }}
        >
          <Copy copyText={textValue} isTransparentBg />
        </div>
        <br />
      </div>
    </div>
  );
}

const TabButton = ({ active, onClick, icon, text }) => {
  const isDarkMode = isDarkModeActive();
  return (
    <span
      onClick={onClick}
      className="mr-2 d-flex align-items-center justify-content-center c-pointer"
      style={{
        background: active ? '#992288' : 'transparent',
        // eslint-disable-next-line no-nested-ternary
        color: active ? '#fff' : isDarkMode ? '#fff' : '#000',
        padding: 10,
        borderRadius: 5,
        minWidth: '150px',
        fontSize: 13
      }}
    >
      <span className="mr-2">{icon}</span>
      <IntlMessages id={text} />
    </span>
  );
};

export default Tab;
