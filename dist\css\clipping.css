.clipCard {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  border: 1px solid silver;
}
.clipCard .preview {
  width: 15%;
}
.clipCard .mid {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 55%;
}
@media (max-width: 1050px) {
  .clipCard .mid {
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    row-gap: 5px;
  }
}
.clipCard .buttons {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 25%;
}
@media (max-width: 950px) {
  .clipCard .preview {
    width: 10%;
  }
  .clipCard .buttons {
    width: 20%;
  }
}
@media (max-width: 610px) {
  .clipCard {
    display: flex;
    flex-direction: column;
    width: 90%;
  }
  .clipCard .preview {
    width: 100%;
    height: 150px;
    text-align: center;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }
  .clipCard .buttons {
    width: 100%;
    justify-content: space-between !important;
  }
}/*# sourceMappingURL=clipping.css.map */