import IntlMessages from 'helpers/IntlMessages';
import React, { useState } from 'react';
import { Card } from 'reactstrap';

const HeroCards = ({ history, subscriptionDisabled }) => {
  return (
    <div className="watch-demo">
      <div className="videocard">
        <Iframe
          src="https://play.innerloop.stream/playersvc/ins-share/cu-n3OQYzXKjeLzOd870pwPF?ai=none"
          title="Innerloop Demo"
        />
      </div>
      <FAQCard history={history} disabled={subscriptionDisabled} />
    </div>
  );
};

const FAQCard = ({ history, disabled }) => {
  const handleClick = () => {
    history.push('/app/home?tutorial=true');
  };

  const redirectToStreamingVideo = () => {
    history.push('/app/home?tutorial=true&openFirst=true');
  };

  return (
    <>
      <Card aria-disabled={disabled} className="faqcard">
        <div
          className="pb-2 px-3 pt-3 "
          style={{ borderBottom: '1px solid silver' }}
        >
          <h5 className="text-align-left px-2">
            <b>
              <IntlMessages id="Frequently Asked Questions (FAQ)" />
            </b>
          </h5>
        </div>
        <div className="p-3">
          <div className="w-full d-flex flex-row" aria-disabled={disabled}>
            <div className="w-50" onClick={redirectToStreamingVideo}>
              <img
                height="100%"
                width="95%"
                alt="Streaming"
                src="/images/screenshots/streaming.png"
              />
            </div>
            <div className="w-50 p-2 d-flex text-primary flex-column">
              <h5 style={{ textAlign: 'start' }}>
                How to create your first Streaming Experience ?
              </h5>

              <h4
                className="mt-4 font-bolder c-pointer"
                style={{ textAlign: 'start' }}
                onClick={handleClick}
              >
                <IntlMessages id="home-faq-view-more" />
                <img
                  src="/assets/icons/home/<USER>"
                  height={20}
                  width={20}
                  alt="redirect icon"
                />
              </h4>
              {/* <p
                className="text-left font-bolder mb-0 c-pointer"
                onClick={redirectToStreamingVideo}
              >
                <IntlMessages id="home-faq-intro" />
              </p>
              <span
                className="c-pointer d-flex flex-row gap-2 align-items-center justify-content-end"
                onClick={handleClick}
              >
                <p className="mb-0 text-right">
                  <IntlMessages id="home-faq-view-more" />
                </p>
                <img
                  src="/assets/icons/home/<USER>"
                  height={12}
                  width={12}
                  alt="redirect icon"
                />
              </span> */}
            </div>
          </div>
        </div>
      </Card>
    </>
  );
};

function Iframe({ src, title, ...props }) {
  const [isLoading, setIsLoading] = useState(true);

  return (
    <>
      {isLoading ? (
        <div className="h-full w-full">
          <div className="dataloading" />
        </div>
      ) : null}
      <span className="w-full h-full p-1">
        <iframe
          src={src}
          width="100%"
          height="100%"
          title={title}
          allowFullScreen
          className={isLoading ? 'd-none' : 'border-0'}
          onLoad={() => setIsLoading(false)}
          {...props}
        />
      </span>
    </>
  );
}

export default HeroCards;
