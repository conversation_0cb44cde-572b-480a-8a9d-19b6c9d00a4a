/* eslint-disable no-unused-vars */
import React, { useState } from 'react';
import ConfirmationModal from 'components/common/ConfirmationModal';
import { deleteContent } from 'functions/api/spacesApi';
import { NotificationManager } from 'components/common/react-notifications';
import IntlMessages from 'helpers/IntlMessages';

const DeleteContentModal = ({
  openModal,
  toggleModal,
  spaceId,
  contentId,
  reload,
  mediaTitle,
  setUpdatingContent,
  aiContentKind,
  parentId
}) => {
  const [associatedStoryCount, setAssociatedStoryCount] = useState(false);
  const [openConfirmation, setOpenConfirmation] = useState(false);

  const toggleConfirmationModal = () => {
    setOpenConfirmation(!openConfirmation);
  };

  const handleConfirm = async (deleteIt = false) => {
    if (!deleteIt) {
      toggleModal();
    }
    if (deleteIt) {
      toggleConfirmationModal();
    }
    setUpdatingContent(true);
    const resp = await deleteContent(
      {
        spaceId,
        contentId
      },
      deleteIt,
      {
        aiContentKind,
        parentId
      }
    );
    if (typeof resp !== 'string') {
      if (
        (!resp.data && !resp.isError) ||
        Number.isInteger(resp?.data?.associatedStoryCount)
      ) {
        if (+resp?.data?.associatedStoryCount > 0) {
          setOpenConfirmation(true);
          setAssociatedStoryCount(+resp?.data?.associatedStoryCount);
          return;
        }
        if (reload) {
          await reload(contentId, 'delete');
        }
        NotificationManager.success(
          <IntlMessages id="content-del-sucess" />,
          <IntlMessages id="req.success" />,
          3000,
          null,
          null,
          ''
        );
        setUpdatingContent(false);
      } else {
        setUpdatingContent(false);
        NotificationManager.error(
          <IntlMessages id="content-del-error" />,
          <IntlMessages id="req.failed" />,
          3000,
          null,
          null,
          ''
        );
      }
    } else {
      setUpdatingContent(false);
    }
  };

  const handleClose = () => {
    toggleModal();
  };

  return (
    <>
      <ConfirmationModal
        openConfirmationModal={openConfirmation}
        toggleConfirmationModal={toggleConfirmationModal}
        handleConfirm={() => handleConfirm(true)}
        handleClose={toggleConfirmationModal}
        type="deleteContent"
        mediaTitle={mediaTitle}
        description={
          <p>
            {associatedStoryCount}&nbsp;
            <IntlMessages id="del-content-associated-story" />
          </p>
        }
      />
      <ConfirmationModal
        openConfirmationModal={openModal}
        toggleConfirmationModal={toggleModal}
        handleConfirm={handleConfirm}
        handleClose={handleClose}
        type="deleteContent"
        mediaTitle={mediaTitle}
      />
    </>
  );
};

export default DeleteContentModal;
