/* eslint-disable no-else-return */

/* eslint-disable no-nested-ternary */
import React from 'react';
import * as DropdownMenu from '@radix-ui/react-dropdown-menu';
import IntlMessages from 'helpers/IntlMessages';
import { getTextColor, RoleTypes } from 'helpers/Utils';
import { PinIcon } from 'components/ShowcaseThumbnail/ShowcaseIcons';

function ShowcaseDropdown({
  handleSubscription,
  subscribing,
  isSubscribed,
  handleShare,
  isLoggedIn,
  myShowcases,
  toggleEditShowcase,
  setOpen,
  open,
  showSubscribeButton = true,
  roleType,
  isDarkMode,
  highlightShowcase,
  isHighlighting,
  isPinned,
  expandFor,
  isShowcaseBgLoading,
  showcasedBg,
  makeShowcaseContentBg,
  mediaType,
  showcaseExpires,
  isDuration
}) {
  const getText = (() => {
    if (isLoggedIn) {
      if (isSubscribed && subscribing) {
        return 'Unsubscribing...';
      } else if (!isSubscribed && subscribing) {
        return 'Subscribing...';
      } else if (isSubscribed) {
        return 'Unsubscribe';
      }
      return 'Subscribe';
    }
    return 'Subscribe';
  })();

  return (
    <DropdownMenu.Root
      open={open}
      onOpenChange={(o) => {
        if (!subscribing) {
          setOpen(o);
        }
      }}
    >
      <DropdownMenu.Trigger asChild>
        <i
          className="simple-icon-options-vertical cursor-pointer"
          style={{ fontSize: '1rem' }}
        />
      </DropdownMenu.Trigger>
      <DropdownMenu.Portal>
        <DropdownMenu.Content
          style={{ zIndex: 10000 }}
          className="DropdownMenuContent dropdown-radix p-2 "
          sideOffset={5}
        >
          <DropdownMenu.Item
            onClick={handleShare}
            className="cursor-pointer outline-none"
          >
            <i style={{ fontSize: '1em' }} className="simple-icon-share mr-3" />
            Share
          </DropdownMenu.Item>
          {!myShowcases && showSubscribeButton && (
            <DropdownMenu.Item
              onClick={handleSubscription}
              disabled={subscribing}
              className="cursor-pointer outline-none"
            >
              <i
                style={{ fontSize: '1em' }}
                className="simple-icon-user-following mr-3"
              />
              <IntlMessages id={getText} />
            </DropdownMenu.Item>
          )}
          {myShowcases && +roleType <= RoleTypes.CONTENT_MANAGER && (
            <DropdownMenu.Item
              onClick={toggleEditShowcase}
              className="cursor-pointer outline-none"
            >
              <i
                style={{ fontSize: '1em' }}
                className="simple-icon-pencil mr-3"
              />
              Edit
            </DropdownMenu.Item>
          )}
          {myShowcases && expandFor && +roleType <= RoleTypes.CONTENT_MANAGER && (
            <DropdownMenu.Item
              className="cursor-pointer outline-none"
              disabled={isHighlighting}
              onClick={() => {
                highlightShowcase();
              }}
            >
              <PinIcon color={getTextColor(isDarkMode)} className="mr-3" />
              {isPinned ? 'Highlighted' : 'Highlight'}
            </DropdownMenu.Item>
          )}
          {myShowcases &&
            expandFor &&
            isDuration &&
            +roleType <= RoleTypes.CONTENT_MANAGER &&
            +mediaType === 1 &&
            !showcaseExpires && (
              <DropdownMenu.Item
                className="cursor-pointer outline-none"
                disabled={isShowcaseBgLoading}
                onClick={() => {
                  makeShowcaseContentBg(!!showcasedBg);
                }}
              >
                <span className="mr-2">
                  {showcasedBg ? <RemoveIcon /> : <AddIcon />}
                </span>
                {showcasedBg ? 'Remove From Background' : 'Add to Background'}
              </DropdownMenu.Item>
            )}
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
}

const AddIcon = () => {
  return (
    <svg
      width="19"
      height="19"
      viewBox="0 0 19 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.5 17.8125C14.0909 17.8125 17.8125 14.0909 17.8125 9.5C17.8125 4.90913 14.0909 1.1875 9.5 1.1875C4.90913 1.1875 1.1875 4.90913 1.1875 9.5C1.1875 14.0909 4.90913 17.8125 9.5 17.8125Z"
        stroke="#565D72"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.9375 9.5H13.0625"
        stroke="#565D72"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.5 5.9375V13.0625"
        stroke="#565D72"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

const RemoveIcon = () => {
  return (
    <svg
      width="19"
      height="19"
      viewBox="0 0 19 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.5 17.8125C14.0909 17.8125 17.8125 14.0909 17.8125 9.5C17.8125 4.90913 14.0909 1.1875 9.5 1.1875C4.90913 1.1875 1.1875 4.90913 1.1875 9.5C1.1875 14.0909 4.90913 17.8125 9.5 17.8125Z"
        stroke="#565D72"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.9375 9.5H13.0625"
        stroke="#565D72"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.5 5.9375V13.0625"
        stroke="#565D72"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default ShowcaseDropdown;
