/* eslint-disable no-unused-vars */
import React, { useState, useEffect } from 'react';
import { tenantSummary } from 'functions/api/analyticsApi';
import DashboardCardList from 'components/cards/DashboardCardList';
import MainLoader from 'components/loaders/MainLoader';
import { subscriptionChecker } from 'helpers/subscriptionChecker';
import { Button } from 'reactstrap';
import { adminRoot } from 'constants/defaultValues';
import { useHistory } from 'react-router-dom';
import { RoleTypes, formatDate, getLocalUserRoleType } from 'helpers/Utils';
import { NotificationManager } from 'components/common/react-notifications';
import IntlMessages from 'helpers/IntlMessages';
import DashboardCharts from './components/DashboardCharts';
import useSubscriptionChecker from 'hooks/useSubscriptionChecker';

const Loader = () => {
  return (
    <div className="position-relative">
      <div className="Overlapping">
        <MainLoader
          text="&nbsp; &nbsp;&nbsp; &nbsp;&nbsp;&nbsp; Please Wait. &nbsp; &nbsp;&nbsp; &nbsp;
          Preparing Data Insights."
        />
      </div>
    </div>
  );
};

const Default = ({ subscriptionDetails, previewCache, roleType }) => {
  const [dashboardData, setDashboardData] = useState(null);
  const [lastUpdated, setLastUpdated] = useState('');
  const history = useHistory();
  const { isActive: dashboardAnalytics, isLoading } = useSubscriptionChecker({
    subscriptionDetails,
    condition: {
      dashboardAnalytics: true
    }
  });

  useEffect(() => {
    if (roleType && roleType > RoleTypes.ADMIN) {
      history?.push('/error');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [roleType]);

  const DashboardAnalyticsData = [
    {
      info: 'dashboard.totalContent',
      icon: 'iconsminds-gift-box',
      cardTitle: 'dashboard.overall',
      numberValue: '0'
    },
    {
      info: 'dashboard.totalVideos',
      icon: 'simple-icon-camrecorder',
      cardTitle: 'dashboard.videos',
      numberValue: '0'
    },
    {
      info: 'dashboard.totalAudios',
      icon: 'simple-icon-earphones',
      cardTitle: 'dashboard.audios',
      numberValue: '0'
    },
    {
      info: 'dashboard.totalStories',
      icon: 'iconsminds-film-video',
      cardTitle: 'dashboard.stories',
      numberValue: '0'
    },
    {
      info: 'dashboard.totalImages',
      icon: 'simple-icon-picture',
      cardTitle: 'dashboard.images',
      numberValue: '0'
    }
  ];

  const loadDashboardData = async () => {
    const result = await Promise.allSettled([tenantSummary()]);

    if (result[0].status === 'fulfilled') {
      const updatedData = DashboardAnalyticsData;
      if (result[0]?.value?.mediaCount) {
        const { audio, video, image } = result[0].value.mediaCount;
        updatedData[0].numberValue =
          video + audio + image + result[0].value.storiesCount;
        updatedData[1].numberValue = video;
        updatedData[2].numberValue = audio;
        updatedData[3].numberValue = result[0].value.storiesCount;
        updatedData[4].numberValue = image;
      }
      setDashboardData(updatedData);
      setLastUpdated(result[0]?.value?.nextUpdateTime);
    }
  };

  useEffect(() => {
    if (!isLoading && dashboardAnalytics) {
      loadDashboardData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoading]);

  if (isLoading) {
    return <Loader />;
  }

  if (!dashboardAnalytics) {
    return (
      <div
        className="d-flex flex-column justify-content-center align-items-center h-100"
        style={{ gap: '1rem' }}
      >
        <IntlMessages id="subs.message" />
        <br />
        <Button
          color="primary"
          className="ml-2"
          onClick={() => {
            if (getLocalUserRoleType() <= RoleTypes.BILLING_ADMIN)
              history.push(`${adminRoot}/help-support?subs=dashboardAnalytics`);
            else
              NotificationManager.warning(
                <IntlMessages id="subs.billingadminOrabove" />,
                'Not Allowed',
                5000,
                null,
                null
              );
          }}
        >
          <IntlMessages id="subs.upgrade" />
        </Button>
      </div>
    );
  }

  if (!dashboardData) {
    return <Loader />;
  }

  return (
    <>
      <DashboardCardList
        data={dashboardData}
        lastUpdated={lastUpdated}
        className="dashboard-cards-row"
      />
      <DashboardCharts
        previewCache={previewCache}
        roleType={roleType}
        subscriptionDetails={subscriptionDetails}
      />
    </>
  );
};

export default Default;
