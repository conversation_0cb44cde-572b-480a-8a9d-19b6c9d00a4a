/* eslint-disable react/no-array-index-key */
/* eslint-disable no-unused-vars */
import React, { useEffect } from 'react';
import TimeInput from '../../TimeInput';
import PollPreview from './PollPreview';
import TextBoxInput from '../Survey/AnswerInput/TextBoxInput';
import { NotificationManager } from 'components/common/react-notifications';
import { INTERACTION_TYPE } from 'constants/VideoInteraction';
import Remove from '../../Remove';

const PollTab = ({
  pollQuestion,
  setPollQuestion,
  pollOptions,
  setPollOptions,
  startTime,
  setStartTime,
  interactiondetails
}) => {
  useEffect(() => {
    if (
      interactiondetails &&
      interactiondetails?.type === INTERACTION_TYPE.POLL
    ) {
      setStartTime(interactiondetails?.showAtTimecode);
      setPollQuestion(interactiondetails.data.title);
      setPollOptions(interactiondetails.data.pollOptions.map((m) => m.text));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [interactiondetails]);

  return (
    <>
      <div className="polltab">
        <TimeInput
          label="Start Time"
          required
          className="w-30 mt-3"
          value={startTime}
          onChange={(v) => {
            setStartTime(v);
          }}
        />

        <div className="questions">
          <h1>Poll Question</h1>
          <TextBoxInput
            value={pollQuestion}
            onChange={(e) => {
              const txtValue = e.target.value;
              const value = txtValue.substring(0, 140);
              if (txtValue.length > 140) {
                NotificationManager.warning('Maximum characters are 140');
              }
              setPollQuestion(value);
            }}
            isTextArea={false}
            className="w-full-imp mt-0"
          />
        </div>

        <div className="options">
          <h1>
            Poll Options{' '}
            <span style={{ color: 'gray', fontWeight: 500, fontSize: 16 }}>
              (Max options limit 4)
            </span>
          </h1>
          <div className="op-list">
            {pollOptions.map((m, i) => (
              <div key={i} className="op-input">
                <TextBoxInput
                  value={m}
                  className="w-full-imp m-0"
                  onChange={(e) => {
                    const txtValue = e.target.value;
                    const value = txtValue.substring(0, 140);
                    if (txtValue.length > 140) {
                      NotificationManager.warning('Maximum characters are 140');
                    }
                    setPollOptions((prev) => {
                      const newOptions = prev.map((l, idx) => {
                        if (i === idx) {
                          return value;
                        }
                        return l;
                      });
                      return newOptions;
                    });
                  }}
                  isTextArea={false}
                />
                {pollOptions.length > 2 && (
                  <Remove
                    onClick={() => {
                      setPollOptions((prev) => {
                        const newList = prev.filter((f, j) => i !== j);
                        return newList;
                      });
                    }}
                  />
                )}
              </div>
            ))}
          </div>
        </div>

        {pollOptions.length < 4 && (
          <button
            type="button"
            color="primary"
            onClick={() => {
              setPollOptions((prev) => [...prev, '']);
            }}
            style={{
              background: 'transparent',
              borderRadius: 10,
              color: '#992288',
              fontSize: 14,
              width: 'max-content',
              padding: '4px 10px'
            }}
            className="dotted-border-primary-box mb-4 mt-3"
          >
            Add Option
          </button>
        )}

        <PollPreview options={pollOptions} question={pollQuestion} />
      </div>
    </>
  );
};

export default PollTab;
