/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-vars */
import { RESPONSE_TYPES } from 'constants/VideoInteraction';
import React from 'react';
import CheckboxInput from '../AnswerInput/CheckboxInput';
import TextBoxInput from '../AnswerInput/TextBoxInput';
import DateInput from '../AnswerInput/DateInput';
import CheckBoxPreview from './CheckBoxPreview';
import MCQPreview from './MCQPreview';

const QuesAnsPreview = ({ data }) => {
  const { responseType, values } = data;
  const answer = data?.answer ?? null;
  return (
    <div className="QuesAnsPreview">
      <div className="QuesAnsPreviewQuestion">{data.question}</div>
      <div className="QuesAnsPreviewOptions">
        {+responseType === RESPONSE_TYPES.CHECKBOX ? (
          <CheckBoxPreview answer={answer} values={values} />
        ) : +responseType === RESPONSE_TYPES.DATE ? (
          <DateInput answer={answer} disabled value={values[0].value} />
        ) : +responseType === RESPONSE_TYPES.TEXT_BOX ? (
          <TextBoxInput answer={answer} disabled value={values[0].value} />
        ) : (
          <MCQPreview answer={answer} values={values} />
        )}
      </div>
    </div>
  );
};

export default QuesAnsPreview;
