import React from 'react';
import VidAudDescription from './VidAudDescription';
import OtherDescription from './OtherDescription';
import InfoMessage from 'components/infoMessage';

const Description = ({
  content,
  getContent,
  roleType,
  selectedTenantId,
  isImage,
  isYoutubeContent,
  isStory = false,
  contentIds,
  isAiEnabled,
  setIsAiEnabled
}) => {
  const vidAudLocal =
    // !content?.metadata?.SummaryAuto &&
    content.mediaType !== 3 && !isStory && !isYoutubeContent;

  console.log({
    vidAudLocal,
    isYoutubeContent,
    isStory,
    'content?.metadata?.SummaryAuto': content?.metadata?.SummaryAuto,
    condition: vidAudLocal,
    'content.mediaType': content.mediaType
  });

  const title = content?.metadata?.TitleAuto;
  const description = content?.metadata?.SummaryAuto;
  const tags = content?.metadata?.TagsAuto;

  return (
    <>
      <div
        className={`p-4 mb-${isYoutubeContent ? '1' : '4'} cardborder`}
        style={{ borderRadius: 20 }}
      >
        {vidAudLocal ? (
          <VidAudDescription
            isStory={isStory}
            content={content}
            roleType={roleType}
            selectedTenantId={selectedTenantId}
            getContent={getContent}
            isAiEnabled={isAiEnabled}
            setIsAiEnabled={setIsAiEnabled}
          />
        ) : (
          <OtherDescription
            content={content}
            contentIds={contentIds}
            isStory={isStory}
            getContent={getContent}
            isYoutubeContent={isYoutubeContent}
            description={description}
            title={title}
            tags={tags}
            selectedTenantId={selectedTenantId}
            roleType={roleType}
            isImage={isImage}
          />
        )}
      </div>
      {isYoutubeContent && (
        <InfoMessage style={{ fontSize: 14 }} message="yt-message-describe" />
      )}
    </>
  );
};

export default Description;
