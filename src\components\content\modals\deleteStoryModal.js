import React from 'react';
import ConfirmationModal from 'components/common/ConfirmationModal';

const DeleteStoryModal = ({
  openModal,
  toggleModal,
  deleteStory,
  mediaTitle,
}) => {
  const handleConfirm = async () => {
    toggleModal();
    deleteStory();
  };

  const handleClose = () => {
    toggleModal();
  };

  return (
    <ConfirmationModal
      openConfirmationModal={openModal}
      toggleConfirmationModal={toggleModal}
      handleConfirm={handleConfirm}
      handleClose={handleClose}
      type="deleteStory"
      mediaTitle={mediaTitle}
    />
  );
};

export default DeleteStoryModal;
