/* eslint-disable no-unused-vars */

import React from 'react';
import {
  ColorPickerButton,
  EditButton,
  EditImageButton,
  ShareButton,
  SubcribeButton
} from './Buttons';
import UserAvatar from './UserAvatar';
import VerifedIcon from 'constants/VerifedIcon';
import { handleOwnerShare } from 'helpers/Utils';
import ThemePicker from './ThemePicker';
import { NotificationManager } from 'components/common/react-notifications';
import IntlMessages from 'helpers/IntlMessages';

const VideoOwner = ({
  isVideoFile,
  bgImg,
  showEditOptions,
  isMyShowcase,
  setEditImageModalOpen,
  isEdit,
  profileImg,
  setIsEdit,
  isThemePicker,
  setIsThemePicker,
  setThemeColors,
  themeColors,
  orgThemeColors,
  setOrgThemeColors,
  toggleEditOwnerModal,
  handleSubscription,
  ownerData,
  expandFor,
  userIdentifier,
  isSubscribed,
  isLoggedIn,
  accountName,
  currentColor,
  subscribing,
  handleEditBg,
  isShowcaseBgLoading
}) => {
  return (
    <>
      <div className={`position-relative showcase-banner-video`}>
        {bgImg && (
          <video
            alt="background"
            height="100%"
            width="100%"
            src={bgImg}
            muted
            autoPlay
            loop
            style={{ overflow: 'hidden', objectFit: 'cover' }}
          />
        )}
        {isMyShowcase && showEditOptions && (
          <EditImageButton
            isShowcaseBgLoading={isShowcaseBgLoading}
            disabled={isEdit}
            onClick={() => {
              handleEditBg();
            }}
          />
        )}
        <span
          className="position-absolute w-full align-items-center justify-content-center d-flex"
          style={{ bottom: '105px' }}
        >
          <UserAvatar
            isEdit={isEdit}
            setEditImageModalOpen={setEditImageModalOpen}
            profileImageUrl={profileImg}
            isMyShowcase={isMyShowcase && showEditOptions}
          />
        </span>
        <div
          className="showcase-owner-video-title position-absolute text-ellipsis"
          style={{
            bottom: '2rem',
            right: '10%',
            left: '10%',
            textAlign: 'center'
          }}
        >
          <h1
            style={{
              color: '#fff',
              wordWrap: 'break-word'
            }}
          >
            {accountName}
            <VerifedIcon isVerfied={ownerData?.isVerified} />
          </h1>
        </div>
        <div
          className="d-flex flex-row w-full position-absolute align-items-center justify-content-end"
          style={{
            bottom: '10px',
            right: '10px',
            gap: '10px'
          }}
        >
          <SubcribeButton
            showColorPicker={isEdit && isMyShowcase}
            subscribing={subscribing}
            themeColors={themeColors}
            handleSubscription={handleSubscription}
            ownerData={ownerData}
            setThemeColors={setThemeColors}
            isLoggedIn={isLoggedIn}
            isSubscribed={isSubscribed}
            orgThemeColors={orgThemeColors}
            setOrgThemeColors={setOrgThemeColors}
            userIdentifier={userIdentifier}
          />
          <ShareButton
            isMyShowcase={isMyShowcase && isEdit}
            expandFor={expandFor}
            themeColors={themeColors}
            orgThemeColors={orgThemeColors}
            setOrgThemeColors={setOrgThemeColors}
            setThemeColors={setThemeColors}
            onClick={() =>
              handleOwnerShare(window.location.href, ownerData?.profileTitle)
            }
          />
        </div>
      </div>
      <div>
        {isMyShowcase && showEditOptions && (
          <div className="d-flex flex-row w-full align-items-center justify-content-end mt-3">
            <div className="owner-button-cont">
              <div className="d-flex flex-row gap-2 card-and-theme">
                <ColorPickerButton
                  isEdit={isEdit}
                  setIsEdit={setIsEdit}
                  isThemePicker={isThemePicker}
                />
                <ThemePicker
                  themeColors={themeColors}
                  setThemeColors={setThemeColors}
                  isOpen={isThemePicker}
                  setIsOpen={setIsThemePicker}
                  orgThemeColors={orgThemeColors}
                  setOrgThemeColors={setOrgThemeColors}
                  disabled={isEdit}
                />
              </div>
              <EditButton
                label="Edit"
                onClick={toggleEditOwnerModal}
                iconClassName="simple-icon-pencil"
                disabled={isEdit || isThemePicker}
              />
            </div>
          </div>
        )}
      </div>
      <h4 className="text-center my-4">About</h4>
    </>
  );
};

export default VideoOwner;
