import React from 'react';
import LoadingSkeleton from 'components/LoadingSkeleton';

const DetailLoading = () => {
  return (
    <div className="p-4" style={{ height: '90vh' }}>
      <div className="d-flex flex-row w-full mb-2">
        <div className="d-flex flex-column w-50">
          <LoadingSkeleton
            style={{ height: '30px', width: '60%' }}
            containerClassName="d-flex align-items-center"
          />
          <LoadingSkeleton
            style={{ height: '15px', width: '10%' }}
            containerClassName="d-flex align-items-center mt-1"
          />
        </div>
        <div className="d-flex flex-row w-50 align-items-center gap-1 justify-content-end">
          <LoadingSkeleton
            circle
            style={{
              height: '50px',
              width: '50px'
            }}
          />
          <LoadingSkeleton
            style={{ height: '50px', borderRadius: 40 }}
            containerClassName="d-flex align-items-center w-20"
          />
          <LoadingSkeleton
            circle
            style={{
              height: '50px',
              width: '50px'
            }}
          />
        </div>
      </div>
      <LoadingSkeleton
        style={{ width: '100%', height: '100%' }}
        containerClassName="d-flex align-items-center justify-content-center h-85"
      />
      <div className="d-flex flex-row w-full mt-2">
        <div className="d-flex flex-column w-50">
          <LoadingSkeleton
            style={{ height: '15px', width: '10%' }}
            containerClassName="d-flex align-items-center mt-1"
          />
          <LoadingSkeleton
            style={{ height: '25px', width: '50%' }}
            containerClassName="d-flex align-items-center"
          />
        </div>
        <div className="d-flex flex-row w-50 align-items-center gap-1 justify-content-end">
          <LoadingSkeleton
            style={{ height: '40px', borderRadius: 10 }}
            containerClassName="d-flex align-items-center w-5"
          />
          <LoadingSkeleton
            style={{ height: '50px', borderRadius: 40 }}
            containerClassName="d-flex align-items-center w-20"
          />
          <LoadingSkeleton
            style={{ height: '50px', borderRadius: 40 }}
            containerClassName="d-flex align-items-center w-20"
          />
        </div>
      </div>
    </div>
  );
};

export default DetailLoading;
