/* eslint-disable no-nested-ternary */
import { INTERACTION_TYPE } from 'constants/VideoInteraction';
import { isDarkModeActive } from 'helpers/Utils';
import React from 'react';

const sizes = {
  width: 14,
  height: 14
};

const RichTextIcon = ({ active }) => {
  const isDarkMode = isDarkModeActive();
  const fill = active ? '#922C88' : isDarkMode ? '#8f8f8f' : '#474D66';
  return (
    <svg width="16" height="18" {...sizes} viewBox="0 0 16 18" fill="none">
      <path
        d="M5.66667 11.8571V10.1429M5.66667 10.1429V4.42857C5.66667 4.12547 5.78958 3.83478 6.00838 3.62045C6.22717 3.40612 6.52391 3.28571 6.83333 3.28571C7.14275 3.28571 7.4395 3.40612 7.65829 3.62045C7.87708 3.83478 8 4.12547 8 4.42857V9H11.9118C12.9735 9 13.8333 9.84229 13.8333 10.8823V11.8571C13.8333 13.2211 13.2802 14.5292 12.2956 15.4937C11.3111 16.4582 9.97572 17 8.58333 17H8C6.76232 17 5.57534 16.5184 4.70017 15.6611C3.825 14.8038 3.33333 13.641 3.33333 12.4286C3.33333 11.8224 3.57917 11.241 4.01675 10.8123C4.45434 10.3837 5.04783 10.1429 5.66667 10.1429ZM9.75 6.71429H12.0833C12.8569 6.71429 13.5987 6.41327 14.1457 5.87745C14.6927 5.34163 15 4.6149 15 3.85714C15 3.09938 14.6927 2.37266 14.1457 1.83684C13.5987 1.30102 12.8569 1 12.0833 1H3.91667C3.14312 1 2.40125 1.30102 1.85427 1.83684C1.30729 2.37266 1 3.09938 1 3.85714C1 4.6149 1.30729 5.34163 1.85427 5.87745C2.40125 6.41327 3.14312 6.71429 3.91667 6.71429"
        stroke={fill}
        strokeWidth="1.5"
      />
    </svg>
  );
};

const ImageIcon = ({ active }) => {
  const isDarkMode = isDarkModeActive();
  const fill = active ? '#922C88' : isDarkMode ? '#8f8f8f' : '#474D66';

  return (
    <svg width="20" height="20" {...sizes} viewBox="0 0 20 20" fill="none">
      <path
        d="M2.02 20C1.44417 20 0.96375 19.8075 0.57875 19.4225C0.19375 19.0375 0.000833333 18.5567 0 17.98V2.02C0 1.44417 0.192916 0.96375 0.57875 0.57875C0.964583 0.19375 1.445 0.000833333 2.02 0H17.9812C18.5562 0 19.0367 0.192916 19.4225 0.57875C19.8083 0.964583 20.0008 1.445 20 2.02V17.9812C20 18.5562 19.8075 19.0367 19.4225 19.4225C19.0375 19.8083 18.5567 20.0008 17.98 20H2.02ZM2.02 18.75H17.9812C18.1729 18.75 18.3492 18.67 18.51 18.51C18.6708 18.35 18.7508 18.1733 18.75 17.98V2.02C18.75 1.8275 18.67 1.65083 18.51 1.49C18.35 1.32917 18.1733 1.24917 17.98 1.25H2.02C1.8275 1.25 1.65083 1.33 1.49 1.49C1.32917 1.65 1.24917 1.82667 1.25 2.02V17.9812C1.25 18.1729 1.33 18.3492 1.49 18.51C1.65 18.6708 1.82625 18.7508 2.01875 18.75M4.375 15.625H15.8175L12.2837 10.9125L9.015 15.0475L6.8275 12.4037L4.375 15.625Z"
        fill={fill}
      />
    </svg>
  );
};

const PollIcon = ({ active }) => {
  const isDarkMode = isDarkModeActive();
  const fill = active ? '#922C88' : isDarkMode ? '#8f8f8f' : '#474D66';
  return (
    <svg width="18" height="18" {...sizes} viewBox="0 0 18 18" fill="none">
      <path
        d="M16 0H2C0.9 0 0 0.9 0 2V16C0 17.1 0.9 18 2 18H16C17.1 18 18 17.1 18 16V2C18 0.9 17.1 0 16 0ZM16 16H2V2H16V16ZM4 7H6V14H4V7ZM8 4H10V14H8V4ZM12 10H14V14H12V10Z"
        fill={fill}
      />
    </svg>
  );
};

const SurveyIcon = ({ active }) => {
  const isDarkMode = isDarkModeActive();
  const fill = active ? '#922C88' : isDarkMode ? '#8f8f8f' : '#474D66';
  return (
    <svg width="14" height="18" {...sizes} viewBox="0 0 14 18" fill="none">
      <path
        d="M4.10153 -2.83805e-05C3.94115 0.0304764 3.7967 0.116682 3.69371 0.243361C3.59072 0.370041 3.53581 0.529042 3.53869 0.692279V1.38459H1.05053C0.496687 1.38459 0.0771484 1.80413 0.0771484 2.35797V16.9615C0.0771484 17.5846 0.496687 18 1.05053 18H12.8848C13.4387 18 13.8582 17.5804 13.8582 17.0266V2.35797C13.9275 1.80413 13.5038 1.38459 12.9499 1.38459H10.4618V0.692279C10.4618 0.508668 10.3888 0.332577 10.259 0.202744C10.1292 0.072911 9.95307 -2.83805e-05 9.76946 -2.83805e-05H4.23099C4.20931 -0.00104848 4.1876 -0.00104848 4.16592 -2.83805e-05C4.14424 -0.00104848 4.12321 -0.00104848 4.10153 -2.83805e-05ZM4.9233 1.38459H9.07715V2.7692H4.9233V1.38459ZM1.46176 2.7692H3.53869V3.46151C3.53869 3.64512 3.61163 3.82121 3.74146 3.95105C3.87129 4.08088 4.04738 4.15382 4.23099 4.15382H9.76946C9.95307 4.15382 10.1292 4.08088 10.259 3.95105C10.3888 3.82121 10.4618 3.64512 10.4618 3.46151V2.7692H12.5387V16.6154H1.46176V2.7692ZM2.84638 6.23074V8.99997H5.61561V6.23074H2.84638ZM3.53869 6.92305H4.9233V8.30766H3.53869V6.92305ZM7.00023 6.92305V8.30766H11.1541V6.92305H7.00023ZM2.84638 10.3846V13.1538H5.61561V10.3846H2.84638ZM7.00023 11.0769V12.4615H11.1541V11.0769H7.00023Z"
        fill={fill}
      />
    </svg>
  );
};

const InteractiveIcon = ({ iconType = INTERACTION_TYPE.RICH_TEXT, active }) => {
  if (iconType === INTERACTION_TYPE.IMAGE) {
    return <ImageIcon active={active} />;
  }
  if (iconType === INTERACTION_TYPE.POLL) {
    return <PollIcon active={active} />;
  }
  if (iconType === INTERACTION_TYPE.RICH_TEXT) {
    return <RichTextIcon active={active} />;
  }
  return <SurveyIcon active={active} />;
};

export default InteractiveIcon;
