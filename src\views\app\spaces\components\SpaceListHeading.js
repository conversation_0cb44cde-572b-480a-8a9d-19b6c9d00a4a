import React, { useState } from 'react';
import { Row, Button, Collapse } from 'reactstrap';
import { injectIntl } from 'react-intl';
import { ThumbListIcon, ImageListIcon } from 'components/svg';
import { Colxx } from 'components/common/CustomBootstrap';
import IntlMessages from 'helpers/IntlMessages';

const SpaceListHeading = ({
  intl,
  displayMode,
  changeDisplayMode,
  search,
  setSearch,
}) => {
  const [displayOptionsIsOpen, setDisplayOptionsIsOpen] = useState(false);
  const [tempSearch, setTempSearch] = useState(search);
  const { messages } = intl;

  const handleTempSearch = (event) => {
    setTempSearch(event.target.value);
  };

  const handleClearClick = () => {
    setTempSearch("");
    setSearch("");
  };

  return (
    <Row>
      <Colxx xxs="12">
        <div className="mb-3">
          <Button
            color="empty"
            className="pt-0 pl-3 d-inline-block d-md-none"
            onClick={() => setDisplayOptionsIsOpen(!displayOptionsIsOpen)}
          >
            <IntlMessages id="pages.display-options" />{' '}
            <i className="simple-icon-arrow-down align-middle" />
          </Button>
          <Collapse
            isOpen={displayOptionsIsOpen}
            className="ml-3 d-flex flex-row"
            id="displayOptions"
          >
            <span className="mt-1 mr-3 d-flex flex-row float-md-left mb-2">
              <a
                href="#/"
                className={`mr-3 view-icon ${displayMode === 'card' ? 'active' : ''
                  }`}
                onClick={() => changeDisplayMode('card')}
              >
                <ImageListIcon />
              </a>
              <a
                href="#/"
                className={`mr-3 view-icon ${displayMode === 'list' ? 'active' : ''
                  }`}
                onClick={() => changeDisplayMode('list')}
              >
                <ThumbListIcon />
              </a>
            </span>
            <div className="d-block d-md-inline-block mt-3 mt-md-0">
              <div className="search-sm d-inline-block float-md-left mr-1 mb-1 align-top">
                <button
                  type="button"
                  className="search-sm-btn-space"
                  onClick={() => setSearch(tempSearch)}
                >
                  <i className="simple-icon-magnifier search-sm-icon" />
                </button>
                {/* <span><i className="simple-icon-magnifier search-sm-icon" /></span> */}
                <input
                  type="text"
                  name="keyword"
                  id="search"
                  placeholder={messages['menu.search']}
                  value={tempSearch}
                  onChange={handleTempSearch}
                  onKeyPress={(event) => {
                    if (event.key === 'Enter') {
                      setSearch(event.target.value);
                    }
                  }}
                />
                {tempSearch &&
                  <button
                    type="button"
                    className="search-sm-btn-2"
                    onClick={handleClearClick}
                  >
                    <span style={{color: '#131313'}} aria-hidden="true">×</span>
                  </button>}
              </div>
            </div>
          </Collapse>
        </div>
      </Colxx>
    </Row>
  );
};

export default injectIntl(SpaceListHeading);
