/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable react/no-array-index-key */
/* eslint-disable no-unused-vars */
import { getInteractionAnalyticsById } from 'functions/api/analyticsApi';
import { formatDate, formatInteraction, formatTime } from 'helpers/Utils';
import React, { useEffect, useMemo, useState } from 'react';
import xlsx from 'json-as-xlsx';
import { PollResultLoading } from '../Loading';

const PollResult = ({ selectedPollId, setSelectedPollId, content }) => {
  const contentId = content?.id;
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const { data, isError } = await getInteractionAnalyticsById({
          contentId,
          interactionId: selectedPollId.id
        });
        if (data) {
          setResult(data);
        }
        setIsLoading(false);
      } catch (error) {
        console.error('Something went wrong in fetchData due to ', error);
        setIsLoading(false);
      }
    };

    fetchData();
  }, [contentId, selectedPollId]);

  const createdAt = new Date(selectedPollId?.creationDateTime);
  const updatedAt = new Date(selectedPollId?.lastUpdatedDateTime);

  const isDisabledDate = updatedAt > createdAt;

  const pollOptionsWithInfo = useMemo(() => {
    const pollOptions = selectedPollId?.data?.pollOptions ?? [];
    const resultsWithParsedEventData = result.map((m) => {
      return {
        ...m,
        eventdata: JSON.parse(m.eventdata)
      };
    });
    return pollOptions.map((m) => {
      const sameAnswer = resultsWithParsedEventData.filter(
        (f) => f.eventdata.answer === m.text
      );
      const selectedBy = sameAnswer ? sameAnswer.length : 0;
      return {
        name: m.text,
        percentage: Math.round((+selectedBy / result.length) * 100),
        numOfVotes: selectedBy
      };
    });
    // return selectedPollId.data.pollOptions;
  }, [selectedPollId, result]);

  const handleDownload = () => {
    const settings = {
      fileName: `player_interactions_content_${contentId}_interaction_${selectedPollId.id}`
    };
    const formattedData = formatInteraction({
      data: result,
      isPoll: true
    });
    xlsx(formattedData, settings);
  };

  return (
    <div className="InteractionAnalytics">
      <div className="IALeft">
        <svg
          onClick={() => {
            setSelectedPollId(null);
          }}
          width="12"
          height="23"
          viewBox="0 0 12 23"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="c-pointer"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M11.5003 20.7975L10.194 22.125L0.611547 12.3875C0.380923 12.1496 0.251953 11.8313 0.251953 11.5C0.251953 11.1687 0.380923 10.8504 0.611547 10.6125L10.194 0.875L11.5003 2.20375L2.3528 11.5L11.5003 20.7975Z"
            fill="black"
          />
        </svg>
      </div>
      <div className="IARight PollResult">
        {isLoading ? (
          <PollResultLoading />
        ) : (
          <>
            <div className="headbar">
              <h4 className="m-0">Poll Results</h4>
              <DownloadExcel
                onClick={() => {
                  handleDownload();
                }}
              />
            </div>

            <div className="sub-headbar">
              <div className="d-flex flex-row">
                <p className="font-bolder">Created on: &nbsp;</p>
                <p>
                  {formatDate(new Date(createdAt))}{' '}
                  {formatTime(new Date(createdAt))}
                </p>
              </div>
              {isDisabledDate && !selectedPollId.isEnabled && (
                <div className="d-flex flex-row">
                  <p className="font-bolder" style={{ color: 'red' }}>
                    Disabled on: &nbsp;
                  </p>
                  <p>
                    {formatDate(new Date(updatedAt))}{' '}
                    {formatTime(new Date(updatedAt))}
                  </p>
                </div>
              )}
            </div>

            <p className="ques">
              What type of content do you prefer engaging with the most?
            </p>
            {pollOptionsWithInfo.map((m, i) => (
              <PollOption key={i} data={m} />
            ))}
            <p className="ques">{result.length} votes</p>
          </>
        )}
      </div>
    </div>
  );
};

const DownloadExcel = (props) => {
  return (
    <span className="c-pointer" {...props}>
      <svg
        width={25}
        height={25}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12 2a1 1 0 0 1 1 1v10.586l2.293-2.293a1 1 0 0 1 1.414 1.414l-4 4a1 1 0 0 1-1.414 0l-4-4a1 1 0 1 1 1.414-1.414L11 13.586V3a1 1 0 0 1 1-1M5 17a1 1 0 0 1 1 1v2h12v-2a1 1 0 1 1 2 0v2a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2v-2a1 1 0 0 1 1-1"
          fill="#992288"
        />
      </svg>
    </span>
  );
};

const PollOption = ({ data }) => {
  const { name, percentage, numOfVotes } = data;
  return (
    <div className="PollOption">
      <div className="PollOptionCard">
        <div className="option-bg" style={{ width: `${percentage}%` }}>
          &nbsp;
        </div>
        <div className="option-val">{name}</div>
      </div>
      <div className="PollOptionRight">
        <p>
          <b>{percentage}%</b>
        </p>
        <p>({numOfVotes}) votes</p>
      </div>
    </div>
  );
};

export default PollResult;
