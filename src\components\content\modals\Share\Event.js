/* eslint-disable consistent-return */
/* eslint-disable array-callback-return */
/* eslint-disable no-unreachable */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState, memo } from 'react';
import KeyValue from './components/KeyValue';
import { Button, Col, Form, FormGroup, Label, Input } from 'reactstrap';
import SelectedImg from 'components/SelectedImg';
import Tab from './components/Tab';
import SwitchButton from 'components/buttons/SwitchButton';
import {
  formatDate,
  formatTime,
  getRandomChars,
  getTimeStamp,
  isMoreThanAYear,
  removeHtmlTags,
  timeToUTCMillisConvert,
  UTCMillisToDateTime,
  validateUrl
} from 'helpers/Utils';
import SelectPlayer from 'components/SelectPlayer';
import IntlMessages from 'helpers/IntlMessages';
import LoadingButton from 'components/buttons/LoadingButton';
import {
  createShare,
  deleteEventImage,
  eventImageUploadAndComplete,
  updateShare,
  uploadEventImage
} from 'functions/api/spacesApi';
import { NotificationManager } from 'components/common/react-notifications';
import uploadFileToBlob from 'helpers/Azure';
import timeZone from 'data/timeZoneList';
import ReactQuill from 'react-quill';

function Event({
  MediaType,
  isYtVimeoVideo,
  spaceId,
  isShared,
  handleShareNow,
  sharingData,
  title,
  handleRemove,
  entityId,
  entityKind,
  isRemoving,
  isEditing,
  setIsEditing,
  handleUpdateEntity,
  setSharingData,
  showSelectPlayer,
  isDRM
}) {
  const showEdit = !isEditing && isShared;
  const [details, setDetails] = useState({
    startTime: getTimeStamp('min'),
    endDate: null,
    timeError: '',
    eventName: '',
    eventNameError: '',
    eventDesc: '',
    eventDescError: '',
    actionButton: '',
    actionButtonError: '',
    actionButtonLink: '',
    actionButtonLinkError: '',
    tmzn: Intl.DateTimeFormat().resolvedOptions().timeZone
  });
  const [isCustomEventImg, setIsCustomEventImg] = useState(false);

  console.log('details', details);

  useEffect(() => {
    if (sharingData) {
      const setUpdatedSharedData = {
        startTime: sharingData?.startDateTime
          ? UTCMillisToDateTime(
              sharingData?.startDateTime,
              sharingData?.timeZone
            )
          : UTCMillisToDateTime(
              Date.now(),
              Intl.DateTimeFormat().resolvedOptions().timeZone
            ),
        // startTime: UTCMillisToDateTime(
        //   sharingData?.startDateTime,
        //   sharingData?.timeZone
        // ),

        endDate: sharingData?.endDateTime
          ? UTCMillisToDateTime(sharingData?.endDateTime, sharingData?.timeZone)
          : null,
        // endDate: UTCMillisToDateTime(
        //   sharingData?.endDateTime,
        //   sharingData?.timeZone
        // ),
        tmzn:
          sharingData?.timeZone?.length > 0
            ? sharingData?.timeZone
            : Intl.DateTimeFormat().resolvedOptions().timeZone,
        eventName: sharingData?.eventName,
        eventDesc: sharingData?.eventDescription,
        actionButton: sharingData?.eventActionButton,
        actionButtonLink: sharingData?.eventActionButtonLink,

        timeError: '',
        eventNameError: '',
        eventDescError: '',
        actionButtonError: '',
        actionButtonLinkError: ''
      };
      if (sharingData?.imageId && sharingData?.imageUrl) {
        setIsCustomEventImg(true);
      } else {
        setIsCustomEventImg(false);
      }
      setDetails(setUpdatedSharedData);
    }
  }, [sharingData]);

  return (
    <div className="w-full ">
      {!showEdit ? (
        <CreateOrEdit
          MediaType={MediaType}
          isYtVimeoVideo={isYtVimeoVideo}
          spaceId={spaceId}
          isEditing={isEditing}
          setIsEditing={setIsEditing}
          entityKind={entityKind}
          entityId={entityId}
          sharingData={sharingData}
          isShared={isShared}
          details={details}
          setDetails={setDetails}
          handleShareNow={handleShareNow}
          handleUpdateEntity={handleUpdateEntity}
          setSharingData={setSharingData}
          showSelectPlayer={showSelectPlayer}
          isCustomEventImg={isCustomEventImg}
          setIsCustomEventImg={setIsCustomEventImg}
        />
      ) : (
        <Shared
          isEditing={isEditing}
          isRemoving={isRemoving}
          showEdit={showEdit}
          setIsEditing={setIsEditing}
          MediaType={MediaType}
          sharingData={sharingData}
          isYtVimeoVideo={isYtVimeoVideo}
          spaceId={spaceId}
          title={title}
          isDRM={isDRM}
          handleRemove={handleRemove}
        />
      )}
    </div>
  );
}

export default Event;

const Shared = ({
  sharingData,
  title,
  handleRemove,
  isEditing,
  setIsEditing,
  showEdit,
  isRemoving,
  isDRM
}) => {
  const details = [
    {
      id: 1,
      name: 'Share Type : ',
      value: 'Event Schedule'
    },
    {
      id: 2,
      name: 'Name : ',
      value: sharingData?.eventName ?? ''
    },
    {
      id: 3,
      name: 'Description : ',
      value: sharingData?.eventDescription ?? ''
    },
    {
      id: 4,
      name: 'Action button : ',
      value: sharingData?.eventActionButton ?? ''
    },
    {
      id: 5,
      name: 'Action Button Link : ',
      value: sharingData?.eventActionButtonLink ?? ''
    }
  ];
  const schedules = [
    {
      id: 1,
      name: 'Time Zone :',
      value: sharingData?.timeZone
    },
    {
      id: 2,
      name: 'Start Date & Time : ',
      value:
        sharingData?.startDateTime &&
        `${formatDate(
          new Date(sharingData?.startDateTime),
          false,
          sharingData?.timeZone
        )} ${formatTime(
          new Date(sharingData?.startDateTime),
          false,
          sharingData?.timeZone
        )}`
    },
    {
      id: 3,
      name: 'End Date & Time : ',
      value:
        sharingData?.endDateTime &&
        `${formatDate(
          new Date(sharingData?.endDateTime),
          false,
          sharingData?.timeZone
        )} ${formatTime(
          new Date(sharingData?.endDateTime),
          false,
          sharingData?.timeZone
        )}`
    },
    {
      id: 4,
      name: 'Player : ',
      value: sharingData?.basicPlayerInfo?.name
    }
  ];

  return (
    <div className="w-full">
      <Tab
        entityKind={sharingData?.entityKind}
        shareId={sharingData?.id}
        title={title}
        isDRM={isDRM}
      />
      <div className="w-full d-flex flex-row align-items-start justify-content-between">
        <div className="d-flex flex-column">
          <h5 className="font-bolder mb-2">Event Image</h5>
          <SelectedImg
            src={
              sharingData?.imageUrl ??
              'https://play.innerloop.stream/playersvc/ins-share/cu-LKT1l6SA9b-x9ItTVHHcy'
            }
            showCloseIcon={false}
          />
        </div>
        <div>
          {showEdit && (
            <Button color="primary" onClick={() => setIsEditing(true)}>
              Edit Sharing
            </Button>
          )}
        </div>
      </div>
      <div className="w-full d-flex flex-row mt-3 gap-1">
        <div className="w-50">
          <h5 className="font-bolder mb-3">Event Details</h5>
          <div>
            {details.map((m) => {
              if (m.id === 3 && m.value && removeHtmlTags(m.value).length > 0) {
                return (
                  <>
                    <span className="font-bolder" style={{ fontSize: 14 }}>
                      <IntlMessages id={m.name} />
                    </span>
                    <ReactQuill
                      key={m.id}
                      className={`react-quill w-full-imp mb-2 reactQuill-border-remove`}
                      theme="bubble"
                      style={{ border: '1px' }}
                      value={m?.value ?? ''}
                      readOnly
                    />
                  </>
                );
              }
              if (m.id !== 3) {
                return (
                  <KeyValue
                    key={m.id}
                    name={m.name}
                    value={m.value}
                    isUrl={m.id === 5}
                  />
                );
              }
            })}
          </div>
        </div>
        <div className="w-50">
          <h5 className="font-bolder mb-3">Event Schedule</h5>
          <div>
            {schedules.map((m) => (
              <KeyValue key={m.id} name={m.name} value={m.value} />
            ))}
          </div>
        </div>
      </div>
      <div className="d-flex align-items-center justify-content-center mt-5">
        <LoadingButton
          width={175}
          disabled={isRemoving}
          loading={isRemoving}
          text={isRemoving ? 'Removing...' : 'Remove Sharing'}
          onClick={handleRemove}
        />
      </div>
    </div>
  );
};

const CreateOrEdit = ({
  MediaType,
  isYtVimeoVideo,
  spaceId,
  handleShareNow,
  entityKind,
  entityId,
  sharingData,
  isShared,
  isEditing,
  handleUpdateEntity,
  setIsEditing,
  details,
  setDetails,
  setSharingData,
  showSelectPlayer,
  isCustomEventImg,
  setIsCustomEventImg
}) => {
  const [isSharing, setIsSharing] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [selectedImg, setSelectedImg] = useState(null);
  const [selectedPlayer, setSelectedPlayer] = useState(
    sharingData?.basicPlayerInfo ?? {
      name: null,
      id: null
    }
  );

  const handleShare = async () => {
    try {
      setIsSharing(true);
      const data = {
        Id: getRandomChars(21),
        PlayerId: selectedPlayer.id,
        EntityKind: entityKind,
        EntityId: entityId,
        ShareAlways: false,
        StartDateTime: 0,
        EndDateTime: null,
        TimeZone: '',
        EventName: details?.eventName,
        EventDescription: details?.eventDesc,
        EventActionButton: details?.actionButton,
        EventActionButtonLink: details?.actionButtonLink
      };
      if (!selectedImg) {
        data.imageId = null;
        data.imageUrl = null;
      }
      data.StartDateTime = timeToUTCMillisConvert(
        details.startTime,
        details.tmzn
      );
      data.EndDateTime = timeToUTCMillisConvert(details.endDate, details.tmzn);
      data.TimeZone = details.tmzn;
      const result = await createShare(data, spaceId);
      const isLimitError = result.errorMsg === 'SHARE_LIMIT_REACHED';
      if (!result.isError) {
        const createdShareId = result?.data?.id;
        if (createdShareId) {
          if (selectedImg) {
            const { data, isError } = await uploadEventImage(
              spaceId,
              createdShareId,
              [selectedImg.name]
            );
            console.log({
              data,
              isError
            });
            if (!isError && data) {
              const fileItems = [];
              fileItems.push(data.items[0]);
              const fileId = data?.items[0]?.fileName.split('/input/')[1];
              const fileSasObj = {
                items: fileItems,
                sasUrl: data?.sasUrl
              };
              const { isError: isUploadingError } = await uploadFileToBlob(
                selectedImg,
                () => {},
                () => {},
                fileSasObj
              );
              if (isUploadingError) {
                NotificationManager.error('Failed to upload Event Image');
                return;
              }
              const { data: uploadCompleteData, isError: uploadCompleteError } =
                await eventImageUploadAndComplete(
                  spaceId,
                  fileId,
                  createdShareId
                );
              console.log({
                uploadCompleteData,
                uploadCompleteError
              });
              if (uploadCompleteError) {
                NotificationManager.error(
                  'Something went wrong while uploading brand'
                );
              } else {
                handleUpdateEntity(createdShareId, true);
              }
            }
          }
          handleUpdateEntity(createdShareId, true);
          NotificationManager.success(
            <IntlMessages id="share.sucess" />,
            <IntlMessages id="share.share-success" />
          );
        }
        setIsEditing(false);
      } else {
        NotificationManager.error(
          <IntlMessages id={isLimitError ? 'share.limit' : 'share.error'} />,
          <IntlMessages id="req.failed" />
        );
      }
      setIsSharing(false);
    } catch (error) {
      setIsSharing(false);
      console.log('erropr', error);
    }
  };

  const handleUpdate = async () => {
    try {
      console.log('details', details);
      console.log('sharingData', sharingData);
      setIsUpdating(true);
      const data = {
        Id: sharingData.id,
        PlayerId: selectedPlayer.id,
        EntityKind: entityKind,
        EntityId: entityId,
        ShareAlways: false,
        StartDateTime: 0,
        EndDateTime: 0,
        TimeZone: '',
        EventName: details.eventName,
        EventDescription: details.eventDesc,
        EventActionButton: details.actionButton,
        EventActionButtonLink: details.actionButtonLink
      };
      if (!selectedImg) {
        data.imageId = null;
        data.imageUrl = null;
      }
      if (!details.startTime || !details.endDate) {
        NotificationManager.error(
          <IntlMessages id="share.start-end" />,
          <IntlMessages id="req.failed" />
        );
        return;
      }
      data.StartDateTime = timeToUTCMillisConvert(
        details.startTime,
        details.tmzn
      );
      data.EndDateTime = timeToUTCMillisConvert(details.endDate, details.tmzn);
      data.TimeZone = details.tmzn;
      // if (data.StartDateTime >= data.EndDateTime) {
      //   NotificationManager.error(
      //     <IntlMessages id="share.end-great" />,
      //     <IntlMessages id="req.failed" />
      //   );
      //   return;
      // }
      const result = await updateShare(data, spaceId);
      if (result.isError) {
        NotificationManager.error(
          <IntlMessages id="share.error" />,
          <IntlMessages id="req.failed" />,
          3000,
          null,
          null,
          ''
        );
      } else {
        const createdShareId = result?.data?.id;
        if (createdShareId) {
          if (selectedImg) {
            const { data, isError } = await uploadEventImage(
              spaceId,
              createdShareId,
              [selectedImg.name]
            );
            console.log({
              data,
              isError
            });
            if (!isError && data) {
              const fileItems = [];
              fileItems.push(data.items[0]);
              const fileId = data?.items[0]?.fileName.split('/input/')[1];
              const fileSasObj = {
                items: fileItems,
                sasUrl: data?.sasUrl
              };
              const { isError: isUploadingError } = await uploadFileToBlob(
                selectedImg,
                () => {},
                () => {},
                fileSasObj
              );
              if (isUploadingError) {
                NotificationManager.error('Failed to upload Event Image');
                return;
              }
              const { data: uploadCompleteData, isError: uploadCompleteError } =
                await eventImageUploadAndComplete(
                  spaceId,
                  fileId,
                  createdShareId
                );
              console.log({
                uploadCompleteData,
                uploadCompleteError
              });
              if (uploadCompleteError) {
                NotificationManager.error(
                  'Something went wrong while uploading brand'
                );
              } else {
                handleUpdateEntity(createdShareId, true);
              }
            }
          }
          if (sharingData?.imageId && !isCustomEventImg) {
            try {
              const { data, isError } = await deleteEventImage(
                spaceId,
                sharingData.imageId,
                sharingData?.id
              );
              if (isError) {
                NotificationManager.error(
                  <IntlMessages id="event-img-del-error" />
                );
              }
            } catch (error) {
              console.error('error', error);
              NotificationManager.error(
                <IntlMessages id="event-img-del-error" />
              );
            }
          }

          // eslint-disable-next-line no-unreachable
          handleUpdateEntity(createdShareId, true);
          setIsEditing(false);
          NotificationManager.success(
            <IntlMessages id="share.sucess" />,
            <IntlMessages id="share.update-success" />
          );
        }
      }
      setIsUpdating(false);
    } catch (error) {
      setIsUpdating(false);
      console.log('error', error);
    }
  };

  const selectPlayerValidation = showSelectPlayer ? !selectedPlayer?.id : false;
  const actionButtonAndLink = (() => {
    if (details?.actionButton?.length > 0) {
      if (
        details.actionButtonError ||
        details?.actionButtonLink?.length === 0 ||
        details.actionButtonLinkError
      ) {
        return true;
      }
      return false;
    }
    return false;
  })();

  const disableButton =
    selectPlayerValidation ||
    isDeleting ||
    details.eventNameError ||
    details.eventDescError ||
    // details?.eventName?.length === 0 ||
    // details?.eventDesc?.length === 0 ||
    details.actionButtonError ||
    // details?.actionButton?.length === 0 ||
    // details?.actionButtonLink?.length === 0 ||
    details.actionButtonLinkError ||
    actionButtonAndLink ||
    details.timeError ||
    !details.endDate ||
    !details.tmzn;

  console.log({
    selectPlayerValidation,
    isDeleting,
    showSelectPlayer,
    actionButtonAndLink,
    timeError: details.timeError,
    endDate: !details.endDate,
    tmzn: !details.tmzn,
    disableButton
  });

  useEffect(() => {
    const timeError = (() => {
      if (details.endDate && details.startTime) {
        if (isMoreThanAYear(details.startTime, details.endDate)) {
          return 'End date cannot be more than a year';
        }
        if (details.startTime >= details.endDate) {
          return 'End date should be greater than start date!';
        }
      }
      return '';
    })();
    setDetails({
      ...details,
      timeError
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [details.startTime, details.endDate]);

  return (
    <Col className="p-0" aria-disabled={isUpdating || isSharing || isDeleting}>
      {isEditing && (
        <div className="d-flex align-items-center justify-content-end gap-1">
          <SwitchButton name="Share Now" onClick={handleShareNow} />
          {sharingData?.id && sharingData?.endDateTime && (
            <SwitchButton
              name="View Details"
              showIcon={false}
              onClick={() => setIsEditing(false)}
            />
          )}
        </div>
      )}
      <div className="EventRow">
        <EventDetails
          details={details}
          setDetails={setDetails}
          MediaType={MediaType}
          isYtVimeoVideo={isYtVimeoVideo}
          spaceId={spaceId}
          selectedPlayer={selectedPlayer}
          setSelectedPlayer={setSelectedPlayer}
          showSelectPlayer={showSelectPlayer}
        />
        <div className="EventRowdetails p-2">
          <EventDuration details={details} setDetails={setDetails} />
          <EventImage
            setSelectedImg={setSelectedImg}
            selectedImg={selectedImg}
            spaceId={spaceId}
            sharingData={sharingData}
            setIsCustomEventImg={setIsCustomEventImg}
            isCustomEventImg={isCustomEventImg}
            isDeleting={isDeleting}
            setSharingData={setSharingData}
            setIsDeleting={setIsDeleting}
          />
        </div>
      </div>
      <div className="d-flex align-items-center justify-content-center my-4">
        <LoadingButton
          text={isShared ? 'Update' : 'Share'}
          onClick={() => {
            if (isShared) {
              handleUpdate();
            } else {
              handleShare();
            }
          }}
          loading={isUpdating || isSharing}
          disabled={disableButton || isUpdating || isSharing}
        />
      </div>
    </Col>
  );
};

const EventDetails = ({
  details,
  setDetails,
  MediaType,
  isYtVimeoVideo,
  spaceId,
  selectedPlayer,
  setSelectedPlayer,
  showSelectPlayer
}) => {
  const {
    eventNameError,
    eventDescError,
    actionButtonError,
    actionButtonLinkError
  } = details;
  const [players, setPlayers] = useState([]);
  const reactQuillRef = useRef(null);

  const limit = 2000;

  return (
    <Form className="EventRowdetails p-2">
      <h5 className="font-bolder mb-4">Event Details</h5>
      <FormGroup className="form-group has-float-label mt-3 mb-4">
        <Label for="Industry">
          <IntlMessages id="Name" />
          {/* <span style={{ color: '#922c88' }}> *</span> */}
        </Label>
        <Input
          type="text"
          placeholder="Enter Event Name"
          value={details?.eventName}
          onChange={(e) => {
            const text = e.target.value;
            const tooLarge = text.length > 100;
            // const validatedEventName = (() => {
            //   // if (!text) {
            //   //   return 'Event name cannot be empty';
            //   // }
            //   if (text) {
            //     if (text.length < 3) {
            //       return 'Event name is too small';
            //     }
            //     if (tooLarge) {
            //       return 'Event name is too large';
            //     }
            //   }
            //   return '';
            // })();
            if (tooLarge) {
              NotificationManager.warning('Event name is too large');
            }
            if (!tooLarge) {
              setDetails({
                ...details,
                eventName: text
                // eventNameError: validatedEventName
              });
            }
          }}
        />
        {eventNameError && (
          <div className="invalid-feedback d-block">{eventNameError}</div>
        )}
      </FormGroup>
      <FormGroup className="form-group has-float-label mt-3 mb-4">
        <Label for="Industry">
          <IntlMessages id="Add Description" />
        </Label>
        <ReactQuill
          className={`react-quill w-full-imp `}
          theme="bubble"
          style={{ border: '1px' }}
          value={details?.eventDesc ?? ''}
          ref={reactQuillRef}
          onChange={(value) => {
            const quill = reactQuillRef?.current?.getEditor();
            if (quill) {
              quill.on('text-change', () => {
                const currentLength = quill.getLength();
                console.log('currentLength', currentLength);
                if (currentLength > limit) {
                  quill.deleteText(limit, currentLength);
                }
              });
              if (quill.getLength() > limit) {
                setDetails({
                  ...details,
                  eventDesc: value,
                  eventDescError: `Please enter no more than ${limit} characters`
                });
              } else {
                setDetails({
                  ...details,
                  eventDesc: value,
                  eventDescError: ''
                });
              }
              // setDetails({
              //   ...details,
              //   eventDesc: value,
              //   eventDescError: ''
              // });
            }
          }}
        />
        {eventDescError && (
          <div className="invalid-feedback d-block">{eventDescError}</div>
        )}
      </FormGroup>
      <FormGroup className="form-group has-float-label mt-3 mb-4">
        <Label for="Industry">
          <IntlMessages id="Action Button Text" />
          {/* <span style={{ color: '#922c88' }}> *</span> */}
        </Label>
        <Input
          type="text"
          placeholder="Enter Action Button Text"
          value={details?.actionButton}
          onChange={(e) => {
            const text = e.target.value;
            const tooLarge = text.length > 20;
            // const validatedActionButton = (() => {
            //   if (text) {
            //     if (text.length < 3) {
            //       return 'Action Button text is too small';
            //     }
            //     if (text.length > 20) {
            //       return 'Action Button text is too large';
            //     }
            //   }
            //   return '';
            // })();
            if (tooLarge) {
              NotificationManager.warning('Action Button text is too large');
            }
            if (!tooLarge) {
              setDetails({
                ...details,
                actionButton: text
                // actionButtonError: validatedActionButton
              });
            }
          }}
        />
        {actionButtonError && (
          <div className="invalid-feedback d-block">{actionButtonError}</div>
        )}
      </FormGroup>
      {details?.actionButton && (
        <FormGroup className="form-group has-float-label mt-3 mb-4">
          <Label for="Industry">
            <IntlMessages id="Action Button link" />
            <span style={{ color: '#922c88' }}> *</span>
          </Label>
          <Input
            type="text"
            placeholder="Enter Action Button Link"
            value={details?.actionButtonLink}
            onChange={(e) => {
              const text = e.target.value;
              const validatedActionButton = validateUrl(text);
              setDetails({
                ...details,
                actionButtonLink: text,
                actionButtonLinkError: validatedActionButton
              });
            }}
          />
          {actionButtonLinkError && (
            <div className="invalid-feedback d-block">
              {actionButtonLinkError}
            </div>
          )}
        </FormGroup>
      )}
      {showSelectPlayer && (
        <SelectPlayer
          spaceId={spaceId}
          players={players}
          selectedPlayer={selectedPlayer}
          setPlayers={setPlayers}
          infoAbove={false}
          className="mt-4 mb-5"
          handleItemClick={(playerInfo) => {
            setSelectedPlayer(playerInfo);
          }}
        />
      )}
    </Form>
  );
};

const EventDuration = ({ details, setDetails }) => {
  return (
    <Form>
      <h5 className="font-bolder mb-4">Event Duration</h5>
      <FormGroup className="has-float-label">
        <Label>
          Time Zone<span style={{ color: '#922c88' }}> *</span>
        </Label>
        <Input
          id="timeZone"
          name="timeZone"
          type="select"
          defaultValue={details?.tmzn}
          onChange={(e) => {
            console.log('e.target.value', e.target.value);
            setDetails({
              ...details,
              tmzn: e.target.value
            });
          }}
        >
          {details.tmzn && (
            <option
              selected={timeZone.find((f) => f.utc.includes(details?.tmzn))}
            >
              {details.tmzn}
            </option>
          )}
          {timeZone.map((tz) => (
            <option
              key={tz.value}
              value={tz.utc[0]}
              selected={tz.utc.includes(details?.tmzn)}
            >
              {tz.text}
            </option>
          ))}
        </Input>
      </FormGroup>
      <FormGroup className="has-float-label ">
        <Label>
          Start Date & time
          <span style={{ color: '#922c88' }}> *</span>
        </Label>
        <Input
          style={{ flex: 1.5 }}
          min={getTimeStamp()}
          max={getTimeStamp('max')}
          type="datetime-local"
          defaultValue={details?.startTime}
          onChange={(e) => {
            setDetails({
              ...details,
              startTime: e.target.value
            });
          }}
        />
      </FormGroup>
      <FormGroup className="has-float-label">
        <Label>
          End Date & time<span style={{ color: '#922c88' }}> *</span>
        </Label>
        <Input
          style={{ flex: 1.5 }}
          min={details?.startTime}
          max={getTimeStamp('max')}
          defaultValue={details?.endDate}
          type="datetime-local"
          onChange={(e) => {
            const selectedDateTime = e.target.value;
            setDetails({
              ...details,
              endDate: selectedDateTime
            });
          }}
        />
        {details.timeError && (
          <div className="invalid-feedback d-block">{details.timeError}</div>
        )}
      </FormGroup>
    </Form>
  );
};

const EventImage = ({
  setSelectedImg,
  selectedImg,
  isCustomEventImg,
  setIsCustomEventImg,
  sharingData,
  setIsDeleting,
  spaceId,
  setSharingData
}) => {
  console.log('sharingData', sharingData);
  const thumbnailRef = useRef();
  const handleChoose = () => {
    thumbnailRef.current.click();
  };

  const handleChangeLogo = (e) => {
    const imageFile = e.target.files[0];
    setSelectedImg(imageFile);
    setIsCustomEventImg(true);
  };

  const handleDeleteImage = async (imgId) => {
    try {
      setIsDeleting(true);
      const { data, isError } = await deleteEventImage(
        spaceId,
        imgId,
        sharingData?.id
      );
      if (!isError) {
        setSharingData({ ...sharingData, imageUrl: '', imageId: '' });
      }
      console.log({
        data,
        isError
      });
      setIsDeleting(false);
    } catch (error) {
      setIsDeleting(false);
    }
  };

  return (
    <div className="w-full flex-column mt-3">
      <div className="d-flex flex-row align-items-center justify-content-start">
        <h5 className="font-bolder mr-3">Event Image</h5>
        <span
          aria-disabled={!!sharingData?.imageUrl}
          onClick={handleChoose}
          className="p-1 dotted-border px-3 text-primary c-pointer border-10"
          style={{ fontSize: 13 }}
        >
          Add Image
        </span>
        <input
          type="file"
          className="dashboard-file-input"
          accept="image/jpeg, image/jpg, image/png, image/gif"
          ref={thumbnailRef}
          onChange={handleChangeLogo}
        />
      </div>
      <div className="d-flex flex-row justify-content-start gap-1 mt-3">
        <SelectedImg
          active={!isCustomEventImg}
          onClick={() => setIsCustomEventImg(false)}
          src="https://play.innerloop.stream/playersvc/ins-share/cu-LKT1l6SA9b-x9ItTVHHcy"
          showCloseIcon={false}
        />
        {sharingData?.imageUrl && (
          <SelectedImg
            src={sharingData?.imageUrl}
            showCloseIcon
            active={isCustomEventImg}
            onCloseClick={() => handleDeleteImage(sharingData?.imageId)}
          />
        )}
        {selectedImg && !sharingData?.imageUrl && (
          <SelectedImg
            active={isCustomEventImg}
            onClick={() => setIsCustomEventImg(true)}
            src={URL.createObjectURL(selectedImg)}
            showCloseIcon
            onCloseClick={() => setSelectedImg(null)}
          />
        )}
      </div>
      {(sharingData?.imageUrl || selectedImg) && (
        <p style={{ fontSize: 12 }} className="text-primary mb-0">
          <i className="simple-icon-info mr-1" />
          <span>
            To add a new image you need to delete the existing image and add a
            new one.
          </span>
        </p>
      )}
    </div>
  );
};
