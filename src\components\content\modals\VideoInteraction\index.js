/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-vars */
import React, { useEffect, useMemo, useState } from 'react';
import InfoMessage from 'components/infoMessage';
import { Button, Modal, ModalBody } from 'reactstrap';
import { VIDEO_INTERACTION_TABS } from 'constants/VideoInteraction';
import InteractionDetail from './components/InteractionDetail';
import InteractionList from './InteractionList';
import { getPlayerPauseList } from 'helpers/Utils';
import Analytics from './Analytics';
import PollResult from './Analytics/SelectedSessionDetails/PollResult';

const tabs = [
  {
    id: VIDEO_INTERACTION_TABS.PARTICULAR,
    name: 'At a Particular Timestamp'
  },
  {
    id: VIDEO_INTERACTION_TABS.PLAYER_PAUSE,
    name: 'Player Pause interaction'
  }
];

const VideoInteractionModal = ({
  content,
  getContent,
  handlePlayerChange,
  modalOpen,
  setModalOpen
}) => {
  const [activeTab, setActiveTab] = useState(VIDEO_INTERACTION_TABS.PARTICULAR);
  const [isCreateInteraction, setIsCreateInteraction] = useState(false);
  const [updateInteractionId, setUpdateInteractionId] = useState(null);
  const [list, setList] = useState([]);
  const [skip, setSkip] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedInteractions, setSelectedInteractions] = useState(null);
  const [selectedPollId, setSelectedPollId] = useState(null);

  console.log({
    selectedPollId,
    selectedInteractions
  });

  const isParticular = useMemo(() => {
    return activeTab === VIDEO_INTERACTION_TABS.PARTICULAR;
  }, [activeTab]);

  useEffect(() => {
    setIsCreateInteraction(false);
  }, [activeTab]);

  const disableAddInteractionButton = (() => {
    if (isParticular) {
      return false;
    }
    return getPlayerPauseList(list).length === 1 || isLoading;
  })();

  const handleToggle = () => {
    setModalOpen((prev) => !prev);
  };

  return (
    <Modal
      isOpen={modalOpen}
      toggle={handleToggle}
      contentClassName="border-radius-10"
      centered
      style={{
        minWidth: '70%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 20
      }}
      backdrop="static"
    >
      <ModalBody
        className="m-3 border-radius-10 d-flex align-items-center flex-column"
        style={{
          border: '1px solid #992288'
        }}
      >
        <Header toggle={handleToggle} />
        {selectedPollId ? (
          <PollResult
            setSelectedPollId={setSelectedPollId}
            selectedPollId={selectedPollId}
            content={content}
          />
        ) : selectedInteractions ? (
          <Analytics
            selectedInteractions={selectedInteractions}
            setSelectedInteractions={setSelectedInteractions}
          />
        ) : (
          <div className="vidInteraction">
            {!isCreateInteraction && !updateInteractionId && (
              <>
                <div className="vi-tabs">
                  {tabs.map((t) => (
                    <div
                      key={t.id}
                      className={`vi-tab ${
                        t.id === activeTab ? 'active-bg' : ''
                      }`}
                      onClick={() => {
                        setActiveTab(t.id);
                        setIsCreateInteraction(false);
                      }}
                    >
                      <TabIcon iconType={t.id} isActive={t.id === activeTab} />
                      &nbsp;
                      {t.name}
                    </div>
                  ))}
                </div>
                <div className="info-btns">
                  <InfoMessage
                    textAlign="left"
                    style={{ fontSize: 14, fontWeight: '600' }}
                    message={
                      isParticular
                        ? 'The selected interaction will be displayed  between the Start time and End time specified by you '
                        : 'The selected interaction will be displayed when the user pauses the video.Only 1 interaction can be added for the Player Pause Interaction.'
                    }
                  />
                  <Button
                    style={{ textWrap: 'nowrap' }}
                    color="primary"
                    disabled={disableAddInteractionButton}
                    onClick={() => {
                      setIsCreateInteraction(true);
                    }}
                  >
                    Add Interaction
                  </Button>
                </div>
              </>
            )}
            {isCreateInteraction || updateInteractionId ? (
              <InteractionDetail
                isCreateInteraction={isCreateInteraction}
                content={content}
                updateInteractionId={updateInteractionId}
                setUpdateInteractionId={setUpdateInteractionId}
                showAtTimecodeList={
                  list
                    ?.filter((f) => (f) => f.showAtTimecode.length > 0)
                    .filter((f) => f.isEnabled) ?? []
                }
                toggle={() => {
                  setIsCreateInteraction(false);
                  if (updateInteractionId) {
                    setUpdateInteractionId(null);
                  }
                }}
                isParticular={isParticular}
                handlePlayerChange={handlePlayerChange}
              />
            ) : (
              <>
                <InteractionList
                  content={content}
                  isParticular={isParticular}
                  setUpdateInteractionId={setUpdateInteractionId}
                  getContent={getContent}
                  handlePlayerChange={handlePlayerChange}
                  list={list}
                  setList={setList}
                  skip={skip}
                  setSkip={setSkip}
                  hasMore={hasMore}
                  setHasMore={setHasMore}
                  isLoading={isLoading}
                  setIsLoading={setIsLoading}
                  setSelectedInteractions={setSelectedInteractions}
                  setSelectedPollId={setSelectedPollId}
                />
              </>
            )}
          </div>
        )}
      </ModalBody>
    </Modal>
  );
};

const Header = ({ toggle }) => {
  return (
    <div
      className="d-flex align-items-start justify-content-between w-full p-0 mb-4"
      style={{ borderBottom: '1px solid rgba(146, 44, 136, 1)' }}
    >
      <div className="d-flex align-items-center w-95 my-2">
        <h2
          style={{
            fontSize: '1.2rem'
            // borderBottom: '1px solid rgba(146, 44, 136, 1)'
          }}
          className="mb-0 font-bolder"
        >
          Video Interactions
        </h2>
      </div>
      <span
        onClick={toggle}
        className="w-5 d-flex flex-row align-items-center justify-content-end"
      >
        <i className="simple-icon-close close-btn" />
      </span>
    </div>
  );
};

const TabIcon = ({
  iconType = VIDEO_INTERACTION_TABS.PARTICULAR,
  isActive
}) => {
  const activeColor = isActive ? '#992288' : '#000';
  const size = 20;

  if (iconType === VIDEO_INTERACTION_TABS.PARTICULAR) {
    return (
      <svg
        width={size}
        height={size}
        viewBox="0 0 23 23"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M1.625 11.5C1.625 12.8296 1.88689 14.1462 2.39572 15.3747C2.90455 16.6031 3.65035 17.7193 4.59054 18.6595C5.53074 19.5996 6.64691 20.3455 7.87533 20.8543C9.10375 21.3631 10.4204 21.625 11.75 21.625C13.0796 21.625 14.3962 21.3631 15.6247 20.8543C16.8531 20.3455 17.9693 19.5996 18.9095 18.6595C19.8496 17.7193 20.5955 16.6031 21.1043 15.3747C21.6131 14.1462 21.875 12.8296 21.875 11.5C21.875 8.81468 20.8083 6.23935 18.9095 4.34054C17.0106 2.44174 14.4353 1.375 11.75 1.375C9.06468 1.375 6.48935 2.44174 4.59054 4.34054C2.69174 6.23935 1.625 8.81468 1.625 11.5Z"
          stroke={activeColor}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M11.75 5.875V11.5L15.125 14.875"
          stroke={activeColor}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    );
  }
  return (
    <svg
      width="15"
      height="15"
      viewBox="0 0 13 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.24984 1.08331H2.08317C1.77375 1.08331 1.47701 1.20623 1.25821 1.42502C1.03942 1.64381 0.916504 1.94056 0.916504 2.24998V13.9166C0.916504 14.2261 1.03942 14.5228 1.25821 14.7416C1.47701 14.9604 1.77375 15.0833 2.08317 15.0833H3.24984C3.55926 15.0833 3.856 14.9604 4.07479 14.7416C4.29359 14.5228 4.4165 14.2261 4.4165 13.9166V2.24998C4.4165 1.94056 4.29359 1.64381 4.07479 1.42502C3.856 1.20623 3.55926 1.08331 3.24984 1.08331ZM10.8332 1.08331H9.6665C9.35708 1.08331 9.06034 1.20623 8.84155 1.42502C8.62275 1.64381 8.49984 1.94056 8.49984 2.24998V13.9166C8.49984 14.2261 8.62275 14.5228 8.84155 14.7416C9.06034 14.9604 9.35708 15.0833 9.6665 15.0833H10.8332C11.1426 15.0833 11.4393 14.9604 11.6581 14.7416C11.8769 14.5228 11.9998 14.2261 11.9998 13.9166V2.24998C11.9998 1.94056 11.8769 1.64381 11.6581 1.42502C11.4393 1.20623 11.1426 1.08331 10.8332 1.08331Z"
        stroke={activeColor}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default VideoInteractionModal;
