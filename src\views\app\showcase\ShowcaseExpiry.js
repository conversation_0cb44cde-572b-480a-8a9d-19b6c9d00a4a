import { formatDate, formatTime } from 'helpers/Utils';
import React from 'react';

function ShowcaseExpiry({ date, isCol = false, margin = 'mt-1', style = {} }) {
  const hide = date === 1;
  date = hide ? new Date() : new Date(date);
  return (
    <span style={style}>
      <span
        style={{ opacity: hide ? 0 : 1 }}
        className={`date d-flex ${margin} align-items-center w-full flex-${
          isCol ? 'col' : 'row justify-content-between'
        }`}
      >
        <span className="mr-1">
          <i className="iconsminds-sand-watch-2 mr-1" />
          {formatDate(date)}
        </span>{' '}
        <span className={!isCol && 'ml-1'}>{formatTime(date)}</span>
      </span>
    </span>
  );
}

export default ShowcaseExpiry;
