/* eslint-disable no-unused-vars */
import { NotificationManager } from 'components/common/react-notifications';
import PaymentInfo from 'components/content/modals/ShowcaseModal/PaymentInfo';
import FormInput from 'components/input/FormInput';
import React, { useMemo, useRef } from 'react';

const ProductDetails = ({
  originalPrice,
  setOriginalPrice,
  sellingPrice,
  setSellingPrice,
  currency,
  setCurrency,
  setImg,
  img,
  title,
  setTitle,
  imgSrc,
  handleDeleteImage = () => {}
}) => {
  const imageRef = useRef(null);

  const handleSelectImage = (e) => {
    const imageFile = e.target.files[0];
    setImg(imageFile);
  };

  return (
    <div className="productDetails">
      <input
        type="file"
        className="dashboard-file-input"
        accept="image/jpeg, image/jpg, image/png, image/gif"
        ref={imageRef}
        onChange={handleSelectImage}
      />
      <div className="PD-left">
        {imgSrc ? (
          <div className="PDCard">
            <span
              className="del"
              onClick={() => {
                handleDeleteImage();
              }}
            >
              <i className="simple-icon-close" />
            </span>
            <img height="100%" alt="selected img" width="100%" src={imgSrc} />
          </div>
        ) : (
          <div
            className="PDCard"
            onClick={() => {
              imageRef.current.click();
            }}
          >
            <i className="simple-icon-plus" />
            <p>Add Image</p>
          </div>
        )}
      </div>
      <div className="PD-right">
        <FormInput
          label="Product Title"
          isImportant
          placeholder="Add Title"
          value={title}
          onChangeText={(e) => {
            const trimmedText = e.trim();
            if (trimmedText.length > 30) {
              NotificationManager.warning('Product title is too long');
            } else {
              setTitle(trimmedText);
            }
          }}
        />
        <PaymentInfo
          originalPrice={originalPrice}
          setOriginalPrice={setOriginalPrice}
          sellingPrice={sellingPrice}
          setSellingPrice={setSellingPrice}
          currency={currency}
          setCurrency={setCurrency}
        />
      </div>
    </div>
  );
};

export default ProductDetails;
