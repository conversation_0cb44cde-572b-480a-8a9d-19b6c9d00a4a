/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-vars */
import { NotificationManager } from 'components/common/react-notifications';
import Copy from 'components/copy';
import {
  getAllPlayers,
  isUserNameAvailable,
  updateTenantProfile
} from 'functions/api';
import IntlMessages from 'helpers/IntlMessages';
import {
  getCurrentColor,
  getCurrentUserDetails,
  getDefaultSpaceId
} from 'helpers/Utils';
import React, { useEffect, useRef, useState } from 'react';
import ReactQuill from 'react-quill';
import TagsInput from 'react-tagsinput';
import {
  Button,
  FormGroup,
  Input,
  Label,
  Modal,
  ModalBody,
  Row,
  UncontrolledDropdown,
  DropdownItem,
  DropdownMenu,
  DropdownToggle
} from 'reactstrap';
import InfiniteScroll from 'react-infinite-scroll-component';
import CreatePlayerButton from 'components/buttons/CreatePlayerButton';
import InfoMessage from 'components/infoMessage';
import { TagInput } from 'components/input';

const descriptionLimit = 2000;

export const validateAlias = (alias) => {
  if (!alias) {
    return 'Alias cannot be empty';
  }
  if (alias.length <= 4) {
    return 'Alias is too short!';
  }
  if (alias.length > 30) {
    return 'Alias is too long!';
  }
  const regex = /^[a-zA-Z][a-zA-Z0-9_]*$/;
  if (!regex.test(alias)) {
    return 'Invalid alias. Please use only underscores, and alphanumeric characters.';
  }
  return '';
};

const getProfileTitle = (title) => {
  if (title) {
    return title;
  }
  const { fullName } = getCurrentUserDetails();
  return `${fullName}'s Showcase Home Page`;
};

function EditOwnerModal({
  isOpen,
  toggle,
  tenantId,
  ownerData,
  setOwnerData,
  history,
  setTenantDetails
}) {
  const [description, setDescription] = useState(
    ownerData?.profileDescription ?? ''
  );
  const spaceId = getDefaultSpaceId();
  const [tag, setTag] = useState('');
  const [alias, setAlias] = useState(ownerData?.username ?? '');
  const [tags, setTags] = useState(ownerData?.tags ?? []);
  const [title, setTitle] = useState(getProfileTitle(ownerData?.profileTitle));
  const [playerId, setPlayerId] = useState(ownerData?.playerId ?? null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [disabled, setDisabled] = useState(true);
  const [players, setPlayers] = useState([]);
  const [hasmore, setHasmore] = useState(false);
  const [maxResultCount] = useState(5);
  const [skipCount, setSkipCount] = useState(0);
  const currentColor = getCurrentColor().includes('dark');
  const [isPlayerLoading, setIsPlayerLoading] = useState(false);
  const [descError, setDescError] = useState(false);
  const [aliasError, setAliasError] = useState('');
  const [selectedPlayerName, setSelectedPlayerName] = useState(
    ownerData?.basicPlayerInfo?.name ?? null
  );
  const reactQuillRef = useRef(null);

  const handleChangeTags = (all) => {
    if (all && all.length > 5) {
      NotificationManager.warning(<IntlMessages id="max-five" />);
      return;
    }
    setTags(all);
  };

  const handleChangeSingleTag = (singleTag) => {
    if (singleTag.length > 20) {
      NotificationManager.warning(<IntlMessages id="max-twenty" />);
      return;
    }
    setTag(singleTag);
  };

  const handleToggle = () => {
    setIsUpdating(false);
    toggle();
  };

  const handleUpdate = async () => {
    const handleTenantInfo = async (updateAlias) => {
      try {
        const payload = {
          id: tenantId,
          username: alias,
          profileDescription: description,
          profileTitle: title,
          tags,
          playerId
        };
        if (!updateAlias) {
          delete payload.username;
        }
        const { data, isError } = await updateTenantProfile(payload);
        if (!isError && data) {
          NotificationManager.success('Updated Successfully');
          const localTenantDetails = JSON.parse(
            localStorage.getItem('tenantDetails')
          );
          const newTenantDetails = {
            ...localTenantDetails.data,
            selectedTenant: {
              ...localTenantDetails.data.selectedTenant,
              username: alias
            }
          };
          setTenantDetails(newTenantDetails);
          localStorage.setItem(
            'tenantDetails',
            JSON.stringify({
              data: newTenantDetails,
              isError: false
            })
          );
          setOwnerData({
            ...ownerData,
            ...payload
          });
          if (updateAlias) {
            history.push(`/app/showcase/@${alias}`);
          }
          handleToggle();
        } else {
          NotificationManager.error('Something went wrong');
          setIsUpdating(false);
        }
      } catch (error) {
        NotificationManager.error('Something went wrong');
        console.error(error);
        setIsUpdating(false);
      }
    };
    try {
      setIsUpdating(true);
      if (ownerData?.username !== alias) {
        const { data, isError } = await isUserNameAvailable(alias);
        if (!isError && data) {
          if (data.isAvailable) {
            await handleTenantInfo(true);
          } else {
            NotificationManager.error(
              'Username is already in used.Please enter another Username'
            );
          }
        } else {
          NotificationManager.error('Something went wrong');
          handleToggle();
        }
      } else {
        await handleTenantInfo();
      }
      setIsUpdating(false);
    } catch (error) {
      NotificationManager.error('Something went wrong');
      console.error(error);
      setIsUpdating(false);
    }
  };

  const getTenantPlayers = async () => {
    try {
      skipCount === 0 && setIsPlayerLoading(true);
      const { data } = await getAllPlayers(maxResultCount, skipCount);
      const playersList = data?.data?.items;
      if (playersList && !data.isError) {
        setPlayers([...players, ...playersList]);
        setSkipCount(skipCount + maxResultCount);
        if (playersList.length < maxResultCount) {
          setHasmore(false);
        } else {
          setHasmore(true);
        }
      }
      setIsPlayerLoading(false);
    } catch (error) {
      console.log('error');
      setIsPlayerLoading(false);
    }
  };

  useEffect(() => {
    getTenantPlayers();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (
      ownerData?.username !== alias ||
      ownerData?.tags !== tags ||
      ownerData?.profileTitle !== title ||
      ownerData?.profileDescription !== description ||
      ownerData?.playerId !== playerId
    ) {
      setDisabled(false);
    } else {
      setDisabled(true);
    }
    if (alias) {
      const err = validateAlias(alias);
      setAliasError(err);
    }
  }, [ownerData, title, alias, tags, description, playerId]);

  useEffect(() => {
    if (descError) {
      NotificationManager.warning('Description is too large');
    }
  }, [descError]);

  const isProfileDataEmpty =
    title.length === 0 ||
    // tags.length === 0 ||
    alias.length === 0 ||
    // description.length === 0 ||
    !playerId;

  return (
    <Modal
      isOpen={isOpen}
      toggle={handleToggle}
      centered
      keyboard
      style={{ minWidth: '60%' }}
      backdrop="static"
    >
      <ModalBody className="dotted-border m-4 py-4 px-5">
        <Row className="px-2">
          <p
            className="ml-1 font-weight-lightbold"
            style={{
              fontSize: '20px'
            }}
          >
            <IntlMessages id="sh-details" />
          </p>
          <Button
            color="white"
            type="button"
            className="d-flex justify-content-end p-3"
            onClick={handleToggle}
          >
            <i className="simple-icon-close close-btn" />
          </Button>
        </Row>
        {/* TITLE  */}
        <div
          className="d-flex flex-column gap-2  mt-4 mb-4"
          style={{ width: '55%' }}
        >
          <FormGroup className="form-group has-float-label mb-0">
            <Label for="Industry">
              <IntlMessages id="sh-title" />
              <span style={{ color: '#922c88' }}> *</span>
            </Label>
            <Input
              type="text"
              placeholder="Add title"
              value={title}
              onChange={(e) => {
                const val = e.target.value;
                const tooLarge = val.length > 100;
                if (tooLarge) {
                  NotificationManager.error('Too large title');
                } else {
                  setTitle(val);
                }
              }}
            />
          </FormGroup>
          <InfoMessage message="sh-hp-title-max" />
        </div>
        {/* ALIAS  */}
        <div
          className="d-flex flex-column gap-2 position-relative  mt-3 mb-4"
          style={{ width: '55%' }}
        >
          <FormGroup className="form-group has-float-label mb-0">
            <Label for="Industry">
              <IntlMessages id="sh-alias" />
              <span style={{ color: '#922c88' }}> *</span>
            </Label>
            <Input
              type="text"
              placeholder="Add Alias"
              value={alias}
              onChange={(e) => {
                const aliasValue = e.target.value;
                if (aliasValue.length > 30) {
                  NotificationManager.warning('Alias is too long!');
                } else {
                  setAlias(aliasValue);
                }
              }}
            />
            {aliasError && alias && (
              <div className="invalid-feedback d-block">{aliasError}</div>
            )}
          </FormGroup>
          {alias && (
            <span
              className="position-absolute"
              style={{ right: '-30px', top: '25%' }}
            >
              <Copy
                height={20}
                width={20}
                copyText={`${window.location.origin}/app/showcase/@${alias}`}
              />
            </span>
          )}
          <InfoMessage message="sh-hp-alias-max" />
        </div>
        {/* SELECT PLAYER  */}
        <div className="d-flex flex-column  gap-2 position-relative w-full my-3">
          <div className="d-flex flex-row">
            <div style={{ width: '55%' }}>
              <FormGroup className="has-float-label w-full position-relative mb-1">
                <Label>
                  <IntlMessages id="sh-player" />
                  <span style={{ color: '#922c88' }}> *</span>
                </Label>
                <UncontrolledDropdown
                  className="badge-outline-light"
                  color="primary"
                >
                  <DropdownToggle
                    caret
                    className="btn-sm w-full"
                    style={{
                      color: currentColor ? 'silver' : '#000',
                      backgroundColor: 'transparent',
                      border: 'none'
                    }}
                  >
                    <span>{selectedPlayerName ?? 'Select Player'}</span>
                  </DropdownToggle>
                  <DropdownMenu
                    center
                    className="dropmenuheightcont position-absolute"
                  >
                    <InfiniteScroll
                      dataLength={players.length}
                      next={() => {
                        console.log('calling next');
                        getTenantPlayers();
                      }}
                      hasMore={hasmore}
                      height={players.length > 0 ? 110 : 50}
                      loader={
                        <p
                          style={{
                            textAlign: 'center',
                            fontSize: '14px',
                            padding: '10px 0px',
                            margin: '0',
                            color: '#94308A'
                          }}
                        >
                          Loading...
                        </p>
                      }
                      endMessage={
                        <p
                          style={{
                            textAlign: 'center',
                            fontSize: '14px',
                            padding: '10px 0px',
                            margin: '0',
                            color: '#94308A'
                          }}
                        >
                          Yay! You have seen it all
                        </p>
                      }
                    >
                      {isPlayerLoading ? (
                        <DropdownItem className="text-center">
                          loading....
                        </DropdownItem>
                      ) : players.length === 0 ? (
                        <div className="d-flex flex-column align-items-center justify-content-center text-center">
                          <CreatePlayerButton
                            onClick={() =>
                              history.push(
                                `/app/spaces/space/${spaceId}/media-player/create-player`
                              )
                            }
                          />
                        </div>
                      ) : (
                        players.map((dropdown) => {
                          return (
                            <DropdownItem
                              key={dropdown.id}
                              onClick={() => {
                                setSelectedPlayerName(dropdown.name);
                                setPlayerId(dropdown.id);
                              }}
                            >
                              <IntlMessages id={dropdown.name} />
                            </DropdownItem>
                          );
                        })
                      )}
                    </InfiniteScroll>
                  </DropdownMenu>
                </UncontrolledDropdown>
              </FormGroup>
            </div>
            {ownerData?.basicPlayerInfo?.name === selectedPlayerName && (
              <span
                style={{
                  fontSize: 14,
                  color: '#992288',
                  width: 'fit-content'
                }}
                className="px-2 py-1 border-10 c-pointer"
                onClick={() => {
                  history.push(
                    `/app/spaces/space/${ownerData?.basicPlayerInfo?.spaceId}/media-player/${ownerData?.basicPlayerInfo?.id}/edit`
                  );
                }}
              >
                <i className="iconsminds-play-music mr-1" />
                View Player
              </span>
            )}
          </div>
          <p className="m-0 text-primary" style={{ fontSize: 12 }}>
            <i className="simple-icon-info mr-2" />
            <IntlMessages id="sh-player-info" />
          </p>
        </div>
        {/* TAGS  */}
        <div className="mb-5">
          <TagInput
            value={tags ?? []}
            inputValue={tag}
            onChange={handleChangeTags}
            onChangeInput={handleChangeSingleTag}
          />
          <InfoMessage message="sh-hp-tags-max" />
        </div>
        {/* DESCRIPTION  */}
        <FormGroup className="form-group has-float-label mb-0 w-full">
          <Label for="Industry">
            <IntlMessages id="sh-description" />
            {/* <span style={{ color: '#922c88' }}> *</span> */}
          </Label>
          <ReactQuill
            className={`react-quill w-full-imp`}
            theme="bubble"
            ref={reactQuillRef}
            value={description}
            onChange={(value) => {
              // setDescription(value);
              const quill = reactQuillRef?.current?.getEditor();
              if (quill) {
                quill.on('text-change', () => {
                  const currentLength = quill.getLength();
                  const tooLarge = currentLength > descriptionLimit;
                  if (tooLarge) {
                    quill.deleteText(descriptionLimit, currentLength);
                  }
                });
                const currentLength = quill.getLength();
                const tooLarge = currentLength > descriptionLimit;
                setDescError(tooLarge);
                if (tooLarge) {
                  quill.deleteText(descriptionLimit, currentLength);
                } else {
                  setDescription(value);
                }
              }
            }}
          />
          <InfoMessage message="sh-hp-desc-max" />
        </FormGroup>
        {/* SAVE  */}
        <div className="centered mt-4">
          <Button
            color="primary"
            size="md"
            onClick={handleUpdate}
            style={{ alignSelf: 'center' }}
            className={`btn-shadow auth-btn ${
              isUpdating ? 'btn-multiple-state show-spinner' : ''
            }`}
            disabled={
              isUpdating || disabled || isProfileDataEmpty || aliasError
            }
          >
            <span className="spinner d-inline-block">
              <span className="bounce1" />
              <span className="bounce2" />
              <span className="bounce3" />
            </span>
            <span className="label">
              <IntlMessages id="sm.save" />
            </span>
          </Button>
        </div>
      </ModalBody>
    </Modal>
  );
}

export default EditOwnerModal;
