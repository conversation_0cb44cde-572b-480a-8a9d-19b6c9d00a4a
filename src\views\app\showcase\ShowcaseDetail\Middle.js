/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-vars */
import MainLoader from 'components/loaders/MainLoader';
import IntlMessages from 'helpers/IntlMessages';
import React, { useEffect, useRef, useState } from 'react';
import LockedContent from './LockedContent';

const Middle = ({
  isStory,
  isIframeLoading,
  token,
  isImage,
  preview,
  setIsIframeLoading,
  data,
  setPreview,
  getIframeSource,
  tokenError,
  windowHeight,
  windowWidth,
  lockedContent
}) => {
  const myDivRef = useRef(null);
  const [height, setHeight] = useState('auto');
  useEffect(() => {
    const newHeight = myDivRef?.current?.offsetHeight;
    const refHeight = newHeight ? newHeight - 10 : 'auto';
    setHeight(refHeight);
  }, [windowHeight, windowWidth]);

  return (
    <div
      ref={myDivRef}
      style={{ height: Math.round(windowHeight * 0.78) }}
      role="banner"
      className="d-flex align-items-center justify-content-center SSbodyMiddle"
    >
      {isImage ? (
        <ImageContent
          isIframeLoading={isIframeLoading}
          tokenError={tokenError}
          preview={preview}
          height={height}
          setIsIframeLoading={setIsIframeLoading}
          lockedContent={lockedContent}
          setPreview={setPreview}
          data={data}
        />
      ) : (
        <>
          {isIframeLoading && (
            <div className="dataloading-overlay-showcase ">
              {isStory ? (
                <MainLoader
                  noBackdrop
                  text="&nbsp; &nbsp;&nbsp; &nbsp;&nbsp;&nbsp; Please Wait. &nbsp; &nbsp;&nbsp; &nbsp;
        Preparing your Story"
                />
              ) : (
                <div className="dataloading" />
              )}
            </div>
          )}
          <iframe
            src={getIframeSource}
            title="Innerloop player"
            onLoad={() => setIsIframeLoading(false)}
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            className={isIframeLoading ? 'd-none' : 'd-block h-full w-full'}
          />
        </>
      )}
    </div>
  );
};

const ImageContent = ({
  isIframeLoading,
  tokenError,
  preview,
  height,
  setIsIframeLoading,
  lockedContent,
  setPreview,
  data
}) => {
  return (
    <>
      {isIframeLoading ? (
        <div className="dataloading-overlay-showcase ">
          <div className="dataloading" />
        </div>
      ) : (
        <>
          {tokenError ? (
            <p>
              <IntlMessages id="unauthorized" />
            </p>
          ) : preview ? (
            <img
              src={preview}
              alt="embed-content"
              height={height}
              style={{ objectFit: 'contain' }}
              width="100%"
              className={isIframeLoading ? 'd-none' : 'd-block'}
              onLoad={() => setIsIframeLoading(false)}
            />
          ) : lockedContent ? (
            <LockedContent contentId={data?.entityId} setPreview={setPreview} />
          ) : null}
        </>
      )}
    </>
  );
};

export default Middle;
