import React from 'react';
import ConfirmationModal from 'components/common/ConfirmationModal';
import { NotificationManager } from 'components/common/react-notifications';
import { deleteWatermark } from 'functions/api/spacesApi';
import IntlMessages from 'helpers/IntlMessages';

const DeleteWatermarkModal = ({
  openModal,
  toggleModal,
  mediaTitle,
  selectedSlideName,
  setSelectedSlideName,
  setSelectedSlideSrc,
  setDeletedWatermarkName,
  isWatermarkModal,
  setAllowContinue,
  getAvailableWatermarks,
}) => {
  const handleConfirm = async () => {
    toggleModal();
    try {
      const resp = await deleteWatermark({
        watermark: mediaTitle,
      });

      if (resp && !resp?.isError) {
        if (selectedSlideName === mediaTitle) {
          setSelectedSlideName('');
          setSelectedSlideSrc('');
          if (isWatermarkModal) setAllowContinue(false);
        }
        await getAvailableWatermarks();
        NotificationManager.success(
          <IntlMessages id="ins.watermark-del-sucess" />,
          <IntlMessages id="req.success" />,
          3000,
          null,
          null,
          ''
        );
      } else if (!resp || resp.isError) {
        NotificationManager.error(
          <IntlMessages id="ins.please-try" />,
          <IntlMessages id="req.failed" />,
          3000,
          null,
          null,
          ''
        );
      }
      setDeletedWatermarkName('');
    } catch (e) {
      NotificationManager.error(
        <IntlMessages id="team-invitation-error" />,
        <IntlMessages id="req.failed" />,
        3000,
        null,
        null,
        ''
      );
    }
  };

  const handleClose = () => {
    toggleModal();
  };

  return (
    <ConfirmationModal
      openConfirmationModal={openModal}
      toggleConfirmationModal={toggleModal}
      handleConfirm={handleConfirm}
      handleClose={handleClose}
      type="deleteWatermark"
      mediaTitle=""
    />
  );
};

export default DeleteWatermarkModal;
