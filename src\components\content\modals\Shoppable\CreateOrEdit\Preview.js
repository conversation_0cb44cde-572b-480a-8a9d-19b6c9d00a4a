/* eslint-disable no-unused-vars */
import { POSITION_TYPE } from 'constants/VideoInteraction';
import React, { useMemo } from 'react';
import { Card } from 'reactstrap';

const smallIcon = {
  height: 12,
  width: 12
};

const color = '#992288';

const Preview = (props) => {
  const style = useMemo(() => {
    const pos = props?.position;

    if (pos === POSITION_TYPE.BOTTOM_LEFT) {
      return {
        alignItems: 'flex-start',
        justifyContent: 'flex-end'
      };
    }
    if (pos === POSITION_TYPE.BOTTOM_RIGHT) {
      return {
        alignItems: 'flex-end',
        justifyContent: 'flex-end'
      };
    }
    if (pos === POSITION_TYPE.TOP_LEFT) {
      return {
        alignItems: 'flex-start',
        justifyContent: 'flex-start'
      };
    }
    if (pos === POSITION_TYPE.TOP_RIGHT) {
      return {
        alignItems: 'flex-end',
        justifyContent: 'flex-start'
      };
    }
    return {
      alignItems: 'center',
      justifyContent: 'center'
    };
  }, [props?.position]);

  return (
    <div className="ShoppablePreview">
      <h4 className="text-center my-4">Preview</h4>

      <span
        className="plyrimg"
        style={{
          ...style,
          paddingBottom: '1.5rem'
        }}
      >
        <CTADetails {...props} />
        {/* <img
          id="PLAYER_IMG"
          src="/assets/player.webp"
          height="100%"
          width="100%"
          alt="player preview"
        /> */}
        <div
          className="position-absolute w-full d-flex flex-row align-items-center justify-content-between gap-1 p-1"
          style={{ bottom: 5, background: '#384157' }}
        >
          <div
            style={{ width: '25%' }}
            className="d-flex flex-row align-items-center justify-content-between"
          >
            <img {...smallIcon} alt="play" src="/assets/player/play.svg" />
            <img
              {...smallIcon}
              alt="backward"
              src="/assets/player/backward.svg"
            />
            <img
              {...smallIcon}
              alt="forward"
              src="/assets/player/forward.svg"
            />
            <img {...smallIcon} alt="volume" src="/assets/player/volume.svg" />
            <input
              type="range"
              disabled
              color={color}
              style={{
                width: 'inherit',
                accentColor: color
              }}
            />
          </div>
          <div
            className="d-flex flex-row align-items-center justify-content-between"
            style={{ width: '75%' }}
          >
            <input
              type="range"
              disabled
              style={{
                width: '90%',
                accentColor: color
              }}
            />
            <img {...smallIcon} alt="time" src="/assets/player/time.svg" />
          </div>
          <div
            style={{ width: '10%' }}
            className="d-flex flex-row align-items-center justify-content-around"
          >
            <img
              {...smallIcon}
              alt="fullscreen"
              src="/assets/player/fullscreen.svg"
            />
            <img {...smallIcon} alt="pip" src="/assets/player/pip.svg" />
          </div>
        </div>
      </span>
    </div>
  );
};

export default Preview;

const CTADetails = ({
  title,
  originalPrice,
  sellingPrice,
  currency,
  imgSrc
}) => {
  return (
    <Card className="ctaShoppablePreview">
      {imgSrc && (
        <div className="CSP-left">
          <img height="100%" width="100%" alt="" src={imgSrc} />
        </div>
      )}
      <div className="CSP-right" style={{ width: imgSrc ? '80%' : '100%' }}>
        <h1>{title}</h1>
        <div className="pricingDetails">
          <span className="selling">
            {currency} {sellingPrice}
          </span>
          <span className="original">
            {currency} {originalPrice}
          </span>
        </div>
        <div className="d-flex flex-row align-items-center justify-content-end">
          <button type="button">Buy Now</button>
        </div>
      </div>
    </Card>
  );
};
