/* eslint-disable no-empty */
// eslint-disable import/prefer-default-export
import axios from 'axios';

const baseUrl = process.env.REACT_APP_SERVER_BASE_URL;
const service = 'spaceSummary';

// -------------------------->
// space summary cards
// -------------------------->

export const watchTime = async (spaceId) => {
  let response = 0;
  try {
    const { data } = await axios.get(
      `${baseUrl}/${service}/watchTime/${spaceId}`
    );
    response = {
      data: data.data[0]?.watchtime_s || 0,
      nextUpdateTime: data.data.nextUpdateTime
    };
  } catch (e) {}
  return response;
};

export const avgWatchTime = async (spaceId) => {
  let response = 0;
  try {
    const { data } = await axios.get(
      `${baseUrl}/${service}/avgWatchTime/${spaceId}`
    );
    response = {
      data: data.data[0]?.avg_Watchtime_s || 0,
      nextUpdateTime: data.data.nextUpdateTime
    };
  } catch (e) {}
  return response;
};

export const avgMediaImpr = async (spaceId) => {
  let response = 0;
  try {
    const { data } = await axios.get(
      `${baseUrl}/${service}/avgMediaImpr/${spaceId}`
    );
    response = {
      data: data.data[0]?.avg_Media_Impr || 0,
      nextUpdateTime: data?.data?.nextUpdateTime
    };
  } catch (e) {}
  return response;
};

export const mediaImpr = async (spaceId) => {
  let response = 0;
  try {
    const { data } = await axios.get(
      `${baseUrl}/${service}/mediaImpr/${spaceId}`
    );
    response = {
      data: data.data[0]?.media_impressions || 0,
      nextUpdateTime: data?.data?.nextUpdateTime
    };
  } catch (e) {}
  return response;
};

export const spaceSummary = async (spaceId) => {
  let response = {};
  try {
    const { data } = await axios.get(`${baseUrl}/${service}/space/${spaceId}`);
    response = {
      data: data?.data || {},
      nextUpdateTime: data?.data?.nextUpdateTime
    };
  } catch (e) {}
  return response;
};

// space summary chart

export const viewTimeLine = async (spaceId, currentDate) => {
  let response = [];
  try {
    const { data } = await axios.get(
      `${baseUrl}/${service}/viewTimeLine/${spaceId}/${currentDate}`
    );
    response = {
      data: data.data.data,
      nextUpdateTime: data?.data?.nextUpdateTime
    };
  } catch (e) {}
  return response;
};

// ------------------------->
// dashboards
// ------------------------->

export const tenantSummary = async () => {
  let response = {};
  try {
    const { data } = await axios.get(`${baseUrl}/${service}/tenantSummary`);
    response = data.data || {};
  } catch (e) {}
  return response;
};

export const viewImpressionsByMonth = async () => {
  let response = {};
  try {
    const { data } = await axios.get(
      `${baseUrl}/${service}/viewImpressionsByMonth`
    );
    response = data.data || {};
  } catch (e) {}
  return response;
};

export const topMediaContentsByImpressions = async (
  year,
  // eslint-disable-next-line no-shadow
  watchTime = false
) => {
  let response = {};
  try {
    const { data } = await axios.get(
      `${baseUrl}/${service}/topMediaContentsByImpressions/${year}/${watchTime}`
    );
    response = data.data || {};
  } catch (e) {
    console.error(e);
  }
  return response;
};

export const topAllCountries = async (city = false) => {
  let response = [];
  try {
    const { data } = await axios.get(`${baseUrl}/${service}/alltopCountries`, {
      headers: {
        'x-city': city
      }
    });
    response = data.data;
  } catch (e) {
    console.error(e);
  }
  return response;
};

export const getUniqueViewCount = async (contentId) => {
  let response = {};
  try {
    const { data } = await axios.get(
      `${baseUrl}/${service}/unique-view-count/${contentId}`
    );
    response = data?.data || {};
  } catch (e) {
    console.error(e);
  }
  return response;
};

export const getLoadedDataCount = async (contentId) => {
  let response = {};
  try {
    const { data } = await axios.get(
      `${baseUrl}/${service}/loaded-data-count/${contentId}`
    );
    response = data.data || {};
  } catch (e) {
    console.error(e);
  }
  return response;
};
export const sessions = async () => {
  let response = {};
  try {
    const { data } = await axios.get(`${baseUrl}/${service}/sessions`);
    response = data.data || {};
  } catch (e) {
    console.error(e);
  }
  return response;
};

export const getTopCountriesCities = async (
  spaceId,
  showcaseId,
  city = false,
  isMediaAnalytics = false
) => {
  let response = {};
  try {
    const { data } = await axios.get(
      `${baseUrl}/${service}/get-top-countries-cities/${spaceId}/${showcaseId}`,
      {
        headers: {
          'x-city': city,
          'x-media-analytics': isMediaAnalytics
        }
      }
    );
    response = data;
  } catch (e) {
    console.error(e);
  }
  return response;
};

export const getPersonalizedInsights = async (type) => {
  let response = {};
  try {
    const { data } = await axios.get(
      `${baseUrl}/${service}/personalized-insights/${type}`
    );
    response = data;
  } catch (e) {
    console.error(e);
  }
  return response;
};

export const getDetailedInsights = async (type) => {
  let response = {};
  try {
    const { data } = await axios.get(
      `${baseUrl}/${service}/detailed-insights/${type}`
    );
    response = data;
  } catch (e) {
    console.error(e);
  }
  return response;
};

export const getChatAnalytics = async ({ type, contentId, isQna }) => {
  let response = {};
  try {
    const { data } = await axios.get(
      `${baseUrl}/${service}/chat-analytics/${contentId}/${type}`,
      {
        params: {
          isQNA: isQna
        }
      }
    );
    response = data;
  } catch (e) {
    console.error(e);
  }
  return response;
};

export const getInteractionAnalytics = async ({
  type,
  contentId,
  by,
  interactionId
}) => {
  let response = {};
  try {
    const { data } = await axios.get(
      `${baseUrl}/${service}/interaction-analytics/${contentId}/${interactionId}/${type}`,
      {
        params: {
          by
        }
      }
    );
    response = data;
  } catch (e) {
    console.error(e);
  }
  return response;
};

export const getInteractionAnalyticsById = async ({
  contentId,
  interactionId
}) => {
  let response = {};
  try {
    const { data } = await axios.get(
      `${baseUrl}/${service}/player-interaction-by-id/${contentId}/${interactionId}`
    );
    response = data;
  } catch (e) {
    console.error(e);
  }
  return response;
};
