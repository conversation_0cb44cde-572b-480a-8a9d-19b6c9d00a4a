/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable no-unused-vars */
/* eslint-disable no-unused-expressions */
import { NotificationManager } from 'components/common/react-notifications';
import getBlobDuration from 'get-blob-duration';
import { getMediaType } from 'helpers/Utils';
import React, { useEffect, useRef, useState } from 'react';

const FIFTEEN_MINUTES = 900;

const getFilesFromFileList = (files) => {
  const newFiles = Array.from(files).map((f) => f);
  return newFiles;
};

const MediaDropzone = ({ handleContinue, selectedFiles, ...props }) => {
  const [files, setFiles] = useState(selectedFiles);
  const ref = useRef(null);

  useEffect(() => {
    if (selectedFiles) {
      setFiles(selectedFiles);
    }
  }, [selectedFiles]);

  const handleFileStoring = async (newfiles) => {
    const localFiles = [];
    const promises = newfiles.map(async (m) => {
      const isVideo = getMediaType(m.type) === 1;
      if (isVideo) {
        const blobUrl = URL.createObjectURL(m);
        const dur = await getBlobDuration(blobUrl);
        console.log({
          dur,
          name: m.name,
          condition: dur >= FIFTEEN_MINUTES
        });
        if (dur >= FIFTEEN_MINUTES) {
          NotificationManager.error(
            `${m.name} duration is greater than 15 minutes`
          );
        } else {
          localFiles.push(m);
        }
      } else {
        localFiles.push(m);
      }
    });

    await Promise.all(promises);

    const continueFiles = [...files, ...localFiles].slice(0, 5);
    console.log('storing files', continueFiles);
    handleContinue && handleContinue(continueFiles);
    setFiles(continueFiles);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
    e.dataTransfer.dropEffect = 'copy';
  };

  const handleDrop = async (e) => {
    if (files.length === 5) {
      NotificationManager.warning('Only 5 contents allowed');
      return;
    }
    e.preventDefault();
    e.stopPropagation();
    const localfiles = getFilesFromFileList(e.dataTransfer.files);
    console.log('files handleDrop', files);
    await handleFileStoring(localfiles);
    e.target.value = '';
  };

  const handleOpenFilePicker = () => {
    ref?.current?.click();
  };

  const handleFileSelect = async (e) => {
    if (files.length === 5) {
      NotificationManager.warning('Only 5 contents allowed');
      return;
    }
    const selectedFiles = e.target.files;
    if (selectedFiles) {
      const files = getFilesFromFileList(selectedFiles);
      console.log('files handleFileSelect', files);
      await handleFileStoring(files);
      e.target.value = '';
    }
  };

  return (
    <>
      <div
        onClick={() => handleOpenFilePicker()}
        onDragOver={(e) => handleDragOver(e)}
        onDrop={(e) => handleDrop(e)}
        {...props}
        className="custom-dropzone centered"
      >
        <i className="simple-icon-cloud-upload" />
        <h2 className="font-bolder">Upload your contents here</h2>
        <p>Maximum duration limit per content is 15 mins</p>
      </div>
      <input
        multiple
        type="file"
        accept="image/jpeg, image/jpg, image/png, image/gif, image/webp, audio/*, video/*"
        ref={ref}
        style={{ display: 'none' }}
        onChange={(e) => handleFileSelect(e)}
      />
    </>
  );
};

export default MediaDropzone;
