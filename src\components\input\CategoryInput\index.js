/* eslint-disable no-unused-vars */
import React, { useState } from 'react';
import Creatable from 'react-select/creatable';
import { useDispatch, useSelector } from 'react-redux';
import { addCategory } from 'functions/api/categoryApi';
import { addCategoryInRedux } from 'redux/categories/action';
import { NotificationManager } from 'components/common/react-notifications';
import IntlMessages from 'helpers/IntlMessages';

const MAX_CATEGORY_LENGTH = 20;

function CategoryInput({
  value,
  onChange,
  className,
  disabled = false,
  required
}) {
  const [inputValue, setInputValue] = useState('');
  const dispatch = useDispatch();
  const optionsForCategory = useSelector((state) => state.categories.data);

  const handleAddCategory = async (category) => {
    try {
      const { data } = await addCategory(category);
      if (!data.isError) {
        dispatch(addCategoryInRedux(category));
      }
    } catch (error) {
      console.log('error');
    }
  };

  return (
    <fieldset className={`tagsbox ${className}`}>
      <legend>
        Category
        {required && <span style={{ color: '#922c88' }}> *</span>}
      </legend>
      <Creatable
        options={optionsForCategory ?? []}
        value={value ? [{ value, label: value }] : null}
        isMulti
        name="categories"
        className="basic-multi-select"
        classNamePrefix="select"
        onChange={(e) => {
          onChange(e);
        }}
        inputValue={inputValue}
        onInputChange={(newInput) => {
          if (newInput.length > MAX_CATEGORY_LENGTH) {
            NotificationManager.warning(
              <IntlMessages id="category-warn-large" />
            );
          } else {
            setInputValue(newInput);
          }
        }}
        onCreateOption={(e) => {
          const optionLength = e.trim().length;
          const value = e.slice(0, MAX_CATEGORY_LENGTH);
          if (optionLength > MAX_CATEGORY_LENGTH) {
            NotificationManager.warning(
              <IntlMessages id="category-warn-large" />
            );
          }
          if (optionLength < 4) {
            NotificationManager.warning(
              <IntlMessages id="category-warn-small" />
            );
            return;
          }
          const payload = [{ label: value, value, __isNew__: true }];
          onChange(payload);
          handleAddCategory(value);
        }}
        isDisabled={disabled}
      />
    </fieldset>
  );
}

export default CategoryInput;
