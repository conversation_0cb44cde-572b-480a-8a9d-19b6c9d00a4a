import React, { useEffect, useRef, useState } from 'react';

function AudioVisualization({ mediaRecorder }) {
  const canvasRef = useRef(null);
  const [audioContext, setAudioContext] = useState(null);

  useEffect(() => {
    if (!mediaRecorder) return;

    const canvas = canvasRef.current;
    const canvasContext = canvas.getContext('2d');

    // Create an AudioContext
    const audioCtxt = new (window.AudioContext || window.webkitAudioContext)();
    setAudioContext(audioCtxt);

    const analyser = audioCtxt.createAnalyser();
    analyser.fftSize = 256; // Adjust this for higher/lower resolution

    const source = audioCtxt.createMediaStreamSource(mediaRecorder.stream);
    source.connect(analyser);

    const bufferLength = analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    function draw() {
      analyser.getByteTimeDomainData(dataArray);

      canvasContext.clearRect(0, 0, canvas.width, canvas.height);

      canvasContext.beginPath();
      const sliceWidth = (canvas.width * 1.0) / bufferLength;
      let x = 0;
      // eslint-disable-next-line no-plusplus
      for (let i = 0; i < bufferLength; i++) {
        const v = dataArray[i] / 128.0;
        const y = (v * canvas.height) / 2;

        if (i === 0) {
          canvasContext.moveTo(x, y);
        } else {
          canvasContext.lineTo(x, y);
        }

        x += sliceWidth;
      }
      canvasContext.lineTo(canvas.width, canvas.height / 2);
      canvasContext.stroke();

      requestAnimationFrame(draw);
    }

    draw();

    // eslint-disable-next-line consistent-return
    return () => {
      audioContext?.close();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mediaRecorder]);

  return (
    <div className="position-relative d-flex align-items-center justify-content-center">
      <i
        className="simple-icon-microphone position-absolute"
        style={{ fontSize: '20px' }}
      />
      <canvas
        className='canvas-recording-circle'
        ref={canvasRef}
      />
    </div>
  );
}

export default AudioVisualization;
