/* eslint-disable react/no-array-index-key */
/* eslint-disable no-nested-ternary */
import GlideComponent from 'components/carousel/GlideComponent';
import { URLTypeNameEnum } from 'data/urlTypes';
import { Formik, Form, Field } from 'formik';
import IntlMessages from 'helpers/IntlMessages';
import useWatermark from 'hooks/useWatermark';
import Switch from 'rc-switch';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { NavLink } from 'react-router-dom/cjs/react-router-dom';
import { FormGroup, Progress, Row } from 'reactstrap';
import { storePhoton } from 'redux/photon/action';
import mediaPlayerPreview from '../../../assets/img/video/player_preview.svg';

const WaterMarkPositionData = [
  {
    id: 1,
    img: '/assets/img/watermark/tleft.svg',
    title: 'topright',
    description: 'Top Right'
  },
  {
    id: 2,
    img: '/assets/img/watermark/tright.svg',
    title: 'topleft',
    description: 'Top Left'
  },
  {
    id: 3,
    img: '/assets/img/watermark/bleft.svg',
    title: 'bottomright',
    description: 'Bottom Right'
  },
  {
    id: 4,
    img: '/assets/img/watermark/bright.svg',
    title: 'bottomleft',
    description: 'Bottom Left'
  }
];

const Watermark = ({
  condition,
  isWatermarkModal,
  type,
  enableWatermarks,
  setEnableWatermark,
  toggleConfirmationModal,
  loadingWatermarks,
  slideSettings,
  watermarkSlides,
  newWatermarkImg,
  handleChooseWatermark,
  watermarkRef,
  handleChangeWatermark,
  selectedSlideName,
  handleSelectNewImgForWatermark,
  handleRemoveNewWatermarkImg,
  handleSlideSelection,
  handleRemoveWatermark,
  initialValues,
  markWidth,
  validateMarkWidth,
  markHeight,
  validateMarkHeight,
  markOpacity,
  validateMarkOpacity,
  videoView,
  remoteFileUrl,
  images,
  position,
  selectedSlideSrc,
  setWatermarkedImg,
  MediaType,
  isImageModal,
  contentIdForWatermark,
  setPosition,
  setIsWatermarkApplying
}) => {
  const [isLoadingPhoton, setIsLoadingPhoton] = useState(false);
  const [imagesConverted, setImagesConverted] = useState(0);
  const photonLib = useSelector((state) => state.photon);

  const allowWatermarking = (() => {
    const heightCorrect = markHeight >= 40 && markHeight <= 150;
    const widthCorrect = markWidth >= 40 && markWidth <= 150;
    const opacityCorrect = markOpacity >= 1 && markOpacity <= 100;
    const allow = widthCorrect && heightCorrect && opacityCorrect;
    console.log({
      allow,
      heightCorrect,
      widthCorrect,
      opacityCorrect,
      markHeight,
      markOpacity,
      markWidth
    });
    return allow;
  })();

  const dispatch = useDispatch();
  const { isConverting, watermarkedImages } = useWatermark({
    height: +markHeight,
    images,
    opacity: +markOpacity,
    photon: photonLib,
    position,
    watermark: selectedSlideSrc,
    width: +markWidth,
    waterMarkOffset: 10,
    setProgress: setImagesConverted,
    allowWatermarking
  });

  useEffect(() => {
    setIsWatermarkApplying(isConverting);
    if (!isConverting && watermarkedImages) {
      setWatermarkedImg(watermarkedImages.map((m) => m.completedImg));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isConverting, watermarkedImages]);

  useEffect(() => {
    const getPhoton = async () => {
      setIsLoadingPhoton(true);
      const photon = await import('@silvia-odwyer/photon');
      setIsLoadingPhoton(false);
      dispatch(storePhoton(photon));
    };
    if (images.length > 0 && !photonLib && !isLoadingPhoton) {
      getPhoton();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [images, photonLib, isLoadingPhoton]);

  if (condition) {
    return (
      <>
        {!isWatermarkModal &&
          type !== URLTypeNameEnum.VIMEO &&
          type !== URLTypeNameEnum.YOUTUBE && (
            <div className="d-flex flex-column align-align-items-center justify-content-center mb-4">
              <div className="d-flex flex-row align-items-center justify-content-center mb-2">
                <span className="media-label-title">Enable Watermark :</span>
                <Switch
                  className="custom-switch custom-switch-primary custom-switch-small ml-3"
                  checked={enableWatermarks}
                  onClick={() => {
                    setEnableWatermark(!enableWatermarks);
                  }}
                />
              </div>
              {enableWatermarks && (
                <p className="dashboard-center-this-item pr-3 pl-3  text-center text-primary">
                  <strong>
                    <i className="simple-icon-info font-weight-bold"> </i>
                    This feature allows you to add watermark for this content
                  </strong>
                </p>
              )}
            </div>
          )}
        {enableWatermarks && (
          <div>
            {isWatermarkModal && (
              <div className="d-flex justify-content-end">
                <NavLink
                  className="btn-link text-decoration-none "
                  to="#"
                  onClick={() => toggleConfirmationModal()}
                >
                  <i className="simple-icon-close close-btn" />
                </NavLink>
              </div>
            )}
            {loadingWatermarks ? (
              <div className="dataloading-overlay" style={{ height: '100px' }}>
                <div className="dataloading" />
              </div>
            ) : (
              <GlideComponent
                settings={slideSettings}
                itemsCount={watermarkSlides.length + 1}
              >
                <>
                  {!newWatermarkImg && (
                    <div
                      className="dotted-border-primary-box watermark-item shadow-none
                  m-0 ml-3 text-primary card mr-0 p-1"
                      role="button"
                      onClick={handleChooseWatermark}
                      aria-hidden="true"
                    >
                      <i
                        className="simple-icon-plus"
                        style={{ fontSize: '1.5em', margin: '3px' }}
                      >
                        {' '}
                      </i>
                      <p style={{ fontWeight: 'bold' }}>Add</p>
                      <input
                        type="file"
                        className="dashboard-file-input"
                        accept="image/jpeg, image/jpg, image/png"
                        ref={watermarkRef}
                        onChange={handleChangeWatermark}
                      />
                    </div>
                  )}
                  {newWatermarkImg && (
                    <div
                      className={`watermark-item m-0 ml-3 text-primary card mr-0 p-1
                  ${
                    selectedSlideName === 'newmark'
                      ? 'solid-active-primary-box'
                      : 'dotted-border-primary-box '
                  }`}
                      onClick={() => handleSelectNewImgForWatermark()}
                      aria-hidden="true"
                    >
                      <img
                        src={URL.createObjectURL(newWatermarkImg)}
                        alt="thumbnailImage"
                        className="watermark-size"
                      />
                      <i
                        onClick={(event) => handleRemoveNewWatermarkImg(event)}
                        className="iconsminds-close"
                        style={{ fontSize: 'large' }}
                        aria-hidden="true"
                      >
                        {' '}
                      </i>
                    </div>
                  )}
                  {watermarkSlides?.length > 0 &&
                    watermarkSlides.map((item) => (
                      <div
                        key={item.name}
                        className={`watermark-item ml-3 text-primary card p-1 ${
                          selectedSlideName === item.name &&
                          'solid-active-primary-box'
                        }`}
                        onClick={() => handleSlideSelection(item)}
                        aria-hidden="true"
                      >
                        <img
                          src={item.url}
                          className="watermark-size"
                          alt={`${item.name}`}
                        />
                        <i
                          onClick={(event) =>
                            handleRemoveWatermark(event, item)
                          }
                          className="iconsminds-close"
                          style={{ fontSize: 'large' }}
                          aria-hidden="true"
                        >
                          {' '}
                        </i>
                      </div>
                    ))}
                </>
              </GlideComponent>
            )}
            {selectedSlideName && (
              <>
                <Formik initialValues={initialValues}>
                  {({ errors, touched }) => (
                    <Form
                      className="av-tooltip tooltip-label-bottom"
                      aria-disabled={isConverting}
                    >
                      <div className="d-flex justify-content-center align-items-center mt-3">
                        <IntlMessages id="menu.markWidth" />
                        <FormGroup className="form-group has-float-label mb-0">
                          <Field
                            className="form-control markSize"
                            type="number"
                            name="markWidth"
                            value={markWidth}
                            validate={validateMarkWidth}
                          />
                          {errors.markWidth && touched.markWidth && (
                            <div className="invalid-feedback d-block">
                              {errors.markWidth}
                            </div>
                          )}
                        </FormGroup>
                        <span>px</span>
                        <span className="ml-3 mr-3">X</span>
                        <IntlMessages id="menu.markHeight" />
                        <FormGroup className="form-group has-float-label mb-0">
                          <Field
                            className="form-control markSize"
                            type="number"
                            name="markHeight"
                            value={markHeight}
                            validate={validateMarkHeight}
                          />
                          {errors.markHeight && touched.markHeight && (
                            <div className="invalid-feedback d-block">
                              {errors.markHeight}
                            </div>
                          )}
                        </FormGroup>
                        <span>px</span>
                      </div>
                      <div className="d-flex justify-content-center align-items-center mt-3">
                        <IntlMessages id="menu.markOpacity" />
                        <FormGroup className="form-group has-float-label mb-0">
                          <Field
                            className="form-control markSize"
                            type="number"
                            name="markOpacity"
                            value={markOpacity}
                            validate={validateMarkOpacity}
                          />
                          {errors.markOpacity && touched.markOpacity && (
                            <div className="invalid-feedback d-block">
                              {errors.markOpacity}
                            </div>
                          )}
                        </FormGroup>
                        <span>%</span>
                      </div>
                    </Form>
                  )}
                </Formik>
                {isConverting && (
                  <div className="w-100 d-flex flex-row align-items-center justify-content-center">
                    <div className="mt-4 w-60">
                      <Progress
                        animated
                        max={images?.length}
                        value={imagesConverted}
                        className="innerloop-progress-bar"
                      />
                      <p className="text-primary text-center mt-2">
                        <i className="simple-icon-info mr-2" />
                        Applying watermark
                      </p>
                    </div>
                  </div>
                )}
                <div
                  className={`${
                    videoView || remoteFileUrl
                      ? 'video-watermark-preview-container'
                      : 'w-full'
                  }`}
                >
                  {videoView || remoteFileUrl ? (
                    <img
                      className="player-preview-image"
                      src={mediaPlayerPreview}
                      alt="video preview"
                    />
                  ) : (
                    <>
                      <div
                        className="d-flex flex-row flex-wrap w-full align-items-center justify-content-center mt-3"
                        style={{ gap: '2%' }}
                      >
                        {!isConverting &&
                          watermarkedImages.map((m, i) => (
                            // eslint-disable-next-line react/no-array-index-key
                            <div
                              style={{ width: '150px', height: '150px' }}
                              key={i}
                            >
                              <img
                                height="100%"
                                width="100%"
                                crossOrigin="anonymous"
                                alt="converted GIF"
                                src={URL.createObjectURL(m.completedImg)}
                              />
                            </div>
                          ))}
                      </div>
                    </>
                  )}
                  {MediaType !== 3 && (
                    <img
                      className={`${
                        videoView || remoteFileUrl
                          ? `${
                              position === 'topright'
                                ? 'video-watermark-preview-image-tr'
                                : position === 'topleft'
                                ? 'video-watermark-preview-image-tl'
                                : position === 'bottomright'
                                ? 'video-watermark-preview-image-br'
                                : position === 'bottomleft'
                                ? 'video-watermark-preview-image-bl'
                                : 'display-none'
                            }`
                          : 'image-watermark-preview-image'
                      }`}
                      src={selectedSlideSrc}
                      style={{
                        width: markWidth * 0.5,
                        height: markHeight * 0.5,
                        opacity: markOpacity / 100
                      }}
                      alt="watermark position"
                    />
                  )}
                  <p
                    style={{
                      width: '100%',
                      textAlign: 'center',
                      fontSize: '16px',
                      margin: '10px'
                    }}
                  >
                    <IntlMessages id="spaces.previewPosition" />
                  </p>
                </div>
              </>
            )}
            {selectedSlideName &&
              (videoView ||
                remoteFileUrl ||
                isImageModal ||
                (contentIdForWatermark && isWatermarkModal)) && (
                <div className="mt-4">
                  <p className="media-label-title text-center">
                    <IntlMessages id="ins-select-wm" />
                  </p>
                  <Row
                    style={{ justifyContent: 'center', gap: '2%' }}
                    aria-disabled={isConverting}
                  >
                    {WaterMarkPositionData.map((d) => (
                      <WaterMarkPositionCard
                        key={d.id}
                        img={d.img}
                        title={d.title}
                        setPosition={setPosition}
                        description={d.description}
                        position={position}
                      />
                    ))}
                  </Row>
                </div>
              )}
          </div>
        )}
      </>
    );
  }
  return null;
};

const WaterMarkPositionCard = ({
  img,
  title,
  setPosition,
  description,
  position
}) => {
  const active = position === title;
  return (
    <div onClick={() => setPosition(title)} className="text-center">
      <img
        title={title}
        alt={title}
        src={img}
        height={50}
        width={100}
        className="img-fluid"
        style={{
          border: active && '3px solid #922c88'
        }}
      />
      <p style={{ color: active && '#922c88' }}>{description}</p>
    </div>
  );
};

export default Watermark;
