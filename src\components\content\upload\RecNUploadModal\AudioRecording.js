/* eslint-disable no-unused-expressions */
import React, { useCallback, useEffect, useState } from 'react';
import {
  Button,
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  UncontrolledDropdown
} from 'reactstrap';
import AudioVisualization from '../AudioVisualization';
import IntlMessages from 'helpers/IntlMessages';
import InfoIcon from 'components/icon/InfoIcon';
import {
  PauseRecordingButton,
  ResumeRecordingButton,
  StopRecordingButton
} from './CommonButtons';

const slash = 1050;

const AudioRecording = ({
  setMediaFile,
  setDuration,
  setIsAnyTabRecording
}) => {
  const [microphones, setMicrophones] = useState([]);
  const [selectedMicrophone, setSelectedMicrophone] = useState(null);
  const [recording, setRecording] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState(null);
  const [isError, setIsError] = useState(false);
  const [permissionDenied, setPermissionDenied] = useState(false);
  const [startTime, setStartTime] = useState(null);
  const [isResumed, setIsResumed] = useState(false);
  const [isPaused, setIsPaused] = useState(false);

  useEffect(() => {
    return () => {
      // making camera off when we change the tab
      // eslint-disable-next-line array-callback-return
      mediaRecorder?.stream?.getTracks()?.map((track) => {
        const time = Date.now() - startTime;
        // console.log({ time, timeAfterDiving: time / slash });
        setDuration(time / slash);
        track?.stop();
      });
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mediaRecorder]);

  useEffect(() => {
    async function fetchMicrophones() {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: true
        });
        const devices = await navigator.mediaDevices.enumerateDevices();
        stream.getTracks().forEach((track) => {
          track.stop();
        });
        if (devices?.length === 0) {
          setIsError(true);
        }
        const audioDevices = devices.filter(
          (device) => device.kind === 'audioinput'
        );
        setMicrophones(audioDevices);
      } catch (error) {
        setIsError(true);
        setPermissionDenied(error?.message === 'Permission denied');
        console.error('Error fetching microphones:', error);
      }
    }
    fetchMicrophones();
  }, []);

  useEffect(() => {
    async function getAudioStream() {
      if (selectedMicrophone || isError) {
        try {
          setDuration(0);
          const stream = await navigator.mediaDevices.getUserMedia({
            audio: isError ? true : { deviceId: selectedMicrophone }
          });
          const recorder = new MediaRecorder(stream);
          recorder.state === 'inactive' &&
            (recorder.ondataavailable = (event) => {
              if (event.data.size > 0) {
                const blobData = event.data;
                const file = new File([blobData], `${Date.now()}.mp3`, {
                  type: blobData?.type
                });
                setMediaFile(file);
              }
            });
          recorder.onresume = () => {
            setIsResumed(true);
            setIsPaused(false);
          };
          recorder.onpause = () => {
            setIsResumed(false);
            setIsPaused(true);
          };
          setMediaRecorder(recorder);
        } catch (error) {
          console.error('Error getting audio stream:', error);
        }
      }
    }
    getAudioStream();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedMicrophone, isError]);

  const toggleRecording = () => {
    if (recording) {
      mediaRecorder.stop();
      setIsAnyTabRecording(false);
      // eslint-disable-next-line array-callback-return
      mediaRecorder.stream.getTracks().map((track) => {
        const time = Date.now() - startTime;
        // console.log({ time, timeAfterDiving: time / slash });
        setDuration(time / slash);
        track.stop();
      });
    } else {
      setStartTime(Date.now());
      mediaRecorder.start();
      setIsAnyTabRecording(true);
    }
    setRecording(!recording);
  };

  const pauseRecording = useCallback(() => {
    if (mediaRecorder.state === 'recording') {
      mediaRecorder.pause();
    }
  }, [mediaRecorder]);

  const resumeRecording = useCallback(() => {
    if (mediaRecorder.state === 'paused') {
      mediaRecorder.resume();
    }
  }, [mediaRecorder]);

  return (
    <>
      {permissionDenied && (
        <div
          className="mb-4"
          style={{
            fontSize: '14px',
            padding: '20px',
            color: 'red',
            border: '1px solid red',
            borderRadius: '10px',
            fontWeight: 'bold'
          }}
        >
          <i className="simple-icon-info font-weight-bold"> </i>
          <IntlMessages id="no-mic-permission" />
        </div>
      )}
      {!recording && (
        <div
          aria-disabled={permissionDenied}
          className="align-items-center mt-3 d-flex justify-content-center"
          style={{ gap: '15px' }}
        >
          <div className=" d-flex align-items-center justify-content-center">
            <span className="media-label-title">
              <InfoIcon
                name="mic"
                message="enable your microphone while recording the screen"
              />
            </span>
          </div>
          <UncontrolledDropdown style={{ width: '250px' }}>
            <DropdownToggle
              caret
              color="primary"
              className="btn-sm w-full overflow-hidden"
              outline
            >
              {!isError && selectedMicrophone
                ? selectedMicrophone
                : 'Select microphone'}
            </DropdownToggle>
            <DropdownMenu center>
              {isError && (
                <DropdownItem
                  onClick={() => {
                    setSelectedMicrophone('');
                  }}
                >
                  Default Microphone
                </DropdownItem>
              )}
              <DropdownItem
                onClick={() => {
                  setSelectedMicrophone('');
                }}
              >
                Select Microphone
              </DropdownItem>
              {microphones?.map((micro) => (
                <DropdownItem
                  key={micro.deviceId}
                  onClick={() => {
                    setSelectedMicrophone(micro.deviceId);
                  }}
                >
                  {micro.label || `Microphone ${micro.deviceId}`}
                </DropdownItem>
              ))}
            </DropdownMenu>
          </UncontrolledDropdown>
        </div>
      )}
      {recording &&
        mediaRecorder?.state &&
        mediaRecorder.state === 'recording' && (
          <AudioVisualization mediaRecorder={mediaRecorder} />
        )}
      {mediaRecorder && !recording && (
        <span className="mt-4">
          <AudioVisualization mediaRecorder={mediaRecorder} />
        </span>
      )}
      <div className="mt-3 bottom-rec-btns ">
        {(recording || isResumed) && !isPaused && (
          <PauseRecordingButton onClick={pauseRecording} />
        )}
        {isPaused && <ResumeRecordingButton onClick={resumeRecording} />}
        <div aria-disabled={permissionDenied}>
          {recording ? (
            <StopRecordingButton
              onClick={toggleRecording}
              disabled={!isError ? !selectedMicrophone : false}
            />
          ) : (
            <Button
              className="mt-4 align-self-center"
              type="button"
              style={{ minWidth: '200px', color: '#fff' }}
              disabled={!isError ? !selectedMicrophone : false}
              color="primary"
              onClick={toggleRecording}
            >
              <i className="iconsminds-mic" />
              <IntlMessages id="rec.start-rec" />
            </Button>
          )}
        </div>
      </div>
    </>
  );
};

export default AudioRecording;
