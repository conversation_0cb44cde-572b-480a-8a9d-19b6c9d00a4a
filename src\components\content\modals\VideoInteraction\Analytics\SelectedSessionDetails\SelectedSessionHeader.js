import { formatDate, formatTime } from 'helpers/Utils';
import React from 'react';

const SelectedSessionHeader = ({ selectedSessionDetails }) => {
  const date = selectedSessionDetails.datetimestamp.value;
  return (
    <div className="SelectedSessionHeader">
      <NameValue
        name="Session ID : "
        value={selectedSessionDetails.sessionid}
      />
      <NameValue name="Time : " value={formatTime(date)} />
      <NameValue name="Date : " value={formatDate(date)} />
    </div>
  );
};

const NameValue = ({ name, value }) => {
  return (
    <div className="nameNValue">
      <p>{name}</p>
      <p>
        <b>{value}</b>
      </p>
    </div>
  );
};

export default SelectedSessionHeader;
