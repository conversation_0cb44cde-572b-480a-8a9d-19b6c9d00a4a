/* eslint-disable no-loop-func */
/* eslint-disable no-continue */
/* eslint-disable no-await-in-loop */
/* eslint-disable no-restricted-syntax */
/* eslint-disable consistent-return */
/* eslint-disable no-unused-vars */
/* eslint-disable array-callback-return */
/* eslint-disable react/no-array-index-key */
/* eslint-disable prefer-destructuring */
/* eslint-disable no-else-return */
/* eslint-disable no-nested-ternary */
import React, { useEffect, useMemo, useRef, useState } from 'react';
import MediaDropzone from './MediaDropzone';
import {
  getCurrentUserDetails,
  getDefaultSpaceId,
  getLastVisitedSpaceDetails,
  getLastVisitedSpaceId,
  getMediaType,
  isVideo,
  returnFileSize
} from 'helpers/Utils';
import {
  Button,
  Card,
  Modal,
  ModalBody,
  Progress,
  Row,
  Spinner
} from 'reactstrap';
import { nanoid } from 'nanoid';
import {
  createStory,
  handleContentEncoding,
  handleCreateSasUrl,
  handleUpdateContentStatus,
  updateContentUploadStatus
} from 'functions/api/spacesApi';
import uploadFileToBlob from 'helpers/Azure';
import { NotificationManager } from 'components/common/react-notifications';
import IntlMessages from 'helpers/IntlMessages';
import ProgressBar from '../components/ProgressBar';
import InfoMessage from 'components/infoMessage';
import { spaceHomeRoute } from 'constants/defaultValues';
import { NavLink, useHistory } from 'react-router-dom';
import { Colxx } from 'components/common/CustomBootstrap';
import getBlobDuration from 'get-blob-duration';
import Switch from 'rc-switch';
import ConfirmationModal from 'components/common/ConfirmationModal';
import InfoPopover from 'components/InfoPopover';
import { useSelector } from 'react-redux';

const getFileKey = (name) => {
  return name?.replace(/[\s.]+/g, '-');
};

const QuickUploadModal = ({ toggle, isOpen }) => {
  const history = useHistory();
  const [files, setFiles] = useState([]);
  const dropzone = useRef();
  const [uploading, setUploading] = useState(false);
  const { id: spaceId, name } = getLastVisitedSpaceDetails();
  const [isUploadComplete, setIsUploadComplete] = useState(false);
  const [isCreateStory, setIsCreateStory] = useState(false);
  const [uploadMediaStatus, setUploadMediaStatus] = useState([]);
  const [storyStatus, setStoryStatus] = useState({
    isCreating: false,
    isError: false
  });
  const [openOptConfirmation, setOpenOptConfirmation] = useState(false);
  const [optimizationMap, setOptimizationMap] = useState(new Map());
  const { optimizationEnabled } = useSelector((state) => state.subscription);

  console.log('optimizationMap', optimizationMap);

  const toggleOptConfirmation = () => {
    setOpenOptConfirmation(!openOptConfirmation);
  };

  const redirectLink = useMemo(() => {
    const isAllImages = (() => {
      if (files.length > 0) {
        return files?.every((f) => f.type.includes('image'));
      }
      return false;
    })();

    const isAllVideos = (() => {
      if (files.length > 0) {
        return files?.every((f) => isVideo(f));
      }
      return false;
    })();

    const isAllAudios = (() => {
      if (files.length > 0) {
        return files?.every((f) => f.type.includes('audio'));
      }
      return false;
    })();

    const tabIndex = (() => {
      if (isAllImages) {
        return 3;
      }
      if (isAllVideos) {
        return 1;
      }
      if (isAllAudios) {
        return 2;
      }
      return 1;
    })();

    return `${spaceHomeRoute}/space/${spaceId}/manage-content?tabIndex=${tabIndex}&triggerRefresh=${Date.now()}`;
  }, [files, spaceId]);

  const handleSelectedFiles = (files) => {
    setFiles(files);
    if (files.length > 0) {
      files?.map((m) => {
        setOptimizationMap((prev) => prev.set(getFileKey(m.name), false));
      });
    }
  };

  const handleUploadError = async ({ contentId, spaceId }) => {
    await handleUpdateContentStatus({ contentId, ContentStatus: 4 }, spaceId);
  };

  const handleUpload = async () => {
    if (files.length === 0) {
      NotificationManager.warning('Please select atleast one file');
      return;
    }
    try {
      setUploading(true);
      setIsUploadComplete(false);

      const contentIds = [];

      setUploadMediaStatus(
        files.map((f) => ({
          name: f.name,
          size: f.size,
          type: f.type,
          file: f,
          progress: 0,
          isFailed: false
        }))
      );

      const promises = files.map(async (file) => {
        try {
          const mediaType = getMediaType(file.type);
          let duration = 0;
          if (mediaType !== 3) {
            duration = await getBlobDuration(file);
          }

          const optimize =
            mediaType === 3 ? true : optimizationMap.get(getFileKey(file.name));
          const getPayloadBasedOnMediaType = (() => {
            const common = {
              id: nanoid(),
              Title: `Quick Upload ${Date.now()}`,
              Filename: file.name,
              ContentType: file.type,
              Size: file.size,
              duration,
              MediaType: mediaType,
              quality: 3,
              tags: [],
              category: '',
              isDRM: false,
              isAES: false,
              streaming: false,
              optimization: optimize,
              adaptiveBitRate: false
            };
            if (mediaType === 3) {
              return {
                ...common,
                isGIF: file.type === 'image/gif',
                width: file?.width,
                height: file?.height
              };
            } else {
              return common;
            }
          })();
          const payload = [getPayloadBasedOnMediaType];

          let sasObj;
          try {
            const createdSas = await handleCreateSasUrl(payload, spaceId, true);
            if (createdSas?.isError || typeof createdSas === 'string') {
              return null; // Skip to the next file if there's an error
            }
            const { data } = createdSas;
            sasObj = data;
            const { sasUrl } = sasObj;

            if (sasObj) {
              const fileItems = [];
              fileItems.push(sasObj.items[0]);
              const contentId = fileItems[0].contentId;

              const fileSasObj = {
                items: fileItems,
                sasUrl
              };

              setUploadMediaStatus((prev) =>
                prev.map((m) => {
                  if (m.name === file.name) {
                    return {
                      ...m,
                      id: contentId,
                      progress: 0,
                      isFailed: false
                    };
                  } else {
                    return m;
                  }
                })
              );

              const { isError: isUploadingError } = await uploadFileToBlob(
                file,
                () => {
                  setUploadMediaStatus((prev) => {
                    const newList = prev.map((j) => {
                      if (j.id === contentId) {
                        return {
                          ...j,
                          progress: 50
                        };
                      } else {
                        return j;
                      }
                    });
                    return newList;
                  });
                },
                () => {},
                fileSasObj
              );
              if (!isUploadingError) {
                const { data, isError } = await updateContentUploadStatus(
                  {
                    ContentIds: [contentId],
                    UploadStatus: 3
                  },
                  spaceId
                );
                if (!isError) {
                  setUploadMediaStatus((prev) => {
                    const newList = prev.map((j) => {
                      if (j.id === contentId) {
                        return {
                          ...j,
                          progress: 70
                        };
                      } else {
                        return j;
                      }
                    });
                    return newList;
                  });
                  const encodedresp = await handleContentEncoding(
                    {
                      spaceId,
                      contentId,
                      MediaType: mediaType,
                      quality: 3,
                      isDRM: false,
                      isAES: false,
                      optimize,
                      adaptiveBitRate: false
                    },
                    false
                  );

                  if (!encodedresp?.isError && encodedresp?.data) {
                    await updateContentUploadStatus(
                      {
                        ContentIds: [contentId],
                        UploadStatus: 3
                      },
                      spaceId
                    );
                    await handleUpdateContentStatus(
                      { contentId, ContentStatus: 2 },
                      spaceId
                    );
                    contentIds.push(contentId);
                    setUploadMediaStatus((prev) => {
                      const newList = prev.map((j) => {
                        if (j.id === contentId) {
                          return {
                            ...j,
                            progress: 100
                          };
                        } else {
                          return j;
                        }
                      });
                      return newList;
                    });
                  } else {
                    await handleUploadError({
                      contentId,
                      spaceId
                    });
                    setUploadMediaStatus((prev) => {
                      const newList = prev.map((j) => {
                        if (j.id === contentId) {
                          return {
                            ...j,
                            progress: 0,
                            isFailed: true
                          };
                        } else {
                          return j;
                        }
                      });
                      return newList;
                    });
                    NotificationManager.error(
                      <IntlMessages id="content-opt-failed" />,
                      <IntlMessages id="req.failed" />,
                      3000,
                      null,
                      null,
                      ''
                    );
                  }
                } else {
                  setUploadMediaStatus((prev) => {
                    const newList = prev.map((j) => {
                      if (j.id === contentId) {
                        return {
                          ...j,
                          progress: 0,
                          isFailed: true
                        };
                      } else {
                        return j;
                      }
                    });
                    return newList;
                  });
                  await handleUploadError({
                    contentId,
                    spaceId
                  });
                  NotificationManager.error(
                    <IntlMessages id="failed-upload" />,
                    <IntlMessages id="req.failed" />,
                    3000,
                    null,
                    null,
                    ''
                  );
                }
              } else {
                await handleUploadError({
                  contentId,
                  spaceId
                });
                NotificationManager.error(
                  <IntlMessages id="failed-upload" />,
                  <IntlMessages id="req.failed" />,
                  3000,
                  null,
                  null,
                  ''
                );
              }
            }
          } catch (error) {
            console.log(error);
          }
        } catch (error) {
          console.error('error', file, error);
        }
      });
      await Promise.all(promises);

      if (isCreateStory) {
        setStoryStatus({
          isCreating: true,
          isError: false
        });
        const { isError } = await createStory({
          accessControl: {
            regionsRestriction: false,
            ipAddressRestriction: false,
            domainsRestriction: false,
            domains: null,
            minAllowedAge: null,
            allowedCode: null,
            expiryTimestamp: null
          },
          id: nanoid(),
          spaceId,
          isQuickUploadStory: true,
          name: `Quick-upload-story-${Date.now()}`,
          isEnabled: true,
          isDefault: true,
          autoplay: false,
          loop: true,
          items: contentIds.map((m, i) => ({
            contentId: m,
            order: i + 1,
            textContent: ''
          }))
        });

        if (isError) {
          NotificationManager.error('Failed to create story.Please try again');
          setStoryStatus({
            isCreating: false,
            isError: true
          });
        } else {
          setStoryStatus({
            isCreating: false,
            isError: false
          });
        }
      }

      setUploading(false);
      setIsUploadComplete(true);
    } catch (error) {
      console.error('error', error);
      setUploading(false);
    }
  };

  const handleOnClickMyContents = () => {
    toggle();
    history.push(redirectLink);
  };
  const handleOnClickMyStory = () => {
    toggle();
    history.push(`${spaceHomeRoute}/space/${spaceId}/stories`);
  };

  return (
    <>
      <ConfirmationModal
        openConfirmationModal={openOptConfirmation}
        toggleConfirmationModal={toggleOptConfirmation}
        handleConfirm={() => {
          toggleOptConfirmation();
          handleUpload();
        }}
        handleClose={() => {
          toggleOptConfirmation();
        }}
        type="confirmQuickOptimization"
      />
      <Modal
        isOpen={isOpen}
        toggle={toggle}
        centered
        contentClassName="border-radius-10"
        style={{
          minWidth: '50%',
          borderRadius: 20
        }}
        backdrop="static"
      >
        {isUploadComplete ? (
          <div className="up-sucess-container">
            <div className="up-content-container">
              <div className="upload-success-icon-container">
                <i className="simple-icon-check upload-success-icon" />
              </div>
              <div className="px-3">
                <p className="mb-1" style={{ fontSize: '17px' }}>
                  {isCreateStory ? (
                    <>
                      <IntlMessages id="story-one" />
                      <b>
                        &nbsp;
                        <IntlMessages id="story-two" />
                        &nbsp;
                      </b>
                      <IntlMessages id="story-three" />
                      <b>
                        &nbsp;
                        <IntlMessages id="story-four" />
                        &nbsp;
                      </b>
                      <IntlMessages id="story-five" />
                    </>
                  ) : (
                    <IntlMessages id={'UploadMediaSuccess-info'} />
                  )}
                </p>
                {isCreateStory && (
                  <p style={{ fontSize: '17px' }}>
                    <IntlMessages id="Thank you for your patience!" />
                  </p>
                )}
              </div>
              <div className="d-flex flex-row align-items-center justify-content-center gap-1 flex-wrap mb-4 mt-2">
                <Button
                  style={{ fontSize: 14, width: 200 }}
                  color="primary"
                  onClick={handleOnClickMyContents}
                >
                  <IntlMessages id="view-my-contents" />
                </Button>
                {isCreateStory && (
                  <Button
                    style={{ fontSize: 14, width: 200 }}
                    outline
                    color="primary"
                    onClick={handleOnClickMyStory}
                  >
                    <IntlMessages id="view-my-story" />
                  </Button>
                )}
              </div>
            </div>
          </div>
        ) : uploading ? (
          <div className="progressBar-container-house p-3">
            <div className="dashboard-media-upload-container progressBar-container">
              {uploadMediaStatus.map((m, i) => (
                <UploadingCard data={m} key={m?.name} />
              ))}
              {isCreateStory && storyStatus.isCreating && (
                <div className="d-flex w-full centered">
                  Creating Story{' '}
                  <Spinner size="sm" color="info">
                    Loading...
                  </Spinner>
                </div>
              )}
            </div>
          </div>
        ) : (
          <ModalBody className="d-flex flex-column pt-0 dotted-border m-4 p-3">
            <ModalHeader toggle={toggle} />
            <div className="my-4">
              <InfoMessage
                textAlign="start"
                message="Your contents wil get uploaded to the space: "
              />
              <span className="font-bolder">{name}</span>
            </div>

            <SelectZone
              dropzone={dropzone}
              handleSelectedFiles={handleSelectedFiles}
              selectedFiles={files}
            />

            <div className="my-4">
              <p>You can add upto 5 contents</p>
              <div style={{ maxHeight: '30vh', overflowY: 'scroll' }}>
                {files.map((m, i) => {
                  return (
                    <SelectedContentCard
                      key={m.name}
                      index={i}
                      file={m}
                      mediaType={getMediaType(m.type)}
                      removeContent={(title) => {
                        const removeFiles = files.filter(
                          (f) => f.name !== title
                        );
                        setFiles(removeFiles);
                      }}
                      size={m?.size ?? 0}
                      title={m?.name ?? 'Media Title'}
                      toggleOptimize={() => {
                        const keyOfTheMap = getFileKey(m?.name);
                        const updatedMap = new Map(optimizationMap);
                        updatedMap.set(
                          keyOfTheMap,
                          !updatedMap.get(keyOfTheMap)
                        );
                        setOptimizationMap(updatedMap);
                      }}
                      optimizationEnabled={optimizationEnabled}
                      optimize={optimizationMap.get(getFileKey(m?.name))}
                    />
                  );
                })}
              </div>
            </div>
            <div className="d-flex flex-row align-items-center justify-content-center mb-4">
              <span className="media-label-title">
                <IntlMessages id="Create Story" />
              </span>
              <Switch
                className="custom-switch custom-switch-primary custom-switch-small ml-3"
                checked={isCreateStory}
                onClick={() => setIsCreateStory(!isCreateStory)}
              />
            </div>
            <div className="centered">
              <Button
                color="primary"
                style={{ width: 150 }}
                onClick={() => {
                  const atleastOneVideo = files?.some(
                    (f) =>
                      isVideo(f) && !optimizationMap.get(getFileKey(f.name))
                  );
                  const atleastOneAudio = files?.some(
                    (f) =>
                      f.type.includes('audio') &&
                      !optimizationMap.get(getFileKey(f.name))
                  );

                  if (atleastOneAudio || atleastOneVideo) {
                    setOpenOptConfirmation(true);
                    return;
                  }
                  handleUpload();
                }}
              >
                Upload
              </Button>
            </div>
          </ModalBody>
        )}
      </Modal>
    </>
  );
};

const UploadingCard = ({ data }) => {
  const m = data;
  return (
    <Card
      style={{ border: m.isFailed ? '1px solid red' : '' }}
      className="d-flex mb-2 flex-row p-2 align-items-center justify-content-center"
    >
      <IconDecider
        mediaType={
          m.type.includes('image') ? 3 : m.type.includes('video') ? 1 : 2
        }
        file={m.file}
      />
      <div className="d-flex flex-column w-full px-2 align-items-center justify-content-center">
        {m.isFailed ? (
          <p className="text-center" style={{ fontSize: 12, color: 'red' }}>
            Failed
          </p>
        ) : (
          <Progress
            animated
            striped
            max="100"
            value={m.progress}
            className="innerloop-progress-bar"
          />
        )}
        <p className="mb-0 w-full text-center" style={{ fontSize: 12 }}>
          {m.name}
        </p>
      </div>
    </Card>
  );
};

const ModalHeader = ({ toggle }) => {
  return (
    <Row className="d-flex align-items-start justify-content-between w-full m-0">
      <span style={{ fontSize: 18 }} className="mb-0 font-bolder">
        Upload Contents
      </span>
      <span onClick={toggle}>
        <i className="simple-icon-close close-btn" />
      </span>
    </Row>
  );
};

const SelectedContentCard = ({
  removeContent,
  mediaType,
  title,
  size = 0,
  file,
  optimize = false,
  toggleOptimize = () => {},
  index,
  optimizationEnabled
}) => {
  const id = `SelectedContentCard-${index}`;
  return (
    <Card
      className="mb-2 d-flex flex-row w-full justify-content-between px-3 py-2 align-items-center border-10"
      style={{ border: '1px solid #c0c0c052' }}
    >
      <IconDecider mediaType={mediaType} file={file} />
      <div className="w-70">
        <p className="m-0 font-bolder text-ellipsis">{title}</p>
        <p className="m-0" style={{ fontSize: 11 }}>
          {returnFileSize(size).sizeText}
        </p>
      </div>
      {mediaType !== 3 && (
        <div
          className="d-flex flex-row align-items-center"
          aria-disabled={!optimizationEnabled}
        >
          <InfoPopover
            infoId={id}
            iconClassName="text-primary"
            style={{
              fontSize: 14
            }}
            message="opt-quick-upload"
          />
          &nbsp;
          <span style={{ textWrap: 'nowrap', fontSize: 14, fontWeight: '700' }}>
            <IntlMessages id="up.optimize" />
          </span>
          <Switch
            className="custom-switch custom-switch-primary custom-switch-small ml-3"
            checked={optimize}
            onClick={toggleOptimize}
          />
        </div>
      )}
      <span
        className="w-10 d-flex flex-row align-items-center justify-content-end"
        onClick={() => removeContent(title)}
      >
        <i className="simple-icon-close close-btn c-pointer" />
      </span>
    </Card>
  );
};

const IconDecider = ({ mediaType, file }) => {
  const videoRef = useRef(null);
  const fileUrl = useRef(URL.createObjectURL(file));

  useEffect(() => {
    const currentVideo = videoRef.current;

    return () => {
      // Pause and clean up on unmount
      if (currentVideo) {
        currentVideo.pause();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
      URL.revokeObjectURL(fileUrl.current);
    };
  }, [file]);

  const icon =
    mediaType === 1
      ? 'simple-icon-camrecorder'
      : mediaType === 2
      ? 'simple-icon-earphones'
      : 'simple-icon-picture';
  return (
    <div className="w-10 centered">
      {mediaType === 3 ? (
        <img
          alt={file.name}
          height="60"
          width="100%"
          src={URL.createObjectURL(file)}
        />
      ) : (
        <video
          ref={videoRef}
          muted
          loop
          autoPlay
          height="60"
          width="100%"
          src={fileUrl.current}
          onPlay={() => console.log('Playing:', file.name)}
        />
      )}
      {/* <i className={icon} style={{ fontSize: 26 }} /> */}
    </div>
  );
};

const SelectZone = ({ dropzone, handleSelectedFiles, selectedFiles }) => {
  return (
    <MediaDropzone
      className="mb-3"
      ref={dropzone}
      selectedFiles={selectedFiles}
      handleContinue={(props) => handleSelectedFiles(props)}
    />
  );
};

export default QuickUploadModal;
