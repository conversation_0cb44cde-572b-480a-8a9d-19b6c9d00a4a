.warning-info {
  background: rgba(255, 252, 223, 0.4941176471);
  border-radius: 10px;
  padding: 10px 5px;
  color: #000;
  border: 1px solid #FFFCDF;
  margin: 1rem 0px;
}
.warning-info p {
  margin-bottom: 0px;
}

.dubCard {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  border: 1px solid silver;
  margin-bottom: 1.5rem;
}
.dubCard .preview {
  width: 15%;
}
.dubCard .dubcard-mid {
  display: flex;
  width: 55%;
  justify-content: space-between;
  flex-direction: row;
}
.dubCard .buttons {
  width: 25%;
}
@media (max-width: 1150px) {
  .dubCard .dubcard-mid {
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
  }
}/*# sourceMappingURL=dubbing.css.map */