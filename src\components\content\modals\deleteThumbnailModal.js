import React from 'react';
import ConfirmationModal from 'components/common/ConfirmationModal';
import { NotificationManager } from 'components/common/react-notifications';
import { deleteThumbnail } from 'functions/api/spacesApi';
import IntlMessages from 'helpers/IntlMessages';

const DeleteThumbnailModal = ({
  openModal,
  toggleModal,
  mediaTitle,
  setDeletedThumbnailName,
  selectedThumbnail,
  setSelectedThumbnail,
  spaceId,
  contentId,
  isPosterImage,
  setUpdating
}) => {
  const handleConfirm = async () => {
    toggleModal();
    try {
      const resp = await deleteThumbnail(spaceId, contentId, {
        thumbnail: mediaTitle
      });

      if (resp && !resp?.isError) {
        if (selectedThumbnail === mediaTitle) {
          setSelectedThumbnail('');
        }
        setUpdating(true);
        NotificationManager.success(
          isPosterImage ? (
            <IntlMessages id="poster-del-sucess" />
          ) : (
            <IntlMessages id="thumb-del-sucess" />
          ),
          <IntlMessages id="req.success" />,
          3000,
          null,
          null,
          ''
        );
      } else if (resp && !resp.isError) {
        NotificationManager.error(
          `${resp?.data}`,
          <IntlMessages id="req.failed" />,
          3000,
          null,
          null,
          ''
        );
      }
      setDeletedThumbnailName('');
    } catch (e) {
      NotificationManager.error(
        <IntlMessages id="team-invitation-error" />,
        <IntlMessages id="req.failed" />,
        3000,
        null,
        null,
        ''
      );
    }
  };

  const handleClose = () => {
    toggleModal();
  };

  return (
    <ConfirmationModal
      openConfirmationModal={openModal}
      toggleConfirmationModal={toggleModal}
      handleConfirm={handleConfirm}
      handleClose={handleClose}
      type={isPosterImage ? 'deletePosterImage' : 'deleteThumbnail'}
      mediaTitle={mediaTitle}
    />
  );
};

export default DeleteThumbnailModal;
