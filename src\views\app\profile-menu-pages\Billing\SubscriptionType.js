import IntlMessages from 'helpers/IntlMessages';
import React from 'react';
import { Col, FormGroup, Label } from 'reactstrap';

function SubscriptionType({ label, data }) {
  let style = {};
  if (data === 'Expired') {
    style = {
      color: 'red',
      fontSize: '16px',
      backgroundColor: '#fdcdcd',
      padding: '6px 35px',
      borderRadius: '20px',
      fontWeight: 600
    };
  } else {
    style = {
      color: 'green',
      fontSize: '16px',
      backgroundColor: '#ceffd6',
      padding: '6px 35px',
      borderRadius: '20px',
      fontWeight: 600
    };
  }

  return (
    <FormGroup row>
      <Label sm={2}>
        <b>
          <IntlMessages id={label} />
        </b>
      </Label>

      <Col sm={10}>
        <span style={style}>{data}</span>
      </Col>
    </FormGroup>
  );
}

export default SubscriptionType;
