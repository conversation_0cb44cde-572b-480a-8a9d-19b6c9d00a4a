/* eslint-disable no-unused-vars */
import IntlMessages from 'helpers/IntlMessages';
import React, { useEffect, useState } from 'react';
import { Card, CardHeader, CardBody } from 'reactstrap';
import UsageDetailsCard from './UsageDetailsCard';
import { AI_OPERATION_TYPE } from 'constants/defaultValues';
import { secondsToTimeString } from 'helpers/Utils';

const FeaturesAccessibilty = ({ data }) => {
  const [details, setDetails] = useState({
    left: [],
    right: []
  });

  useEffect(() => {
    if (data) {
      const aesCountLimit = data?.aesCountLimitPerMonth ?? 0;
      const aesCount = data?.aesCountCurrentMonth ?? 0;
      const currentTeamMembersNo = data?.currentTeamMembersNo ?? 0;
      const teamMembersNo = data?.teamMembersNo ?? 0;
      const drmLicenceCurrentCount = data?.drmLicenceCurrentCount ?? 0;
      const drmLicenseCountLimitPerMonth =
        data?.drmLicenseCountLimitPerMonth ?? 0;

      const ytDetailedInsights = data?.ytDetailedInsights ?? 0;
      const ytDetailedInsightsLimit = (() => {
        const count =
          data?.ai.find(
            (f) => f.aiOperationType === AI_OPERATION_TYPE.Description
          )?.count ?? 0;
        if (count > 0) {
          return count;
        }
        return 0;
      })();

      const imgDetailedInsights = data?.imgDetailedInsights ?? 0;
      const imgDetailedInsightsLimit = (() => {
        const count =
          data?.ai.find(
            (f) => f.aiOperationType === AI_OPERATION_TYPE.ImageInSight
          )?.count ?? 0;
        if (count > 0) {
          return count;
        }
        return 0;
      })();

      const audVidDetailedInsights = data?.audVidDetailedInsights ?? 0;
      const audVidDetailedInsightsLimit = (() => {
        const count =
          data?.ai.find(
            (f) => f.aiOperationType === AI_OPERATION_TYPE.Description
          )?.duration ?? 0;
        if (count > 0) {
          return count;
        }
        return 0;
      })();

      const storyDetailedInsights = data?.storyDetailedInsights ?? 0;
      const storyDetailedInsightsLimit = (() => {
        const count =
          data?.ai.find(
            (f) => f.aiOperationType === AI_OPERATION_TYPE.StoryInsights
          )?.count ?? 0;
        if (count > 0) {
          return count;
        }
        return 0;
      })();

      const ytPersonalizedInsights = data?.ytPersonalizedInsights ?? 0;
      const ytPersonalizedInsightsLimit = (() => {
        const count =
          data?.ai.find(
            (f) => f.aiOperationType === AI_OPERATION_TYPE.Narrative
          )?.count ?? 0;

        if (count > 0) {
          return count;
        }
        return 0;
      })();

      const imgPersonalizedInsights = data?.imgPersonalizedInsights ?? 0;
      const imgPersonalizedInsightsLimit = (() => {
        const count =
          data?.ai.find(
            (f) => f.aiOperationType === AI_OPERATION_TYPE.Narrative
          )?.count ?? 0;

        if (count > 0) {
          return count;
        }
        return 0;
      })();

      const audVidPersonalizedInsights = data?.audVidPersonalizedInsights ?? 0;
      const audVidPersonalizedInsightsLimit = (() => {
        const count =
          data?.ai.find(
            (f) => f.aiOperationType === AI_OPERATION_TYPE.Narrative
          )?.duration ?? 0;

        if (count > 0) {
          return count;
        }
        return 0;
      })();

      const storyPersonalizedInsights = data?.storyPersonalizedInsights ?? 0;
      const storyPersonalizedInsightsLimit = (() => {
        const count =
          data?.ai.find(
            (f) => f.aiOperationType === AI_OPERATION_TYPE.Narrative
          )?.count ?? 0;
        if (count > 0) {
          return count;
        }
        return 0;
      })();

      const transribeUsage = data?.transribeUsage ?? 0;
      const transribeUsageLimit = (() => {
        const count =
          data?.ai.find(
            (f) => f.aiOperationType === AI_OPERATION_TYPE.AutoTranscribe
          )?.duration ?? 0;
        if (count > 0) {
          return count;
        }
        return 0;
      })();

      const clippingUsage = data?.clippingUsage ?? 0;
      const clippingUsageLimit = (() => {
        const count =
          data?.ai.find(
            (f) => f.aiOperationType === AI_OPERATION_TYPE.AutoClipping
          )?.duration ?? 0;
        if (count > 0) {
          return count;
        }
        return 0;
      })();

      const repurposeUsage = data?.repurposeUsage ?? 0;
      const repurposeUsageLimit = (() => {
        const count =
          data?.ai.find(
            (f) => f.aiOperationType === AI_OPERATION_TYPE.Repurpose
          )?.duration ?? 0;
        if (count > 0) {
          return count;
        }
        return 0;
      })();

      const dubbingUsage = data?.dubbingUsage ?? 0;
      const dubbingUsageLimit = (() => {
        const count =
          data?.ai.find(
            (f) => f.aiOperationType === AI_OPERATION_TYPE.AutoDubbing
          )?.duration ?? 0;
        if (count > 0) {
          return count;
        }
        return 0;
      })();

      const chatUsage = data?.chatUsage ?? 0;
      const chatUsageLimit = (() => {
        const count =
          data?.ai.find((f) => f.aiOperationType === AI_OPERATION_TYPE.Chat)
            ?.count ?? 0;
        if (count > 0) {
          return count;
        }
        return 0;
      })();

      const qnaUsage = data?.qnaUsage ?? 0;
      const qnaUsageLimit = (() => {
        const count =
          data?.ai.find(
            (f) => f.aiOperationType === AI_OPERATION_TYPE.ContentQnA
          )?.count ?? 0;
        if (count > 0) {
          return count;
        }
        return 0;
      })();

      setDetails({
        left: [
          {
            id: 4,
            title: 'Story Detailed Insights ',
            isActive: data?.ai.find(
              (f) => f.aiOperationType === AI_OPERATION_TYPE.StoryInsights
            )?.enable,
            aiEnabled: true,
            values: [
              {
                left: 'Story Analyzer Count ',
                percent: storyDetailedInsightsLimit
                  ? (
                      (storyDetailedInsights / storyDetailedInsightsLimit) *
                      100
                    ).toFixed(1)
                  : 0,
                right: `${storyDetailedInsights} / ${storyDetailedInsightsLimit} Queries`
              }
            ]
          },
          {
            id: 44,
            title: 'Image Detailed Insights ',
            isActive: data?.ai.find(
              (f) => f.aiOperationType === AI_OPERATION_TYPE.ImageInSight
            )?.enable,
            aiEnabled: true,
            values: [
              {
                left: 'Image Analyzer Count ',
                percent: imgDetailedInsightsLimit
                  ? (
                      (imgDetailedInsights / imgDetailedInsightsLimit) *
                      100
                    ).toFixed(1)
                  : 0,
                right: `${imgDetailedInsights} / ${imgDetailedInsightsLimit} Queries`
              }
            ]
          },
          {
            id: 5,
            title: 'Detailed Insights ',
            isActive: data?.ai.find(
              (f) => f.aiOperationType === AI_OPERATION_TYPE.Description
            )?.enable,
            aiEnabled: true,
            values: [
              {
                left: 'Youtube Analyser Count',
                percent: ytDetailedInsightsLimit
                  ? (
                      (ytDetailedInsights / ytDetailedInsightsLimit) *
                      100
                    ).toFixed(1)
                  : 0,
                right: `${ytDetailedInsights} / ${ytDetailedInsightsLimit} Queries`
              },
              {
                left: 'Audio / Video Analyzer Duration ',
                percent: audVidDetailedInsightsLimit
                  ? (
                      (audVidDetailedInsights / audVidDetailedInsightsLimit) *
                      100
                    ).toFixed(1)
                  : 0,
                right: `${secondsToTimeString(
                  audVidDetailedInsights
                )} / ${secondsToTimeString(audVidDetailedInsightsLimit)}`
              }
            ]
          },
          {
            id: 6,
            title: 'Personalized Insights ',
            isActive: data?.ai.find(
              (f) => f.aiOperationType === AI_OPERATION_TYPE.Narrative
            )?.enable,
            aiEnabled: true,
            values: [
              {
                left: 'Image Analyzer Query Count',
                percent: imgPersonalizedInsightsLimit
                  ? (
                      (imgPersonalizedInsights / imgPersonalizedInsightsLimit) *
                      100
                    ).toFixed(1)
                  : 0,
                right: `${imgPersonalizedInsights} / ${imgPersonalizedInsightsLimit} Queries`
              },
              {
                left: 'Youtube Analyser Count',
                percent: ytPersonalizedInsightsLimit
                  ? (
                      (ytPersonalizedInsights / ytPersonalizedInsightsLimit) *
                      100
                    ).toFixed(1)
                  : 0,
                right: `${ytPersonalizedInsights} / ${ytPersonalizedInsightsLimit} Queries`
              },
              {
                left: 'Audio / Video Analyzer Duration',
                percent: audVidPersonalizedInsightsLimit
                  ? (
                      (audVidPersonalizedInsights /
                        audVidPersonalizedInsightsLimit) *
                      100
                    ).toFixed(1)
                  : 0,
                right: `${secondsToTimeString(
                  audVidPersonalizedInsights
                )} / ${secondsToTimeString(audVidPersonalizedInsightsLimit)}`
              },
              {
                left: 'Story Analyser Count',
                percent: storyPersonalizedInsightsLimit
                  ? (
                      (storyPersonalizedInsights /
                        storyPersonalizedInsightsLimit) *
                      100
                    ).toFixed(1)
                  : 0,
                right: `${storyPersonalizedInsights} / ${storyPersonalizedInsightsLimit} Queries`
              }
            ]
          },
          {
            id: 11,
            title: 'Ai Transcription',
            isActive: data?.ai.find(
              (f) => f.aiOperationType === AI_OPERATION_TYPE.AutoTranscribe
            )?.enable,
            aiEnabled: true,
            left: 'Ai Transcription Content Duration',
            percent: transribeUsageLimit
              ? ((transribeUsage / transribeUsageLimit) * 100).toFixed(1)
              : 0,
            right: `${secondsToTimeString(
              transribeUsage
            )} / ${secondsToTimeString(transribeUsageLimit)}`
          },
          {
            id: 13,
            title: 'Video Auto Crop',
            isActive: data?.ai.find(
              (f) => f.aiOperationType === AI_OPERATION_TYPE.Repurpose
            )?.enable,
            aiEnabled: true,
            left: 'Video Auto Crop Content Duration ',
            percent: repurposeUsageLimit
              ? ((repurposeUsage / repurposeUsageLimit) * 100).toFixed(1)
              : 0,
            right: `${secondsToTimeString(
              repurposeUsage
            )} / ${secondsToTimeString(repurposeUsageLimit)}`
          }
        ],
        right: [
          {
            id: 1,
            title: 'subs.fd.access-mgmt',
            isActive: data?.accessManagement,
            values: [
              {
                left: 'Team Members',
                right: `${currentTeamMembersNo} / ${teamMembersNo} Team members`,
                percent: teamMembersNo
                  ? ((currentTeamMembersNo / teamMembersNo) * 100).toFixed(1)
                  : 0
              }
            ]
          },
          {
            id: 11,
            title: 'subs.fd.drm',
            isActive: data?.drmEnabled,
            aiEnabled: false,
            infoMessage: 'subs.fd.drm-info',
            values: [
              {
                left: 'DRM Licensed Count (per month)',
                right: `${drmLicenceCurrentCount} / ${drmLicenseCountLimitPerMonth} DRM Licenced`,
                percent: drmLicenseCountLimitPerMonth
                  ? (
                      (drmLicenceCurrentCount / drmLicenseCountLimitPerMonth) *
                      100
                    ).toFixed(1)
                  : 0
              }
            ]
          },
          {
            id: 12,
            title: 'subs.fd.aes',
            isActive: data?.aesEnabled,
            infoMessage: 'ad-aes-info',
            aiEnabled: false,
            values: [
              {
                left: 'Advanced Encryption Standard (per month)',
                percent: aesCountLimit
                  ? ((aesCount / aesCountLimit) * 100).toFixed(1)
                  : 0,
                right: `${aesCount} / ${aesCountLimit}`
              }
            ]
          },
          {
            id: 12,
            title: 'Clipping',
            isActive: data?.ai.find(
              (f) => f.aiOperationType === AI_OPERATION_TYPE.AutoClipping
            )?.enable,
            aiEnabled: true,
            left: 'Clipping Content Duration',
            percent: clippingUsageLimit
              ? ((clippingUsage / clippingUsageLimit) * 100).toFixed(1)
              : 0,
            right: `${secondsToTimeString(
              clippingUsage
            )} / ${secondsToTimeString(clippingUsageLimit)}`
          },
          {
            id: 14,
            title: 'Dubbing',
            isActive: data?.ai.find(
              (f) => f.aiOperationType === AI_OPERATION_TYPE.AutoDubbing
            )?.enable,
            aiEnabled: true,
            left: 'Dubbing Content Duration',
            percent: dubbingUsageLimit
              ? ((dubbingUsage / dubbingUsageLimit) * 100).toFixed(1)
              : 0,
            right: `${secondsToTimeString(
              dubbingUsage
            )} / ${secondsToTimeString(dubbingUsageLimit)}`
          },
          {
            id: 11,
            title: 'subs.fd.chat',
            isActive: data?.ai.find(
              (f) => f.aiOperationType === AI_OPERATION_TYPE.Chat
            )?.enable,
            aiEnabled: true,
            left: 'Chat response count',
            percent: chatUsageLimit
              ? ((chatUsage / chatUsageLimit) * 100).toFixed(1)
              : 0,
            right: `${chatUsage} / ${chatUsageLimit}`
          },
          {
            id: 12,
            title: 'subs.fd.qna',
            isActive: data?.ai.find(
              (f) => f.aiOperationType === AI_OPERATION_TYPE.ContentQnA
            )?.enable,
            aiEnabled: true,
            left: 'QnA response count',
            percent: qnaUsageLimit
              ? ((qnaUsage / qnaUsageLimit) * 100).toFixed(1)
              : 0,
            right: `${qnaUsage} / ${qnaUsageLimit}`
          },
          {
            id: 2,
            title: 'subs.fd.workflow',
            isActive: data?.workflowEnabled,
            showBarGraph: false
          },
          {
            id: 3,
            title: 'subs.fd.ds-analytics',
            isActive: data?.dashboardAnalytics,
            showBarGraph: false
          },
          {
            id: 4,
            title: 'subs.fd.ss-analytics',
            isActive: data?.spaceSummaryAnalytics,
            showBarGraph: false
          },
          {
            id: 5,
            title: 'subs.fd.ns-enabled',
            isActive: data?.nonStreamingEnabled,
            showBarGraph: false
          },
          {
            id: 6,
            title: 'subs.fd.rec-enabled',
            isActive: data?.recordingEnabled,
            showBarGraph: false
          },
          {
            id: 7,
            title: 'subs.fd.whitelabel',
            isActive: data?.allowPlayerWhitelabel,
            showBarGraph: false
          },
          {
            id: 8,
            title: 'subs.fd.allow-download',
            isActive: data?.allowDownload,
            showBarGraph: false
          },
          {
            id: 9,
            title: 'subs.fd.player-analytics',
            isActive: data?.playerAnalytics,
            showBarGraph: false
          },
          {
            id: 10,
            title: 'subs.fd.opt',
            isActive: data?.optimization,
            showBarGraph: false
          }
        ]
      });
    }
  }, [data]);

  return (
    <Card className="mt-3 p-2" style={{ borderRadius: '10px' }}>
      <CardHeader className="p-3 headerBorder">
        <p className="m-0" style={{ fontSize: '18px' }}>
          <b>
            <IntlMessages id="subs-feature" />
          </b>
        </p>
      </CardHeader>
      <CardBody className="subscriptionDetailsCards">
        <div className="AccCard p-2">
          {details?.left?.map((d) => (
            <UsageDetailsCard key={d.id} data={d} />
          ))}
        </div>
        <div className="AccCard p-2">
          {details?.right?.map((d) => (
            <UsageDetailsCard
              key={d.id}
              data={d}
              showBarGraph={d.showBarGraph}
            />
          ))}
        </div>
      </CardBody>
    </Card>
  );
};

export default FeaturesAccessibilty;
