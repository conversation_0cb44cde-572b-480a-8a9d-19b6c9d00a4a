/* eslint-disable react/destructuring-assignment */
/* eslint-disable import/no-extraneous-dependencies */
import React, { Component } from 'react';
import DropzoneComponent from 'react-dropzone-component';
import 'dropzone/dist/min/dropzone.min.css';
import IntlMessages from 'helpers/IntlMessages';
import { NotificationManager } from 'components/common/react-notifications';

const ReactDOMServer = require('react-dom/server');

const dropzoneComponentConfig = {
  postUrl: '#'
};

const dropzoneConfig = () => {
  return {
    thumbnailHeight: 160,
    thumbnailWidth: 160,
    // maxFilesize:  52428800,
    maxFiles: 5,
    acceptedFiles: 'image/jpeg, image/jpg, image/png, image/gif, image/webp',
    autoProcessQueue: false,
    uploadMultiple: true,
    dictDefaultMessage: 'Click to browse OR Drop file here',
    previewTemplate: ReactDOMServer.renderToStaticMarkup(
      <div className="dz-preview dz-file-preview mb-3">
        <div className="d-flex flex-row ">
          <div className="p-0 w-30 position-relative">
            <div className="dz-error-mark">
              <span>
                <i />{' '}
              </span>
            </div>
            <div className="dz-success-mark">
              <span>
                <i />
              </span>
            </div>
            <div className="preview-container">
              {/*  eslint-disable-next-line jsx-a11y/alt-text */}
              <img data-dz-thumbnail className="border-0 img-thumbnail" />
              <i className="simple-icon-doc preview-icon" />
            </div>
          </div>
          <div className="pl-3 pt-2 pr-2 pb-1 w-70 dz-details position-relative">
            <div>
              {' '}
              <span data-dz-name />{' '}
            </div>
            <div className="text-primary text-extra-small" data-dz-size />
            <div className="dz-progress">
              <span className="dz-upload" data-dz-uploadprogress />
            </div>
            <div className="dz-error-message">
              <span data-dz-errormessage />
            </div>
          </div>
        </div>
        <a href="#/" className="remove" data-dz-remove>
          {' '}
          <i className="glyph-icon simple-icon-trash" />{' '}
        </a>
      </div>
    ),
    headers: { 'My-Awesome-Header': 'header value' }
  };
};

export default class Dropzone extends Component {
  constructor(props) {
    super(props);

    this.state = {
      imageFiles: [],
      showOneError: true
    };
  }

  componentDidUpdate(prevProps, prevState) {
    const { imageFiles } = this.state;
    const { handleContinue } = this.props;
    if (prevState.imageFiles !== imageFiles) {
      handleContinue(imageFiles);
    }
  }

  clear() {
    this.myDropzone.removeAllFiles(true);
  }

  render() {
    return (
      <DropzoneComponent
        config={dropzoneComponentConfig}
        djsConfig={dropzoneConfig()}
        eventHandlers={{
          init: (dropzone) => {
            this.myDropzone = dropzone;
          },
          addedfile: (file) => {
            const isImgAlreadyPresent = this.state.imageFiles.some(
              (s) => s.name === file.name
            );
            if (isImgAlreadyPresent) {
              NotificationManager.warning(
                `Image with name ${file.name} is already present`,
                '',
                3000,
                null,
                null,
                ''
              );
              this.myDropzone.removeFile(file);
            }
            if (this.state.imageFiles.length < 5) {
              this.setState((state) => ({
                ...state,
                imageFiles: [...state.imageFiles, file]
              }));
            }
          },
          removedfile: (file) => {
            this.setState((state) => ({
              ...state,
              imageFiles: state.imageFiles.filter(
                ({ name }) => name !== file.name
              )
            }));
          },
          error: (file, msg) => {
            if (this.state.showOneError === true) {
              if (msg === 'You can not upload any more files.') {
                NotificationManager.warning(
                  <IntlMessages id="spaces.max5files" />,
                  '',
                  3000,
                  null,
                  null,
                  ''
                );

                this.setState((state) => ({
                  ...state,
                  showOneError: false
                }));
              } else
                NotificationManager.error(
                  msg,
                  <IntlMessages id="req.failed" />,
                  3000,
                  null,
                  null,
                  ''
                );
            }
            this.myDropzone.removeFile(file);
          }
        }}
      />
    );
  }
}
