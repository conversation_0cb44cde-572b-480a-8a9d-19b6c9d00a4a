/* eslint-disable react/no-array-index-key */
import LoadingSkeleton from 'components/LoadingSkeleton';
import React from 'react';
import { Row } from 'reactstrap';

const SingleSALoading = () => {
  return (
    <div className="SACard">
      <div>
        <LoadingSkeleton className="SACardImg" />
      </div>
      <div className="w-full h-full">
        <LoadingSkeleton
          style={{ height: '15px', width: '90%', marginTop: '1rem' }}
          containerClassName="d-flex align-items-center justify-content-center"
        />
        <LoadingSkeleton
          style={{ height: '10px', width: '50%', marginTop: '1px' }}
          containerClassName="d-flex align-items-center justify-content-center"
        />
      </div>
    </div>
  );
};

const TopListLoading = () => {
  return (
    <Row className="m-0 p-0 justify-content-start">
      {new Array(5).fill('').map((m, i) => (
        <SingleSALoading key={i} />
      ))}
    </Row>
  );
};

const ShowcaseLoading = () => {
  return (
    <div className="d-flex flex-row align-items-center justify-content-center flex-wrap gap-2">
      {new Array(20).fill('').map((m, i) => (
        <SingleSALoading key={i} />
      ))}
    </div>
  );
};

export { TopListLoading, SingleSALoading, ShowcaseLoading };
