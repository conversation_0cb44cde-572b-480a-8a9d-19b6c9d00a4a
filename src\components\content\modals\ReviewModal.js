/* eslint-disable no-nested-ternary */
/* eslint-disable no-else-return */
import Avtar from 'components/common/Avtar';
import { NotificationManager } from 'components/common/react-notifications';
import MainLoader from 'components/loaders/MainLoader';
import { getUserRole } from 'functions/api';
import {
  createWorkflow,
  getUsersForWorkflow,
  getWorkflows
} from 'functions/api/mgmtApi';
import { handleDeleteShowcase } from 'functions/api/showcaseApi';
import {
  deleteShare,
  getStoryById,
  handleUpdateContentStatus
} from 'functions/api/spacesApi';
import IntlMessages from 'helpers/IntlMessages';
import {
  RoleTypes,
  formatDateNTime,
  getCurrentColor,
  getCurrentUser
} from 'helpers/Utils';
import React, { useEffect, useState } from 'react';
import { NavLink } from 'react-router-dom';
import {
  Button,
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  FormGroup,
  Input,
  Label,
  Modal,
  ModalBody,
  UncontrolledDropdown
} from 'reactstrap';
import ReadyToDraftConfirmation from './ReadyToDraftConfirmation';
import WorkflowPill from 'components/WorkflowPill';
import ConfirmationModal from 'components/common/ConfirmationModal';
import { useSelector } from 'react-redux';

const workflowEnum = {
  NONE: 0,
  APPROVED: 1,
  REVIEWED: 2,
  ASSIGNEDFORREVIEW: 3,
  REJECTED: 4
};

const ReviewModal = ({
  modalOpen,
  toggleModal,
  content,
  role,
  setReloadRequire = () => {},
  setWorkFlowStatus,
  isStory,
  disableEnableSpaceStory,
  inReview
}) => {
  const [mode, setMode] = useState();
  const [eligibleReviewers, setEligibleReviewers] = useState([]);
  const [selectedReviewer, setSelectedReviewer] = useState();
  const [selectedReviewerName, setSelectedReviewerName] = useState();
  const [isReviewersLoading, setIsReviewersLoading] = useState(false);
  const [workflow, setWorkflow] = useState();
  const [review, setReview] = useState();
  const [assignReviewComment, setAssignReviewComment] = useState();
  const [approvalComment, setApprovalComment] = useState();
  const [rejectComment, setRejectComment] = useState();
  const [loading, setLoading] = useState(false);
  const [accountOwner, setAccountOwner] = useState();
  const [roleType, setRoleType] = useState(role);
  const [draftModal, setDraftModal] = useState(false);
  const [message] = useState(isStory ? 'story' : 'content');
  const [fullName, setFullName] = useState('');
  const currentUserId = getCurrentUser()?.uid;
  const [contentStatus, setContentStatus] = useState(+content?.workflowStatus);
  const [openConfirmationModal, setOpenConfirmationModal] = useState(false);
  const [associatedStoryCount, setAssociatedStoryCount] = useState(0);
  const [openAssociatedConfirmation, setAssociatedConfirmation] =
    useState(false);
  const toggleAssociatedConfirmation = () =>
    setAssociatedConfirmation(!openAssociatedConfirmation);
  const { accessManagement } = useSelector((state) => state.subscription);

  useEffect(() => {
    if (modalOpen) {
      const handleEscape = (e) => {
        if (e.key === 'Escape') {
          if (openConfirmationModal) {
            setOpenConfirmationModal(false);
          } else {
            setOpenConfirmationModal(true);
          }
        }
      };
      window.addEventListener('keydown', handleEscape);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [modalOpen, openConfirmationModal]);

  useEffect(() => {
    if (content && typeof content?.workflowStatus === 'number') {
      setContentStatus(content?.workflowStatus);
    }
  }, [content]);

  const getRoleType = () => {
    getUserRole(content?.spaceId)
      .then((res) => {
        setRoleType(res?.data?.roleType ? res?.data?.roleType : res?.data);
      })
      .catch((err) => {
        console.error(err);
      });
  };

  useEffect(() => {
    const tenantDetails = localStorage.getItem('tenantDetails')
      ? JSON.parse(localStorage.getItem('tenantDetails'))
      : null;
    setAccountOwner(tenantDetails?.data?.selectedTenant?.ownerName);
    setFullName(tenantDetails.data.fullName);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!role) {
      getRoleType();
    } else {
      setRoleType(role);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [role]);

  const currentColor = getCurrentColor().includes('dark');

  const getRandomChars = (len) => {
    const chars =
      '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_';
    let result = '';
    // eslint-disable-next-line no-plusplus
    for (let i = 0; i < len; i++) {
      result += chars[Math.floor(Math.random() * chars.length)];
    }
    return result;
  };

  const getWorkflow = async () => {
    setLoading(true);
    const response = await getWorkflows(content?.spaceId, content?.id);
    setWorkflow(response);
    setLoading(false);
  };

  const getEligibleReviewers = async () => {
    setIsReviewersLoading(true);
    const reviewers = await getUsersForWorkflow(content?.spaceId);
    const allReviewers = reviewers?.items;
    if (allReviewers) {
      setEligibleReviewers(
        allReviewers?.filter((item) => item.id !== currentUserId)
      );
    } else {
      setEligibleReviewers([]);
    }
    setIsReviewersLoading(false);
  };

  // eslint-disable-next-line consistent-return
  const createflow = async () => {
    if (
      (contentStatus === 1 || contentStatus === 4) &&
      +roleType !== +RoleTypes.OWNER
    ) {
      return NotificationManager.error(
        <>
          <IntlMessages id="App-or-rej-worning-1" /> &nbsp;
          {accountOwner} &nbsp;
          <IntlMessages id="App-or-rej-worning-2" />
        </>,
        'Error',
        3000
      );
    }
    if (mode === '1') {
      if (workflow?.items[0]?.status === 1)
        return NotificationManager.error(
          `${message} is already approved!`,
          'Error',
          3000
        );
      if (!approvalComment)
        return NotificationManager.error(
          'Please enter a comment',
          'Error',
          3000
        );
    }
    if (mode === '2') {
      if (!review)
        return NotificationManager.error(
          'Please enter a comment',
          'Error',
          3000
        );
      if (workflow?.items[0]?.status === 1) {
        setDraftModal(true);
        setLoading(true);
        return null;
      }
    }
    if (mode === '3') {
      if (!selectedReviewer)
        return NotificationManager.error(
          'Please select a reviewer',
          'Error',
          3000
        );
      if (
        workflow?.items?.length > 0 &&
        workflow?.items[0]?.status === 3 &&
        workflow?.items[0]?.assignedUserId === selectedReviewer
      )
        return NotificationManager.error(
          `Selected reviewer is already assigned to this ${message}!`,
          'Error',
          3000
        );
      if (workflow?.items[0]?.status === 1) {
        setDraftModal(true);
        setLoading(true);
        return null;
      }
    }
    if (mode === '4') {
      if (workflow?.items[0]?.status === 4)
        return NotificationManager.error(
          `${message} is already rejected!`,
          'Error',
          3000
        );
      if (!rejectComment)
        return NotificationManager.error(
          'Please enter a comment',
          'Error',
          3000
        );
      if (rejectComment) {
        setDraftModal(true);
        setLoading(true);
        return null;
      }
    }
    setLoading(true);

    const respData = await createWorkflow(
      {
        id: getRandomChars(22),
        entityId: content?.id,
        shareId: content.shareId,
        entityKind: content?.mediaType ? 1 : 2,
        assignedUserId: mode === '3' ? selectedReviewer : null,
        comment: (() => {
          switch (mode) {
            case '1':
              return approvalComment;
            case '2':
              return review;
            case '3':
              return assignReviewComment;
            default:
              return '';
          }
        })(),

        status: mode
      },
      content?.spaceId
    );
    if (respData.isError) {
      NotificationManager.error(respData?.message, 'Error', 3000);
      setLoading(false);
      return setLoading(false);
    }
    if (respData?.data?.id) {
      setWorkFlowStatus({
        status: mode,
        id: content?.id,
        assignedUser: selectedReviewerName?.split(' (')[0] ?? fullName,
        shareId: content?.shareId,
        showcaseId: content?.showcaseId
      });
      setContentStatus(+mode);
    }
    setSelectedReviewer();
    setSelectedReviewerName();
    setReview();
    setApprovalComment();
    setRejectComment();
    await getWorkflow();
    if (!isStory) {
      setLoading(false);
    }

    setReloadRequire(true);
    return NotificationManager.success(
      `${message} ${
        // eslint-disable-next-line no-nested-ternary
        (() => {
          switch (+mode) {
            case 1:
              return 'approved';
            case 2:
              return 'reviewed';
            case 3:
              return 'assigned';
            default:
              return 'rejected';
          }
        })()
      } successfully`,
      'Success',
      3000
    );
  };

  useEffect(() => {
    setWorkflow();
    if (content) {
      getWorkflow();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [content]);

  useEffect(() => {
    if (roleType <= RoleTypes.CONTENT_MANAGER) setMode('1');
    else setMode('2');
  }, [roleType]);

  useEffect(() => {
    if (mode === '3' && eligibleReviewers.length === 0) getEligibleReviewers();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mode]);

  useEffect(() => {
    if (modalOpen) {
      if (roleType <= RoleTypes.CONTENT_MANAGER) setMode('1');
      else setMode('2');
    } else {
      setEligibleReviewers([]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [modalOpen]);

  // eslint-disable-next-line consistent-return
  const rejectWorkFlow = async (dontCheckAssociated = false) => {
    setDraftModal(false);
    let updateCnt = null;
    if (content?.mediaType) {
      console.log('dontCheckAssociated', dontCheckAssociated);
      updateCnt = await handleUpdateContentStatus(
        { contentId: content.id, ContentStatus: 3 },
        content.spaceId,
        true,
        dontCheckAssociated,
        true
      );
      if (+updateCnt?.data?.associatedStoryCount > 0) {
        setAssociatedStoryCount(+updateCnt?.data?.associatedStoryCount);
        toggleAssociatedConfirmation();
        return;
      }
      if (updateCnt.isError) {
        setLoading(false);
        NotificationManager.error('Something went wrong', 'Error', 3000);
        return;
      }
    } else if (content?.bucketNo) {
      const { data, isError } = await getStoryById({
        spaceId: content.spaceId,
        storyId: content.id
      });
      if (isError) {
        setLoading(false);
        NotificationManager.error('Something went wrong', 'Error', 3000);
        return;
      }
      if (data?.showcaseId) {
        const deleteShowcase = await handleDeleteShowcase(
          data.showcaseId,
          data.spaceId
        );
        if (deleteShowcase.isError) {
          setLoading(false);
          NotificationManager.error('Something went wrong', 'Error', 3000);
          return;
        }
      }
      if (data.shareId) {
        const deleteSr = await deleteShare(data.spaceId, data.shareId);
        if (deleteSr.isError) {
          setLoading(false);
          NotificationManager.error('Something went wrong', 'Error', 3000);
          return;
        }
      }
    } else {
      if (content?.showcaseId) {
        const deleteShowcase = await handleDeleteShowcase(
          content.showcaseId,
          content.spaceId
        );
        if (deleteShowcase.isError) {
          setLoading(false);
          NotificationManager.error('Something went wrong', 'Error', 3000);
          return;
        }
      }
      if (content.shareId) {
        const deleteSr = await deleteShare(content.spaceId, content.shareId);
        if (deleteSr.isError) {
          setLoading(false);
          NotificationManager.error('Something went wrong', 'Error', 3000);
          return;
        }
      }
    }
    const respData = await createWorkflow(
      {
        id: getRandomChars(22),
        entityId: content?.id,
        shareId: content.shareId,
        entityKind: content?.mediaType ? 1 : 2,
        assignedUserId: mode === '3' ? selectedReviewer : null,
        comment: (() => {
          switch (mode) {
            case '1':
              return approvalComment;
            case '2':
              return review;
            case '4':
              return rejectComment;
            case '3':
              return assignReviewComment;
            default:
              return '';
          }
        })(),

        status: mode
      },
      content?.spaceId,
      true
    );
    if (respData?.data?.id) {
      setWorkFlowStatus({
        status: mode,
        id: content?.id,
        shareId: null,
        showcaseId: null,
        assignedUser: selectedReviewerName?.split(' (')[0] ?? fullName
      });
      setContentStatus(+mode);
      if (mode === '4' && isStory) {
        await disableEnableSpaceStory(false, false);
      }
    }
    if (respData.isError) {
      setLoading(false);
      NotificationManager.error('Something went wrong', 'Error', 3000);
      return;
    }

    if (mode !== '1' && !isStory) {
      await getWorkflow();
    }
    setSelectedReviewer();
    setSelectedReviewerName();
    setReview();
    setApprovalComment();
    setRejectComment();
    if (inReview) {
      if (mode === '4') {
        toggleModal();
      }
      setLoading(false);
    }
    setReloadRequire(true);
    if (!updateCnt?.data?.associatedStoryCount > 0) {
      NotificationManager.success(
        `${message} ${
          // eslint-disable-next-line no-nested-ternary
          (() => {
            switch (+mode) {
              case 1:
                return 'approved';
              case 2:
                return 'reviewed';
              case 3:
                return 'assigned';
              default:
                return 'rejected';
            }
          })()
        } successfully`,
        'Success',
        3000
      );
    }
  };

  const showOnlyAssignedOrOwnerMessage = (() => {
    const isAssignedForReview = content === workflowEnum.ASSIGNEDFORREVIEW;
    const isAssignedToMe = currentUserId === content?.assignedUser?.id;
    const isOwner = +roleType === +RoleTypes.OWNER;
    const returnValue = isAssignedForReview && (isAssignedToMe || isOwner);
    // console.log({
    //   type: 'showOnlyAssignedOrOwnerMessage',
    //   isAssignedForReview,
    //   isAssignedToMe,
    //   isOwner,
    //   returnValue
    // });
    return returnValue;
  })();

  return (
    <>
      {openConfirmationModal && (
        <ConfirmationModal
          openConfirmationModal={openConfirmationModal}
          toggleConfirmationModal={() => setOpenConfirmationModal(false)}
          handleClose={() => {
            setOpenConfirmationModal(false);
          }}
          handleConfirm={() => {
            toggleModal();
            setOpenConfirmationModal(false);
          }}
        />
      )}
      {typeof associatedStoryCount === 'number' && associatedStoryCount > 0 && (
        <ConfirmationModal
          openConfirmationModal={openAssociatedConfirmation}
          toggleConfirmationModal={() => {
            toggleAssociatedConfirmation();
            toggleModal();
            setOpenConfirmationModal(false);
          }}
          handleConfirm={() => {
            rejectWorkFlow(true);
            toggleAssociatedConfirmation();
          }}
          handleClose={() => {
            toggleAssociatedConfirmation();
            toggleModal();
            setOpenConfirmationModal(false);
          }}
          type="review"
          mediaTitle={content?.title || content?.name}
          description={
            <p>
              {associatedStoryCount}&nbsp;
              <IntlMessages id="del-content-associated-story" />
            </p>
          }
        />
      )}
      <Modal
        isOpen={modalOpen}
        toggle={toggleModal}
        centered
        style={{
          minWidth: '80%'
        }}
        backdrop="static"
      >
        <ModalBody className="dotted-border m-4">
          <div className="d-flex justify-content-between align-items-start">
            <h2>{content?.title || content?.name}</h2>
            <ReadyToDraftConfirmation
              handleDraft={() => rejectWorkFlow(false)}
              modalOpen={draftModal}
              toggleModal={() => {
                setLoading(false);
                setDraftModal(!draftModal);
              }}
              message={`Your ${message} will not be available externally if it is already shared / showcased.`}
            />
            {loading && <MainLoader dataLoad />}
            <NavLink
              className="btn-link text-decoration-none"
              to="#"
              onClick={toggleModal}
              style={{
                marginTop: '-.3rem'
              }}
            >
              <i className="simple-icon-close close-btn" />
            </NavLink>
          </div>
          <div className="d-flex justify-content-center align-items-center font-weight-bold">
            <i className="simple-icon-info font-weight-bold mr-2 text-primary" />
            <IntlMessages id="review.action-cont" />
          </div>
          <div className="d-flex justify-content-around align-items-center mt-4 mb-4 flex-wrap">
            {roleType <= RoleTypes.CONTENT_MANAGER && (
              <div className="c-pointer">
                <Input
                  type="radio"
                  id="approve"
                  value="1"
                  name="review"
                  className="c-pointer"
                  defaultChecked={roleType <= RoleTypes.CONTENT_MANAGER}
                  onChange={(e) => setMode(e.target.value)}
                />
                <Label for="approve" className="c-pointer">
                  <IntlMessages id="review.approve" />
                </Label>
              </div>
            )}
            <div className="c-pointer">
              <Input
                type="radio"
                id="review"
                value="2"
                name="review"
                className="c-pointer"
                defaultChecked={roleType > RoleTypes.CONTENT_MANAGER}
                onChange={(e) => setMode(e.target.value)}
              />
              <Label for="review" className="c-pointer">
                <IntlMessages id="review.review" />
              </Label>
            </div>
            <div className="c-pointer" aria-disabled={!accessManagement}>
              <Input
                type="radio"
                id="assign"
                value="3"
                name="review"
                className="c-pointer"
                onChange={(e) => setMode(e.target.value)}
              />
              <Label for="assign" className="c-pointer">
                <IntlMessages id="review.assign" />
              </Label>
            </div>
            {roleType <= RoleTypes.CONTENT_MANAGER && (
              <div className="c-pointer">
                <Input
                  type="radio"
                  id="reject"
                  value="4"
                  name="review"
                  className="c-pointer"
                  onChange={(e) => setMode(e.target.value)}
                />
                <Label className="c-pointer" for="reject">
                  Reject
                </Label>
              </div>
            )}
          </div>

          <div className="d-flex justify-content-between flex-wrap mr-2 w-full">
            {mode === '1' && (
              <div
                style={{ flex: 0.9, borderRight: '1px solid' }}
                className=" px-4"
              >
                <StateChangeMessage
                  contentStatus={contentStatus}
                  accountOwner={accountOwner}
                />
                <FormGroup className="has-float-label">
                  <Label>
                    <IntlMessages id="Approve-Area" />
                    <span style={{ color: '#922c88' }}> *</span>
                  </Label>
                  <Input
                    type="textarea"
                    style={{
                      height: '150px'
                    }}
                    value={approvalComment}
                    onChange={(e) => {
                      const text = e.target.value;
                      if (text.length <= 100) {
                        setApprovalComment(e.target.value);
                      } else {
                        NotificationManager.warning(
                          'Please enter less than 100 words'
                        );
                      }
                    }}
                  />
                </FormGroup>

                <Button
                  color="primary"
                  className="mt-3"
                  onClick={() => {
                    if (showOnlyAssignedOrOwnerMessage) {
                      return NotificationManager.error(
                        `Only assigned user or Owner can approve/reject/review this ${message}.`,
                        'Error',
                        3000
                      );
                    } else {
                      return createflow();
                    }
                  }}
                  disabled={!approvalComment}
                >
                  <IntlMessages id="approve-content" />
                </Button>
                <Button
                  color=""
                  className="mt-3 ml-2 btn-outline-primary-imp"
                  onClick={toggleModal}
                >
                  <IntlMessages id="cancel" />
                </Button>
              </div>
            )}
            {mode === '2' && (
              <div
                style={{ flex: 0.9, borderRight: '1px solid' }}
                className=" px-4"
              >
                <StateChangeMessage
                  contentStatus={contentStatus}
                  accountOwner={accountOwner}
                />
                <FormGroup className="has-float-label">
                  <Label>
                    Comments
                    <span style={{ color: '#922c88' }}> *</span>
                  </Label>
                  <Input
                    type="textarea"
                    style={{
                      height: '150px'
                    }}
                    value={review}
                    onChange={(e) => {
                      const text = e.target.value;
                      if (text.length <= 100) {
                        setReview(e.target.value);
                      } else {
                        NotificationManager.warning(
                          'Please enter less than 100 words'
                        );
                      }
                    }}
                  />
                </FormGroup>

                <Button
                  color="primary"
                  className="mt-3"
                  onClick={() => {
                    if (showOnlyAssignedOrOwnerMessage) {
                      return NotificationManager.error(
                        `Only assigned user or Owner can approve/reject/review this ${message}.`,
                        'Error',
                        3000
                      );
                    } else {
                      return createflow();
                    }
                  }}
                  disabled={!review}
                >
                  <IntlMessages id="add-review" />
                </Button>
                <Button
                  color=""
                  className="mt-3 ml-2 btn-outline-primary-imp"
                  onClick={toggleModal}
                >
                  <IntlMessages id="cancel" />
                </Button>
              </div>
            )}
            {mode === '3' && (
              <div
                style={{ flex: 0.9, borderRight: '1px solid' }}
                className=" px-4"
              >
                <StateChangeMessage
                  contentStatus={contentStatus}
                  accountOwner={accountOwner}
                />
                <FormGroup className="has-float-label">
                  <Label>
                    <IntlMessages id="review-area" />
                    <span style={{ color: '#922c88' }}> *</span>
                  </Label>
                  <UncontrolledDropdown
                    className="badge-outline-light"
                    color="primary"
                  >
                    <DropdownToggle
                      caret
                      className="btn-sm w-full"
                      style={{
                        color: currentColor ? 'silver' : '#000',
                        backgroundColor: 'transparent',
                        border: 'none'
                      }}
                    >
                      <span>{selectedReviewerName || 'Select Assignee'}</span>
                    </DropdownToggle>
                    <DropdownMenu
                      center
                      className="dropmenuheightcont"
                      style={{
                        overflowY: 'scroll'
                      }}
                    >
                      {isReviewersLoading ? (
                        <DropdownItem
                          style={{ height: '50px' }}
                          className="text-center"
                        >
                          loading...
                        </DropdownItem>
                      ) : eligibleReviewers?.length > 0 ? (
                        eligibleReviewers?.map((item) => {
                          if (item?.id !== currentUserId)
                            return (
                              <DropdownItem
                                key={item?.id}
                                onClick={() => {
                                  setSelectedReviewer(item?.id);
                                  setSelectedReviewerName(
                                    `${item?.fullName} (${item?.email})`
                                  );
                                }}
                              >{`${item?.fullName} (${item?.email}) `}</DropdownItem>
                            );
                          return null;
                        })
                      ) : (
                        <DropdownItem
                          style={{ height: '50px' }}
                          className="text-center"
                        >
                          No team members available to assign this content for
                          review
                        </DropdownItem>
                      )}
                    </DropdownMenu>
                  </UncontrolledDropdown>
                </FormGroup>
                {selectedReviewer && (
                  <FormGroup className="has-float-label">
                    <Label>
                      <IntlMessages id="Approve-Area" />
                      {/* <span style={{ color: '#922c88' }}> *</span> */}
                    </Label>
                    <Input
                      type="textarea"
                      style={{
                        height: '150px'
                      }}
                      value={assignReviewComment}
                      onChange={(e) => {
                        const text = e.target.value;
                        if (text.length <= 100) {
                          setAssignReviewComment(e.target.value);
                        } else {
                          NotificationManager.warning(
                            'Please enter less than 100 words'
                          );
                        }
                      }}
                    />
                  </FormGroup>
                )}
                <Button
                  color="primary"
                  className="mt-3"
                  onClick={createflow}
                  disabled={
                    !selectedReviewer || assignReviewComment?.length === 0
                  }
                >
                  {contentStatus ? 'Update Reviewer' : 'Start Review Process'}
                </Button>
                <Button
                  color=""
                  className="mt-3 ml-2 btn-outline-primary-imp"
                  onClick={toggleModal}
                >
                  <IntlMessages id="cancel" />
                </Button>
              </div>
            )}
            {mode === '4' && (
              <div
                style={{ flex: 0.9, borderRight: '1px solid' }}
                className=" px-4"
              >
                <StateChangeMessage
                  contentStatus={contentStatus}
                  accountOwner={accountOwner}
                />
                <FormGroup className="has-float-label">
                  <Label>
                    Comments
                    <span style={{ color: '#922c88' }}> *</span>
                  </Label>
                  <Input
                    type="textarea"
                    style={{
                      height: '150px'
                    }}
                    value={rejectComment}
                    onChange={(e) => {
                      const text = e.target.value;
                      if (text.length <= 100) {
                        setRejectComment(e.target.value);
                      } else {
                        NotificationManager.warning(
                          'Please enter less than 100 words'
                        );
                      }
                    }}
                  />
                </FormGroup>

                <Button
                  color="primary"
                  className="mt-3"
                  onClick={() => {
                    if (showOnlyAssignedOrOwnerMessage) {
                      return NotificationManager.error(
                        `Only assigned user or Owner can approve/reject/review this ${message}.`,
                        'Error',
                        3000
                      );
                    } else {
                      return createflow();
                    }
                  }}
                  disabled={!rejectComment}
                >
                  <IntlMessages id="reject-content" />
                </Button>
              </div>
            )}

            <div
              className="pl-2 pr-2"
              style={{
                flex: 1.1,
                maxHeight: '40vh',
                overflowY: 'auto'
              }}
            >
              <div
                className="Activities-sticky"
                style={{
                  position: 'sticky',
                  top: '0px',
                  right: '0px',
                  left: '0px',
                  width: '100%'
                }}
              >
                Activities
              </div>
              {loading ? (
                <p className="text-center text-primary">loading...</p>
              ) : (
                workflow?.items?.map((item) => (
                  <div key={item?.id}>
                    <ReactionArea
                      data={{
                        name: item?.actor?.fullName,
                        approved: item?.status === 2,
                        message: item?.comment,
                        date: formatDateNTime(item?.creationDateTime),
                        assignedTo: item?.assignedUser?.fullName,
                        status: item?.status
                      }}
                    />
                  </div>
                ))
              )}
            </div>
          </div>
        </ModalBody>
      </Modal>
    </>
  );
};

// eslint-disable-next-line no-unused-vars
const ReactionArea = ({ data }) => {
  const approved = data.status === 1;
  const rejected = data.status === 4;
  const border = approved
    ? '1px solid #59E859'
    : rejected
    ? '1px solid red'
    : '1px solid ';
  if (data?.assignedTo)
    return (
      <div
        style={{
          border
        }}
        className="mt-2 p-1"
      >
        <div className="d-flex p-2 py-3" style={{ flex: 0.1 }}>
          <div className="px-3">
            <Avtar fName={data?.name} size={30} newColor />
          </div>
          <div style={{ flex: 0.9 }}>
            <div>
              <b>{data?.name}</b>
              {/* {approved && (
                <span>
                  <span
                    style={{
                      color: '#59E859'
                    }}
                  >
                    &nbsp; <IntlMessages id="review.approved" />
                  </span>
                  <IntlMessages id="review.this-content" />
                </span>
              )} */}
            </div>
            <div className="d-flex align-items-center mt-2">
              <WorkflowPill status={3} />
              &nbsp; &nbsp;
              <span style={{ fontSize: '12px' }}>To</span>
              &nbsp; &nbsp;
              <div style={{ flex: 0.2 }}>
                <Avtar fName={data?.assignedTo} size={20} newColor />
              </div>
              <span>{data?.assignedTo}</span>
            </div>
            <div>{data?.message}</div>
          </div>
        </div>
        <div className="d-flex justify-content-end f">
          <span
            style={{
              fontSize: '12px'
            }}
            className="pb-2 pr-2"
          >
            {data?.date}
          </span>
        </div>
      </div>
    );
  return (
    <div
      style={{
        border
      }}
      className="mt-2"
    >
      <div className="d-flex p-2" style={{ flex: 0.1 }}>
        <div className="p-3">
          <Avtar fName={data?.name} size={30} newColor />
        </div>
        <div style={{ flex: 1 }} className="mt-3">
          <div>
            <b>{data?.name}</b>
          </div>
          <div className="mt-2 d-flex align-items-center">
            <WorkflowPill status={data?.status} />
          </div>
          <div>{data?.message}</div>
          <div className="d-flex justify-content-end">
            <span
              style={{
                fontSize: '12px'
              }}
              className="pb-2 pr-2"
            >
              {data?.date}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

const StateChangeMessage = ({ contentStatus, accountOwner }) => {
  if (+contentStatus === 4) {
    return (
      <div style={{ flex: 0.9, color: '#922c88' }} className="mb-4">
        <IntlMessages id="App-or-rej-worning-1" />
        <b>&nbsp;{accountOwner}&nbsp;</b>
        <IntlMessages id="App-or-rej-worning-2" />
      </div>
    );
  }
  return null;
};

export default ReviewModal;
