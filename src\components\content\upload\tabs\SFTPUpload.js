import { adminRoot } from 'constants/defaultValues';
import IntlMessages from 'helpers/IntlMessages';
import React from 'react';
import { Link } from 'react-router-dom';

function SFTPUpload({ condition }) {
  if (condition) {
    return (
      <div className="d-flex justify-content-center flex-column">
        <p className="ml-3 mr-3 text-center text-primary mt-3 mb-3">
          <strong>
            <i className="simple-icon-info font-weight-bold"> </i>
            <IntlMessages id="sftp-info" />
          </strong>
          <IntlMessages id="sftp-adv" />
          <Link to={`${adminRoot}/help-support`}>
            <strong className="text-primary">
              <IntlMessages id="sftp-contact" />
            </strong>
          </Link>
          <IntlMessages id="to-km" />
        </p>
      </div>
    );
  }
  return null;
}

export default SFTPUpload;
