import { Col, FormGroup, Label } from 'reactstrap';
import React from 'react';
import AiIcon from 'constants/AiIcon';
import IntlMessages from 'helpers/IntlMessages';

const LabelValue = ({
  label,
  data,
  className,
  isLarge = false,
  smData,
  smLabel,
  aiIcon
}) => {
  return (
    <FormGroup row className={className}>
      <Label sm={smLabel || (isLarge ? 2 : 4)}>
        <b>
          <IntlMessages id={label} />
        </b>
        {aiIcon && (
          <span className="ml-2">
            <AiIcon primary active height={17} />
          </span>
        )}
      </Label>
      <Col sm={smData || (isLarge ? 10 : 8)}>{data}</Col>
    </FormGroup>
  );
};

export default LabelValue;
