/* eslint-disable react/no-array-index-key */
import React from 'react';

const values = [
  {
    id: 1,
    name: 'Google',
    value: 'Google'
  },
  {
    id: 2,
    name: 'Invitation',
    value: 'Invitation'
  },
  {
    id: 3,
    name: 'LinkedIn',
    value: 'LinkedIn'
  },
  {
    id: 4,
    name: 'Twitter',
    value: 'Twitter'
  },
  {
    id: 5,
    name: 'Facebook',
    value: 'Facebook'
  },
  {
    id: 6,
    name: 'Instagram',
    value: 'Instagram'
  },
  {
    id: 7,
    name: 'Word of mouth',
    value: 'Word of mouth'
  },
  {
    id: 8,
    name: 'Referral',
    value: 'Referral'
  },
  {
    id: 9,
    name: 'Blog',
    value: 'Blog'
  },
  {
    id: 10,
    name: 'Others',
    value: 'Others'
  }
];

const HearAboutOptions = () => {
  return (
    <>
      <option disabled>Select Your Choice</option>
      {values.map((m, i) => (
        <option key={i} value={m.value}>
          {m.name}
        </option>
      ))}
    </>
  );
};

export default HearAboutOptions;
