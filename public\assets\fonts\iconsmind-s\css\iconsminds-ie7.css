[class^="iconsminds-"], [class*=" iconsminds-"] {
  font-family: 'iconsminds';
  font-style: normal;
  font-weight: normal;
 
  /* fix buttons height */
  line-height: 1em;
 
  /* you can be more comfortable with increased icons size */
  /* font-size: 120%; */
}
 
.iconsminds-add-space-after-paragraph { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe800;&nbsp;'); }
.iconsminds-add-space-before-paragraph { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe801;&nbsp;'); }
.iconsminds-align-center { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe802;&nbsp;'); }
.iconsminds-align-justify-all { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe803;&nbsp;'); }
.iconsminds-align-justify-center { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe804;&nbsp;'); }
.iconsminds-align-justify-left { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe805;&nbsp;'); }
.iconsminds-align-justify-right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe806;&nbsp;'); }
.iconsminds-align-left { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe807;&nbsp;'); }
.iconsminds-align-right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe808;&nbsp;'); }
.iconsminds-decrase-inedit { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe809;&nbsp;'); }
.iconsminds-increase-inedit { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe80a;&nbsp;'); }
.iconsminds-indent-first-line { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe80b;&nbsp;'); }
.iconsminds-indent-left-margin { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe80c;&nbsp;'); }
.iconsminds-indent-right-margin { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe80d;&nbsp;'); }
.iconsminds-line-spacing { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe80e;&nbsp;'); }
.iconsminds-arrow-fork { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe80f;&nbsp;'); }
.iconsminds-arrow-from { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe810;&nbsp;'); }
.iconsminds-arrow-inside-45 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe811;&nbsp;'); }
.iconsminds-arrow-inside-gap-45 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe812;&nbsp;'); }
.iconsminds-arrow-inside-gap { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe813;&nbsp;'); }
.iconsminds-arrow-inside { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe814;&nbsp;'); }
.iconsminds-arrow-into { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe815;&nbsp;'); }
.iconsminds-arrow-junction { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe817;&nbsp;'); }
.iconsminds-arrow-loop { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe818;&nbsp;'); }
.iconsminds-arrow-merge { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe819;&nbsp;'); }
.iconsminds-arrow-mix { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe81a;&nbsp;'); }
.iconsminds-arrow-out-left { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe81b;&nbsp;'); }
.iconsminds-arrow-out-right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe81c;&nbsp;'); }
.iconsminds-arrow-outside-45 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe81d;&nbsp;'); }
.iconsminds-arrow-outside-gap-45 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe81e;&nbsp;'); }
.iconsminds-arrow-outside-gap { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe81f;&nbsp;'); }
.iconsminds-arrow-outside { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe820;&nbsp;'); }
.iconsminds-arrow-over { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe821;&nbsp;'); }
.iconsminds-arrow-shuffle { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe822;&nbsp;'); }
.iconsminds-arrow-squiggly { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe823;&nbsp;'); }
.iconsminds-arrow-through { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe824;&nbsp;'); }
.iconsminds-arrow-to { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe825;&nbsp;'); }
.iconsminds-double-circle { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe826;&nbsp;'); }
.iconsminds-full-view-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe827;&nbsp;'); }
.iconsminds-full-view { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe828;&nbsp;'); }
.iconsminds-maximize { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe829;&nbsp;'); }
.iconsminds-minimize { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe82a;&nbsp;'); }
.iconsminds-resize { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe82b;&nbsp;'); }
.iconsminds-three-arrow-fork { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe82c;&nbsp;'); }
.iconsminds-view-height { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe82d;&nbsp;'); }
.iconsminds-view-width { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe82e;&nbsp;'); }
.iconsminds-arrow-around { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe82f;&nbsp;'); }
.iconsminds-arrow-barrier { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe830;&nbsp;'); }
.iconsminds-arrow-circle { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe831;&nbsp;'); }
.iconsminds-arrow-cross { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe832;&nbsp;'); }
.iconsminds-arrow-back-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe833;&nbsp;'); }
.iconsminds-arrow-back-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe834;&nbsp;'); }
.iconsminds-arrow-back { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe835;&nbsp;'); }
.iconsminds-arrow-down-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe836;&nbsp;'); }
.iconsminds-arrow-down-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe837;&nbsp;'); }
.iconsminds-arrow-down-in-circle { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe838;&nbsp;'); }
.iconsminds-arrow-down { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe839;&nbsp;'); }
.iconsminds-arrow-forward-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe83a;&nbsp;'); }
.iconsminds-arrow-forward { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe83b;&nbsp;'); }
.iconsminds-arrow-left-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe83c;&nbsp;'); }
.iconsminds-arrow-left-in-circle { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe83d;&nbsp;'); }
.iconsminds-arrow-left { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe83e;&nbsp;'); }
.iconsminds-arrow-next { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe83f;&nbsp;'); }
.iconsminds-arrow-refresh-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe840;&nbsp;'); }
.iconsminds-arrow-refresh { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe841;&nbsp;'); }
.iconsminds-arrow-right-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe842;&nbsp;'); }
.iconsminds-arrow-right-in-circle { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe843;&nbsp;'); }
.iconsminds-arrow-right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe844;&nbsp;'); }
.iconsminds-arrow-turn-left { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe845;&nbsp;'); }
.iconsminds-arrow-turn-right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe846;&nbsp;'); }
.iconsminds-arrow-up-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe847;&nbsp;'); }
.iconsminds-arrow-up-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe848;&nbsp;'); }
.iconsminds-arrow-up-in-circle { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe849;&nbsp;'); }
.iconsminds-arrow-up { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe84a;&nbsp;'); }
.iconsminds-arrow-x-left { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe84b;&nbsp;'); }
.iconsminds-arrow-x-right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe84c;&nbsp;'); }
.iconsminds-bottom-to-top { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe84d;&nbsp;'); }
.iconsminds-down { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe84e;&nbsp;'); }
.iconsminds-down-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe84f;&nbsp;'); }
.iconsminds-down-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe850;&nbsp;'); }
.iconsminds-download { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe851;&nbsp;'); }
.iconsminds-end { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe852;&nbsp;'); }
.iconsminds-fit-to-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe853;&nbsp;'); }
.iconsminds-fit-to { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe854;&nbsp;'); }
.iconsminds-full-screen-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe855;&nbsp;'); }
.iconsminds-full-screen { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe856;&nbsp;'); }
.iconsminds-go-bottom { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe857;&nbsp;'); }
.iconsminds-go-top { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe858;&nbsp;'); }
.iconsminds-left---right-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe859;&nbsp;'); }
.iconsminds-left---right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe85a;&nbsp;'); }
.iconsminds-left { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe85b;&nbsp;'); }
.iconsminds-left-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe85c;&nbsp;'); }
.iconsminds-left-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe85d;&nbsp;'); }
.iconsminds-left-to-right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe85e;&nbsp;'); }
.iconsminds-loop { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe85f;&nbsp;'); }
.iconsminds-navigate-end { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe860;&nbsp;'); }
.iconsminds-navigat-start { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe861;&nbsp;'); }
.iconsminds-reload { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe862;&nbsp;'); }
.iconsminds-reload-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe863;&nbsp;'); }
.iconsminds-repeat { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe864;&nbsp;'); }
.iconsminds-repeat-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe865;&nbsp;'); }
.iconsminds-repeat-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe866;&nbsp;'); }
.iconsminds-repeat-4 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe867;&nbsp;'); }
.iconsminds-right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe868;&nbsp;'); }
.iconsminds-right-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe869;&nbsp;'); }
.iconsminds-right-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe86a;&nbsp;'); }
.iconsminds-right-to-left { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe86b;&nbsp;'); }
.iconsminds-shuffle { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe86c;&nbsp;'); }
.iconsminds-shuffle-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe86d;&nbsp;'); }
.iconsminds-start { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe86e;&nbsp;'); }
.iconsminds-sync { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe86f;&nbsp;'); }
.iconsminds-to-bottom-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe870;&nbsp;'); }
.iconsminds-to-bottom { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe871;&nbsp;'); }
.iconsminds-to-left { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe872;&nbsp;'); }
.iconsminds-top-to-bottom { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe873;&nbsp;'); }
.iconsminds-to-right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe874;&nbsp;'); }
.iconsminds-to-top-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe875;&nbsp;'); }
.iconsminds-to-top { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe876;&nbsp;'); }
.iconsminds-triangle-arrow-down { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe877;&nbsp;'); }
.iconsminds-triangle-arrow-left { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe878;&nbsp;'); }
.iconsminds-triangle-arrow-right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe879;&nbsp;'); }
.iconsminds-triangle-arrow-up { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe87a;&nbsp;'); }
.iconsminds-turn-down-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe87b;&nbsp;'); }
.iconsminds-turn-down-from-left { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe87c;&nbsp;'); }
.iconsminds-turn-down-from-right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe87d;&nbsp;'); }
.iconsminds-turn-down { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe87e;&nbsp;'); }
.iconsminds-turn-left-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe87f;&nbsp;'); }
.iconsminds-turn-left { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe880;&nbsp;'); }
.iconsminds-turn-right-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe881;&nbsp;'); }
.iconsminds-turn-right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe882;&nbsp;'); }
.iconsminds-turn-up-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe883;&nbsp;'); }
.iconsminds-turn-up { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe884;&nbsp;'); }
.iconsminds-up---down-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe885;&nbsp;'); }
.iconsminds-up---down { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe886;&nbsp;'); }
.iconsminds-up { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe887;&nbsp;'); }
.iconsminds-up-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe888;&nbsp;'); }
.iconsminds-up-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe889;&nbsp;'); }
.iconsminds-upload { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe88a;&nbsp;'); }
.iconsminds-billing { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe88b;&nbsp;'); }
.iconsminds-binocular { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe88c;&nbsp;'); }
.iconsminds-bone { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe88d;&nbsp;'); }
.iconsminds-box-close { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe88e;&nbsp;'); }
.iconsminds-box-with-folders { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe88f;&nbsp;'); }
.iconsminds-brush { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe890;&nbsp;'); }
.iconsminds-bucket { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe891;&nbsp;'); }
.iconsminds-camera-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe892;&nbsp;'); }
.iconsminds-camera-4 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe893;&nbsp;'); }
.iconsminds-candle { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe894;&nbsp;'); }
.iconsminds-candy { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe895;&nbsp;'); }
.iconsminds-chair { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe896;&nbsp;'); }
.iconsminds-control { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe897;&nbsp;'); }
.iconsminds-control-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe898;&nbsp;'); }
.iconsminds-crop-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe899;&nbsp;'); }
.iconsminds-crown-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe89a;&nbsp;'); }
.iconsminds-dashboard { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe89b;&nbsp;'); }
.iconsminds-data-center { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe89c;&nbsp;'); }
.iconsminds-data-cloud { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe89d;&nbsp;'); }
.iconsminds-data-download { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe89e;&nbsp;'); }
.iconsminds-data-storage { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe89f;&nbsp;'); }
.iconsminds-delete-file { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8a0;&nbsp;'); }
.iconsminds-dice { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8a1;&nbsp;'); }
.iconsminds-drill { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8a2;&nbsp;'); }
.iconsminds-duplicate-layer { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8a3;&nbsp;'); }
.iconsminds-electricity { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8a4;&nbsp;'); }
.iconsminds-factory { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8a5;&nbsp;'); }
.iconsminds-feather { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8a6;&nbsp;'); }
.iconsminds-file { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8a7;&nbsp;'); }
.iconsminds-file-clipboard-file-+-text { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8a8;&nbsp;'); }
.iconsminds-file-clipboard { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8a9;&nbsp;'); }
.iconsminds-file-copy { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8aa;&nbsp;'); }
.iconsminds-file-edit { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ab;&nbsp;'); }
.iconsminds-file-horizontal { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ac;&nbsp;'); }
.iconsminds-files { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ad;&nbsp;'); }
.iconsminds-file-zip { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ae;&nbsp;'); }
.iconsminds-filter-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8af;&nbsp;'); }
.iconsminds-flash-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8b0;&nbsp;'); }
.iconsminds-folder { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8b1;&nbsp;'); }
.iconsminds-folder-add-+ { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8b2;&nbsp;'); }
.iconsminds-folder-block { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8b3;&nbsp;'); }
.iconsminds-folder-close { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8b4;&nbsp;'); }
.iconsminds-folder-cloud { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8b5;&nbsp;'); }
.iconsminds-folder-delete { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8b6;&nbsp;'); }
.iconsminds-folder-edit { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8b7;&nbsp;'); }
.iconsminds-folder-open { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8b8;&nbsp;'); }
.iconsminds-folders { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8b9;&nbsp;'); }
.iconsminds-folder-zip { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ba;&nbsp;'); }
.iconsminds-funny-bicycle { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8bb;&nbsp;'); }
.iconsminds-gas-pump { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8bc;&nbsp;'); }
.iconsminds-gear { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8bd;&nbsp;'); }
.iconsminds-gear-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8be;&nbsp;'); }
.iconsminds-gears { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8bf;&nbsp;'); }
.iconsminds-gift-box { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8c1;&nbsp;'); }
.iconsminds-grave { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8c2;&nbsp;'); }
.iconsminds-headphone { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8c3;&nbsp;'); }
.iconsminds-headset { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8c4;&nbsp;'); }
.iconsminds-hipster-men { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8c5;&nbsp;'); }
.iconsminds-hub { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8c6;&nbsp;'); }
.iconsminds-idea { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8c7;&nbsp;'); }
.iconsminds-information { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8c8;&nbsp;'); }
.iconsminds-key { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8c9;&nbsp;'); }
.iconsminds-knife { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ca;&nbsp;'); }
.iconsminds-lantern { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8cb;&nbsp;'); }
.iconsminds-layer-backward { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8cc;&nbsp;'); }
.iconsminds-layer-forward { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8cd;&nbsp;'); }
.iconsminds-library { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ce;&nbsp;'); }
.iconsminds-light-bulb-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8d0;&nbsp;'); }
.iconsminds-loading { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8d1;&nbsp;'); }
.iconsminds-loading-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8d2;&nbsp;'); }
.iconsminds-loading-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8d3;&nbsp;'); }
.iconsminds-magic-wand { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8d4;&nbsp;'); }
.iconsminds-magnifi-glass-- { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8d5;&nbsp;'); }
.iconsminds-magnifi-glass-+ { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8d6;&nbsp;'); }
.iconsminds-magnifi-glass { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8d7;&nbsp;'); }
.iconsminds-memory-card-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8d8;&nbsp;'); }
.iconsminds-mine { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8d9;&nbsp;'); }
.iconsminds-mustache-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8da;&nbsp;'); }
.iconsminds-office-lamp { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8db;&nbsp;'); }
.iconsminds-old-sticky-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8dc;&nbsp;'); }
.iconsminds-on-off { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8dd;&nbsp;'); }
.iconsminds-on-off-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8de;&nbsp;'); }
.iconsminds-on-off-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8df;&nbsp;'); }
.iconsminds-palette { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8e0;&nbsp;'); }
.iconsminds-paper { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8e1;&nbsp;'); }
.iconsminds-pen { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8e2;&nbsp;'); }
.iconsminds-photo { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8e3;&nbsp;'); }
.iconsminds-photo-album-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8e4;&nbsp;'); }
.iconsminds-power-station { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8e5;&nbsp;'); }
.iconsminds-preview { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8e6;&nbsp;'); }
.iconsminds-pricing { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8e7;&nbsp;'); }
.iconsminds-profile { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8e8;&nbsp;'); }
.iconsminds-project { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8e9;&nbsp;'); }
.iconsminds-puzzle { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ea;&nbsp;'); }
.iconsminds-refinery { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8eb;&nbsp;'); }
.iconsminds-remove-file { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ec;&nbsp;'); }
.iconsminds-rename { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ed;&nbsp;'); }
.iconsminds-repair { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ee;&nbsp;'); }
.iconsminds-ruler { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ef;&nbsp;'); }
.iconsminds-save { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8f0;&nbsp;'); }
.iconsminds-scissor { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8f2;&nbsp;'); }
.iconsminds-scroller { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8f3;&nbsp;'); }
.iconsminds-scroller-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8f4;&nbsp;'); }
.iconsminds-share { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8f5;&nbsp;'); }
.iconsminds-smoking-pipe { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8f6;&nbsp;'); }
.iconsminds-solar { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8f7;&nbsp;'); }
.iconsminds-statistic { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8f8;&nbsp;'); }
.iconsminds-suitcase { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8f9;&nbsp;'); }
.iconsminds-support { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8fa;&nbsp;'); }
.iconsminds-switch { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8fb;&nbsp;'); }
.iconsminds-tripod-with-camera { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8fc;&nbsp;'); }
.iconsminds-upgrade { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8fd;&nbsp;'); }
.iconsminds-user { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8fe;&nbsp;'); }
.iconsminds-windmill { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ff;&nbsp;'); }
.iconsminds-witch-hat { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe900;&nbsp;'); }
.iconsminds-wrench { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe901;&nbsp;'); }
.iconsminds-add-file { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe902;&nbsp;'); }
.iconsminds-affiliate { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe903;&nbsp;'); }
.iconsminds-anchor { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe904;&nbsp;'); }
.iconsminds-balloon { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe905;&nbsp;'); }
.iconsminds-beard-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe906;&nbsp;'); }
.iconsminds-bicycle { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe907;&nbsp;'); }
.iconsminds-big-data { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe908;&nbsp;'); }
.iconsminds-eifel-tower { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe909;&nbsp;'); }
.iconsminds-el-castillo { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe90a;&nbsp;'); }
.iconsminds-embassy { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe90b;&nbsp;'); }
.iconsminds-empire-state-building { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe90c;&nbsp;'); }
.iconsminds-factory-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe90d;&nbsp;'); }
.iconsminds-fire-staion { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe90e;&nbsp;'); }
.iconsminds-home { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe90f;&nbsp;'); }
.iconsminds-home-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe910;&nbsp;'); }
.iconsminds-home-4 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe911;&nbsp;'); }
.iconsminds-hotel { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe912;&nbsp;'); }
.iconsminds-japanese-gate { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe913;&nbsp;'); }
.iconsminds-leaning-tower { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe914;&nbsp;'); }
.iconsminds-lighthouse { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe915;&nbsp;'); }
.iconsminds-museum { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe916;&nbsp;'); }
.iconsminds-office { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe917;&nbsp;'); }
.iconsminds-opera-house { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe918;&nbsp;'); }
.iconsminds-piramids { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe91b;&nbsp;'); }
.iconsminds-police-station { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe91c;&nbsp;'); }
.iconsminds-post-office { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe91d;&nbsp;'); }
.iconsminds-prater { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe91e;&nbsp;'); }
.iconsminds-roof { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe91f;&nbsp;'); }
.iconsminds-space-needle { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe920;&nbsp;'); }
.iconsminds-the-white-house { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe922;&nbsp;'); }
.iconsminds-tower { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe923;&nbsp;'); }
.iconsminds-bank { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe924;&nbsp;'); }
.iconsminds-berlin-tower { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe925;&nbsp;'); }
.iconsminds-big-bang { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe926;&nbsp;'); }
.iconsminds-building { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe927;&nbsp;'); }
.iconsminds-castle { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe928;&nbsp;'); }
.iconsminds-chinese-temple { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe929;&nbsp;'); }
.iconsminds-chrysler-building { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe92a;&nbsp;'); }
.iconsminds-city-hall { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe92b;&nbsp;'); }
.iconsminds-clothing-store { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe92c;&nbsp;'); }
.iconsminds-colosseum { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe92d;&nbsp;'); }
.iconsminds-column { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe92e;&nbsp;'); }
.iconsminds-coins { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe92f;&nbsp;'); }
.iconsminds-coins-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe930;&nbsp;'); }
.iconsminds-diamond { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe933;&nbsp;'); }
.iconsminds-dollar { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe934;&nbsp;'); }
.iconsminds-dollar-sign-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe935;&nbsp;'); }
.iconsminds-euro { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe936;&nbsp;'); }
.iconsminds-euro-sign-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe937;&nbsp;'); }
.iconsminds-financial { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe938;&nbsp;'); }
.iconsminds-handshake { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe939;&nbsp;'); }
.iconsminds-pie-chart-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe93c;&nbsp;'); }
.iconsminds-pie-chart { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe93d;&nbsp;'); }
.iconsminds-pound { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe93e;&nbsp;'); }
.iconsminds-pound-sign-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe93f;&nbsp;'); }
.iconsminds-safe-box { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe940;&nbsp;'); }
.iconsminds-wallet { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe941;&nbsp;'); }
.iconsminds-bar-chart-4 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe942;&nbsp;'); }
.iconsminds-jeans { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe946;&nbsp;'); }
.iconsminds-sunglasses-w-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe947;&nbsp;'); }
.iconsminds-tie { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe948;&nbsp;'); }
.iconsminds-t-shirt { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe949;&nbsp;'); }
.iconsminds-baby-clothes { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe94a;&nbsp;'); }
.iconsminds-belt { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe94b;&nbsp;'); }
.iconsminds-bikini { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe94c;&nbsp;'); }
.iconsminds-blouse { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe94d;&nbsp;'); }
.iconsminds-boot { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe94e;&nbsp;'); }
.iconsminds-bow-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe94f;&nbsp;'); }
.iconsminds-bra { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe950;&nbsp;'); }
.iconsminds-cap { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe951;&nbsp;'); }
.iconsminds-coat { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe952;&nbsp;'); }
.iconsminds-dress { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe953;&nbsp;'); }
.iconsminds-glasses-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe954;&nbsp;'); }
.iconsminds-gloves { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe955;&nbsp;'); }
.iconsminds-hanger { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe956;&nbsp;'); }
.iconsminds-heels-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe957;&nbsp;'); }
.iconsminds-jacket { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe958;&nbsp;'); }
.iconsminds-walkie-talkie { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe959;&nbsp;'); }
.iconsminds-wifi { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe95a;&nbsp;'); }
.iconsminds-address-book-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe95b;&nbsp;'); }
.iconsminds-bell { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe95c;&nbsp;'); }
.iconsminds-bird-delivering-letter { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe95d;&nbsp;'); }
.iconsminds-communication-tower-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe95e;&nbsp;'); }
.iconsminds-fax { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe95f;&nbsp;'); }
.iconsminds-megaphone { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe960;&nbsp;'); }
.iconsminds-newspaper { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe961;&nbsp;'); }
.iconsminds-old-telephone { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe962;&nbsp;'); }
.iconsminds-router { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe963;&nbsp;'); }
.iconsminds-telephone-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe964;&nbsp;'); }
.iconsminds-smartphone-4 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe965;&nbsp;'); }
.iconsminds-tablet-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe966;&nbsp;'); }
.iconsminds-computer { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe967;&nbsp;'); }
.iconsminds-laptop-+-phone { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe968;&nbsp;'); }
.iconsminds-laptop-+-tablet { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe969;&nbsp;'); }
.iconsminds-laptop-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe96a;&nbsp;'); }
.iconsminds-monitor { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe96b;&nbsp;'); }
.iconsminds-monitor-+-laptop { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe96c;&nbsp;'); }
.iconsminds-monitor-+-phone { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe96d;&nbsp;'); }
.iconsminds-monitor-+-tablet { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe96e;&nbsp;'); }
.iconsminds-monitor-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe96f;&nbsp;'); }
.iconsminds-monitor-vertical { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe970;&nbsp;'); }
.iconsminds-orientation { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe971;&nbsp;'); }
.iconsminds-phone-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe972;&nbsp;'); }
.iconsminds-smartphone-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe973;&nbsp;'); }
.iconsminds-quill-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe974;&nbsp;'); }
.iconsminds-student-hat { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe975;&nbsp;'); }
.iconsminds-blackboard { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe976;&nbsp;'); }
.iconsminds-book { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe977;&nbsp;'); }
.iconsminds-bookmark { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe978;&nbsp;'); }
.iconsminds-books { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe979;&nbsp;'); }
.iconsminds-compass-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe97a;&nbsp;'); }
.iconsminds-diploma-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe97b;&nbsp;'); }
.iconsminds-eraser-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe97c;&nbsp;'); }
.iconsminds-formula { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe97d;&nbsp;'); }
.iconsminds-notepad { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe97e;&nbsp;'); }
.iconsminds-open-book { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe97f;&nbsp;'); }
.iconsminds-pen-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe980;&nbsp;'); }
.iconsminds-pi { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe981;&nbsp;'); }
.iconsminds-pipette { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe982;&nbsp;'); }
.iconsminds-mail-block { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe983;&nbsp;'); }
.iconsminds-mailbox-empty { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe984;&nbsp;'); }
.iconsminds-mailbox-full { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe985;&nbsp;'); }
.iconsminds-mail-delete { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe986;&nbsp;'); }
.iconsminds-mail-favorite { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe987;&nbsp;'); }
.iconsminds-mail-forward { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe988;&nbsp;'); }
.iconsminds-mail-gallery { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe989;&nbsp;'); }
.iconsminds-mail-inbox { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe98a;&nbsp;'); }
.iconsminds-mail-link { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe98b;&nbsp;'); }
.iconsminds-mail-lock { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe98c;&nbsp;'); }
.iconsminds-mail-love { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe98d;&nbsp;'); }
.iconsminds-mail-money { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe98e;&nbsp;'); }
.iconsminds-mail-open { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe98f;&nbsp;'); }
.iconsminds-mail-outbox { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe990;&nbsp;'); }
.iconsminds-mail-password { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe991;&nbsp;'); }
.iconsminds-mail-photo { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe992;&nbsp;'); }
.iconsminds-mail-read { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe993;&nbsp;'); }
.iconsminds-mail-remove-x { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe994;&nbsp;'); }
.iconsminds-mail-reply-all { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe995;&nbsp;'); }
.iconsminds-mail-reply { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe996;&nbsp;'); }
.iconsminds-mail-search { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe997;&nbsp;'); }
.iconsminds-mail-send { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe998;&nbsp;'); }
.iconsminds-mail-settings { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe999;&nbsp;'); }
.iconsminds-mail-unread { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe99a;&nbsp;'); }
.iconsminds-mail-video { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe99b;&nbsp;'); }
.iconsminds-mail-with-at-sign { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe99c;&nbsp;'); }
.iconsminds-mail-with-cursors { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe99d;&nbsp;'); }
.iconsminds-new-mail { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe99e;&nbsp;'); }
.iconsminds-post-mail-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe99f;&nbsp;'); }
.iconsminds-post-mail { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9a0;&nbsp;'); }
.iconsminds-spam-mail { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9a1;&nbsp;'); }
.iconsminds-stamp { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9a2;&nbsp;'); }
.iconsminds-stamp-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9a3;&nbsp;'); }
.iconsminds-voicemail { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9a4;&nbsp;'); }
.iconsminds-at-sign { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9a5;&nbsp;'); }
.iconsminds-box-full { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9a6;&nbsp;'); }
.iconsminds-empty-box { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9a7;&nbsp;'); }
.iconsminds-envelope { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9a8;&nbsp;'); }
.iconsminds-envelope-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9a9;&nbsp;'); }
.iconsminds-inbox { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9aa;&nbsp;'); }
.iconsminds-inbox-empty { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ab;&nbsp;'); }
.iconsminds-inbox-forward { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ac;&nbsp;'); }
.iconsminds-inbox-full { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ad;&nbsp;'); }
.iconsminds-inbox-into { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ae;&nbsp;'); }
.iconsminds-inbox-out { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9af;&nbsp;'); }
.iconsminds-inbox-reply { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9b0;&nbsp;'); }
.iconsminds-letter-close { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9b1;&nbsp;'); }
.iconsminds-letter-open { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9b2;&nbsp;'); }
.iconsminds-letter-sent { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9b3;&nbsp;'); }
.iconsminds-mail { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9b4;&nbsp;'); }
.iconsminds-mail-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9b5;&nbsp;'); }
.iconsminds-mail-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9b6;&nbsp;'); }
.iconsminds-mail-add-+ { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9b7;&nbsp;'); }
.iconsminds-mail-attachement { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9b8;&nbsp;'); }
.iconsminds-ice-cream { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9b9;&nbsp;'); }
.iconsminds-lollipop { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ba;&nbsp;'); }
.iconsminds-open-banana { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9bb;&nbsp;'); }
.iconsminds-pepper { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9bc;&nbsp;'); }
.iconsminds-tee-mug { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9bd;&nbsp;'); }
.iconsminds-tomato { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9be;&nbsp;'); }
.iconsminds-apple { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9bf;&nbsp;'); }
.iconsminds-apple-bite { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9c0;&nbsp;'); }
.iconsminds-beer-glass { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9c1;&nbsp;'); }
.iconsminds-birthday-cake { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9c2;&nbsp;'); }
.iconsminds-bread { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9c3;&nbsp;'); }
.iconsminds-cake { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9c4;&nbsp;'); }
.iconsminds-can { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9c5;&nbsp;'); }
.iconsminds-can-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9c6;&nbsp;'); }
.iconsminds-cheese { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9c7;&nbsp;'); }
.iconsminds-chef-hat { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9c8;&nbsp;'); }
.iconsminds-chopsticks { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9c9;&nbsp;'); }
.iconsminds-cocktail { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ca;&nbsp;'); }
.iconsminds-coffee { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9cb;&nbsp;'); }
.iconsminds-coffee-bean { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9cc;&nbsp;'); }
.iconsminds-coffee-to-go { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9cd;&nbsp;'); }
.iconsminds-cookies { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ce;&nbsp;'); }
.iconsminds-croissant { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9cf;&nbsp;'); }
.iconsminds-cupcake { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9d0;&nbsp;'); }
.iconsminds-doughnut { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9d1;&nbsp;'); }
.iconsminds-fish { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9d2;&nbsp;'); }
.iconsminds-glass-water { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9d3;&nbsp;'); }
.iconsminds-hamburger { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9d4;&nbsp;'); }
.iconsminds-hot-dog { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9d5;&nbsp;'); }
.iconsminds-webcam { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9d7;&nbsp;'); }
.iconsminds-battery-0% { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9d8;&nbsp;'); }
.iconsminds-battery-100% { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9d9;&nbsp;'); }
.iconsminds-battery-charge { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9da;&nbsp;'); }
.iconsminds-charger { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9db;&nbsp;'); }
.iconsminds-cpu { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9dc;&nbsp;'); }
.iconsminds-disk { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9dd;&nbsp;'); }
.iconsminds-dvd { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9de;&nbsp;'); }
.iconsminds-fan { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9df;&nbsp;'); }
.iconsminds-gamepad-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9e0;&nbsp;'); }
.iconsminds-hdd { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9e1;&nbsp;'); }
.iconsminds-keyboard { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9e2;&nbsp;'); }
.iconsminds-mouse { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9e3;&nbsp;'); }
.iconsminds-mouse-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9e4;&nbsp;'); }
.iconsminds-plug-in { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9e5;&nbsp;'); }
.iconsminds-power { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9e6;&nbsp;'); }
.iconsminds-power-cable { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9e7;&nbsp;'); }
.iconsminds-remote-controll-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9e8;&nbsp;'); }
.iconsminds-server-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9e9;&nbsp;'); }
.iconsminds-speaker { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ea;&nbsp;'); }
.iconsminds-start-ways { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9eb;&nbsp;'); }
.iconsminds-synchronize { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ec;&nbsp;'); }
.iconsminds-synchronize-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ed;&nbsp;'); }
.iconsminds-undo { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ee;&nbsp;'); }
.iconsminds-up-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ef;&nbsp;'); }
.iconsminds-upload-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9f0;&nbsp;'); }
.iconsminds-upward { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9f1;&nbsp;'); }
.iconsminds-yes { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9f2;&nbsp;'); }
.iconsminds-add { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9f3;&nbsp;'); }
.iconsminds-back { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9f4;&nbsp;'); }
.iconsminds-broken-link { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9f5;&nbsp;'); }
.iconsminds-check { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9f6;&nbsp;'); }
.iconsminds-close { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9f8;&nbsp;'); }
.iconsminds-cursor { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9f9;&nbsp;'); }
.iconsminds-cursor-click-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9fa;&nbsp;'); }
.iconsminds-cursor-click { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9fb;&nbsp;'); }
.iconsminds-cursor-move-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9fc;&nbsp;'); }
.iconsminds-cursor-select { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9fd;&nbsp;'); }
.iconsminds-down-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9fe;&nbsp;'); }
.iconsminds-download-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ff;&nbsp;'); }
.iconsminds-downward { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea00;&nbsp;'); }
.iconsminds-endways { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea01;&nbsp;'); }
.iconsminds-forward { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea02;&nbsp;'); }
.iconsminds-left-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea03;&nbsp;'); }
.iconsminds-link { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea04;&nbsp;'); }
.iconsminds-next { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea05;&nbsp;'); }
.iconsminds-orientation-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea06;&nbsp;'); }
.iconsminds-pointer { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea07;&nbsp;'); }
.iconsminds-previous { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea08;&nbsp;'); }
.iconsminds-redo { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea09;&nbsp;'); }
.iconsminds-refresh { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea0a;&nbsp;'); }
.iconsminds-reload-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea0b;&nbsp;'); }
.iconsminds-remove { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea0c;&nbsp;'); }
.iconsminds-repeat-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea0d;&nbsp;'); }
.iconsminds-reset { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea0e;&nbsp;'); }
.iconsminds-rewind { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea0f;&nbsp;'); }
.iconsminds-right-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea10;&nbsp;'); }
.iconsminds-rotation { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea11;&nbsp;'); }
.iconsminds-rotation-390 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea12;&nbsp;'); }
.iconsminds-spot { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea13;&nbsp;'); }
.iconsminds-satelite-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea15;&nbsp;'); }
.iconsminds-compass-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea16;&nbsp;'); }
.iconsminds-direction-east { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea17;&nbsp;'); }
.iconsminds-edit-map { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea18;&nbsp;'); }
.iconsminds-geo2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea19;&nbsp;'); }
.iconsminds-geo2-- { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea1a;&nbsp;'); }
.iconsminds-geo2-+ { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea1b;&nbsp;'); }
.iconsminds-globe-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea1c;&nbsp;'); }
.iconsminds-location-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea1d;&nbsp;'); }
.iconsminds-map2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea1e;&nbsp;'); }
.iconsminds-map-marker-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea1f;&nbsp;'); }
.iconsminds-map-marker { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea20;&nbsp;'); }
.iconsminds-stop { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea21;&nbsp;'); }
.iconsminds-stop-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea22;&nbsp;'); }
.iconsminds-back-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea23;&nbsp;'); }
.iconsminds-back-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea24;&nbsp;'); }
.iconsminds-eject { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea25;&nbsp;'); }
.iconsminds-eject-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea26;&nbsp;'); }
.iconsminds-end-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea27;&nbsp;'); }
.iconsminds-end-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea28;&nbsp;'); }
.iconsminds-next-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea29;&nbsp;'); }
.iconsminds-next-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea2a;&nbsp;'); }
.iconsminds-pause { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea2b;&nbsp;'); }
.iconsminds-pause-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea2c;&nbsp;'); }
.iconsminds-power-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea2d;&nbsp;'); }
.iconsminds-power-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea2e;&nbsp;'); }
.iconsminds-record { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea2f;&nbsp;'); }
.iconsminds-record-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea30;&nbsp;'); }
.iconsminds-repeat-5 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea31;&nbsp;'); }
.iconsminds-repeat-6 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea32;&nbsp;'); }
.iconsminds-shuffle-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea33;&nbsp;'); }
.iconsminds-shuffle-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea34;&nbsp;'); }
.iconsminds-start-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea35;&nbsp;'); }
.iconsminds-start-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea36;&nbsp;'); }
.iconsminds-volume-down { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea37;&nbsp;'); }
.iconsminds-volume-up { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea38;&nbsp;'); }
.iconsminds-back-music { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea39;&nbsp;'); }
.iconsminds-cd-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea3a;&nbsp;'); }
.iconsminds-clef { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea3b;&nbsp;'); }
.iconsminds-earphones-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea3c;&nbsp;'); }
.iconsminds-equalizer { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea3e;&nbsp;'); }
.iconsminds-first { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea3f;&nbsp;'); }
.iconsminds-headphones { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea41;&nbsp;'); }
.iconsminds-last { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea42;&nbsp;'); }
.iconsminds-loudspeaker { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea43;&nbsp;'); }
.iconsminds-mic { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea44;&nbsp;'); }
.iconsminds-microphone-4 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea45;&nbsp;'); }
.iconsminds-next-music { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea46;&nbsp;'); }
.iconsminds-old-radio { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea47;&nbsp;'); }
.iconsminds-play-music { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea48;&nbsp;'); }
.iconsminds-radio { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea49;&nbsp;'); }
.iconsminds-record-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea4a;&nbsp;'); }
.iconsminds-record-music { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea4b;&nbsp;'); }
.iconsminds-sound { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea4c;&nbsp;'); }
.iconsminds-speaker-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea4d;&nbsp;'); }
.iconsminds-stop-music { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea4e;&nbsp;'); }
.iconsminds-trumpet { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea4f;&nbsp;'); }
.iconsminds-voice { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea50;&nbsp;'); }
.iconsminds-tree-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea51;&nbsp;'); }
.iconsminds-eci-icon { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea52;&nbsp;'); }
.iconsminds-environmental { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea53;&nbsp;'); }
.iconsminds-environmental-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea54;&nbsp;'); }
.iconsminds-fire-flame-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea55;&nbsp;'); }
.iconsminds-green-energy { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea58;&nbsp;'); }
.iconsminds-green-house { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea59;&nbsp;'); }
.iconsminds-leafs { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea5b;&nbsp;'); }
.iconsminds-light-bulb-leaf { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea5c;&nbsp;'); }
.iconsminds-palm-tree { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea5d;&nbsp;'); }
.iconsminds-plant { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea5e;&nbsp;'); }
.iconsminds-recycling-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea5f;&nbsp;'); }
.iconsminds-seed { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea60;&nbsp;'); }
.iconsminds-trash-with-men { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea61;&nbsp;'); }
.iconsminds-id-card { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea62;&nbsp;'); }
.iconsminds-king-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea63;&nbsp;'); }
.iconsminds-male { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea64;&nbsp;'); }
.iconsminds-male+female { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea65;&nbsp;'); }
.iconsminds-male-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea66;&nbsp;'); }
.iconsminds-man-sign { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea67;&nbsp;'); }
.iconsminds-mens { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea68;&nbsp;'); }
.iconsminds-network { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea69;&nbsp;'); }
.iconsminds-student-female { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea6a;&nbsp;'); }
.iconsminds-student-male { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea6b;&nbsp;'); }
.iconsminds-student-male+female { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea6c;&nbsp;'); }
.iconsminds-students { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea6d;&nbsp;'); }
.iconsminds-woman+man { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea6e;&nbsp;'); }
.iconsminds-add-user { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea6f;&nbsp;'); }
.iconsminds-administrator { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea70;&nbsp;'); }
.iconsminds-assistant { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea71;&nbsp;'); }
.iconsminds-business-man { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea72;&nbsp;'); }
.iconsminds-business-man+woman { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea73;&nbsp;'); }
.iconsminds-business-mens { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea74;&nbsp;'); }
.iconsminds-business-woman { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea75;&nbsp;'); }
.iconsminds-conference { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea76;&nbsp;'); }
.iconsminds-doctor { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea77;&nbsp;'); }
.iconsminds-engineering { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea78;&nbsp;'); }
.iconsminds-female { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea79;&nbsp;'); }
.iconsminds-female-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea7a;&nbsp;'); }
.iconsminds-temperature { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea7b;&nbsp;'); }
.iconsminds-test-tube { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea7c;&nbsp;'); }
.iconsminds-ambulance { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea7d;&nbsp;'); }
.iconsminds-atom { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea7e;&nbsp;'); }
.iconsminds-band-aid { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea7f;&nbsp;'); }
.iconsminds-bio-hazard { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea80;&nbsp;'); }
.iconsminds-biotech { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea81;&nbsp;'); }
.iconsminds-brain { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea82;&nbsp;'); }
.iconsminds-chemical { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea83;&nbsp;'); }
.iconsminds-clinic { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea84;&nbsp;'); }
.iconsminds-danger { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea85;&nbsp;'); }
.iconsminds-dna { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea86;&nbsp;'); }
.iconsminds-dna-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea87;&nbsp;'); }
.iconsminds-first-aid { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea88;&nbsp;'); }
.iconsminds-flask { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea89;&nbsp;'); }
.iconsminds-medical-sign { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea8a;&nbsp;'); }
.iconsminds-medicine-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea8b;&nbsp;'); }
.iconsminds-microscope { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea8c;&nbsp;'); }
.iconsminds-physics { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea8d;&nbsp;'); }
.iconsminds-plasmid { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea8e;&nbsp;'); }
.iconsminds-plaster { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea8f;&nbsp;'); }
.iconsminds-pulse { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea90;&nbsp;'); }
.iconsminds-radioactive { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea91;&nbsp;'); }
.iconsminds-stethoscope { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea92;&nbsp;'); }
.iconsminds-security-settings { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea93;&nbsp;'); }
.iconsminds-securiy-remove { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea94;&nbsp;'); }
.iconsminds-shield { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea95;&nbsp;'); }
.iconsminds-ssl { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea96;&nbsp;'); }
.iconsminds-type-pass { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea97;&nbsp;'); }
.iconsminds-unlock-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea98;&nbsp;'); }
.iconsminds-finger-print { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea99;&nbsp;'); }
.iconsminds-firewall { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea9a;&nbsp;'); }
.iconsminds-key-lock { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea9b;&nbsp;'); }
.iconsminds-laptop-secure { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea9c;&nbsp;'); }
.iconsminds-lock-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea9d;&nbsp;'); }
.iconsminds-password { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea9e;&nbsp;'); }
.iconsminds-password-field { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea9f;&nbsp;'); }
.iconsminds-police { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaa0;&nbsp;'); }
.iconsminds-security-block { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaa2;&nbsp;'); }
.iconsminds-security-bug { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaa3;&nbsp;'); }
.iconsminds-security-camera { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaa4;&nbsp;'); }
.iconsminds-security-check { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaa5;&nbsp;'); }
.iconsminds-testimonal { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaa6;&nbsp;'); }
.iconsminds-broke-link-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaa7;&nbsp;'); }
.iconsminds-coding { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaa8;&nbsp;'); }
.iconsminds-consulting { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaa9;&nbsp;'); }
.iconsminds-copyright { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaaa;&nbsp;'); }
.iconsminds-idea-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaab;&nbsp;'); }
.iconsminds-link-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaac;&nbsp;'); }
.iconsminds-management { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaad;&nbsp;'); }
.iconsminds-monitor-analytics { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaae;&nbsp;'); }
.iconsminds-monitoring { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaaf;&nbsp;'); }
.iconsminds-optimization { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeab0;&nbsp;'); }
.iconsminds-tag { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeab1;&nbsp;'); }
.iconsminds-target { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeab2;&nbsp;'); }
.iconsminds-target-market { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeab3;&nbsp;'); }
.iconsminds-shopping-bag { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeab5;&nbsp;'); }
.iconsminds-shopping-basket { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeab6;&nbsp;'); }
.iconsminds-shopping-cart { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeab7;&nbsp;'); }
.iconsminds-tag-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeab8;&nbsp;'); }
.iconsminds-add-bag { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeab9;&nbsp;'); }
.iconsminds-add-basket { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaba;&nbsp;'); }
.iconsminds-add-cart { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeabb;&nbsp;'); }
.iconsminds-bag-items { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeabc;&nbsp;'); }
.iconsminds-bag-quantity { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeabd;&nbsp;'); }
.iconsminds-basket-coins { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeabf;&nbsp;'); }
.iconsminds-basket-items { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeac0;&nbsp;'); }
.iconsminds-basket-quantity { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeac1;&nbsp;'); }
.iconsminds-car-items { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeac2;&nbsp;'); }
.iconsminds-cart-quantity { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeac3;&nbsp;'); }
.iconsminds-cash-register-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeac4;&nbsp;'); }
.iconsminds-checkout { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeac5;&nbsp;'); }
.iconsminds-checkout-bag { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeac6;&nbsp;'); }
.iconsminds-checkout-basket { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeac7;&nbsp;'); }
.iconsminds-home-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeac8;&nbsp;'); }
.iconsminds-qr-code { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeac9;&nbsp;'); }
.iconsminds-receipt-4 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaca;&nbsp;'); }
.iconsminds-remove-bag { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeacb;&nbsp;'); }
.iconsminds-remove-basket { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeacc;&nbsp;'); }
.iconsminds-remove-cart { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeacd;&nbsp;'); }
.iconsminds-shop { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeace;&nbsp;'); }
.iconsminds-shop-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeacf;&nbsp;'); }
.iconsminds-shop-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xead0;&nbsp;'); }
.iconsminds-ying-yang { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xead1;&nbsp;'); }
.iconsminds-bisexual { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xead2;&nbsp;'); }
.iconsminds-cancer { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xead3;&nbsp;'); }
.iconsminds-couple-sign { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xead4;&nbsp;'); }
.iconsminds-family-sign { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xead5;&nbsp;'); }
.iconsminds-female-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xead6;&nbsp;'); }
.iconsminds-gey { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xead7;&nbsp;'); }
.iconsminds-heart { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xead8;&nbsp;'); }
.iconsminds-homosexual { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xead9;&nbsp;'); }
.iconsminds-inifity { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeada;&nbsp;'); }
.iconsminds-lesbian { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeadb;&nbsp;'); }
.iconsminds-lesbians { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeadc;&nbsp;'); }
.iconsminds-love { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeadd;&nbsp;'); }
.iconsminds-male-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeade;&nbsp;'); }
.iconsminds-men { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeadf;&nbsp;'); }
.iconsminds-no-smoking { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeae0;&nbsp;'); }
.iconsminds-paw { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeae1;&nbsp;'); }
.iconsminds-quotes { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeae2;&nbsp;'); }
.iconsminds-redirect { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeae3;&nbsp;'); }
.iconsminds-ribbon { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeae4;&nbsp;'); }
.iconsminds-venn-diagram { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeae5;&nbsp;'); }
.iconsminds-wheelchair { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeae6;&nbsp;'); }
.iconsminds-women { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeae7;&nbsp;'); }
.iconsminds-instagram { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeae8;&nbsp;'); }
.iconsminds-last-fm { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeae9;&nbsp;'); }
.iconsminds-like { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaea;&nbsp;'); }
.iconsminds-linkedin-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaeb;&nbsp;'); }
.iconsminds-livejournal { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaec;&nbsp;'); }
.iconsminds-newsvine { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaed;&nbsp;'); }
.iconsminds-picasa { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaee;&nbsp;'); }
.iconsminds-pinterest { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaef;&nbsp;'); }
.iconsminds-plaxo { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaf0;&nbsp;'); }
.iconsminds-plurk { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaf1;&nbsp;'); }
.iconsminds-posterous { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaf2;&nbsp;'); }
.iconsminds-qik { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaf3;&nbsp;'); }
.iconsminds-reddit { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaf4;&nbsp;'); }
.iconsminds-reverbnation { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaf5;&nbsp;'); }
.iconsminds-rss { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaf6;&nbsp;'); }
.iconsminds-sharethis { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaf7;&nbsp;'); }
.iconsminds-skype { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaf8;&nbsp;'); }
.iconsminds-soundcloud { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaf9;&nbsp;'); }
.iconsminds-stumbleupon { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeafa;&nbsp;'); }
.iconsminds-technorati { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeafb;&nbsp;'); }
.iconsminds-tumblr { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeafc;&nbsp;'); }
.iconsminds-twitter { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeafd;&nbsp;'); }
.iconsminds-unlike { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeafe;&nbsp;'); }
.iconsminds-ustream { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeaff;&nbsp;'); }
.iconsminds-viddler { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb00;&nbsp;'); }
.iconsminds-vimeo { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb01;&nbsp;'); }
.iconsminds-wordpress { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb02;&nbsp;'); }
.iconsminds-xanga { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb03;&nbsp;'); }
.iconsminds-yahoo { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb04;&nbsp;'); }
.iconsminds-yelp { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb05;&nbsp;'); }
.iconsminds-youtube { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb06;&nbsp;'); }
.iconsminds-ask { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb07;&nbsp;'); }
.iconsminds-behance { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb08;&nbsp;'); }
.iconsminds-bing { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb09;&nbsp;'); }
.iconsminds-blinklist { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb0a;&nbsp;'); }
.iconsminds-blogger { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb0b;&nbsp;'); }
.iconsminds-delicious { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb0c;&nbsp;'); }
.iconsminds-deviantart { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb0d;&nbsp;'); }
.iconsminds-digg { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb0e;&nbsp;'); }
.iconsminds-diigo { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb0f;&nbsp;'); }
.iconsminds-dribble { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb11;&nbsp;'); }
.iconsminds-email { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb12;&nbsp;'); }
.iconsminds-evernote { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb13;&nbsp;'); }
.iconsminds-facebook { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb14;&nbsp;'); }
.iconsminds-feedburner { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb15;&nbsp;'); }
.iconsminds-flickr { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb16;&nbsp;'); }
.iconsminds-formspring { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb17;&nbsp;'); }
.iconsminds-forsquare { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb18;&nbsp;'); }
.iconsminds-friendster { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb19;&nbsp;'); }
.iconsminds-google { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb1a;&nbsp;'); }
.iconsminds-gowalla { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb1b;&nbsp;'); }
.iconsminds-icq { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb1c;&nbsp;'); }
.iconsminds-imdb { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb1d;&nbsp;'); }
.iconsminds-speach-bubble { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb1e;&nbsp;'); }
.iconsminds-speach-bubbles { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb1f;&nbsp;'); }
.iconsminds-speach-bubble-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb20;&nbsp;'); }
.iconsminds-speach-bubble-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb21;&nbsp;'); }
.iconsminds-speach-bubble-4 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb22;&nbsp;'); }
.iconsminds-speach-bubble-5 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb23;&nbsp;'); }
.iconsminds-speach-bubble-6 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb24;&nbsp;'); }
.iconsminds-speach-bubble-7 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb25;&nbsp;'); }
.iconsminds-speach-bubble-8 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb26;&nbsp;'); }
.iconsminds-speach-bubble-9 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb27;&nbsp;'); }
.iconsminds-speach-bubble-10 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb28;&nbsp;'); }
.iconsminds-speach-bubble-11 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb29;&nbsp;'); }
.iconsminds-speach-bubble-12 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb2a;&nbsp;'); }
.iconsminds-speach-bubble-13 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb2b;&nbsp;'); }
.iconsminds-speach-bubble-asking { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb2c;&nbsp;'); }
.iconsminds-speach-bubble-comic-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb2d;&nbsp;'); }
.iconsminds-speach-bubble-comic-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb2e;&nbsp;'); }
.iconsminds-speach-bubble-comic-4 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb2f;&nbsp;'); }
.iconsminds-speach-bubble-comic { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb30;&nbsp;'); }
.iconsminds-speach-bubble-dialog { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb31;&nbsp;'); }
.iconsminds-trekking { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb32;&nbsp;'); }
.iconsminds-trophy { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb33;&nbsp;'); }
.iconsminds-weight-lift { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb35;&nbsp;'); }
.iconsminds-aerobics { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb36;&nbsp;'); }
.iconsminds-archery { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb37;&nbsp;'); }
.iconsminds-ballet-shoes { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb38;&nbsp;'); }
.iconsminds-baseball { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb39;&nbsp;'); }
.iconsminds-basket-ball { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb3a;&nbsp;'); }
.iconsminds-bowling { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb3b;&nbsp;'); }
.iconsminds-box { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb3c;&nbsp;'); }
.iconsminds-chess { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb3d;&nbsp;'); }
.iconsminds-cricket { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb3e;&nbsp;'); }
.iconsminds-dumbbell { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb3f;&nbsp;'); }
.iconsminds-football { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb40;&nbsp;'); }
.iconsminds-football-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb41;&nbsp;'); }
.iconsminds-footprint { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb42;&nbsp;'); }
.iconsminds-footprint-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb43;&nbsp;'); }
.iconsminds-golf { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb44;&nbsp;'); }
.iconsminds-gymnastics { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb45;&nbsp;'); }
.iconsminds-hokey { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb46;&nbsp;'); }
.iconsminds-jump-rope { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb47;&nbsp;'); }
.iconsminds-life-jacket { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb48;&nbsp;'); }
.iconsminds-medal { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb49;&nbsp;'); }
.iconsminds-pilates-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb4a;&nbsp;'); }
.iconsminds-rafting { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb4b;&nbsp;'); }
.iconsminds-running-shoes { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb4c;&nbsp;'); }
.iconsminds-skydiving { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb4d;&nbsp;'); }
.iconsminds-snorkel { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb4e;&nbsp;'); }
.iconsminds-soccer-ball { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb4f;&nbsp;'); }
.iconsminds-swimming { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb50;&nbsp;'); }
.iconsminds-tennis { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb51;&nbsp;'); }
.iconsminds-tennis-ball { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb52;&nbsp;'); }
.iconsminds-over-time-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb53;&nbsp;'); }
.iconsminds-sand-watch-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb54;&nbsp;'); }
.iconsminds-stopwatch { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb55;&nbsp;'); }
.iconsminds-time-backup { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb56;&nbsp;'); }
.iconsminds-timer { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb57;&nbsp;'); }
.iconsminds-watch { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb58;&nbsp;'); }
.iconsminds-24-hour { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb59;&nbsp;'); }
.iconsminds-alarm-clock-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb5a;&nbsp;'); }
.iconsminds-alarm-clock { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb5b;&nbsp;'); }
.iconsminds-clock { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb5c;&nbsp;'); }
.iconsminds-clock-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb5d;&nbsp;'); }
.iconsminds-clock-back { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb5e;&nbsp;'); }
.iconsminds-clock-forward { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb5f;&nbsp;'); }
.iconsminds-old-clock { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb60;&nbsp;'); }
.iconsminds-scooter { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb61;&nbsp;'); }
.iconsminds-ship { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb62;&nbsp;'); }
.iconsminds-skateboard { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb63;&nbsp;'); }
.iconsminds-taxi-sign { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb64;&nbsp;'); }
.iconsminds-traffic-light { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb66;&nbsp;'); }
.iconsminds-train { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb67;&nbsp;'); }
.iconsminds-yacht { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb68;&nbsp;'); }
.iconsminds-bicycle-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb6a;&nbsp;'); }
.iconsminds-bus-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb6b;&nbsp;'); }
.iconsminds-car { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb6c;&nbsp;'); }
.iconsminds-gaugage { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb6e;&nbsp;'); }
.iconsminds-gaugage-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb6f;&nbsp;'); }
.iconsminds-helicopter { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb70;&nbsp;'); }
.iconsminds-jeep { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb71;&nbsp;'); }
.iconsminds-jet { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb72;&nbsp;'); }
.iconsminds-motorcycle { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb73;&nbsp;'); }
.iconsminds-plane { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb74;&nbsp;'); }
.iconsminds-road-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb75;&nbsp;'); }
.iconsminds-sailing-ship { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb76;&nbsp;'); }
.iconsminds-video-tripod { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb77;&nbsp;'); }
.iconsminds-3d-eyeglasses { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb78;&nbsp;'); }
.iconsminds-cinema { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb79;&nbsp;'); }
.iconsminds-director { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb7a;&nbsp;'); }
.iconsminds-film { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb7b;&nbsp;'); }
.iconsminds-film-video { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb7c;&nbsp;'); }
.iconsminds-old-tv { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb7d;&nbsp;'); }
.iconsminds-tv { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb7e;&nbsp;'); }
.iconsminds-video { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb7f;&nbsp;'); }
.iconsminds-video-5 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb80;&nbsp;'); }
.iconsminds-video-6 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb81;&nbsp;'); }
.iconsminds-video-len { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb82;&nbsp;'); }
.iconsminds-sunrise { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb83;&nbsp;'); }
.iconsminds-sunset { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb84;&nbsp;'); }
.iconsminds-temperature-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb85;&nbsp;'); }
.iconsminds-thunder { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb86;&nbsp;'); }
.iconsminds-umbrella-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb87;&nbsp;'); }
.iconsminds-wave { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb88;&nbsp;'); }
.iconsminds-wind-turbine { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb89;&nbsp;'); }
.iconsminds-windy { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb8a;&nbsp;'); }
.iconsminds-cloud-hail { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb8b;&nbsp;'); }
.iconsminds-cloud-moon { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb8c;&nbsp;'); }
.iconsminds-cloud-rain { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb8d;&nbsp;'); }
.iconsminds-cloud-snow { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb8e;&nbsp;'); }
.iconsminds-cloud-sun { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb8f;&nbsp;'); }
.iconsminds-cloud-weather { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb90;&nbsp;'); }
.iconsminds-drop { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb91;&nbsp;'); }
.iconsminds-dry { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb92;&nbsp;'); }
.iconsminds-fog-day { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb93;&nbsp;'); }
.iconsminds-fog-night { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb94;&nbsp;'); }
.iconsminds-half-moon { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb95;&nbsp;'); }
.iconsminds-rain-drop { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb96;&nbsp;'); }
.iconsminds-snow { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb97;&nbsp;'); }
.iconsminds-snowflake-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb98;&nbsp;'); }
.iconsminds-snow-storm { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb99;&nbsp;'); }
.iconsminds-spring { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb9a;&nbsp;'); }
.iconsminds-storm { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb9b;&nbsp;'); }
.iconsminds-summer { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb9c;&nbsp;'); }
.iconsminds-sun { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb9d;&nbsp;'); }
.iconsminds-sun-cloudy-rain { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb9e;&nbsp;'); }
.iconsminds-electric-guitar { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeb9f;&nbsp;'); }
.iconsminds-guitar { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeba0;&nbsp;'); }
.iconsminds-air-balloon-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeba1;&nbsp;'); }
.iconsminds-tractor { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeba2;&nbsp;'); }
.iconsminds-calendar-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeba3;&nbsp;'); }
.iconsminds-calendar-4 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeba4;&nbsp;'); }
.iconsminds-trophy-2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeba5;&nbsp;'); }
.iconsminds-life-safer { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeba6;&nbsp;'); }
.iconsminds-calculator { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeba7;&nbsp;'); }
.iconsminds-taj-mahal { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeba8;&nbsp;'); }
.iconsminds-scale { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xeba9;&nbsp;'); }
.iconsminds-usb { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xebaa;&nbsp;'); }
.iconsminds-flowerpot { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xebab;&nbsp;'); }
.iconsminds-shop-4 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xebac;&nbsp;'); }
.iconsminds-line-chart-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xebad;&nbsp;'); }
.iconsminds-line-chart-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xebae;&nbsp;'); }
.iconsminds-forest-1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xebaf;&nbsp;'); }
.iconsminds-pantone { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xebb0;&nbsp;'); }
.iconsminds-digital-drawing { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xebb1;&nbsp;'); }
.iconsminds-credit-card { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xebb2;&nbsp;'); }
.iconsminds-credit-card-3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xebb3;&nbsp;'); }
.iconsminds-money-bag { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xebb4;&nbsp;'); }
.iconsminds-printer { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xebb5;&nbsp;'); }
.iconsminds-sheep { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xebb8;&nbsp;'); }
.iconsminds-cow { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xebb9;&nbsp;'); }
.iconsminds-dog { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xebba;&nbsp;'); }
.iconsminds-deer { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xebbb;&nbsp;'); }