/* eslint-disable react/no-array-index-key */
/* eslint-disable no-else-return */
/* eslint-disable no-unused-vars */
/* eslint-disable no-unused-expressions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/media-has-caption */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-await-in-loop */
/* eslint-disable  no-restricted-syntax */
import React, { useRef, useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { setSideBarVisibility } from 'redux/actions';
import { NavLink, useHistory } from 'react-router-dom';
import { Row, Button } from 'reactstrap';
import IntlMessages from 'helpers/IntlMessages';
import { Colxx } from 'components/common/CustomBootstrap';
import {
  returnFileSize,
  isVideo,
  isAudio,
  getImageArray,
  getCurrentUser,
  uploadImagesToBlobStorage,
  completedFirstTimeLogin,
  isFirstTimeLogin,
  handleQuotaDurationLimitToaster,
  RoleTypes,
  resizeImageFromFile,
  getLastVisitedSpaceId
} from 'helpers/Utils';
import uploadFileToBlob from 'helpers/Azure';
import { NotificationManager } from 'components/common/react-notifications';
import {
  adminRoot,
  dashboardRoute,
  localFIleLimit,
  oneMB
} from 'constants/defaultValues';
import ConfirmationModal from 'components/common/ConfirmationModal';
import {
  getWatermarks,
  getContentById,
  handleCreateSasUrl,
  handleCreateWatermarkSasUrl,
  handleCreateUrlContent,
  handleContentEncoding,
  handleUpdateContentStatus,
  updateContentUploadStatus,
  getExpandedUrl,
  getActualContentUrl,
  handleCreateSasUrlToReuploadContent
} from 'functions/api/spacesApi';
import { nanoid } from 'nanoid';
import DeleteWatermarkModal from 'components/content/modals/deleteWatermarkModal';

import ProgressBar from 'components/content/upload/components/ProgressBar';
import { ImagesUpload, LocalUpload, RemoteUpload, SFTPUpload } from './tabs';

import PreviewContent from './components/PreviewContent';
import { URLTypeNameEnum, urlTypes } from 'data/urlTypes';
import AutoApprove from './components/AutoApprove';
import Watermark from './Watermark';

const UploadWizard = ({
  showSideBar,
  showMediaProcessing,
  toggleModal,
  openConfirmationModal,
  toggleConfirmationModal,
  isModal,
  isImageModal,
  isWatermarkModal,
  contentIdForWatermark,
  title,
  spaceId,
  MediaType,
  acceptedMediaType,
  reload,
  setContentsLoading,
  uploading,
  setUploading,
  isStreamingModal,
  setIsStreamingModal,
  makeActiveModal,
  drmEnabled,
  subscriptionDetails,
  isPreview,
  setIsPreview,
  aesEnabled,
  roleType,
  workFlowEnabled,
  optimizationEnabled
}) => {
  const fileRef = useRef(null);
  const dropzone = useRef();
  const watermarkRef = useRef(null);
  const history = useHistory();
  const user = getCurrentUser();
  const username = user?.title?.split('@')[0];
  const [previewLink, setPreviewLink] = useState();
  const [videoView, setVideoView] = useState(false);
  const [controller, setController] = useState();
  const [remoteFileUrl, setRemoteFileUrl] = useState('');
  const [newTitle, setNewTitle] = useState('');
  const [markWidth, setMarkWidth] = useState(50);
  const [markHeight, setMarkHeight] = useState(50);
  const [markOpacity, setMarkOpacity] = useState(50);
  const [mediaFile, setMediaFile] = useState(null);
  const [mediaScore, setMediaScore] = useState(3);
  const [DRMStatus, setDRMStatus] = useState(false);
  const [AESStatus, setAESStatus] = useState(false);
  const [uploadProgress, setUploadProgess] = useState(0);
  const { sizeInGB } = returnFileSize(mediaFile?.size);
  const [duration, setDuration] = useState(0);
  const allowSkip = history.location.pathname.includes(dashboardRoute);
  const [allowContinue, setAllowContinue] = useState(false);
  const [images, setImages] = useState([]);
  const [watermarkSlides, setWatermarkSlides] = useState([]);
  const [fetchedWatermarks, setFetchedWatermarks] = useState(false);
  const [loadingWatermarks, setLoadingWatermarks] = useState(false);
  const [selectedSlideName, setSelectedSlideName] = useState('');
  const [selectedSlideSrc, setSelectedSlideSrc] = useState('');
  const [newWatermarkImg, setNewWatermarkImg] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [deletedWatermarkName, setDeletedWatermarkName] = useState('');
  const [openDeleteMarkModal, setOpenDeleteMarkModal] = useState(false);
  const [activeTab, setActiveTab] = useState(makeActiveModal);
  const [position, setPosition] = useState('bottomright');
  const [optimize, setOptimize] = useState(false);
  const [tags, setTags] = useState([]);
  const [category, setCategory] = useState('');
  const [enableWatermarks, setEnableWatermark] = useState(false);
  const [invalidUrl, setInvalidUrl] = useState(false);
  const [cancelEncoding, setCancelEncoding] = useState(null);
  const checkedSpaceId = isFirstTimeLogin() ? getLastVisitedSpaceId() : spaceId;
  const [type, setType] = useState(urlTypes[0].name);
  const [watermarkedImg, setWatermarkedImg] = useState([]);
  const [autoApprove, setAutoApprove] = useState(false);
  const [isWatermarkApplying, setIsWatermarkApplying] = useState(false);
  const [imagesUploadStatus, setImagesUploadStatus] = useState([]);
  const [isImgUploaded, setIsImgUploaded] = useState(false);

  const [openOptConfirmation, setOpenOptConfirmation] = useState(false);
  const toggleOptConfirmation = () => {
    setOpenOptConfirmation(!openOptConfirmation);
  };

  console.log({
    selectedSlideName,
    selectedSlideSrc,
    newWatermarkImg
  });

  const showPreviewAndTitle =
    !isImageModal || (isImageModal && images.length === 1);

  useEffect(() => {
    setActiveTab(makeActiveModal);
  }, [makeActiveModal]);

  const toggleDeleteMarkModal = () => {
    setOpenDeleteMarkModal(!openDeleteMarkModal);
  };

  const percentage = 20;
  const initialValues = { newTitle, markWidth, markHeight, markOpacity };
  const slideSettings = {
    type: 'slider',
    gap: 20,
    peek: { before: 0, after: 0 },
    startAt: 0,
    hideNav: true,
    bound: true,
    perTouch: 1,
    rewind: true
  };

  const getAvailableWatermarks = async () => {
    setLoadingWatermarks(true);
    try {
      const { data, isError } = await getWatermarks();

      if (!isError && data) {
        setWatermarkSlides(data);
        setFetchedWatermarks(true);
      }
      setLoadingWatermarks(false);
    } catch (e) {
      setLoadingWatermarks(false);
      console.log(e.message);
    }
  };

  useEffect(() => {
    if (!loadingWatermarks && enableWatermarks && !fetchedWatermarks) {
      getAvailableWatermarks();
    }
  }, [loadingWatermarks, enableWatermarks, fetchedWatermarks]);

  const getCalculatedWatermarkSize = (
    sourceWidth,
    sourceHeight,
    watermarkWidth,
    watermarkHeight
  ) => {
    let calWidth = sourceWidth * percentage * (1.0 / 100);
    let calHeight = sourceHeight * percentage * (1.0 / 100);

    if (calHeight <= calWidth) {
      calWidth = watermarkWidth * (1.0 / watermarkHeight) * calHeight;
    } else {
      calHeight = watermarkHeight * (1.0 / watermarkWidth) * calWidth;
    }
    return { calWidth, calHeight };
  };

  const handleSlideSelection = (item) => {
    setPosition('');
    if (selectedSlideName === item.name) {
      // Unselect existing watermark
      setSelectedSlideName('');
      setSelectedSlideSrc('');
      setMarkHeight(50);
      setMarkWidth(50);
      setMarkOpacity(50);
      if (isWatermarkModal) setAllowContinue(false);
    } else {
      // Select existing watermark
      setSelectedSlideName(item.name);
      setSelectedSlideSrc(item.url);
      setAllowContinue(true);
      if (item?.watermarkOptions) {
        const { Width, Height, Opacity } = item.watermarkOptions;
        setMarkWidth(parseFloat(Width));
        setMarkHeight(parseFloat(Height));
        setMarkOpacity(parseFloat(Opacity));
      } else {
        setMarkWidth(50);
        setMarkHeight(50);
        setMarkOpacity(50);
      }
    }
  };

  const handleRemoveWatermark = (event, item) => {
    event.stopPropagation();
    event.preventDefault();
    setDeletedWatermarkName(item.name);
    toggleDeleteMarkModal();
  };

  const handleChooseWatermark = () => {
    watermarkRef.current.click();
  };

  // eslint-disable-next-line consistent-return
  const handleChangeWatermark = (e) => {
    // Change new watermark file
    const imageFile = e.target.files[0];
    if (imageFile.size > oneMB) {
      NotificationManager.error(
        <span>
          <IntlMessages id="spaces.bigwatermark" />
          {returnFileSize(imageFile.size).sizeText}
          <IntlMessages id="spaces.maxwatermark" />
        </span>,
        <IntlMessages id="req.failed" />
      );
      return;
    }
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (imageFile) {
      if (!allowedTypes.includes(imageFile.type)) {
        // eslint-disable-next-line consistent-return
        return NotificationManager.warning(
          <IntlMessages id="content-watermark-type" />,
          '',
          3000,
          null,
          null,
          ''
        );
      }
      setNewWatermarkImg(imageFile);
      setAllowContinue(true);
      setSelectedSlideName('newmark');
      const selectedSlideNameURL = URL.createObjectURL(imageFile);
      setSelectedSlideSrc(selectedSlideNameURL);
      if (MediaType === 3) {
        const url = selectedSlideNameURL;
        const img = new Image();
        img.src = url;
        // img.onload = () => {
        //   setMarkWidth(img.width > 150 ? 50 : img.width);
        //   setMarkHeight(img.height > 150 ? 50 : img.height);
        // };
      } else {
        setMarkWidth(50);
        setMarkHeight(50);
      }
      setMarkOpacity(50);
    }
  };

  const handleSelectNewImgForWatermark = () => {
    if (selectedSlideName === 'newmark') {
      // Unselect newWatermark
      setSelectedSlideName('');
      setSelectedSlideSrc('');
      setMarkHeight(50);
      setMarkWidth(50);
      setMarkOpacity(50);
      if (isWatermarkModal) setAllowContinue(false);
    } else {
      // Select newWatermark
      setSelectedSlideName('newmark');
      setSelectedSlideSrc(URL.createObjectURL(newWatermarkImg));
      if (MediaType === 3) {
        const url = URL.createObjectURL(newWatermarkImg);
        const img = new Image();
        img.src = url;
        // img.onload = () => {
        //   setMarkWidth(img.width > 150 ? 50 : img.width);
        //   setMarkHeight(img.height > 150 ? 50 : img.height);
        // };
      } else {
        setMarkWidth(50);
        setMarkHeight(50);
      }

      setMarkOpacity(50);
      setAllowContinue(true);
    }
    setAllowContinue(true);
  };

  const handleRemoveNewWatermarkImg = (event) => {
    event.stopPropagation();
    event.preventDefault();
    if (selectedSlideName === 'newmark') {
      setSelectedSlideName('');
      setSelectedSlideSrc('');
      if (isWatermarkModal) setAllowContinue(false);
    }
    setNewWatermarkImg(null);
  };

  const validateTitle = (value) => {
    let error = '';
    if (!value || value.trim().length === 0) {
      setNewTitle(value);
      error = 'Please enter a content title';
    } else if (value.length > 100) {
      error = 'Maximum character limit for title is 100';
    } else if (value.trim().length < 2) {
      error = 'Title should have atleast 2 characters';
      setNewTitle(value);
    } else {
      setNewTitle(value);
    }
    return error;
  };

  const validateMarkWidth = (value) => {
    setMarkWidth(value);
    let error = '';
    if (!value) {
      error = 'Please enter a watermark width';
    } else if (value < 40 || value > 150) {
      error = 'Please enter a width in between 40 and 150';
    }
    return error;
  };

  const validateMarkHeight = (value) => {
    setMarkHeight(value);
    let error = '';
    if (!value) {
      error = 'Please enter a watermark height';
    } else if (value < 40 || value > 150) {
      error = 'Please enter a height in between 40 and 150';
    }
    return error;
  };

  const validateMarkOpacity = (value) => {
    if (value >= 0 && value <= 100) {
      setMarkOpacity(value);
    }
    let error = '';
    if (!value) {
      error = 'Please enter a watermark opacity';
    } else if (value < 0 || value > 100) {
      error = 'Please enter a opacity in between 0 and 100';
    }
    return error;
  };

  useEffect(() => {
    showSideBar(false);
    return () => {
      showSideBar(true);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const removeSelectedItem = () => {
    setNewTitle('');
    setPreviewLink();
    setMediaFile(null);
    setImages([]);
    // resetRecording();
    if (videoView) setVideoView(false);
  };

  const handleLocalFileChange = (event) => {
    const file = event.target.files[0];
    // Invalid File selected
    if (!file.type) {
      NotificationManager.warning(
        <IntlMessages id="invalid-file" />,
        '',
        3000,
        null,
        null,
        ''
      );
      return;
    }

    // Different type of file selected
    if (acceptedMediaType === 'video/*') {
      if (optimize) {
        if (!isVideo(file)) {
          NotificationManager.warning(
            <IntlMessages id="sel-video" />,
            '',
            3000,
            null,
            null,
            ''
          );
          removeSelectedItem();
          return;
        }
      }
    }

    if (acceptedMediaType === 'audio/*' && !isAudio(file)) {
      NotificationManager.warning(
        <IntlMessages id="sel-audio" />,
        '',
        3000,
        null,
        null,
        ''
      );
      removeSelectedItem();
      return;
    }

    // Valid file selected
    if (isVideo(file)) {
      const url = URL.createObjectURL(file);
      setMediaFile(file);
      setPreviewLink(url);
      setVideoView(true);
    } else if (isAudio(file)) {
      const url = URL.createObjectURL(file);
      setMediaFile(file);
      setPreviewLink(url);
      setVideoView(false);
    }
  };

  useEffect(() => {
    if (isWatermarkModal && MediaType === 1) setVideoView(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isWatermarkModal, MediaType]);

  const handleUrlChange = (e, makeItEmpty = false) => {
    setRemoteFileUrl(makeItEmpty ? '' : e?.target?.value?.trim());
  };

  const CheckMarkUploadProgress = (e) => {
    setUploadProgess((e.loadedBytes / newWatermarkImg.size) * 20);
  };

  const CheckFileUploadProgress = (e) => {
    if (newWatermarkImg)
      setUploadProgess((e.loadedBytes / mediaFile.size) * 70 + 20);
    else setUploadProgess((e.loadedBytes / mediaFile.size) * 90);
  };

  const handleChooseFile = () => {
    fileRef.current.click();
  };

  const SkipUpload = () => {
    completedFirstTimeLogin();
  };

  const AbortUpload = () => {
    if (controller) {
      controller.abort();
    }
  };

  const handleEncodeBody = ({
    isStreaming,
    contentId,
    watermarkFileName,
    viaWatermarkModal = false,
    optmizeValue
  }) => {
    const common = {
      spaceId,
      contentId,
      MediaType,
      watermarkId: watermarkFileName,
      waterMarkPosition: position,
      quality: mediaScore,
      isDRM: DRMStatus,
      isAES: AESStatus
    };
    if (isStreaming) {
      return {
        ...common,
        adaptiveBitRate: true
      };
      // eslint-disable-next-line no-else-return
    }
    return {
      ...common,
      optimize: viaWatermarkModal ? optmizeValue : optimize
    };
  };

  const handleRemoteFileUpload = async (url, watermarkFileName) => {
    if (url) {
      const isHTTPS = url.trim().substring(0, 5).toLowerCase() === 'https';

      setUploading(true);
      const urlIsFromYoutubeORVimeo =
        type === URLTypeNameEnum.VIMEO || type === URLTypeNameEnum.YOUTUBE;
      let sourceUrl = (() => {
        let decodedUrl = decodeURIComponent(url);
        if (!isHTTPS) {
          while (decodedUrl !== url) {
            url = decodedUrl;
            decodedUrl = decodeURIComponent(url);
          }
          return decodedUrl;
        }
        return new URL(decodedUrl).href;
      })();
      // eslint-disable-next-line no-unreachable
      if (urlIsFromYoutubeORVimeo) {
        const rrr = await getExpandedUrl(sourceUrl);
        if (rrr.data && !rrr.isError) {
          sourceUrl = rrr?.data?.data;
        }
      }
      const payload = {
        SourceUrl: sourceUrl,
        Title: newTitle,
        tags,
        category,
        MediaType,
        isAES: AESStatus,
        // removeNoise: deNoise,
        AutoApprove: autoApprove,
        WatermarkFileName: watermarkFileName,
        contentSource:
          type === URLTypeNameEnum.VIMEO
            ? 4
            : type === URLTypeNameEnum.YOUTUBE
            ? 3
            : 2,
        WatermarkOptions: selectedSlideName
          ? {
              Width: markWidth,
              Height: markHeight,
              Opacity: markOpacity
            }
          : null,
        coconutData: {
          quality: mediaScore
        },
        optimization: isStreamingModal ? true : optimize,
        isDRM: DRMStatus,
        adaptiveBitRate: isStreamingModal ? true : optimize,
        streaming: isStreamingModal
        // autoTranscribe: autoTranscribeEnabled ? autoTranscibe : false
      };

      const response = await handleCreateUrlContent(payload, checkedSpaceId);
      const isInvalidUrl = typeof response === 'string';
      if (response?.isError || isInvalidUrl) {
        setUploading(false);
        setRemoteFileUrl('');
        if (!isInvalidUrl) {
          NotificationManager.error(
            <IntlMessages id="upload-failed" />,
            <IntlMessages id="req.failed" />
          );
        }
        return;
      }
      if (response?.data) {
        const remoteContentId = response?.data?.id;
        setUploadProgess(90);
        if (urlIsFromYoutubeORVimeo) {
          setUploadProgess(100);
          completedFirstTimeLogin();
          return;
        }
        if (MediaType !== 3) {
          const { data, isError } = await updateContentUploadStatus(
            {
              ContentIds: [remoteContentId],
              UploadStatus: 3,
              WatermarkFileName: watermarkFileName,
              isNewWatermark: !!isWatermarkModal,
              WatermarkOptions: selectedSlideName
                ? {
                    Width: markWidth,
                    Height: markHeight,
                    Opacity: markOpacity
                  }
                : null
            },
            checkedSpaceId
          );

          if (!isError) {
            const encodedresp = await handleContentEncoding(
              handleEncodeBody({
                isStreaming: isStreamingModal,
                contentId: remoteContentId,
                watermarkFileName
              }),
              false,
              cancelEncoding,
              setCancelEncoding
            );

            if (!encodedresp?.isError && encodedresp?.data) {
              await updateContentUploadStatus(
                {
                  ContentIds: [remoteContentId],
                  UploadStatus: 3,
                  WatermarkFileName: watermarkFileName,
                  isNewWatermark: !!isWatermarkModal,
                  WatermarkOptions: selectedSlideName
                    ? {
                        Width: markWidth,
                        Height: markHeight,
                        Opacity: markOpacity
                      }
                    : null
                },
                checkedSpaceId
              );

              setUploadProgess(100);
              completedFirstTimeLogin();
              setContentsLoading(true);
              await reload(null, 'refresh');
              setContentsLoading(false);
            } else {
              NotificationManager.error(
                <IntlMessages id="content-opt-failed" />,
                <IntlMessages id="req.failed" />,
                3000,
                null,
                null,
                ''
              );
              await handleUpdateContentStatus(
                { contentId: remoteContentId, ContentStatus: 4 },
                checkedSpaceId
              );
              setUploadProgess(-1);
              setUploading(false);
              toggleModal();
            }
          } else {
            NotificationManager.error(
              <IntlMessages id="upload-failed" />,
              <IntlMessages id="req.failed" />,
              3000,
              null,
              null,
              ''
            );
            await handleUpdateContentStatus(
              { contentId: remoteContentId, ContentStatus: 4 },
              checkedSpaceId
            );
            setUploadProgess(-1);
            setUploading(false);
            toggleModal();
          }
        }
      }
    }
  };

  const onFIleUpload = async (checkOptimization = false) => {
    if (!remoteFileUrl && !mediaFile && images.length < 0) return;

    if (
      tags.length === 0 &&
      // activeTab !== 2 &&
      // activeTab !== 3 &&
      !isWatermarkModal
    ) {
      NotificationManager.error(
        '',
        <IntlMessages id="min-one-tag" />,
        3000,
        null,
        null,
        ''
      );
      return;
    }
    if (
      category.length === 0 &&
      // activeTab !== 2 &&
      // activeTab !== 3 &&
      !isWatermarkModal
    ) {
      NotificationManager.error(
        '',
        <IntlMessages id="min-one-category" />,
        3000,
        null,
        null,
        ''
      );
      return;
    }
    const titleError = validateTitle(newTitle);
    if (
      (previewLink && titleError && !isImageModal) ||
      (isImageModal && images.length === 1 && titleError)
    ) {
      NotificationManager.error(
        titleError,
        <IntlMessages id="req.failed" />,
        3000,
        null,
        null,
        ''
      );
      return;
    }
    if (remoteFileUrl) {
      const validRemoteUrl =
        remoteFileUrl.trim().substring(0, 5).toLowerCase() === 'https' ||
        remoteFileUrl.trim().substring(0, 3).toLowerCase() === 'ftp' ||
        remoteFileUrl.trim().substring(0, 4).toLowerCase() === 'sftp' ||
        remoteFileUrl.trim().substring(0, 4).toLowerCase() === 'ftps';

      if (!validRemoteUrl) {
        NotificationManager.error(
          <IntlMessages id="only-links" />,
          <IntlMessages id="req.failed" />,
          3000,
          null,
          null,
          ''
        );
        return;
      }
    }

    if (
      checkOptimization &&
      optimizationEnabled &&
      !optimize &&
      type !== URLTypeNameEnum.YOUTUBE &&
      type !== URLTypeNameEnum.VIMEO &&
      !isStreamingModal &&
      MediaType !== 3 &&
      !selectedSlideName
    ) {
      toggleOptConfirmation();
      return;
    }

    if (!isImageModal && selectedSlideName && MediaType !== 3) {
      // For watermark on video
      const validatedMarlWidth = validateMarkWidth(markWidth);
      if (validatedMarlWidth) {
        NotificationManager.error(
          validatedMarlWidth,
          <IntlMessages id="req.failed" />,
          3000,
          null,
          null,
          ''
        );
        return;
      }
      const validatedMarkHeight = validateMarkHeight(markHeight);
      if (validatedMarkHeight) {
        NotificationManager.error(
          validatedMarkHeight,
          <IntlMessages id="req.failed" />,
          3000,
          null,
          null,
          ''
        );
        return;
      }
    }

    let watermarkFileName = selectedSlideName;
    let markObj;

    // const waterMarkShouldBeUploaded = newWatermarkImg;
    const waterMarkShouldBeUploaded =
      selectedSlideName &&
      (await resizeImageFromFile({
        height: markHeight,
        imageFile:
          selectedSlideName === 'newmark' ? newWatermarkImg : selectedSlideSrc,
        width: markWidth,
        isUrl: selectedSlideName !== 'newmark'
      }));

    if (waterMarkShouldBeUploaded) {
      if (waterMarkShouldBeUploaded.size > oneMB) {
        NotificationManager.error(
          <span>
            <IntlMessages id="spaces.bigwatermark" />
            {returnFileSize(waterMarkShouldBeUploaded.size).sizeText}
            <IntlMessages id="spaces.maxwatermark" />
          </span>,
          <IntlMessages id="req.failed" />,
          3000,
          null,
          null,
          ''
        );

        return;
      }
      if (validateMarkOpacity(markOpacity)) {
        NotificationManager.error(
          validateMarkOpacity(markOpacity),
          <IntlMessages id="req.failed" />,
          3000,
          null,
          null,
          ''
        );
        return;
      }

      setUploading(true);
      if (isImageModal || isWatermarkModal) setIsLoading(true);

      const res = await handleCreateWatermarkSasUrl([
        waterMarkShouldBeUploaded.name
      ]);

      if (res?.isError) {
        setUploading(false);
        if (isImageModal || isWatermarkModal) setIsLoading(false);
        toggleModal();
        return;
      }
      markObj = res?.data;
      const markSasUrl = markObj.sasUrl;
      const markItems = [];
      markItems.push(markObj.items[0]);

      if (selectedSlideName === 'newmark') {
        const uploadedFileName = markObj.items[0].fileName;
        const processedName = uploadedFileName.split('/watermark/')[1];
        watermarkFileName = processedName;
      }

      const watermarkSasObj = {
        items: markItems,
        sasUrl: markSasUrl
      };

      await uploadFileToBlob(
        waterMarkShouldBeUploaded,
        CheckMarkUploadProgress,
        setController,
        watermarkSasObj
      );
    }

    if (remoteFileUrl) {
      handleRemoteFileUpload(remoteFileUrl, watermarkFileName);
      return;
    }
    if (isWatermarkModal && MediaType !== 3) {
      if (!uploading) setUploading(true);
      if (!isLoading) setIsLoading(true);

      try {
        let applicableWidth = markWidth;
        let applicableHeight = markHeight;
        const content = await getContentById({
          spaceId,
          contentId: contentIdForWatermark
        });
        if (content?.isError) {
          NotificationManager.error(
            <IntlMessages id="watermark-failed" />,
            <IntlMessages id="req.failed" />,
            3000,
            null,
            null,
            ''
          );
          return;
        }

        const handleUpdateContentUploadStatus = async () => {
          const resp = await updateContentUploadStatus(
            {
              ContentIds: [contentIdForWatermark],
              UploadStatus: 3,
              WatermarkFileName: watermarkFileName,
              isNewWatermark: !!isWatermarkModal,
              WatermarkOptions: selectedSlideName
                ? {
                    Width: applicableWidth,
                    Height: applicableHeight,
                    Opacity: markOpacity
                  }
                : null
            },
            checkedSpaceId
          );

          if (resp?.isError) {
            NotificationManager.error(
              <IntlMessages id="watermark-failed" />,
              <IntlMessages id="req.failed" />,
              3000,
              null,
              null,
              ''
            );
            return;
          }
          NotificationManager.success(
            <IntlMessages id="watermark-sucess" />,
            <IntlMessages id="req.success" />,
            3000,
            null,
            null,
            ''
          );
        };

        if (MediaType === 3) {
          const { width, height } = content.data;
          const { calWidth, calHeight } = getCalculatedWatermarkSize(
            width,
            height,
            markWidth,
            markHeight
          );

          applicableWidth = calWidth;
          applicableHeight = calHeight;
          await handleUpdateContentUploadStatus();
        }

        if (MediaType === 2) {
          await handleUpdateContentUploadStatus();
        }

        if (content.data.optimization && MediaType === 1) {
          const handledEncodedBody = handleEncodeBody({
            isStreaming: content?.data?.streaming,
            contentId: contentIdForWatermark,
            watermarkFileName,
            viaWatermarkModal: true,
            optmizeValue: content.data.optimization
          });
          const encodedresp = await handleContentEncoding(
            handledEncodedBody,
            false
          );
          if (!encodedresp.isError && encodedresp.data) {
            await handleUpdateContentUploadStatus();
          }
        }

        setContentsLoading(true);
        toggleModal();
        await reload(null, 'refresh');
        setContentsLoading(false);
        setUploading(false);
        setIsLoading(false);
        return;
      } catch (error) {
        console.log(error);
      }
    }

    if (images.length > 0 || (isWatermarkModal && MediaType === 3)) {
      const newArray = watermarkedImg.length > 0 ? watermarkedImg : images;
      const imagesArray = getImageArray(
        newArray,
        watermarkFileName,
        tags,
        category,
        newTitle,
        autoApprove,
        AESStatus,
        DRMStatus,
        true,
        false
      );
      const imagesSize = imagesArray.reduce(
        (total, img) => +total + img.Size,
        0
      );
      console.log('imagesArray', imagesArray);
      setImagesUploadStatus(
        imagesArray.map((m, i) => ({
          ...m,
          id: Date.now(),
          progress: 0,
          imageFile: images[i]
        }))
      );
      if (
        +subscriptionDetails.contentSize + +imagesSize >
        +subscriptionDetails?.contentSizeLimit
      ) {
        handleQuotaDurationLimitToaster({
          subscriptionDetails
        });
        setUploading(false);
        // eslint-disable-next-line no-useless-return
        return;
      }
      try {
        // setIsLoading(true);
        setUploading(true);
        let data = null;
        let isError = false;
        if (isWatermarkModal && MediaType === 3) {
          const res = await handleCreateSasUrlToReuploadContent({
            spaceId: checkedSpaceId,
            contentId: contentIdForWatermark
          });
          data = res.data;
          isError = res.isError;
        } else {
          const res = await handleCreateSasUrl(
            imagesArray,
            checkedSpaceId,
            false
          );
          data = res.data;
          isError = res.isError;
        }
        if (isError) {
          setIsLoading(false);
          setUploading(false);
          return;
        }
        const imagesWithMeta = imagesArray.map((imageObj, index) => ({
          ...imageObj,
          id: data.items[index].contentId,
          imageFile: watermarkedImg[index] ?? images[index]
        }));

        const ErroWhileEncoding = [];

        const handleEncodeImg = async (
          imageData,
          onEncodeComplete = () => {},
          onUpdateStatusComplete = () => {}
        ) => {
          const { id } = imageData;
          const contentId = id;
          const { isError, data } = await updateContentUploadStatus(
            {
              ContentIds: [contentId],
              UploadStatus: 3
            },
            checkedSpaceId
          );

          if (isError) {
            await handleUpdateContentStatus(
              { contentId, ContentStatus: 4 },
              checkedSpaceId
            );
          } else {
            onEncodeComplete && onEncodeComplete();
            const encodedresp = await handleContentEncoding(
              {
                spaceId,
                MediaType: 3,
                contentId,
                optimize: true,
                isGIF: imageData.ContentType === 'image/gif',
                width: imageData.Width
              },
              false,
              cancelEncoding,
              setCancelEncoding
            );
            ErroWhileEncoding.push(encodedresp.isError);
            if (encodedresp.isError) {
              NotificationManager.error(
                <IntlMessages id="content-opt-failed" />,
                <IntlMessages id="req.failed" />,
                3000,
                null,
                null,
                ''
              );
            }
            onUpdateStatusComplete && onUpdateStatusComplete();
          }
        };
        if (data) {
          console.log({
            data,
            imagesWithMeta
          });
          const getNewSasObj = (OldSasObj, imageObj) => {
            if (OldSasObj?.items?.length === 1) return OldSasObj;
            const itemsForImg = OldSasObj?.items.filter(
              (sasObj) => sasObj.contentId === imageObj.id
            );
            return {
              items: itemsForImg,
              sasUrl: OldSasObj?.sasUrl
            };
          };
          setImagesUploadStatus(
            imagesWithMeta.map((m, i) => ({
              ...m,
              id: m.id,
              progress: 0,
              imageFile: images[i]
            }))
          );
          // eslint-disable-next-line array-callback-return
          const promises = imagesWithMeta.map(async (m) => {
            const uploadAndEncode = async () => {
              await uploadFileToBlob(
                m.imageFile,
                (e) => {
                  setImagesUploadStatus((prev) => {
                    const newList = prev.map((j) => {
                      if (j.id === m.id) {
                        return {
                          ...j,
                          progress: 50
                        };
                      } else {
                        return j;
                      }
                    });
                    return newList;
                  });
                },
                () => {},
                getNewSasObj(data, m)
              );
              await handleEncodeImg(
                m,
                () => {
                  setImagesUploadStatus((prev) => {
                    const newList = prev.map((j) => {
                      if (j.id === m.id) {
                        return {
                          ...j,
                          progress: 70
                        };
                      } else {
                        return j;
                      }
                    });
                    return newList;
                  });
                },
                () => {
                  setImagesUploadStatus((prev) => {
                    const newList = prev.map((j) => {
                      if (j.id === m.id) {
                        return {
                          ...j,
                          progress: 100
                        };
                      } else {
                        return j;
                      }
                    });
                    return newList;
                  });
                }
              );
            };
            await uploadAndEncode();
          });
          // console.log('imagesWithMeta', imagesWithMeta);
          // const promises = imagesWithMeta.map((imageData) =>
          // await uploadImagesToBlobStorage(imagesWithMeta, setController, data);
          //   handleEncodeImg(imageData)
          // );
          await Promise.all(promises);
          setWatermarkedImg([]);
          setIsImgUploaded(true);
          // setUploading(false);
          setIsLoading(false);
          const everyImgEncoded = ErroWhileEncoding.every((d) => d === false);
          if (everyImgEncoded) {
            NotificationManager.success(
              <IntlMessages id="img-upload" />,
              <IntlMessages id="req.success" />,
              3000,
              null,
              null,
              ''
            );
          }
          completedFirstTimeLogin();
          setContentsLoading(true);
          await reload(null, 'refresh');
          setContentsLoading(false);
          // toggleModal();
          // history.push(
          //   history.location.pathname.includes('dashboard')
          //     ? adminRoot
          //     : `/app/spaces/space/${spaceId}/manage-content`
          // );
          // toggleModal();
          // setContentsLoading(true);
          // await reload(null, 'refresh');
          // setContentsLoading(false);
        }
      } catch (err) {
        console.log(err);
      }
      return;
    }

    const handleUploadError = async (contentId) => {
      setUploadProgess(-1);
      setUploading(false);
      toggleModal();
      await handleUpdateContentStatus(
        { contentId, ContentStatus: 4 },
        checkedSpaceId
      );
    };

    if (mediaFile) {
      if (!newWatermarkImg) setUploading(true);

      if (sizeInGB > localFIleLimit) {
        NotificationManager.warning(
          `${mediaFile.name} exceeds accepted file size`,
          <IntlMessages id="req.success" />,
          3000,
          null,
          null,
          ''
        );
        return;
      }

      if (sizeInGB < localFIleLimit) {
        const payload = [
          {
            id: nanoid(),
            Title: newTitle,
            Filename: mediaFile.name,
            WatermarkFileName: watermarkFileName,
            ContentType: mediaFile.type,
            Size: mediaFile.size,
            duration,
            MediaType,
            quality: mediaScore,
            tags,
            AutoApprove: autoApprove,
            isAES: AESStatus,
            category,
            isDRM: DRMStatus,
            optimization: isStreamingModal ? true : optimize,
            adaptiveBitRate: isStreamingModal,
            streaming: isStreamingModal
            // removeNoise: deNoise,
            // autoTranscribe: autoTranscribeEnabled ? autoTranscibe : false
          }
        ];

        let sasObj;
        setUploading(true);
        try {
          const createdSas = await handleCreateSasUrl(
            payload,
            checkedSpaceId,
            true
          );

          if (createdSas?.isError || typeof createdSas === 'string') {
            setUploading(false);
            toggleModal();
            return;
          }
          const { data } = createdSas;
          sasObj = data;
          const { sasUrl } = sasObj;

          if (sasObj) {
            const fileItems = [];
            fileItems.push(sasObj.items[0]);

            const fileSasObj = {
              items: fileItems,
              sasUrl
            };

            const { isError: isUploadingError } = await uploadFileToBlob(
              mediaFile,
              CheckFileUploadProgress,
              setController,
              fileSasObj
            );
            if (!isUploadingError) {
              if (MediaType !== 3) {
                const { isError, data } = await updateContentUploadStatus(
                  {
                    ContentIds: [fileItems[0].contentId],
                    UploadStatus: 3,
                    isNewWatermark: !!isWatermarkModal,
                    WatermarkFileName: watermarkFileName,
                    WatermarkOptions: selectedSlideName
                      ? {
                          Width: markWidth,
                          Height: markHeight,
                          Opacity: markOpacity
                        }
                      : null
                  },
                  checkedSpaceId
                );

                if (isError) {
                  NotificationManager.error(
                    <IntlMessages id="failed-upload" />,
                    <IntlMessages id="req.failed" />,
                    3000,
                    null,
                    null,
                    ''
                  );
                  handleUploadError(fileItems[0].contentId);
                } else {
                  const handledData = handleEncodeBody({
                    contentId: fileItems[0].contentId,
                    isStreaming: isStreamingModal,
                    watermarkFileName
                  });
                  const encodedresp = await handleContentEncoding(
                    handledData,
                    false,
                    cancelEncoding,
                    setCancelEncoding
                  );

                  if (!encodedresp?.isError && encodedresp?.data) {
                    if (handledData.optimize === false) {
                      await handleUpdateContentStatus(
                        { contentId: fileItems[0].contentId, ContentStatus: 2 },
                        checkedSpaceId
                      );
                    }
                    setUploadProgess(100);
                    setContentsLoading(true);
                    await reload(null, 'refresh');
                    setContentsLoading(false);
                  } else {
                    NotificationManager.error(
                      <IntlMessages id="content-opt-failed" />,
                      <IntlMessages id="req.failed" />,
                      3000,
                      null,
                      null,
                      ''
                    );
                    handleUploadError(fileItems[0].contentId);
                  }
                }
              }
            } else {
              NotificationManager.error(
                <IntlMessages id="failed-upload" />,
                <IntlMessages id="req.failed" />,
                3000,
                null,
                null,
                ''
              );
              handleUploadError(fileItems[0].contentId);
            }
          }
        } catch (error) {
          console.log(error);
        }
      }
    }
  };

  const handleConfirm = () => {
    toggleConfirmationModal();
    toggleModal();
    if (isStreamingModal) setIsStreamingModal(!isStreamingModal);
  };
  const handleClose = () => {
    toggleConfirmationModal();
    setWatermarkedImg([]);
    if (cancelEncoding) {
      cancelEncoding.cancel('Cancelling encoding');
    }
    if (isStreamingModal) setIsStreamingModal(!isStreamingModal);
  };

  const handleContinue = (imgs) => {
    const n = imgs.length;
    setImages(imgs);
    if (n > 0) {
      setAllowContinue(true);
    }
    if (n === 0) {
      setAllowContinue(false);
    }
  };

  const handleTabChange = (tabNumber) => {
    setActiveTab(tabNumber);
    setOptimize(false);
    setRemoteFileUrl('');
    setInvalidUrl(false);
  };

  useEffect(() => {
    if (isWatermarkModal) {
      setEnableWatermark(true);
    }
  }, [isWatermarkModal]);

  useEffect(() => {
    if (MediaType === 3 && isWatermarkModal) {
      const fetchActualContentUrl = async () => {
        try {
          const res = await getActualContentUrl({
            contentId: contentIdForWatermark,
            spaceId,
            isWatermark: true
          });
          if (res.data) {
            setImages([res.data]);
          }
        } catch (error) {
          console.log('error', error);
        }
      };
      fetchActualContentUrl();
    }
  }, [MediaType, spaceId, isWatermarkModal, contentIdForWatermark]);

  const showPreview =
    previewLink === 'FAKE_PREVIEW' ? remoteFileUrl : previewLink;

  const showWatermarkSection = (() => {
    const itsVideoAndOptimizeTrue = videoView && optimize;
    const watermarkModalIsOpen = isWatermarkModal;
    const itsStreamingVideo = isStreamingModal && videoView;
    const itsRemoteStreamingVideo =
      isStreamingModal && remoteFileUrl && MediaType === 1;
    const itsImagesSection = isImageModal && images.length > 0 && previewLink;
    const nonStreamingOptimizedRemoteVideo =
      remoteFileUrl && optimize && !isWatermarkModal && title !== 'Audio';

    return (
      itsVideoAndOptimizeTrue ||
      watermarkModalIsOpen ||
      itsStreamingVideo ||
      itsImagesSection ||
      (itsRemoteStreamingVideo && showPreview) ||
      (nonStreamingOptimizedRemoteVideo && showPreview)
    );
  })();

  return uploading ? (
    isLoading ? (
      <div className="loading" />
    ) : (
      <ProgressBar
        progress={uploadProgress}
        setProgress={setUploadProgess}
        setUploading={setUploading}
        AbortUpload={AbortUpload}
        reload={reload}
        spaceId={spaceId}
        type={type}
        optimize={optimize}
        remoteFileUrl={remoteFileUrl}
        toggleModal={toggleModal}
        isImgUploaded={isImgUploaded}
        MediaType={MediaType}
        imagesUploadStatus={imagesUploadStatus}
        setImagesUploadStatus={setImagesUploadStatus}
        message={
          acceptedMediaType === 'video/*'
            ? 'Video Upload Successfully!'
            : acceptedMediaType === 'audio/*' && 'Audio Upload Successfully!'
        }
      />
    )
  ) : (
    <>
      <ConfirmationModal
        openConfirmationModal={openConfirmationModal}
        toggleConfirmationModal={toggleConfirmationModal}
        handleConfirm={handleConfirm}
        handleClose={handleClose}
        type="cancel.confirmation"
      />
      <ConfirmationModal
        openConfirmationModal={openOptConfirmation}
        toggleConfirmationModal={toggleOptConfirmation}
        handleConfirm={() => {
          toggleOptConfirmation();
          onFIleUpload();
        }}
        handleClose={() => {
          toggleOptConfirmation();
        }}
        type="confirmOptimization"
      />
      <DeleteWatermarkModal
        openModal={openDeleteMarkModal}
        toggleModal={toggleDeleteMarkModal}
        mediaTitle={deletedWatermarkName}
        selectedSlideName={selectedSlideName}
        setSelectedSlideName={setSelectedSlideName}
        setSelectedSlideSrc={setSelectedSlideSrc}
        isWatermarkModal={isWatermarkModal}
        setAllowContinue={setAllowContinue}
        getAvailableWatermarks={getAvailableWatermarks}
        setDeletedWatermarkName={setDeletedWatermarkName}
      />

      {!isModal && (
        <Row className="mb-3">
          <Colxx xxs="12" className="d-flex justify-content-end">
            <Row
              style={{ marginTop: '-8rem', width: '100%', maxHeight: '50px' }}
            >
              <Colxx
                xxs="5"
                md="8"
                className="d-flex flex-column justify-content-start mt-5 mt-md-0"
              >
                <div>
                  <IntlMessages id="up.wel" /> {username}
                  <span className="d-none d-sm-inline">
                    <IntlMessages id="menu.welcomeuser" />
                  </span>
                </div>
                <div className="d-none d-md-block">
                  <IntlMessages id="menu.letsstart" />
                </div>
              </Colxx>
              <Colxx
                xxs="7"
                md="4"
                className="d-flex justify-content-end mt-5 mt-md-0"
              >
                <NavLink className="btn-link" to="/app/spaces" location={{}}>
                  <IntlMessages id="menu.exploreDefault" />
                </NavLink>
              </Colxx>
            </Row>
          </Colxx>
        </Row>
      )}
      <div className="card ">
        <div className="card-body  dashboard-media-upload-container p-2 p-sm-4">
          {title && (
            <div
              className={`d-flex dashbaord-upload-title-container mb-3 dashboard-upload-heading ${
                !isModal
                  ? 'justify-content-between pb-4 '
                  : 'justify-content-center'
              } `}
            >
              <span />
              <IntlMessages
                id={
                  isStreamingModal && MediaType !== 3
                    ? `dashboard.stream${title}`
                    : `dashboard.upload${title || 'AudioNVideo'}`
                }
              />
              <NavLink
                style={{ position: 'absolute', right: '20px' }}
                className="btn-link text-decoration-none "
                to="#"
                onClick={() => toggleConfirmationModal()}
              >
                <i className="simple-icon-close close-btn" />
              </NavLink>
            </div>
          )}

          {/* tabs */}
          {!isImageModal && !previewLink && !isWatermarkModal && (
            <div className="d-flex mb-2">
              <div
                className="tabupload"
                style={{
                  borderBottom: `${
                    activeTab === 1
                      ? '3px solid #922c88'
                      : '0.7px solid #922c88'
                  }`
                }}
                onClick={() => handleTabChange(1)}
              >
                <IntlMessages id="up.local" />
              </div>

              {!isImageModal && (
                <div
                  className="tabupload"
                  style={{
                    borderBottom: `${
                      activeTab === 2
                        ? '3px solid #922c88'
                        : '0.7px solid #922c88'
                    }`
                  }}
                  onClick={() => handleTabChange(2)}
                >
                  <IntlMessages id="up.remote" />
                </div>
              )}
              <div
                className="tabupload"
                style={{
                  borderBottom: `${
                    activeTab === 3
                      ? '3px solid #922c88'
                      : '0.7px solid #922c88'
                  }`
                }}
                onClick={() => handleTabChange(3)}
              >
                <IntlMessages id="up.sftp" />
              </div>
            </div>
          )}
          {/* tab sections  */}
          {isImageModal && (!previewLink || images.length > 1) && (
            <ImagesUpload dropzone={dropzone} handleContinue={handleContinue} />
          )}

          <LocalUpload
            condition={activeTab === 1 && !isImageModal}
            previewLink={previewLink}
            isImageModal={isImageModal}
            isWatermarkModal={isWatermarkModal}
            isStreamingModal={isStreamingModal}
            DRMStatus={DRMStatus}
            setDRMStatus={setDRMStatus}
            handleChooseFile={handleChooseFile}
            remoteFileUrl={remoteFileUrl}
            fileRef={fileRef}
            acceptedMediaType={acceptedMediaType}
            handleLocalFileChange={handleLocalFileChange}
            title={title}
            MediaType={MediaType}
            drmEnabled={drmEnabled}
            setAESStatus={setAESStatus}
            AESStatus={AESStatus}
            aesEnabled={aesEnabled}
          />
          <RemoteUpload
            condition={activeTab === 2 && !isImageModal && !previewLink}
            handleUrlChange={handleUrlChange}
            isStreamingModal={isStreamingModal}
            MediaType={MediaType}
            DRMStatus={DRMStatus}
            setDRMStatus={setDRMStatus}
            drmEnabled={drmEnabled}
            remoteFileUrl={remoteFileUrl}
            type={type}
            setType={setType}
            setAllowContinue={setInvalidUrl}
            AESStatus={AESStatus}
            setAESStatus={setAESStatus}
            aesEnabled={aesEnabled}
          />
          <SFTPUpload condition={activeTab === 3} />

          {/* Media Processing Section */}
          {/* {!previewLink && showMediaProcessing && !activeTab === 3 && (
            <div className="upload-media-container">
              <MediaQuality
                mediaScore={mediaScore}
                setMediaScore={setMediaScore}
                DRMStatus={DRMStatus}
              />
            </div>
          )} */}

          <PreviewContent
            previewLink={showPreview}
            MediaType={MediaType}
            mediaFile={mediaFile}
            remoteFileUrl={remoteFileUrl}
            setDuration={setDuration}
            removeSelectedItem={removeSelectedItem}
            validateTitle={validateTitle}
            newTitle={newTitle}
            tags={tags}
            setTags={setTags}
            category={category}
            setCategory={setCategory}
            optimize={optimize}
            setOptimize={setOptimize}
            isStreamingModal={isStreamingModal}
            optimizationEnabled={optimizationEnabled}
            drmEnabled={drmEnabled}
            aesEnabled={aesEnabled}
            setIsPreview={setIsPreview}
            isPreview={isPreview}
            DRMStatus={DRMStatus}
            AESStatus={AESStatus}
            isImageModal={isImageModal}
            showPreviewAndTitle={showPreviewAndTitle}
            isLocalTab={activeTab === 1}
            isRemoteTab={activeTab === 2}
            isYoutube={type === URLTypeNameEnum.YOUTUBE}
            isVimeo={type === URLTypeNameEnum.VIMEO}
            isVimeoYTUrl={
              type === URLTypeNameEnum.VIMEO || type === URLTypeNameEnum.YOUTUBE
            }
          />

          <Watermark
            condition={showWatermarkSection}
            isWatermarkModal={isWatermarkModal}
            type={type}
            enableWatermarks={enableWatermarks}
            setEnableWatermark={setEnableWatermark}
            toggleConfirmationModal={toggleConfirmationModal}
            loadingWatermarks={loadingWatermarks}
            slideSettings={slideSettings}
            watermarkSlides={watermarkSlides}
            newWatermarkImg={newWatermarkImg}
            handleChooseWatermark={handleChooseWatermark}
            watermarkRef={watermarkRef}
            handleChangeWatermark={handleChangeWatermark}
            selectedSlideName={selectedSlideName}
            handleSelectNewImgForWatermark={handleSelectNewImgForWatermark}
            handleRemoveNewWatermarkImg={handleRemoveNewWatermarkImg}
            handleSlideSelection={handleSlideSelection}
            handleRemoveWatermark={handleRemoveWatermark}
            initialValues={initialValues}
            markWidth={markWidth}
            validateMarkWidth={validateMarkWidth}
            markHeight={markHeight}
            validateMarkHeight={validateMarkHeight}
            markOpacity={markOpacity}
            validateMarkOpacity={validateMarkOpacity}
            videoView={videoView}
            remoteFileUrl={remoteFileUrl}
            images={images}
            position={position}
            selectedSlideSrc={selectedSlideSrc}
            setWatermarkedImg={setWatermarkedImg}
            watermarkedImg={watermarkedImg}
            MediaType={MediaType}
            isImageModal={isImageModal}
            contentIdForWatermark={contentIdForWatermark}
            setPosition={setPosition}
            setIsWatermarkApplying={setIsWatermarkApplying}
            isWatermarkApplying={isWatermarkApplying}
          />

          {workFlowEnabled &&
            showPreview &&
            roleType <= RoleTypes.CONTENT_MANAGER && (
              <AutoApprove
                autoApprove={autoApprove}
                setAutoApprove={setAutoApprove}
              />
            )}

          {/* button section */}
          {(activeTab === 2 ||
            (MediaType === 3 && activeTab === 1) ||
            mediaFile ||
            MediaType === 3 ||
            isWatermarkModal ||
            previewLink) && (
            <div className="dashboard-action-button-container mb-3 mt-5">
              <span className="dashboard-whitespace" />
              <Button
                type="button"
                color="primary"
                disabled={
                  (!remoteFileUrl && !previewLink && !allowContinue) ||
                  (enableWatermarks && !selectedSlideName) ||
                  invalidUrl ||
                  isWatermarkApplying
                }
                onClick={() => {
                  if (isImageModal) {
                    if (!previewLink) {
                      if (images[0].dataURL) {
                        setPreviewLink(images[0].dataURL);
                      } else {
                        setPreviewLink('FAKE_IMAGES');
                      }
                    } else {
                      onFIleUpload();
                    }
                  } else if (remoteFileUrl && remoteFileUrl !== previewLink) {
                    setPreviewLink(remoteFileUrl);
                  } else {
                    onFIleUpload(true);
                  }
                }}
                // onClick={onFIleUpload}
              >
                {isWatermarkModal ? 'Update' : 'Process'}
              </Button>
              <span className="dashboard-whitespace" />
            </div>
          )}

          {/* skip section */}
          {allowSkip && (
            <Row>
              <Colxx xxs="12" className="mb-4 d-flex justify-content-end">
                <NavLink
                  className="btn-link mr-3"
                  to="#"
                  location={{}}
                  onClick={SkipUpload}
                >
                  <IntlMessages id="up.skip" />
                </NavLink>
              </Colxx>
            </Row>
          )}
        </div>
      </div>
    </>
  );
};
//
export default connect(null, {
  showSideBar: setSideBarVisibility
})(UploadWizard);
