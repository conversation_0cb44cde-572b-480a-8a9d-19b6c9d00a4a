import IntlMessages from 'helpers/IntlMessages';
import React from 'react';

const Decider = ({
  firstActive,
  onFirstClick,
  firstLabel,
  secondLabel,
  onSecondClick,
  disabled
}) => {
  return (
    <div className="showcaseButton mt-3" aria-disabled={disabled}>
      <button
        type="button"
        className="public"
        style={{
          border: firstActive ? '1px solid #992288' : '1px solid gray',
          background: firstActive ? '#ffbff6' : 'transparent',
          color: firstActive ? '#992288' : 'gray',
          borderRightWidth: firstActive ? '1px' : '0px'
        }}
        onClick={onFirstClick}
      >
        <IntlMessages id={firstLabel} />
      </button>
      <button
        type="button"
        className="private"
        style={{
          border: !firstActive ? '1px solid #992288' : '1px solid gray',
          background: !firstActive ? '#ffbff6' : 'transparent',
          color: !firstActive ? '#992288' : 'gray',
          borderLeftWidth: !firstActive ? '1px' : '0px'
        }}
        onClick={onSecondClick}
      >
        <IntlMessages id={secondLabel} />
      </button>
    </div>
  );
};

export default Decider;
