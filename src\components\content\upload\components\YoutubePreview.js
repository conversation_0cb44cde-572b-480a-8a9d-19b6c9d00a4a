/* eslint-disable jsx-a11y/iframe-has-title */
/* eslint-disable prefer-const */
/* eslint-disable no-restricted-syntax */
import React, { useEffect, useState } from 'react';

function getYoutubeEmbedUrl(videoId) {
  return `https://www.youtube.com/embed/${videoId}`;
}

export function isValidYoutubeId(id) {
  if (id.length !== 11) return false;
  const validChars = /^[a-zA-Z0-9_-]+$/;
  if (!validChars.test(id)) return false;
  return true;
}

export function extractYoutubeId(url) {
  if (typeof url !== 'string') return null;

  const patterns = [
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([^&]+)/i,
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/embed\/([^/?]+)/i,
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/v\/([^/?]+)/i,
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/shorts\/([^/?]+)/i,
    /(?:https?:\/\/)?youtu\.be\/([^/?]+)/i,
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/live\/([^/?]+)/i,
    /^([a-zA-Z0-9_-]{11})$/
  ];

  for (let pattern of patterns) {
    const match = url.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }

  return null;
}

const YoutubePreview = ({ url }) => {
  const [ytUrl, setYtUrl] = useState('');

  useEffect(() => {
    if (url) {
      if (!url.startsWith('https')) {
        setYtUrl('');
      } else {
        const videoId = extractYoutubeId(url);
        if (videoId) {
          const validId = isValidYoutubeId(videoId);
          if (validId) {
            setYtUrl(getYoutubeEmbedUrl(videoId));
          } else {
            setYtUrl('');
          }
        } else {
          setYtUrl('');
        }
      }
    } else {
      setYtUrl('');
    }
  }, [url]);

  console.log('ytUrl', ytUrl);

  if (ytUrl) {
    return (
      <div className="w-full h-full overflow-hidden border-10" id="player-yt">
        <iframe
          width="100%"
          height={280}
          src={ytUrl}
          frameBorder="0"
          allowFullscreen
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        />
      </div>
    );
  }
  return null;
};

export default YoutubePreview;
