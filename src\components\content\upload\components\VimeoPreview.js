/* eslint-disable react/self-closing-comp */
/* eslint-disable jsx-a11y/iframe-has-title */
import React, { useEffect, useState } from 'react';

const VimeoPreview = ({ url }) => {
  const [vimeoUrl, setVimeoUrl] = useState('');

  const extractVimeoUrl = (url) => {
    if (!url) {
      return '';
    }
    const regex = /(?:vimeo\.com\/(?:.*\/)?(\d+))/;
    const match = url.match(regex);
    if (match) {
      const videoId = match[1];
      return `https://player.vimeo.com/video/${videoId}`;
    }
    return '';
  };

  useEffect(() => {
    if (url) {
      const newurl = extractVimeoUrl(url);
      if (newurl) {
        setVimeoUrl(newurl);
      } else {
        setVimeoUrl('');
      }
    } else {
      setVimeoUrl('');
    }
  }, [url]);

  if (vimeoUrl) {
    return (
      <div className="w-full h-full overflow-hidden border-10">
        <iframe
          src={vimeoUrl}
          width="100%"
          height={260}
          frameBorder="0"
          allow="autoplay; fullscreen; picture-in-picture"
          allowfullscreen
        />
      </div>
    );
  }
  return null;
};

export default VimeoPreview;
