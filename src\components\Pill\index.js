/* eslint-disable jsx-a11y/no-static-element-interactions */
import React from 'react';

function Pill({ condition, isShowcased, onClick, isShared }) {
  if (condition) {
    return (
      <span
        className={`customeBadge-outline mr-2 ${
          (isShowcased || isShared) && 'cursor-pointer'
        }`}
        onClick={onClick}
      >
        {isShowcased ? 'SHOWCASED' : 'SHARED'}
      </span>
    );
    // eslint-disable-next-line no-else-return
  } else {
    return <span />;
  }
}

export default Pill;
