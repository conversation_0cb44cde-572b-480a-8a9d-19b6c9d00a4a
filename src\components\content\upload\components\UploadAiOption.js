import React from 'react';
import { Row } from 'reactstrap';
import Switch from 'rc-switch';
import InfoIcon from 'components/icon/InfoIcon';
import AiIcon from 'constants/AiIcon';
import IntlMessages from 'helpers/IntlMessages';

function UploadAiOption({
  enableAutoTag,
  setEnableAutoTag,
  autoTranscibe,
  setAutoTranscibe,
  disabled
}) {
  console.log("disabled",disabled)
  return (
    <div className="w-full d-flex align-items-center justify-content-center"  >
      <Row className="w-60">
        <div className="d-flex flex-row align-items-center justify-content-center mb-2">
          <AiIcon primary active height={20} />
          <span className="media-label-title mx-2">
            <IntlMessages id="up-ai-auto-tags"/>
          </span>
          <InfoIcon
            name="auto-tag"
            message="up-ai-auto-tags-info"
          />
          <Switch
            aria-disabled={disabled}
            className="custom-switch custom-switch-primary custom-switch-small ml-3"
            checked={enableAutoTag}
            onClick={() => {
              setEnableAutoTag(!enableAutoTag);
            }}
          />
        </div>
        <div className="d-flex flex-row align-items-center justify-content-center mb-2" >
          <AiIcon primary active height={20} />
          <span className="media-label-title mx-2"> 
            <IntlMessages id="up-ai-auto-transcribe"/>
          </span>
          <InfoIcon
            name="auto-transcribe"
            message="up-ai-auto-transcribe-info"
          />
          <Switch
            aria-disabled={disabled}
            className="custom-switch custom-switch-primary custom-switch-small ml-3"
            checked={autoTranscibe}
            onClick={() => {
              setAutoTranscibe(!autoTranscibe);
            }}
          />
        </div>
      </Row>
    </div>
  );
}

export default UploadAiOption;
