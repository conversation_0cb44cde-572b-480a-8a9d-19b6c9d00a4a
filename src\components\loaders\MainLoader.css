html,
body {
  margin: 0;
  padding: 0;
}
.loader {
  width: 100px;
  height: 80px;
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  margin: auto;
}
.loader .image {
  width: 100px;
  height: 160px;
  font-size: 40px;
  text-align: center;
  transform-origin: bottom center;
  animation: 3s rotate infinite;
  opacity: 0;
}
.loader span {
  display: block;
  width: 100%;
  text-align: center;
  position: absolute;
  bottom: 0;
}

@keyframes rotate {
  100% {
    transform: rotate(90deg);
  }
  80% {
    opacity: 0;
  }
  65% {
    transform: rotate(0deg);
    opacity: 1;
  }
  35% {
    transform: rotate(0deg);
    opacity: 1;
  }
  10% {
    opacity: 0;
  }
  0% {
    transform: rotate(-90deg);
  }
}
