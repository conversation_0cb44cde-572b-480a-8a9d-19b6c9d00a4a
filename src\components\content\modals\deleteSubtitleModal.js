/* eslint-disable no-unused-vars */
import ConfirmationModal from 'components/common/ConfirmationModal';
import { NotificationManager } from 'components/common/react-notifications';
import { deleteSubtitle } from 'functions/api/spacesApi';
import IntlMessages from 'helpers/IntlMessages';
import React from 'react';

function DeleteSubtitleModal({
  openModal,
  toggleModal,
  mediaTitle,
  spaceId,
  subtitle,
  contentId,
  setIsDeletingSubTitle,
  removeFromUI
}) {
  const handleClose = () => {
    toggleModal();
  };

  const handleConfirm = async () => {
    handleClose();
    try {
      setIsDeletingSubTitle(true);
      const res = await deleteSubtitle(spaceId, contentId, {
        subtitle
      });
      if (!res.isError) {
        removeFromUI();
        NotificationManager.success(
          <IntlMessages id="delete-sub-success" />,
          <IntlMessages id="req.success" />
        );
      } else {
        NotificationManager.error(
          <IntlMessages id="delete-sub-failed" />,
          <IntlMessages id="req.failed" />
        );
      }
      setIsDeletingSubTitle(false);
    } catch (error) {
      console.log('error', error);
    }
  };

  return (
    <ConfirmationModal
      openConfirmationModal={openModal}
      toggleConfirmationModal={toggleModal}
      handleConfirm={handleConfirm}
      handleClose={handleClose}
      type="deleteSubtitle"
      mediaTitle={mediaTitle}
    />
  );
}

export default DeleteSubtitleModal;
