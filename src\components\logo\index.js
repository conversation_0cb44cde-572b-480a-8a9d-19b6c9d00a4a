/* eslint-disable jsx-a11y/no-static-element-interactions */
import { getLandingPageBaseUrl, isDarkModeActive } from 'helpers/Utils';
import React, { useMemo } from 'react';

function Logo({ noMarginBottom }) {
  const isDarkMode = isDarkModeActive();
  const landingPageUrl = useMemo(() => {
    return getLandingPageBaseUrl();
  }, []);

  const styles = (() => {
    if (isDarkMode) {
      return {
        color: '#797979',
        backgroundColor: '#292929'
      };
    }
    return {
      color: '#989898',
      backgroundColor: '#F7F7F7'
    };
  })();

  return (
    <div className="white d-flex">
      <a href={landingPageUrl} target="_blank" rel="noopener noreferrer">
        <span
          className="logo-single"
          style={{ marginBottom: noMarginBottom && '0px' }}
        />
      </a>
      <div
        onClick={(e) => e.stopPropagation()}
        className="mt-2"
        style={{
          marginBottom: noMarginBottom ? '0px' : '60px'
        }}
      >
        <span
          style={{
            fontSize: 11,
            padding: '5px 12px',
            fontWeight: '600',
            borderRadius: 15,
            ...styles
          }}
        >
          Public Beta
        </span>
      </div>
    </div>
  );
}

export default Logo;
