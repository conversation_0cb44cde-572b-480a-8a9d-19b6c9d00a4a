/* eslint-disable no-nested-ternary */
import { formatDateNTime } from 'helpers/Utils';
import React from 'react';
import { NavLink } from 'react-router-dom';
import { Button, Modal, ModalBody } from 'reactstrap';
import IntlMessages from 'helpers/IntlMessages';

const DraftToPublishedShowcase = ({
  modalOpen,
  toggleModal,
  handleContinue,
  expiryAt,
  isCreating,
  showcaseAlways
}) => {
  return (
    <Modal isOpen={modalOpen} toggle={toggleModal} centered>
      <NavLink
        className="btn-link text-decoration-none d-flex align-items-center justify-content-end mt-2 mr-2"
        to="#"
        onClick={toggleModal}
      >
        <i className="simple-icon-close close-btn" />
      </NavLink>
      <ModalBody>
        {isCreating && (
          <>
            <p className="text-primary">
              <i className="simple-icon-info mr-2" />
              <IntlMessages id="dtp-draft-again" />
            </p>
            <p className="text-primary">
              <i className="simple-icon-info mr-2" />
              <IntlMessages id="dtp-interactive-portfolio" />
            </p>
            <p className="text-primary">
              <i className="simple-icon-info mr-2" />
              <IntlMessages id="dtp-other-details" />
            </p>
          </>
        )}
        <div className=" d-flex flex-row">
          <span
            className=" font-weight-lightbold text-align-left"
            style={{ width: '154px', fontSize: '13px' }}
          >
            <IntlMessages id="dtp-creation-date-time" />
          </span>
          <span className=" w-auto" style={{ fontSize: '13px' }}>
            {formatDateNTime(new Date())}
          </span>
        </div>
        <div className=" d-flex flex-row mb-4">
          <span
            className=" font-weight-lightbold text-align-left"
            style={{ width: '154px', fontSize: '13px' }}
          >
            <IntlMessages id="dtp-expiry-date-time" />
          </span>
          <span className=" w-auto" style={{ fontSize: '13px' }}>
            {!showcaseAlways ? (
              <IntlMessages id="dtp-no-expiry" />
            ) : (
              expiryAt && formatDateNTime(new Date(expiryAt))
            )}
          </span>
        </div>
        <div className="d-flex justify-content-center">
          <Button
            color="primary"
            onClick={() => {
              handleContinue(true);
              toggleModal();
            }}
          >
            <IntlMessages id="dtp-confirm" />
          </Button>
          <Button
            color
            onClick={toggleModal}
            className="ml-2 badge-outline-primary"
          >
            <IntlMessages id="dtp-cancel" />
          </Button>
        </div>
      </ModalBody>
    </Modal>
  );
};

export default DraftToPublishedShowcase;
