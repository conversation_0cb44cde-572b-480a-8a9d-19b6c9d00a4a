import React from 'react';
import { FormGroup, Input, Label } from 'reactstrap';
import InfoMessage from 'components/infoMessage';
import IntlMessages from 'helpers/IntlMessages';

const TextBoxInput = ({
  maxCharLimit = 140,
  disabled = false,
  isRequired = false,
  label,
  value,
  placeholder,
  onChange = () => {},
  isTextArea = true,
  className = '',
  answer
}) => {
  const inputValue = answer ? answer[0] : value;
  return (
    <div
      className={`TextBoxInput ${className}`}
      aria-disabled={answer ? 'view' : disabled}
    >
      <FormGroup className="form-group has-float-label mb-0">
        {label && (
          <Label for="name" style={{ cursor: 'default' }}>
            <IntlMessages id={label} />
            &nbsp;
            {isRequired && <span style={{ color: '#922c88' }}> *</span>}
          </Label>
        )}
        <Input
          className="form-control tiemInputStyle"
          type={isTextArea ? 'textarea' : 'text'}
          name="name"
          placeholder={placeholder}
          value={inputValue}
          onChange={answer ? () => {} : onChange}
        />
      </FormGroup>
      <InfoMessage
        textAlign="right"
        message={`Max character limit : ${maxCharLimit}`}
        className="mt-0"
      />
    </div>
  );
};

export default TextBoxInput;
