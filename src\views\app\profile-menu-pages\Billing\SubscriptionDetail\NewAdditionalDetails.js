/* eslint-disable react/no-array-index-key */
import React, { useEffect, useState } from 'react';
import LabelValue from './LabelValue';
import { Card, CardHeader, CardBody } from 'reactstrap';
import IntlMessages from 'helpers/IntlMessages';

const NewAdditionalDetails = ({ data }) => {
  const [details, setDetails] = useState({
    left: [],
    right: []
  });

  useEffect(() => {
    if (data) {
      setDetails({
        left: [
          {
            id: 1,
            label: 'subs.ad.max-file',
            data: `${data?.maxFileSizeLimit / (1024 * 1024 * 1024)} GB`
          },
          {
            id: 3,
            label: 'subs.ad.drm-types',
            data: data?.drmTypes?.map((item, index) => (
              <span key={index}>
                {item}
                {index < data?.drmTypes?.length - 1 && ', '}
              </span>
            ))
          },
          {
            id: 4,
            label: 'subs.ad.resolution',
            data: data?.resolutions?.map((item, index) => (
              <span key={index}>
                {item}
                {index < data?.resolutions?.length - 1 && ', '}
              </span>
            ))
          }
        ],
        right: [
          {
            id: 1,
            label: 'subs.ad.max-items-story',
            data:
              data?.maxItemsInStory < 10
                ? `0${data?.maxItemsInStory}`
                : data?.maxItemsInStory
          },
          {
            id: 2,
            label: 'subs.ad.streaming-types',
            data: data?.adaptiveStreaming?.map((item, index) => (
              <span key={index}>
                {item === 'hls' ? 'HLS' : item === 'dash' && 'DASH'}
                {index < data?.adaptiveStreaming?.length - 1 && ', '}
              </span>
            ))
          },
          {
            id: 3,
            label: 'subs.ad.concurrent-limit',
            data: data?.concurrentViewersLimit ?? 0
          }
        ]
      });
    }
  }, [data]);

  return (
    <Card className="mt-3 p-2" style={{ borderRadius: '10px' }}>
      <CardHeader className="p-3 headerBorder">
        <p className="m-0" style={{ fontSize: '18px' }}>
          <b>
            <IntlMessages id="subs-add-details" />
          </b>
        </p>
      </CardHeader>
      <CardBody className="subscriptionDetailsCards">
        <div className="AccCard">
          {details.left?.map((m) => (
            <LabelValue key={m.id} label={m.label} data={m.data} />
          ))}
        </div>
        <div className="AccCard">
          {details.right?.map((m) => (
            <LabelValue key={m.id} label={m.label} data={m.data} />
          ))}
        </div>
      </CardBody>
    </Card>
  );
};

export default NewAdditionalDetails;
