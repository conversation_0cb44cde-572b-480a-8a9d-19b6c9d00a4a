import InfoPopover from 'components/InfoPopover';
import React from 'react';

const SessionHeader = ({ isQna }) => {
  const id = 'session-list-id-header';
  return (
    <div className="SessionH ss-header">
      <div
        className="SessionH-Id"
        style={{
          width: isQna ? '40%' : '20%'
        }}
      >
        Session ID
      </div>
      <div className="SessionH-City">City</div>
      <div className="SessionH-Country">Country</div>
      {!isQna && (
        <div className="SessionH-Engmt">
          <InfoPopover
            infoId={id}
            style={{
              fontSize: 14
            }}
            iconClassName="text-primary"
            message="Engagement Level reflects the number of questions asked by users"
            className="mr-2"
          />
          Engagement Level
        </div>
      )}
      <div className="SessionH-Date">Date</div>
    </div>
  );
};

export default SessionHeader;
