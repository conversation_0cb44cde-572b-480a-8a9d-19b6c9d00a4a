/* eslint-disable no-unused-vars */
import InfoMessage from 'components/infoMessage';
import { formatDateNTime } from 'helpers/Utils';
import React from 'react';
import { Card } from 'reactstrap';

const formatCityCountry = (place) => {
  if (!place) {
    return '-';
  }
  return place;
};

const SessionCard = ({ data, onClick, isQna }) => {
  const date = (() => {
    const firstChat = data.event_details[0];
    const dateOfChat = firstChat.datetimestamp.value;
    return formatDateNTime(dateOfChat);
  })();

  const engagement = (() => {
    const engagements = data?.engagements ?? 0;
    const length = String(engagements)?.length;
    if (length === 1) {
      return `0${engagements}`;
    }
    return engagements;
  })();

  return (
    <div className="SessionH session-card p-3 c-pointer" onClick={onClick}>
      <div
        style={{
          width: isQna ? '40%' : '20%'
        }}
        className="SessionH-Id text-ellipsis sc-text"
      >
        {data.sessionId}
      </div>
      <div className="SessionH-City sc-text">
        {formatCityCountry(data?.city)}
      </div>
      <div className="SessionH-Country sc-text">
        {formatCityCountry(data?.country)}
      </div>
      {!isQna && <div className="SessionH-Engmt sc-text">{engagement}</div>}
      <div className="SessionH-Date sc-text">{date}</div>
    </div>
  );
};

export default SessionCard;
