import React, { useEffect, useRef, useState } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.bubble.css';

const DynamicQuill = ({ description, className }) => {
  const editorRef = useRef(null);
  const [value, setValue] = useState(description);

  useEffect(() => {
    setValue(description);
  }, [description]);
  return (
    <ReactQuill
      ref={editorRef}
      className={`dynamic-quill ${className}`}
      value={value}
      theme="bubble"
      readOnly
      style={{ height: 'auto' }}
    />
  );
};

export default DynamicQuill;
