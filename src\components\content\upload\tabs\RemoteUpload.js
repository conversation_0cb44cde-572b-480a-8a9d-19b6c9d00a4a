/* eslint-disable no-unused-vars */
/* eslint-disable consistent-return */
/* eslint-disable array-callback-return */
import IntlMessages from 'helpers/IntlMessages';
import React, { useEffect, useState } from 'react';
import {
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  Input,
  UncontrolledDropdown
} from 'reactstrap';
import { urlTypes, URLTypeNameEnum } from 'data/urlTypes';
import { isValidYouTubeUrl, vimeoRegex } from 'helpers/Utils';
import AdvancedOptions from '../components/AdvancedOptions';

function RemoteUpload({
  condition,
  handleUrlChange,
  isStreamingModal,
  MediaType,
  DRMStatus,
  setDRMStatus,
  drmEnabled,
  remoteFileUrl,
  type,
  setType,
  setAllowContinue,
  AESStatus,
  setAESStatus,
  aesEnabled
}) {
  const allowedUrls = (() => {
    const nonDisabledUpload = urlTypes.filter((f) => !f.disabled);
    if ((MediaType === 1 && isStreamingModal) || MediaType === 2) {
      return nonDisabledUpload.filter((f) => f.id !== 5 && f.id !== 6);
    }
    return nonDisabledUpload;
  })();
  const [errors, setErrors] = useState('');
  const [placeHolder, setPlaceHolder] = useState(urlTypes[0].placeHolder);
  const isVimeoYTUrl =
    type === URLTypeNameEnum.VIMEO || type === URLTypeNameEnum.YOUTUBE;

  const handleReset = () => {
    setErrors('');
    setAllowContinue(false);
  };

  useEffect(() => {
    if (remoteFileUrl) {
      const testYtRegext = isValidYouTubeUrl(remoteFileUrl);

      const testVimeoRegext =
        vimeoRegex.test(remoteFileUrl) || remoteFileUrl.includes('vimeo.com');

      const isFTPS =
        remoteFileUrl.trim().substring(0, 4).toLowerCase() === 'ftps';

      if (type === URLTypeNameEnum.HTTPS) {
        setPlaceHolder(urlTypes[0].placeHolder);
        if (testYtRegext) {
          setErrors('Youtube url not supported');
          setAllowContinue(true);
          return;
        }
        if (testVimeoRegext) {
          setErrors('Vimeo url not supported');
          setAllowContinue(true);
        }
        if (remoteFileUrl.trim().substring(0, 5).toLowerCase() !== 'https') {
          setAllowContinue(true);
          setErrors('Invalid https link.Please enter valid HTTPS link');
          return;
        }
        handleReset();
        return;
      }

      if (type === URLTypeNameEnum.FTP) {
        setPlaceHolder(urlTypes[1].placeHolder);
        if (
          isFTPS ||
          remoteFileUrl.trim().substring(0, 3).toLowerCase() !== 'ftp'
        ) {
          setAllowContinue(true);
          setErrors('Enter only FTP link');
          return;
        }
        handleReset();
        return;
      }

      if (type === URLTypeNameEnum.FTPS) {
        setPlaceHolder(urlTypes[2].placeHolder);
        if (!isFTPS) {
          setAllowContinue(true);
          setErrors('Enter only FTPS link');
          return;
        }
        handleReset();
        return;
      }

      if (type === URLTypeNameEnum.SFTP) {
        setPlaceHolder(urlTypes[3].placeHolder);
        if (remoteFileUrl.trim().substring(0, 4).toLowerCase() !== 'sftp') {
          setAllowContinue(true);
          setErrors('Please enter SFTP link');
          return;
        }
        handleReset();
        return;
      }

      if (type === URLTypeNameEnum.YOUTUBE) {
        setPlaceHolder(urlTypes[4].placeHolder);
        if (!testYtRegext) {
          setAllowContinue(true);
          setErrors('Please enter Youtube link');
          return;
        }
        handleReset();
        return;
      }

      if (type === URLTypeNameEnum.VIMEO) {
        setPlaceHolder(urlTypes[5].placeHolder);
        if (!testVimeoRegext) {
          setAllowContinue(true);
          setErrors('Please enter Vimeo link');
          return;
        }
        handleReset();
      }
    } else {
      setErrors('Remote URL cannot be empty');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [remoteFileUrl]);

  if (condition) {
    return (
      <div>
        <div className="dashboard-link-upload-section mb-5 av-tooltip tooltip-label-bottom">
          <UncontrolledDropdown style={{ minWidth: '90px' }}>
            <DropdownToggle
              caret
              color="primary"
              size="sm"
              style={{ borderRadius: '0px', minWidth: '90px' }}
            >
              <span className="name">{type}</span>
            </DropdownToggle>
            <DropdownMenu className="mt-3 center-help">
              {allowedUrls.map((d) => {
                return (
                  <DropdownItem
                    className={`text-center ${
                      d?.disabled ? 'cursor-notallowed' : 'c-pointer'
                    }`}
                    key={d.id}
                    disabled={d?.disabled}
                    onClick={() => {
                      setType(d.name);
                      setPlaceHolder(d.placeHolder);
                      handleUrlChange(null, true);
                    }}
                  >
                    <div className="text-center d-flex align-items-center justify-content-start">
                      <i className={d.icon} />
                      &nbsp; &nbsp;
                      <span style={{ textAlign: 'start' }}>{d.name}</span>
                    </div>
                  </DropdownItem>
                );
              })}
            </DropdownMenu>
          </UncontrolledDropdown>
          <Input
            type="url"
            title={placeHolder}
            placeholder={placeHolder}
            onChange={handleUrlChange}
            value={remoteFileUrl}
          />
          {errors && (
            <div className="invalid-feedback d-block mt-4">{errors}</div>
          )}
        </div>

        {isStreamingModal && MediaType === 1 && !isVimeoYTUrl && (
          <div>
            <p className="dashboard-center-this-item pr-3 pl-3 mt-2 text-center text-primary">
              <strong>
                <i className="simple-icon-info font-weight-bold"> </i>
                <IntlMessages id="remote.info" />
              </strong>
            </p>
          </div>
        )}

        {isStreamingModal && (
          <AdvancedOptions
            AESStatus={AESStatus}
            DRMStatus={DRMStatus}
            setAESStatus={setAESStatus}
            showProtectionCard={isStreamingModal}
            setDRMStatus={setDRMStatus}
            aesEnabled={aesEnabled}
            drmEnabled={drmEnabled}
          />
        )}
      </div>
    );
  }
  return null;
}

export default RemoteUpload;
