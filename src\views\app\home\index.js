/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/media-has-caption */
/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import { Card, Row, Button } from 'reactstrap';
import { injectIntl } from 'react-intl';
import IntlMessages from 'helpers/IntlMessages';
import {
  RoleTypes,
  getCurrentUserDetails,
  getLastVisitedSpaceId,
  getTeamUserRoleType
} from 'helpers/Utils';
import { useHistory } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { sessions } from 'functions/api/analyticsApi';
import NextUpdateTime from 'components/nextUpdateTime';
import StoryIcon from 'constants/StoryIcon';
import { adminRoot } from 'constants/defaultValues';
import useQuery from 'hooks/useQuery';
import { NotificationManager } from 'components/common/react-notifications';
import Actioncard from './components/ActionCard';
import HeroCards from './components/HeroCards';
import AccountMenu from 'containers/navs/AccountMenu';
import useWindowDimensions from 'hooks/useWindowDimensions';

const Tutorials = React.lazy(() => import('./Tutorials'));
const Updates = React.lazy(() => import('./Updates'));

function Home({ subscriptionDetails, expiredSubscriptions }) {
  const istutorial = useQuery().get('tutorial') === 'true';
  const openFirst = useQuery().get('openFirst') === 'true';
  const roleType = useSelector((state) => state.roleType);
  const { fullName } = getCurrentUserDetails();
  const history = useHistory();

  const [sessionsData, setSessionsData] = useState([]);
  const [lastUpdatedSession, setLastUpdatedSession] = useState(null);
  const [reloadRequired, setReloadRequired] = useState(
    sessionStorage.getItem('reloadRequired') === 'true'
  );
  useEffect(() => {
    if (reloadRequired) {
      setReloadRequired(false);
      sessionStorage.removeItem('reloadRequired');
      window.location.reload();
    }
  }, [reloadRequired]);

  useEffect(() => {
    const getuserRoleData = async () => {
      try {
        const resp = await sessions();
        if (resp) {
          setSessionsData(resp.data);
          setLastUpdatedSession(resp.nextUpdateTime);
        }
      } catch (error) {
        console.error(error);
      }
    };
    getuserRoleData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="Home d-flex flex-column w-full position-relative p-3">
      <TopLeftRightInfo
        sessionsData={sessionsData}
        lastUpdatedSession={lastUpdatedSession}
        roleType={roleType}
        hideAfter
      />
      <div className="d-flex sm-flex-wrap justify-content-around ">
        {!istutorial ? (
          <All
            fullName={fullName}
            expiredSubscriptions={expiredSubscriptions}
            subscriptionDetails={subscriptionDetails}
            history={history}
            roleType={roleType}
            sessionsData={sessionsData}
            lastUpdatedSession={lastUpdatedSession}
          />
        ) : (
          <Tutorials history={history} openFirst={openFirst} />
        )}
      </div>
    </div>
  );
}

const All = ({
  fullName,
  expiredSubscriptions,
  subscriptionDetails,
  history,
  roleType,
  sessionsData,
  lastUpdatedSession
}) => {
  const defaultSpaceId = getLastVisitedSpaceId();
  const usrDetail = getCurrentUserDetails();
  const { notifications } = useSelector((state) => state.settings);
  const { width } = useWindowDimensions();
  const USE_WIDER_SIDEBAR = width < 768;

  const subscriptionDisabled =
    (expiredSubscriptions && !subscriptionDetails?.id) ||
    !subscriptionDetails?.id;

  return (
    <Row className="all-cont d-flex flex-1 flex-column justify-content-center text-center">
      <div className="my-3 mb-5 show-smaller">
        <h5 className="mb-0">
          <img
            height={25}
            width={25}
            src="/assets/icons/ConcurrentAudienceCount.svg"
            alt="audience count"
            className="mr-1"
          />
          <IntlMessages id="simultaneous-sessions" />: &nbsp;
          <b>
            {(sessionsData && sessionsData[0]?.concurrent_impressions) || 0}
          </b>
        </h5>
        {lastUpdatedSession && (
          <NextUpdateTime
            fontSize="12px"
            time={lastUpdatedSession}
            // className="m-0"
          />
        )}
      </div>
      <h3 className="mb-0">
        <b>
          <IntlMessages id="home.hi" /> <b>{fullName}</b>
        </b>
      </h3>

      {expiredSubscriptions ? (
        <>
          {subscriptionDetails?.id ? (
            <SubWarning text="home.expired" />
          ) : (
            <SubWarning text="home.no-subscription" />
          )}
        </>
      ) : (
        <h5 className="mt-2">
          <IntlMessages id="home-info" />
        </h5>
      )}

      {USE_WIDER_SIDEBAR && (
        <>
          <AccountMenu history={history} />
        </>
      )}

      <h3 className="text-center mt-5 mb-3">
        <strong>
          <IntlMessages id="home-title" />
        </strong>
      </h3>

      <HeroCards history={history} disabled={subscriptionDisabled} />

      <h3 className="text-center mb-3 mt-4">
        <IntlMessages id="Get started by uploading content to enhance your media management and streaming experience." />
      </h3>

      {roleType <= RoleTypes.VIEWER && (
        <QuickLinks
          expiredSubscriptions={expiredSubscriptions}
          defaultSpaceId={defaultSpaceId}
          history={history}
        />
      )}

      <Actioncards history={history} roletype={roleType} />

      <h3 className="text-center mb-3 mt-4">
        <strong>
          <IntlMessages id="Updates" />
        </strong>
      </h3>
      <Updates notifications={notifications} />
    </Row>
  );
};

const TopLeftRightInfo = ({
  sessionsData,
  lastUpdatedSession,
  roleType,
  hideAfter,
  className = ''
}) => {
  return (
    <div className={`top-info ${className} ${hideAfter && 'hide-after'}`}>
      <div className="mb-0 left">
        <h5 className="mb-0">
          <img
            height={25}
            width={25}
            src="/assets/icons/ConcurrentAudienceCount.svg"
            alt="audience count"
            className="mr-1"
          />
          <IntlMessages id="simultaneous-sessions" />: &nbsp;
          <b>
            {(sessionsData && sessionsData[0]?.concurrent_impressions) || 0}
          </b>
        </h5>
        {lastUpdatedSession && (
          <NextUpdateTime time={lastUpdatedSession} className="mt-0 m-0" />
        )}
      </div>
      {/* <div className=" mb-0 right">
        {roleType && (
          <h3 id="user-role">
            <IntlMessages id="home.exp" />
            <b>{getTeamUserRoleType(roleType)}</b>
          </h3>
        )}
      </div> */}
    </div>
  );
};

const SubWarning = ({ text }) => {
  return (
    <h5 className="mt-2" style={{ color: '#bd3439' }}>
      <img
        src="/assets/icons/home/<USER>"
        alt="caution"
        height={20}
        className="mr-1"
        style={{ marginTop: '-4px', mixBlendMode: 'multiply' }}
      />
      <IntlMessages id={text} />
    </h5>
  );
};

const Actioncards = ({ history, roletype }) => {
  const { accessManagement, subscriptionExpired, isLoading } = useSelector(
    (state) => state.subscription
  );

  const handleAccessManagement = () => {
    if (accessManagement) return history.push('/app/team');
    if (roletype >= RoleTypes.ADMIN) {
      if (roletype <= RoleTypes.BILLING_ADMIN)
        return history.push(
          `${adminRoot}/help-support?subs=dashboardAnalytics`
        );
      return NotificationManager.warning(
        <IntlMessages id="subs.billingadminOrabove" />,
        'Not Allowed',
        5000,
        null,
        null
      );
    }
    return history.push(`${adminRoot}/help-support?subs=accessManagement`);
  };

  const handleShowcaseClick = () => {
    history.push(`${adminRoot}/showcase`);
  };

  const showingInvite = roletype < RoleTypes.CONTENT_MANAGER;

  return (
    <div
      className={`d-flex flex-row mt-5 w-full mb-3 home-mid-cards ${
        !showingInvite ? 'justify-content-center' : ''
      }`}
    >
      {showingInvite && (
        <Actioncard
          id="Invite"
          description="team-invite-home"
          title="team-invite-home2"
          btnDisabled={roletype > RoleTypes.ADMIN || subscriptionExpired}
          btnName={accessManagement ? 'home-invite' : 'home-upgrade'}
          onClick={handleAccessManagement}
        />
      )}
      <Actioncard
        id="Showcase"
        title="Experience Community Showcase"
        description="Create a media hub for your team, students, or fans. Share your media with the Innerloop Community and explore global content!"
        btnName="Explore Now"
        singleCard={!showingInvite}
        btnDisabled={false}
        onClick={handleShowcaseClick}
      />
    </div>
  );
};

const QuickLinks = ({ expiredSubscriptions, defaultSpaceId, history }) => {
  const quickLinks = [
    {
      id: 1,
      description: 'Manage Your Videos and Streaming Experience',
      className: 'simple-icon-camrecorder',
      title: 'Manage Videos'
    },
    {
      id: 3,
      description: 'Manage your Images',
      className: 'simple-icon-picture',
      title: 'Manage Images'
    },
    {
      id: 2,
      description: 'Manage Your Audios and Streaming Experience',
      className: 'simple-icon-earphones',
      title: 'Manage Audios'
    },
    {
      id: 4,
      description: 'Manage your Brand Narratives',
      className: null,
      icon: <StoryIcon width={30} />,
      title: 'Manage Brand Narratives'
    }
  ];

  const handleOnClick = (id) => {
    if (id === 1) {
      history.push(
        `${adminRoot}/spaces/space/${defaultSpaceId}/manage-content?tabIndex=1`
      );
    } else if (id === 2) {
      history.push(
        `${adminRoot}/spaces/space/${defaultSpaceId}/manage-content?tabIndex=2`
      );
    } else if (id === 3) {
      history.push(
        `${adminRoot}/spaces/space/${defaultSpaceId}/manage-content?tabIndex=3`
      );
    } else if (id === 4) {
      history.push(`${adminRoot}/spaces/space/${defaultSpaceId}/stories`);
    } else {
      history.push(`${adminRoot}/home?tutorial=true`);
    }
  };

  return (
    <Row className="justify-content-between gap-1 m-0 my-4 w-full ">
      {quickLinks.map((q, i) => (
        <Card
          aria-disabled={q.isDisabled || expiredSubscriptions}
          onClick={() => handleOnClick(q.id)}
          key={q.id}
          className="featrure-card"
        >
          <div className="icon-cont text-primary-hover centered">
            {q.className ? (
              <i style={{ fontSize: '30px' }} className={q.className} />
            ) : (
              q.icon
            )}
          </div>
          <div className="text-cont">
            <p className="text-primary mb-0">{q.description}</p>
          </div>
        </Card>
      ))}
    </Row>
  );
};

export default injectIntl(Home);
