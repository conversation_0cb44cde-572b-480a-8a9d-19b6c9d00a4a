/* eslint-disable jsx-a11y/no-static-element-interactions */
import React from 'react';

const AddtoDescription = ({ onClick, isCopy }) => {
  return (
    <span
      onClick={onClick}
      style={{
        border: '1px solid #FFCAFA',
        padding: '6px 10px',
        borderRadius: '20px',
        width: 'fit-content',
      }}
      className="text-primary c-pointer"
    >
      {isCopy ? 'Copy' : 'Add'} to Description
    </span>
  );
};

const Clear = ({ onClick }) => {
  return (
    <span
      onClick={onClick}
      className="c-pointer"
      style={{
        border: '1px solid #FFCAFA',
        padding: '6px',
        borderRadius: '5px',
        width: 'fit-content',
      }}
    >
      <img src="/assets/icons/clean.svg" alt="clean" />
    </span>
  );
};

const Expand = ({ onClick }) => {
  return (
    <span
      onClick={onClick}
      className="c-pointer"
      style={{
        border: '1px solid #FFCAFA',
        padding: '6px',
        borderRadius: '5px',
        width: 'fit-content',
      }}
    >
      <img src="/assets/icons/expand.svg" alt="expand" />
    </span>
  );
};

export { AddtoDescription, Clear, Expand };
