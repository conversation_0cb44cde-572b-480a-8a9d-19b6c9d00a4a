import { Colxx } from 'components/common/CustomBootstrap';
import React, { useEffect, useState } from 'react';
import useWindowDimensions from 'hooks/useWindowDimensions';
import { Card } from 'reactstrap';
import IntlMessages from 'helpers/IntlMessages';
import BrowseShowcase from 'components/BrowserShowcase';

const AuthImageCard = ({ Img, setCardBorder }) => {
  const [imgCardBorder, setImgCardBorder] = useState('0px 20px 20px 0px');

  // eslint-disable-next-line no-unused-vars
  const { height, width } = useWindowDimensions();
  useEffect(() => {
    if (width < 768) {
      setCardBorder('0px 0px 20px 20px');
      setImgCardBorder('20px 20px 0px 0px');
    } else {
      setCardBorder('20px 0px 0px 20px');
      setImgCardBorder('0px 20px 20px 0px');
    }
  }, [setCardBorder, width]);
  return (
    <Card
      style={{
        flex: 0.7,
        borderRadius: imgCardBorder,
        minHeight: '10vh'
      }}
    >
      <div
        style={{
          flex: 1,
          backgroundImage: `url(${Img})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          borderRadius: imgCardBorder,
          position: 'absolute',
          width: '100%',
          height: '100%'
        }}
      />
      <Colxx
        className="mx-auto d-flex flex-column justify-content-end align-items-center"
        style={{ marginBottom: '10%' }}
      >
        <h1 className="text-white caps text-center">
          <IntlMessages id="user-welcome-message" />
        </h1>
        <p
          className="text-center"
          style={{ fontSize: 13, fontWeight: 100, color: '#ffffff9e' }}
        >
          <IntlMessages id="user-welcome-desc" />
        </p>
        <BrowseShowcase />
      </Colxx>
    </Card>
  );
};

export default AuthImageCard;
