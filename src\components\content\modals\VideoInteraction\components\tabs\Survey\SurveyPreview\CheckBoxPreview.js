/* eslint-disable jsx-a11y/aria-proptypes */
/* eslint-disable no-unneeded-ternary */
/* eslint-disable react/no-array-index-key */
/* eslint-disable react/self-closing-comp */
import React from 'react';

const CheckBoxPreview = ({ values, answer }) => {
  return (
    <div className="CheckBoxPreview" aria-disabled={answer ? 'view' : false}>
      {values.map((m, i) => (
        <div key={i} className="CheckBoxPreviewRow">
          <input
            type="checkbox"
            checked={!answer ? false : answer?.some((s) => s === m.value)}
            disabled={answer ? false : true}
          />
          <span>{m.value}</span>
        </div>
      ))}
    </div>
  );
};

export default CheckBoxPreview;
