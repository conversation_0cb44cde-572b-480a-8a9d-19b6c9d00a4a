import React, { useState } from 'react';
import { Button } from 'reactstrap';

const InteractionButton = ({ isActive, onClick }) => {
  const [hovered, setHovered] = useState(false);

  return (
    <span
      style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center'
      }}
    >
      <Button
        color={isActive && 'primary'}
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
        style={{
          height: '3rem',
          width: '3rem',
          marginRight: '1rem'
        }}
        onClick={onClick}
        className={!isActive && 'badge-outline-primary btn btn-outline-primary'}
      >
        <span style={{ position: 'relative', left: '-5px' }}>
          <InteractionIcon active={isActive} h={16} w={16} hovered={hovered} />
        </span>
      </Button>
      <span
        style={{
          left: '-10px',
          position: 'relative',
          color: '#992288',
          textAlign: 'center'
        }}
      >
        Interactions
        {/* Add Interactions */}
      </span>
    </span>
  );
};

export default InteractionButton;

const InteractionIcon = ({ active, h = 30, w = 30, hovered }) => {
  const fill = active || hovered ? '#fff' : '#992288';
  return (
    <svg
      width={w}
      height={h}
      viewBox="0 0 25 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M21.5 13.053V13.226M21.5 13.226C21.4969 12.7994 21.3423 12.3878 21.0639 12.0645C20.7855 11.7412 20.4014 11.5274 19.98 11.461L18.773 11.263V12.158M21.5 13.226V15.439C21.5 17.383 21.5 18.355 21.199 19.129C20.9693 19.7168 20.6187 20.2497 20.1697 20.6931C19.7207 21.1366 19.1835 21.4807 18.593 21.703C17.809 22 16.823 22 14.854 22C13.821 22 13.304 22 12.824 21.896C12.0921 21.7376 11.4106 21.401 10.84 20.916C10.467 20.599 10.157 20.191 9.53795 19.376L6.80695 15.78C6.60328 15.5124 6.49541 15.1842 6.50067 14.848C6.50593 14.5118 6.62401 14.1871 6.83595 13.926C6.97201 13.7595 7.1413 13.6231 7.33301 13.5257C7.52472 13.4282 7.73464 13.3718 7.94938 13.3601C8.16411 13.3483 8.37894 13.3814 8.58015 13.4573C8.78135 13.5333 8.96452 13.6503 9.11795 13.801L10.5 15.256V6.5C10.5 6.10218 10.658 5.72064 10.9393 5.43934C11.2206 5.15804 11.6021 5 12 5C12.3978 5 12.7793 5.15804 13.0606 5.43934C13.3419 5.72064 13.5 6.10218 13.5 6.5V9.474H14.227C15.231 9.474 16.046 10.274 16.046 11.264M13.5 9.473V12.157M16.046 12.157V11.262V10.367H16.955C17.959 10.367 18.773 11.169 18.773 12.157V13.052"
        stroke={fill}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.5 8H6.676C5.18 8 4.43 8 3.966 7.56C3.5 7.122 3.5 6.415 3.5 5C3.5 3.585 3.5 2.879 3.965 2.44C4.43 2.001 5.18 2 6.676 2H18.323C19.821 2 20.57 2 21.035 2.44C21.5 2.878 21.5 3.585 21.5 5C21.5 6.415 21.5 7.121 21.035 7.56C20.57 7.999 19.82 8 18.323 8H17"
        stroke={fill}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
