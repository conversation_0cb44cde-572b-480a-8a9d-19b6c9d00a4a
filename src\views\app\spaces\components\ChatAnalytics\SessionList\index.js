/* eslint-disable react/no-array-index-key */
import InfoMessage from 'components/infoMessage';
import React, { useEffect, useState } from 'react';
import SessionCard from './SessionCard';
import { CHAT_ANALYTICS_TYPE } from 'constants/defaultValues';
import { SessionListLoading } from '../loading';
import InfoPopover from 'components/InfoPopover';
import SessionEmpty from './SessionEmpty';
import SessionHeader from './SessionHeader';

function sortSessionsByFirstEventTimestamp(sessions) {
  return sessions.sort((a, b) => {
    const dateA = new Date(a.event_details[0]?.datetimestamp.value || 0);
    const dateB = new Date(b.event_details[0]?.datetimestamp.value || 0);
    return dateB - dateA;
  });
}
const SessionList = ({ data, active, setOpenedSession, isQna }) => {
  const [loading, setLoading] = useState(true);
  const [list, setList] = useState([]);

  useEffect(() => {
    if (data) {
      setLoading(true);
      const { date, hour, month } = data;
      const sessionsList = (() => {
        if (+active === CHAT_ANALYTICS_TYPE.DATE) {
          return date.sessions;
        }
        if (+active === CHAT_ANALYTICS_TYPE.HOUR) {
          return hour.sessions;
        }
        if (+active === CHAT_ANALYTICS_TYPE.MONTH) {
          return month.sessions;
        }
        return [];
      })();
      setList(sortSessionsByFirstEventTimestamp(sessionsList));
      setLoading(false);
    }
  }, [active, data]);

  if (loading) {
    return <SessionListLoading />;
  }

  if (list.length === 0) {
    return <SessionEmpty />;
  }

  const infoMessage = (() => {
    if (+active === CHAT_ANALYTICS_TYPE.DATE) {
      return 'Sessions for last 30 days';
    }
    if (+active === CHAT_ANALYTICS_TYPE.HOUR) {
      return 'Sessions for last 7 days';
    }
    return 'Sessions for last 12 months';
  })();

  const id = 'session-list-id';

  return (
    <div className="sessions">
      <div className="mb-3">
        <h1>Session Details</h1>
        <div className="d-flex flex-row align-items-center justify-content-center">
          <InfoPopover
            infoId={id}
            style={{
              fontSize: 14
            }}
            iconClassName="text-primary"
            message="Analytics are generated based on interactions from the shared and showcase views only"
            className="mr-2"
          />
          <InfoMessage
            showInfoIcon={false}
            className="mt-1"
            style={{ fontSize: 14 }}
            message={infoMessage}
            textAlign="center"
          />
        </div>
      </div>

      <SessionHeader isQna={isQna} />

      <div>
        {list.map((m, i) => (
          <SessionCard
            key={i}
            isQna={isQna}
            data={m}
            onClick={() => {
              setOpenedSession(m);
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default SessionList;
