import React from 'react';

const QuestionIcon = () => {
  return (
    <svg width="38" height="38" viewBox="0 0 38 38" fill="none">
      <circle cx="19" cy="19" r="19" fill="#EEF9FF" />
      <path
        d="M26.0159 21.4167C26.5875 21.4172 27.1354 21.6446 27.5394 22.0489C27.9434 22.4533 28.1703 23.0014 28.1703 23.573V24.124C28.1703 24.9808 27.8636 25.8097 27.3078 26.4604C25.8032 28.217 23.5156 29.0843 20.5007 29.0843C17.4858 29.0843 15.1992 28.217 13.6985 26.4585C13.1432 25.8081 12.8381 24.9811 12.8379 24.1259V23.572C12.8381 23.0005 13.0653 22.4524 13.4694 22.0483C13.8736 21.6441 14.4216 21.417 14.9932 21.4167H26.0159ZM26.0159 22.8542H14.9922C14.8016 22.8542 14.6188 22.9299 14.484 23.0647C14.3492 23.1995 14.2735 23.3823 14.2735 23.573V24.1259C14.2735 24.6386 14.4575 25.1351 14.791 25.5251C15.9918 26.9329 17.8768 27.6468 20.4998 27.6468C23.1246 27.6468 25.0097 26.9329 26.2143 25.526C26.5484 25.1353 26.732 24.6381 26.7318 24.124V23.572C26.7316 23.3819 26.656 23.1996 26.5216 23.0651C26.3873 22.9305 26.2061 22.8547 26.0159 22.8542ZM20.5007 9.92151C21.13 9.92151 21.7531 10.0454 22.3344 10.2863C22.9158 10.5271 23.444 10.88 23.8889 11.325C24.3339 11.7699 24.6868 12.2981 24.9276 12.8795C25.1685 13.4608 25.2924 14.0839 25.2924 14.7132C25.2924 15.3424 25.1685 15.9655 24.9276 16.5469C24.6868 17.1282 24.3339 17.6564 23.8889 18.1014C23.444 18.5463 22.9158 18.8993 22.3344 19.1401C21.7531 19.3809 21.13 19.5048 20.5007 19.5048C19.2299 19.5048 18.0111 19 17.1125 18.1014C16.2139 17.2028 15.7091 15.984 15.7091 14.7132C15.7091 13.4423 16.2139 12.2236 17.1125 11.325C18.0111 10.4263 19.2299 9.92151 20.5007 9.92151ZM20.5007 11.359C20.0602 11.359 19.6241 11.4458 19.2171 11.6143C18.8102 11.7829 18.4404 12.03 18.129 12.3414C17.8175 12.6529 17.5704 13.0226 17.4019 13.4296C17.2333 13.8365 17.1466 14.2727 17.1466 14.7132C17.1466 15.1537 17.2333 15.5898 17.4019 15.9968C17.5704 16.4037 17.8175 16.7735 18.129 17.0849C18.4404 17.3964 18.8102 17.6435 19.2171 17.812C19.6241 17.9806 20.0602 18.0673 20.5007 18.0673C21.3903 18.0673 22.2434 17.714 22.8725 17.0849C23.5015 16.4559 23.8549 15.6028 23.8549 14.7132C23.8549 13.8236 23.5015 12.9704 22.8725 12.3414C22.2434 11.7124 21.3903 11.359 20.5007 11.359Z"
        fill="#2CA0F4"
      />
    </svg>
  );
};

const AnswerIcon = () => {
  return (
    <svg
      width="38"
      height="38"
      viewBox="0 0 38 38"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="19" cy="19" r="19" fill="#FFE0FC" />
      <g clipPath="url(#clip0_334_167)">
        <path
          d="M25.4999 17.255C25.4999 17.2997 25.4989 17.344 25.4969 17.388C25.2146 17.1376 24.8502 16.9995 24.4729 17C24.3109 17 24.1532 17.0247 23.9999 17.074V12.75C23.9999 12.5511 23.9209 12.3603 23.7802 12.2197C23.6396 12.079 23.4488 12 23.2499 12H14.7499C14.551 12 14.3602 12.079 14.2196 12.2197C14.0789 12.3603 13.9999 12.5511 13.9999 12.75V17.255C13.9999 17.669 14.3359 18.005 14.7499 18.005H23.0259L23.0159 18.03L23.0129 18.042L22.5629 19.426L22.5529 19.452L22.5339 19.505H14.7499C14.1532 19.505 13.5809 19.2679 13.1589 18.846C12.737 18.424 12.4999 17.8517 12.4999 17.255V12.75C12.4999 12.1533 12.737 11.581 13.1589 11.159C13.5809 10.7371 14.1532 10.5 14.7499 10.5H18.2499V9.75C18.2499 9.56861 18.3157 9.39339 18.435 9.25677C18.5544 9.12016 18.7192 9.03143 18.8989 9.007L18.9999 9C19.1813 9.00004 19.3565 9.06582 19.4931 9.18514C19.6297 9.30447 19.7185 9.46926 19.7429 9.649L19.7499 9.75L19.7489 10.5H23.2489C23.8456 10.5 24.4179 10.7371 24.8399 11.159C25.2619 11.581 25.4989 12.1533 25.4989 12.75L25.4999 17.255ZM20.0429 21.036L20.1549 21H13.2539C12.6572 21 12.0849 21.2371 11.6629 21.659C11.241 22.081 11.0039 22.6533 11.0039 23.25V24.157C11.0038 24.6971 11.1204 25.2308 11.3456 25.7216C11.5708 26.2125 11.8994 26.6489 12.3089 27.001C13.8719 28.344 16.1109 29.001 18.9999 29.001C21.0759 29.001 22.8169 28.662 24.2129 27.973C23.9475 27.9222 23.7001 27.8029 23.495 27.627C23.2899 27.451 23.1344 27.2246 23.0439 26.97L23.0399 26.958L23.0099 26.865C21.9239 27.287 20.5899 27.501 18.9999 27.501C16.4409 27.501 14.5449 26.945 13.2869 25.863C13.0413 25.6518 12.8441 25.39 12.709 25.0956C12.5739 24.8011 12.5039 24.481 12.5039 24.157V23.25C12.5039 23.0511 12.5829 22.8603 12.7236 22.7197C12.8642 22.579 13.055 22.5 13.2539 22.5H18.9999V22.497C18.9994 22.1773 19.0983 21.8654 19.2831 21.6045C19.4678 21.3436 19.7292 21.1467 20.0309 21.041L20.0429 21.036ZM17.9989 14.75C18.0036 14.5829 17.9748 14.4166 17.9141 14.2609C17.8535 14.1052 17.7622 13.9632 17.6457 13.8434C17.5292 13.7235 17.3899 13.6283 17.2359 13.5632C17.082 13.4982 16.9165 13.4646 16.7494 13.4646C16.5823 13.4646 16.4168 13.4982 16.2629 13.5632C16.1089 13.6283 15.9696 13.7235 15.8531 13.8434C15.7366 13.9632 15.6453 14.1052 15.5847 14.2609C15.524 14.4166 15.4952 14.5829 15.4999 14.75C15.5091 15.0753 15.6448 15.3841 15.8781 15.6109C16.1114 15.8378 16.424 15.9646 16.7494 15.9646C17.0748 15.9646 17.3874 15.8378 17.6207 15.6109C17.854 15.3841 17.9897 15.0753 17.9989 14.75ZM21.2419 13.5C21.409 13.4953 21.5753 13.5241 21.731 13.5848C21.8867 13.6454 22.0287 13.7367 22.1486 13.8532C22.2684 13.9697 22.3636 14.109 22.4287 14.263C22.4937 14.4169 22.5273 14.5824 22.5273 14.7495C22.5273 14.9166 22.4937 15.0821 22.4287 15.236C22.3636 15.39 22.2684 15.5293 22.1486 15.6458C22.0287 15.7623 21.8867 15.8536 21.731 15.9142C21.5753 15.9749 21.409 16.0037 21.2419 15.999C20.9166 15.9898 20.6078 15.8541 20.381 15.6208C20.1541 15.3875 20.0273 15.0749 20.0273 14.7495C20.0273 14.4241 20.1541 14.1115 20.381 13.8782C20.6078 13.6449 20.9166 13.5092 21.2419 13.5ZM23.0889 24.412C22.7446 23.9667 22.2752 23.6342 21.7409 23.457L20.3639 23.009C20.2581 22.9713 20.1666 22.9019 20.1018 22.8101C20.0371 22.7183 20.0023 22.6088 20.0023 22.4965C20.0023 22.3842 20.0371 22.2747 20.1018 22.1829C20.1666 22.0911 20.2581 22.0217 20.3639 21.984L21.7409 21.536C22.1489 21.3953 22.5194 21.1635 22.8243 20.8582C23.1293 20.5529 23.3607 20.1821 23.5009 19.774L23.5109 19.74L23.9599 18.363C23.9974 18.2569 24.0668 18.165 24.1587 18.0999C24.2506 18.0349 24.3604 18 24.4729 18C24.5855 18 24.6952 18.0349 24.7871 18.0999C24.879 18.165 24.9485 18.2569 24.9859 18.363L25.4339 19.74C25.5735 20.1584 25.8087 20.5386 26.1208 20.8503C26.4329 21.1621 26.8133 21.3968 27.2319 21.536L28.6099 21.984L28.6369 21.991C28.7427 22.0287 28.8343 22.0981 28.899 22.1899C28.9637 22.2817 28.9985 22.3912 28.9985 22.5035C28.9985 22.6158 28.9637 22.7253 28.899 22.8171C28.8343 22.9089 28.7427 22.9783 28.6369 23.016L27.2589 23.464C26.8403 23.6032 26.4599 23.8379 26.1478 24.1497C25.8357 24.4614 25.6005 24.8416 25.4609 25.26L25.0139 26.637C24.9758 26.7431 24.906 26.8349 24.8139 26.9C24.7452 26.9485 24.6663 26.9805 24.5832 26.9934C24.5001 27.0063 24.4151 26.9997 24.335 26.9742C24.2548 26.9487 24.1817 26.905 24.1213 26.8465C24.0609 26.788 24.0149 26.7163 23.9869 26.637L23.5389 25.26C23.4388 24.9535 23.2866 24.6667 23.0889 24.412ZM30.7829 28.213L30.0179 27.965C29.7854 27.8875 29.5741 27.757 29.4007 27.5838C29.2273 27.4106 29.0966 27.1994 29.0189 26.967L28.7699 26.202C28.7492 26.1429 28.7107 26.0918 28.6596 26.0555C28.6085 26.0193 28.5475 25.9999 28.4849 25.9999C28.4223 25.9999 28.3613 26.0193 28.3102 26.0555C28.2592 26.0918 28.2206 26.1429 28.1999 26.202L27.9509 26.966C27.8749 27.1969 27.7467 27.4072 27.5762 27.5805C27.4057 27.7537 27.1976 27.8853 26.9679 27.965L26.2019 28.213C26.1428 28.2337 26.0917 28.2723 26.0555 28.3233C26.0192 28.3744 25.9998 28.4354 25.9998 28.498C25.9998 28.5606 26.0192 28.6216 26.0555 28.6727C26.0917 28.7237 26.1428 28.7623 26.2019 28.783L26.9679 29.032C27.2008 29.1099 27.4124 29.241 27.5858 29.4149C27.7593 29.5889 27.8897 29.8008 27.9669 30.034L28.2149 30.798C28.2361 30.8566 28.2748 30.9073 28.3258 30.9431C28.3768 30.9789 28.4376 30.9981 28.4999 30.9981C28.5622 30.9981 28.623 30.9789 28.674 30.9431C28.725 30.9073 28.7637 30.8566 28.7849 30.798L29.0349 30.034C29.1124 29.8015 29.2429 29.5902 29.4161 29.4168C29.5893 29.2434 29.8005 29.1127 30.0329 29.035L30.7989 28.787C30.858 28.7663 30.9091 28.7277 30.9454 28.6767C30.9816 28.6256 31.001 28.5646 31.001 28.502C31.001 28.4394 30.9816 28.3784 30.9454 28.3273C30.9091 28.2763 30.858 28.2377 30.7989 28.217L30.7829 28.213Z"
          fill="#922C88"
        />
      </g>
      <defs>
        <clipPath id="clip0_334_167">
          <rect
            width="24"
            height="24"
            fill="white"
            transform="translate(7 7)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

const MidyaAiIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_330_165)">
        <path
          d="M18.4999 10.255C18.4999 10.2997 18.4989 10.344 18.4969 10.388C18.2146 10.1376 17.8502 9.99951 17.4729 10C17.3109 10 17.1532 10.0247 16.9999 10.074V5.75C16.9999 5.55109 16.9209 5.36032 16.7802 5.21967C16.6396 5.07902 16.4488 5 16.2499 5H7.74991C7.55099 5 7.36023 5.07902 7.21958 5.21967C7.07892 5.36032 6.99991 5.55109 6.99991 5.75V10.255C6.99991 10.669 7.33591 11.005 7.74991 11.005H16.0259L16.0159 11.03L16.0129 11.042L15.5629 12.426L15.5529 12.452L15.5339 12.505H7.74991C7.15317 12.505 6.58087 12.2679 6.15892 11.846C5.73696 11.424 5.49991 10.8517 5.49991 10.255V5.75C5.49991 5.15326 5.73696 4.58097 6.15892 4.15901C6.58087 3.73705 7.15317 3.5 7.74991 3.5H11.2499V2.75C11.2499 2.56861 11.3157 2.39339 11.435 2.25677C11.5544 2.12016 11.7192 2.03143 11.8989 2.007L11.9999 2C12.1813 2.00004 12.3565 2.06582 12.4931 2.18514C12.6297 2.30447 12.7185 2.46926 12.7429 2.649L12.7499 2.75L12.7489 3.5H16.2489C16.8456 3.5 17.4179 3.73705 17.8399 4.15901C18.2619 4.58097 18.4989 5.15326 18.4989 5.75L18.4999 10.255ZM13.0429 14.036L13.1549 14H6.25391C5.65717 14 5.08487 14.2371 4.66292 14.659C4.24096 15.081 4.00391 15.6533 4.00391 16.25V17.157C4.00381 17.6971 4.12036 18.2308 4.34559 18.7216C4.57083 19.2125 4.89942 19.6489 5.30891 20.001C6.87191 21.344 9.11091 22.001 11.9999 22.001C14.0759 22.001 15.8169 21.662 17.2129 20.973C16.9475 20.9222 16.7001 20.8029 16.495 20.627C16.2899 20.451 16.1344 20.2246 16.0439 19.97L16.0399 19.958L16.0099 19.865C14.9239 20.287 13.5899 20.501 11.9999 20.501C9.44091 20.501 7.54491 19.945 6.28691 18.863C6.04127 18.6518 5.84414 18.39 5.709 18.0956C5.57387 17.8011 5.50391 17.481 5.50391 17.157V16.25C5.50391 16.0511 5.58292 15.8603 5.72358 15.7197C5.86423 15.579 6.05499 15.5 6.25391 15.5H11.9999V15.497C11.9994 15.1773 12.0983 14.8654 12.2831 14.6045C12.4678 14.3436 12.7292 14.1467 13.0309 14.041L13.0429 14.036ZM10.9989 7.75C11.0036 7.58294 10.9748 7.41663 10.9141 7.2609C10.8535 7.10517 10.7622 6.96319 10.6457 6.84335C10.5292 6.72352 10.3899 6.62826 10.2359 6.56321C10.082 6.49816 9.91653 6.46465 9.74941 6.46465C9.58228 6.46465 9.41685 6.49816 9.2629 6.56321C9.10895 6.62826 8.9696 6.72352 8.85311 6.84335C8.73662 6.96319 8.64534 7.10517 8.58467 7.2609C8.524 7.41663 8.49518 7.58294 8.49991 7.75C8.50911 8.07527 8.64479 8.38413 8.87811 8.61094C9.11143 8.83776 9.42401 8.96465 9.74941 8.96465C10.0748 8.96465 10.3874 8.83776 10.6207 8.61094C10.854 8.38413 10.9897 8.07527 10.9989 7.75ZM14.2419 6.5C14.409 6.49527 14.5753 6.5241 14.731 6.58476C14.8867 6.64543 15.0287 6.73671 15.1486 6.8532C15.2684 6.9697 15.3636 7.10904 15.4287 7.26299C15.4937 7.41694 15.5273 7.58237 15.5273 7.7495C15.5273 7.91663 15.4937 8.08206 15.4287 8.23601C15.3636 8.38996 15.2684 8.5293 15.1486 8.64579C15.0287 8.76229 14.8867 8.85357 14.731 8.91424C14.5753 8.9749 14.409 9.00373 14.2419 8.999C13.9166 8.9898 13.6078 8.85412 13.381 8.6208C13.1541 8.38747 13.0273 8.0749 13.0273 7.7495C13.0273 7.4241 13.1541 7.11153 13.381 6.8782C13.6078 6.64488 13.9166 6.5092 14.2419 6.5ZM16.0889 17.412C15.7446 16.9667 15.2752 16.6342 14.7409 16.457L13.3639 16.009C13.2581 15.9713 13.1666 15.9019 13.1018 15.8101C13.0371 15.7183 13.0023 15.6088 13.0023 15.4965C13.0023 15.3842 13.0371 15.2747 13.1018 15.1829C13.1666 15.0911 13.2581 15.0217 13.3639 14.984L14.7409 14.536C15.1489 14.3953 15.5194 14.1635 15.8243 13.8582C16.1293 13.5529 16.3607 13.1821 16.5009 12.774L16.5109 12.74L16.9599 11.363C16.9974 11.2569 17.0668 11.165 17.1587 11.0999C17.2506 11.0349 17.3604 11 17.4729 11C17.5855 11 17.6952 11.0349 17.7871 11.0999C17.879 11.165 17.9485 11.2569 17.9859 11.363L18.4339 12.74C18.5735 13.1584 18.8087 13.5386 19.1208 13.8503C19.4329 14.1621 19.8133 14.3968 20.2319 14.536L21.6099 14.984L21.6369 14.991C21.7427 15.0287 21.8343 15.0981 21.899 15.1899C21.9637 15.2817 21.9985 15.3912 21.9985 15.5035C21.9985 15.6158 21.9637 15.7253 21.899 15.8171C21.8343 15.9089 21.7427 15.9783 21.6369 16.016L20.2589 16.464C19.8403 16.6032 19.4599 16.8379 19.1478 17.1497C18.8357 17.4614 18.6005 17.8416 18.4609 18.26L18.0139 19.637C17.9758 19.7431 17.906 19.8349 17.8139 19.9C17.7452 19.9485 17.6663 19.9805 17.5832 19.9934C17.5001 20.0063 17.4151 19.9997 17.335 19.9742C17.2548 19.9487 17.1817 19.905 17.1213 19.8465C17.0609 19.788 17.0149 19.7163 16.9869 19.637L16.5389 18.26C16.4388 17.9535 16.2866 17.6667 16.0889 17.412ZM23.7829 21.213L23.0179 20.965C22.7854 20.8875 22.5741 20.757 22.4007 20.5838C22.2273 20.4106 22.0966 20.1994 22.0189 19.967L21.7699 19.202C21.7492 19.1429 21.7107 19.0918 21.6596 19.0555C21.6085 19.0193 21.5475 18.9999 21.4849 18.9999C21.4223 18.9999 21.3613 19.0193 21.3102 19.0555C21.2592 19.0918 21.2206 19.1429 21.1999 19.202L20.9509 19.966C20.8749 20.1969 20.7467 20.4072 20.5762 20.5805C20.4057 20.7537 20.1976 20.8853 19.9679 20.965L19.2019 21.213C19.1428 21.2337 19.0917 21.2723 19.0555 21.3233C19.0192 21.3744 18.9998 21.4354 18.9998 21.498C18.9998 21.5606 19.0192 21.6216 19.0555 21.6727C19.0917 21.7237 19.1428 21.7623 19.2019 21.783L19.9679 22.032C20.2008 22.1099 20.4124 22.241 20.5858 22.4149C20.7593 22.5889 20.8897 22.8008 20.9669 23.034L21.2149 23.798C21.2361 23.8566 21.2748 23.9073 21.3258 23.9431C21.3768 23.9789 21.4376 23.9981 21.4999 23.9981C21.5622 23.9981 21.623 23.9789 21.674 23.9431C21.725 23.9073 21.7637 23.8566 21.7849 23.798L22.0349 23.034C22.1124 22.8015 22.2429 22.5902 22.4161 22.4168C22.5893 22.2434 22.8005 22.1127 23.0329 22.035L23.7989 21.787C23.858 21.7663 23.9091 21.7277 23.9454 21.6767C23.9816 21.6256 24.001 21.5646 24.001 21.502C24.001 21.4394 23.9816 21.3784 23.9454 21.3273C23.9091 21.2763 23.858 21.2377 23.7989 21.217L23.7829 21.213Z"
          fill="#3A424C"
        />
      </g>
      <defs>
        <clipPath id="clip0_330_165">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

const DetailIcon = () => {
  return (
    <svg
      width="30"
      height="30"
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g>
        <circle cx="15" cy="15" r="14.5" stroke="#922C88" />
        <path
          d="M7.6875 9.5625C7.53832 9.5625 7.39524 9.62506 7.28975 9.73641C7.18426 9.84776 7.125 9.99878 7.125 10.1562C7.125 10.3137 7.18426 10.4647 7.28975 10.5761C7.39524 10.6874 7.53832 10.75 7.6875 10.75H22.3125C22.4617 10.75 22.6048 10.6874 22.7102 10.5761C22.8157 10.4647 22.875 10.3137 22.875 10.1562C22.875 9.99878 22.8157 9.84776 22.7102 9.73641C22.6048 9.62506 22.4617 9.5625 22.3125 9.5625H7.6875ZM7.6875 13.125C7.53832 13.125 7.39524 13.1876 7.28975 13.2989C7.18426 13.4103 7.125 13.5613 7.125 13.7188C7.125 13.8762 7.18426 14.0272 7.28975 14.1386C7.39524 14.2499 7.53832 14.3125 7.6875 14.3125H22.3125C22.4617 14.3125 22.6048 14.2499 22.7102 14.1386C22.8157 14.0272 22.875 13.8762 22.875 13.7188C22.875 13.5613 22.8157 13.4103 22.7102 13.2989C22.6048 13.1876 22.4617 13.125 22.3125 13.125H7.6875ZM7.125 17.2812C7.125 17.4387 7.18426 17.5897 7.28975 17.7011C7.39524 17.8124 7.53832 17.875 7.6875 17.875H22.3125C22.4617 17.875 22.6048 17.8124 22.7102 17.7011C22.8157 17.5897 22.875 17.4387 22.875 17.2812C22.875 17.1238 22.8157 16.9728 22.7102 16.8614C22.6048 16.7501 22.4617 16.6875 22.3125 16.6875H7.6875C7.53832 16.6875 7.39524 16.7501 7.28975 16.8614C7.18426 16.9728 7.125 17.1238 7.125 17.2812ZM7.6875 20.25C7.53832 20.25 7.39524 20.3126 7.28975 20.4239C7.18426 20.5353 7.125 20.6863 7.125 20.8438C7.125 21.0012 7.18426 21.1522 7.28975 21.2636C7.39524 21.3749 7.53832 21.4375 7.6875 21.4375H17.25C17.3992 21.4375 17.5423 21.3749 17.6477 21.2636C17.7532 21.1522 17.8125 21.0012 17.8125 20.8438C17.8125 20.6863 17.7532 20.5353 17.6477 20.4239C17.5423 20.3126 17.3992 20.25 17.25 20.25H7.6875Z"
          fill="#922C88"
        />
      </g>
    </svg>
  );
};

export { QuestionIcon, AnswerIcon, MidyaAiIcon, DetailIcon };
