/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/no-array-index-key */
/* eslint-disable no-unused-vars */
/* eslint-disable jsx-a11y/label-has-associated-control */
import {
  INITIAL_STATE_SURVEY,
  RESPONSE_TYPES
} from 'constants/VideoInteraction';
import React, { useEffect, useMemo, useState } from 'react';
import {
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  FormGroup,
  Input,
  Label,
  UncontrolledDropdown
} from 'reactstrap';
import ResponsesIcon from './ResponsesIcon';
import CheckboxInput from './AnswerInput/CheckboxInput';
import DateInput from './AnswerInput/DateInput';
import TextBoxInput from './AnswerInput/TextBoxInput';
import InfoMessage from 'components/infoMessage';
import { NotificationManager } from 'components/common/react-notifications';
import Remove from '../../Remove';

const responsesData = [
  {
    id: RESPONSE_TYPES.TEXT_BOX,
    name: 'Text Box'
  },
  {
    id: RESPONSE_TYPES.MCQ,
    name: 'Radio Options (MCQ)'
  },
  {
    id: RESPONSE_TYPES.CHECKBOX,
    name: 'Checkbox'
  },
  {
    id: RESPONSE_TYPES.DATE,
    name: 'Date'
  }
];

const QuesAnsCard = ({ index, data, polls, setPolls }) => {
  const { question, responseType, values } = data;

  const selectedName = useMemo(() => {
    return responsesData.find((f) => f.id === responseType)?.name;
  }, [responseType]);

  const handleRemove = () => {
    setPolls((prev) => prev.filter((f, i) => i !== index - 1));
  };

  useEffect(() => {
    const emptyValues = [
      {
        value: ''
      },
      {
        value: ''
      }
    ];

    setPolls((prev) => {
      const newPolls = prev.map((m, i) => {
        if (i === index - 1) {
          if (
            +m.responseType === RESPONSE_TYPES.CHECKBOX ||
            +m.responseType === RESPONSE_TYPES.MCQ
          ) {
            return {
              ...m,
              values: values.length > 0 ? values : emptyValues
            };
          }
          return {
            ...m,
            values: values.length > 0 ? values : INITIAL_STATE_SURVEY.values
          };
        }
        return m;
      });
      return newPolls;
    });
  }, [responseType, index, values]);

  return (
    <div className="QNCard">
      <div className="QNCardHeader">
        <label>Question {index}</label>
        {polls?.length !== 1 && <Remove onClick={handleRemove} />}
      </div>

      <TextBoxInput
        label="Question"
        isTextArea={false}
        value={question}
        isRequired
        className="w-full-imp"
        onChange={(e) => {
          const text = e.target?.value;
          if (text.length > 140) {
            NotificationManager.warning('Maximum characters are 140');
          }
          const value = text.substring(0, 140);
          setPolls((prev) => {
            const newPolls = prev.map((m, i) => {
              if (i === index - 1) {
                return {
                  ...m,
                  question: value
                };
              }
              return m;
            });
            return newPolls;
          });
        }}
      />

      <label>
        Response
        <span style={{ color: 'red' }}>*</span>
      </label>
      <UncontrolledDropdown className="QNCardDropdown">
        <DropdownToggle
          caret
          color="primary"
          className="btn-sm w-full "
          style={{
            textAlign: 'center',
            background: 'transparent',
            border: '1px solid #992288',
            color: '#992288',
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '5px'
          }}
        >
          <ResponsesIcon type={responseType} />
          {selectedName}
        </DropdownToggle>
        <DropdownMenu right className="w-full">
          {responsesData.map((m, i) => (
            <DropdownItem
              key={i}
              onClick={() => {
                setPolls((prev) => {
                  const newPrevs = prev.map((j, i) => {
                    if (index - 1 === i) {
                      return {
                        ...j,
                        responseType: m.id
                      };
                    }
                    return j;
                  });
                  return newPrevs;
                });
              }}
            >
              <ResponsesIcon type={m.id} /> &nbsp;
              {m.name}
            </DropdownItem>
          ))}
        </DropdownMenu>
      </UncontrolledDropdown>

      {+responseType === RESPONSE_TYPES.CHECKBOX ||
      +responseType === RESPONSE_TYPES.MCQ ? (
        <CheckboxInput index={index} values={values} setPolls={setPolls} />
      ) : +responseType === RESPONSE_TYPES.DATE ? (
        <DateInput
          disabled
          value={values[0].value}
          onChange={(e) => {
            const val = e.target.value;
            setPolls((prev) => {
              const newPrevs = prev.map((j, i) => {
                if (index - 1 === i) {
                  return {
                    ...j,
                    values: [
                      {
                        value: val
                      }
                    ]
                  };
                }
                return j;
              });
              return newPrevs;
            });
          }}
        />
      ) : (
        <TextBoxInput
          value={values[0].value}
          maxCharLimit={140}
          disabled
          onChange={(e) => {
            const val = e?.target?.value;
            console.log('val', val);
            setPolls((prev) => {
              const newPrevs = prev.map((j, i) => {
                if (index - 1 === i) {
                  return {
                    ...j,
                    values: [
                      {
                        value: val
                      }
                    ]
                  };
                }
                return j;
              });
              return newPrevs;
            });
          }}
        />
      )}
    </div>
  );
};

export default QuesAnsCard;
