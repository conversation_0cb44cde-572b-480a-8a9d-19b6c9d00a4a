import React from 'react';
import IntlMessages from 'helpers/IntlMessages';
import { <PERSON>ton, ModalBody } from 'reactstrap';
import { Colxx } from './CustomBootstrap';
import PortalModal from 'components/Portal/PortalModal';

const ConfirmationModal = ({
  openConfirmationModal,
  toggleConfirmationModal,
  handleConfirm,
  handleClose,
  type,
  mediaTitle,
  description,
  confirmText,
  cancelText
}) => {
  const titleSwitcher = () => {
    let title;
    switch (type) {
      case 'deleteContent':
        title = 'deleteContent.confirmation';
        break;
      case 'deletePlayer':
        title = 'deletePlayer.confirmation';
        break;
      case 'deleteStory':
        title = 'deleteStory.confirmation';
        break;
      case 'deleteWatermark':
        title = 'deleteWatermark.confirmation';
        break;
      case 'deleteThumbnail':
        title = 'deleteThumbnail.confirmation';
        break;
      case 'deletePosterImage':
        title = 'deletePosterImage.confirmation';
        break;
      case 'forcedStopRecording':
        title = 'forcedStopRecording.confirmation';
        break;
      case 'deleteProfile':
        title = 'delete-profile';
        break;
      case 'deleteBg':
        title = 'delete-bg';
        break;
      case 'deleteSubtitle':
        title = 'deleteSubtitle.confirmation';
        break;
      case 'deleteAIdesc':
        title = 'ai.deleteAIdesc';
        break;
      case 'retrigger':
        title = 'ai.retrigger';
        break;
      case 'confirmEditStory':
        title = 'story-confirm-msg';
        break;
      case 'confirmOptimization':
        title = 'optimization-confirm-msg';
        break;
      case 'confirmQuickOptimization':
        title = 'quick-optimization-confirm-msg';
        break;
      case 'confirmDetailedInsights':
        title = 'detailedInsights-confirm-msg';
        break;
      default:
        title = 'cancel.confirmation';
        break;
    }
    return title;
  };

  return (
    <>
      <PortalModal
        isOpen={openConfirmationModal}
        toggle={toggleConfirmationModal}
        isConfirmationModal
        width={type === 'retrigger' ? '420px' : '325px'}
      >
        <Button
          color="white"
          type="button"
          className="d-flex justify-content-end p-2"
          onClick={() => handleClose()}
        >
          <i className="simple-icon-close close-btn" />
        </Button>
        <ModalBody className="d-flex flex-column pt-0">
          <Colxx className="d-flex flex-column justify-content-center text-center mb-3">
            {type !== 'review' && <IntlMessages id={titleSwitcher()} />}
            {mediaTitle && (
              <h6
                className="font-weight-bold"
                style={{ wordBreak: 'break-all' }}
              >
                {mediaTitle}
              </h6>
            )}
          </Colxx>
          <Colxx className="d-flex flex-column justify-content-center text-center mb-3">
            {description && description}
          </Colxx>
          <Colxx className="d-flex justify-content-center gap-2">
            <Button color="primary" onClick={() => handleConfirm()}>
              {confirmText ?? 'Yes'}
            </Button>
            <Button
              color
              className="badge-outline-primary"
              onClick={() => handleClose()}
            >
              {cancelText ?? 'No'}
            </Button>
          </Colxx>
        </ModalBody>
      </PortalModal>
    </>
  );
};

export default ConfirmationModal;
