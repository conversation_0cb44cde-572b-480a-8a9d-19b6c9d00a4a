/* eslint-disable react/no-array-index-key */
import React from 'react';
import LoadingSkeleton from 'components/LoadingSkeleton';

const GraphLoading = () => {
  return (
    <div
      style={{ width: '100%', height: '50px' }}
      className="d-flex flex-column"
    >
      <LoadingSkeleton
        style={{ width: '50%', height: '100%' }}
        containerClassName="d-flex align-items-center justify-content-center h-85"
      />
    </div>
  );
};

const SessionListLoading = () => {
  return (
    <div
      className="w-full-imp h-full-imp d-flex flex-column py-4 "
      style={{ gap: '10px' }}
    >
      {/* <div style={{ height: '50%' }}>
        <LoadingSkeleton
          style={{ width: '100%', height: '90%' }}
          containerClassName="d-flex align-items-center justify-content-center h-85"
        />
      </div> */}

      <div
        className="d-flex flex-column"
        style={{ height: '50%', overflow: 'hidden' }}
      >
        <LoadingSkeleton
          style={{ width: '20%', height: '60px', marginBottom: '10px' }}
          containerClassName="d-flex align-items-center justify-content-center h-85"
        />
        {new Array(10).fill('').map((m, i) => (
          <LoadingSkeleton
            key={i}
            style={{ width: '100%', height: '50px', marginBottom: '10px' }}
            containerClassName="d-flex align-items-center justify-content-center h-85"
          />
        ))}
      </div>
    </div>
  );
};

const ChatAnalyticsLoading = () => {
  return (
    <div className="w-full-imp h-full-imp overflow-hidden">
      <GraphLoading />
      <SessionListLoading />
    </div>
  );
};

export { GraphLoading, SessionListLoading, ChatAnalyticsLoading };
