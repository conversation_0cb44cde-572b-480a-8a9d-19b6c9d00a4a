import React, { useEffect, useState } from 'react';

const Avtar = ({ size, fName, newColor, style = {} }) => {
  const fullName =
    fName ||
    JSON.parse(localStorage.getItem('tenantDetails'))?.data?.fullName ||
    '';
  const [avatarColor, setAvatarColor] = useState('#f5f5f5');
  let avtarName = null;
  // eslint-disable-next-line no-plusplus
  for (let i = 1; i < fullName?.length; i++) {
    if (fullName[i] === ' ') {
      if (fullName[i + 1] !== undefined) avtarName = fullName[i + 1];
    }
  }
  useEffect(() => {
    let avtarColor;
    if (!newColor) {
      avtarColor = sessionStorage.getItem('avtarColor');
    }

    if (!avtarColor) {
      avtarColor = `#${Math.floor(Math.random() * 16777215).toString(16)}`;
      sessionStorage.setItem('avtarColor', avtarColor);
    }
    setAvatarColor(avtarColor);
  }, [newColor]);
  return (
    <div
      style={{
        height: size,
        width: size,
        borderRadius: '50%',
        // eslint-disable-next-line no-bitwise
        backgroundColor: avatarColor,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        margin: 'auto',
        color: 'white',
        fontSize: size / 2.5,
        fontWeight: 'bold',
        ...style
      }}
    >
      {fullName?.length &&
        fullName[0]?.toUpperCase() +
          (avtarName ? avtarName?.toUpperCase() : '')}
    </div>
  );
};

export default Avtar;
