import { getTenantProfileImage } from 'functions/api/tenantApi';
import { isLoggedIn } from 'helpers/Utils';
import { useEffect, useState } from 'react';

const useTenantProfileImg = ({
  profileImgId = undefined,
  profileTenantId = undefined
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isValid, setIsValid] = useState(false);
  const [profileImgUrl, setProfileImgUrl] = useState('');

  const handleError = () => {
    setIsValid(false);
    setIsLoading(false);
    setProfileImgUrl('');
  };

  const fetchProfileImageUrl = async () => {
    try {
      const { data, isError } = await getTenantProfileImage({
        id: profileTenantId,
        profileImageId: profileImgId,
        isPublic: !isLoggedIn
      });
      if (isError || !data) {
        handleError();
      } else {
        const img = document.createElement('img');
        img.src = data;
        img.onerror = () => {
          setIsValid(false);
        };
        img.onload = () => {
          setIsValid(true);
          setProfileImgUrl(data);
        };
      }
    } catch (error) {
      console.error(
        'Somethign went wrong in fetchProfileImageUrl due to ',
        error
      );
      handleError();
    }
  };

  useEffect(() => {
    if (profileImgId && profileTenantId) {
      fetchProfileImageUrl();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [profileImgId, profileTenantId]);

  return {
    isLoading,
    isValid,
    profileImgUrl
  };
};

export default useTenantProfileImg;
