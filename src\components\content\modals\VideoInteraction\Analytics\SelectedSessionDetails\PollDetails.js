/* eslint-disable react/no-array-index-key */
import React from 'react';
import { Card } from 'reactstrap';

const PollDetails = ({ data, pollOptions }) => {
  const { question, answer } = JSON.parse(data.eventdata);
  return (
    <div className="PollDetails">
      <h1>Poll Details</h1>
      <h2>{question}</h2>
      {pollOptions.map((m, i) => {
        const isActive = m.text === answer;
        return (
          <Card
            style={{
              background: isActive ? '#ffcdf8' : ''
            }}
            className="PDCard"
            key={i}
          >
            {m.text}
          </Card>
        );
      })}
    </div>
  );
};

export default PollDetails;
