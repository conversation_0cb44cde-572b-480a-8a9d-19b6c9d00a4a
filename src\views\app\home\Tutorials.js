/* eslint-disable no-unused-vars */
/* eslint-disable react/no-array-index-key */
/* eslint-disable jsx-a11y/media-has-caption */
import React, { useState } from 'react';
import { Colxx } from 'components/common/CustomBootstrap';
import { Card, Collapse, Row } from 'reactstrap';
import { adminRoot } from 'constants/defaultValues';
import IntlMessages from 'helpers/IntlMessages';
import BackButton from 'components/buttons/BackButton';

function TutorialTab({ history, openFirst }) {
  const data = [
    {
      id: 1,
      name: 'How to create your first Streaming Experience ?',
      src: 'https://play.innerloop.stream/playersvc/ins-share/cu-klD3eAgRI7VeqpZg10njr',
    },
    {
      id: 2,
      name: 'How to create Optimized Media Content ?',
      src: 'https://play.innerloop.stream/playersvc/ins-share/cu-U8e8L0bJnQxyqew9ZMxrF',
    },
    {
      id: 3,
      name: 'How to Record and Share with Review Workflowt ?',
      src: 'https://play.innerloop.stream/playersvc/ins-share/cu-L1dVYyM_P80DMry4Uex-_',
    },
    {
      id: 4,
      name: 'How to Share a content ?',
      src: 'https://play.innerloop.stream/playersvc/ins-share/cu-dE64GN922RT21rbRuIEqC',
    },
    {
      id: 5,
      name: 'What is a Showcase ?',
      src: 'https://play.innerloop.stream/playersvc/ins-share/cu-kyCs7TjEbzZpRtzT4coCt',
    },
    {
      id: 6,
      name: 'How to Run a promotion or campaign experiment for your content ?',
      src: 'https://play.innerloop.stream/playersvc/ins-share/cu-Cxx6NL9IBrzH0IdRjoc2j',
    },
    {
      id: 7,
      name: 'How to personalize and add your branding for your Media ?',
      src: 'https://play.innerloop.stream/playersvc/ins-share/cu-QV4wmVTzN3-sNqvVOuUNy',
    },
    {
      id: 8,
      name: 'How to invite a Team Member ?',
      src: 'https://play.innerloop.stream/playersvc/ins-share/cu-OGp_djeuF0VKu2UE0k2U0',
    },
    {
      id: 9,
      name: 'How can I view my Content Analytics ?',
      src: 'https://play.innerloop.stream/playersvc/ins-share/cu-b3gg_EyXGL0AcScAjNiG1',
    },
    {
      id: 10,
      name: 'How can I view my Showcase Analytics ?',
      src: 'https://play.innerloop.stream/playersvc/ins-share/cu-PwMq4vtwxJkkyPOOgwFAb',
    },
    {
      id: 11,
      name: 'How to contact Support Teams ?',
      src: 'https://play.innerloop.stream/playersvc/ins-share/cu-a86LTtz1_ypkKYpogigpE',
    },
    {
      id: 12,
      name: 'How to create DRM protected content ?',
      src: 'https://play.innerloop.stream/playersvc/ins-share/cu-CwuRYNoXdDFQCr7GmclM5',
    },
    {
      id: 13,
      name: 'How to create AES protected content ?',
      src: 'https://play.innerloop.stream/playersvc/ins-share/cu-4sIUG0-3dl6a_bmS8RL2t',
    },
    {
      id: 14,
      name: 'How to view my Subscription details ?',
      src: 'https://play.innerloop.stream/playersvc/ins-share/cu-zE9giFUegumPzThN2FnJ8',
    },
  ];

  return (
    <>
      <Colxx className="mt-4">
        <Row className="d-flex flex-row align-items-center justify-content-
      between text-center my-4">
          <div className='w-100'>
            <span className='position-absolute' style={{ left: 0 }}>
              <BackButton onClick={() => {
                history.push(`${adminRoot}/home?tutorial=false`);
              }} />
            </span>
            <h3 className='text-center'>
              <b>
                <IntlMessages id="home.faq" />
              </b>
            </h3>
          </div>
        </Row>
        <Colxx className="justify-content-start mb-3">
          {data.map((d) => (
            <Accordian
              name={d.name}
              key={d.id}
              id={d.id}
              isImage={d?.isImage}
              src={d.src}
              isInitiallyOpen={d.id === 1 && openFirst}
            />
          ))}
        </Colxx>
      </Colxx>
    </>
  );
}

export default TutorialTab;

// eslint-disable-next-line no-unused-vars
const Accordian = ({ id, name, isImage, src, isInitiallyOpen = false }) => {
  const [isOpen, setIsOpen] = useState(isInitiallyOpen);

  return (
    <Card
      id={id}
      className="border-10 mb-4 w-full column py-3 px-1 c-pointer"
      style={{ border: isInitiallyOpen && '1px solid #992288' }}
      onClick={() => setIsOpen(!isOpen)}
    >
      <div className="w-full row p-2">
        <div className="w-90 pl-4">
          <span className="d-flex justify-content-start">{name}</span>
        </div>

        <div className="w-5 text-primary d-flex align-items-center justify-content-end">
          <i
            style={{ fontWeight: 'bolder' }}
            className={`simple-icon-arrow-${isOpen ? 'up' : 'down'}`}
          />
        </div>
      </div>
      <Collapse isOpen={isOpen}>
        <div className="p-3 d-flex align-items-center justify-content-center">
          <IframeVideo src={src} title={name} />
        </div>
      </Collapse>
    </Card>
  );
};

const IframeVideo = ({ src, title }) => {
  const [isLoading, setIsLoading] = useState(true);
  return (
    <>
      {isLoading ? (
        <div className="dataloading-overlay detailed-content">
          <div className="dataloading" />
        </div>
      ) : null}
      <iframe
        src={src}
        title={title}
        allowFullScreen
        className={isLoading ? 'd-none' : 'border-0'}
        height="400"
        onLoad={() => setIsLoading(false)}
        width="560"
      />
    </>
  );
};
