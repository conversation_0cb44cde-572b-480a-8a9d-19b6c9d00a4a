import { formatDate } from 'helpers/Utils';

/* eslint-disable react/react-in-jsx-scope */
const NextUpdateTime = ({ time, className, fontSize = '13px' }) => {
  if (time) {
    return (
      <p className={`text-center my-2 ${className}`} style={{ fontSize }}>
        Next refresh time :&nbsp;
        {time && formatDate(new Date(time))}
        &nbsp;
        {time && new Date(time).toLocaleTimeString()}
      </p>
    );
  }
  return null;
};

export default NextUpdateTime;
