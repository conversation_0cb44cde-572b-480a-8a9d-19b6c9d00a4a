/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable jsx-a11y/media-has-caption */
/* eslint-disable  no-nested-ternary */
import { CategoryInput, TagInput } from 'components/input';
import { Field, Formik } from 'formik';
import IntlMessages from 'helpers/IntlMessages';
import { returnFileSize } from 'helpers/Utils';
import React, { useEffect, useState } from 'react';
import { Form, FormGroup, Label } from 'reactstrap';
import { NotificationManager } from 'components/common/react-notifications';
import useFileDuration from 'hooks/useFileDuration';
import Switch from 'rc-switch';
import YoutubePreview from './YoutubePreview';
import VimeoPreview from './VimeoPreview';

function PreviewContent({
  previewLink,
  mediaFile,
  setDuration,
  removeSelectedItem,
  validateTitle,
  newTitle,
  tags,
  setTags,
  category,
  setCategory,
  optimize,
  drmEnabled = false,
  aesEnabled = false,
  setIsPreview = () => {},
  DRMStatus = false,
  AESStatus = false,
  MediaType,
  isImageModal,
  showPreviewAndTitle,
  optimizationEnabled,
  setOptimize,
  isStreamingModal,
  isLocalTab,
  isRemoteTab,
  isVimeoYTUrl,
  isYoutube = false,
  isVimeo = false
}) {
  const isVideoFromMedia = mediaFile?.type?.includes('video');
  const isVideo = isVideoFromMedia ?? MediaType === 1;
  const isAudio = MediaType === 2;
  const [singleTag, setSingleTag] = useState('');
  const [blobSize, setBlobSize] = useState('0 bytes');
  const { isLoading, durationInText } = useFileDuration({
    mediaFile,
    condition: MediaType !== 3 && mediaFile?.size,
    onDurationChange: ({ duration }) => {
      console.log('duration', duration);
      setDuration(duration);
    }
  });

  console.log({
    isYoutube,
    isVimeo,
    isVimeoYTUrl
  });

  useEffect(() => {
    setIsPreview(true);
    return () => setIsPreview(false);
  }, []);

  useEffect(() => {
    if (mediaFile) {
      setBlobSize(returnFileSize(mediaFile?.size).sizeText);
    }
  }, [mediaFile]);

  const handleChnageInCategory = (Categories) => {
    if (Categories && Categories.length > 1) {
      NotificationManager.warning(
        <IntlMessages id="single-cat" />,
        '',
        3000,
        null,
        null,
        ''
      );
      return;
    }
    setCategory(Categories[0]);
  };

  const handleChangeTagsInput = (tagg) => {
    if (tagg.length > 20) {
      NotificationManager.warning(
        <IntlMessages id="max-twenty" />,
        '',
        3000,
        null,
        null,
        ''
      );
      return;
    }
    if (tagg.length <= 20) {
      setSingleTag(tagg);
    }
  };

  const handleChangeTags = (tagss) => {
    if (tagss.filter((f) => f === singleTag).length > 1) {
      NotificationManager.warning(
        <IntlMessages id="tag-uni" />,
        '',
        3000,
        null,
        null,
        ''
      );
      tagss.pop();
    }
    if (tagss && tagss.length > 5) {
      NotificationManager.warning(
        <IntlMessages id="max-five" />,
        '',
        3000,
        null,
        null,
        ''
      );
      return;
    }
    setTags(tagss);
  };

  if (previewLink) {
    const videoType = (() => {
      if (previewLink.includes('.webm')) {
        return 'video/webm';
      }
      return 'video/*';
    })();
    return (
      <>
        <div
          className={
            isVideo
              ? 'video-preview-container'
              : !isVideo && !isImageModal && 'video-preview-container w-90'
          }
        >
          <div
            className={
              isVideo
                ? 'video-twoside-preview'
                : showPreviewAndTitle
                ? 'video-twoside-preview'
                : 'audio-twoside-preview w-full p-4'
            }
          >
            {showPreviewAndTitle && (
              <div
                className={
                  isVideo
                    ? 'preview-img'
                    : 'preview-img d-flex justify-content-start'
                }
              >
                <div className="dash-videonclose">
                  {isYoutube ? (
                    <YoutubePreview url={previewLink} />
                  ) : isVimeo ? (
                    <VimeoPreview url={previewLink} />
                  ) : isVideo ? (
                    <video
                      className="dashboard-preview-video_audio"
                      controls
                      type={videoType}
                      src={previewLink}
                    />
                  ) : isImageModal ? (
                    <img
                      className="dashboard-preview-video_audio"
                      alt="preview of th selected img"
                      src={previewLink}
                    />
                  ) : (
                    <video
                      className="dashboard-preview-video_audio"
                      controls
                      src={previewLink}
                      poster="/assets/img/audio/headpho.jpeg"
                    />
                  )}
                  <button
                    type="button"
                    className="btn dash-close-video-btn"
                    onClick={removeSelectedItem}
                  >
                    <i className="simple-icon-close" />
                  </button>
                </div>
              </div>
            )}
            <div className={`dashboard-video-meta-panel ${!isVideo && 'px-3'}`}>
              <ul className="dash-detail-list ">
                {showPreviewAndTitle && (
                  <div>
                    <Formik initialValues={{ newTitle }}>
                      {({ errors, touched }) => (
                        <Form
                          className="av-tooltip tooltip-label-bottom"
                          onSubmit={(e) => e.preventDefault()}
                        >
                          <FormGroup className="form-group has-float-label">
                            <Label>
                              <IntlMessages id="menu.videoMeta" />{' '}
                              <span style={{ color: '#922c88' }}> *</span>
                            </Label>
                            <Field
                              className="form-control"
                              type="text"
                              name="newTitle"
                              placeholder="Enter Content Title "
                              value={newTitle}
                              validate={validateTitle}
                            />
                            {errors.newTitle && touched.newTitle && (
                              <div className="invalid-feedback d-block">
                                {errors.newTitle}
                              </div>
                            )}
                          </FormGroup>
                        </Form>
                      )}
                    </Formik>
                  </div>
                )}
                <TagInput
                  value={tags}
                  required
                  inputValue={singleTag}
                  onChange={handleChangeTags}
                  onChangeInput={handleChangeTagsInput}
                />
                <CategoryInput
                  className="mt-3"
                  value={category}
                  onChange={(e) =>
                    handleChnageInCategory(e.map((a) => a.value))
                  }
                  required
                />

                <div className="d-flex align-items-center justify-content-between mt-4">
                  {blobSize !== '0 bytes' && (
                    <li className="mt-2">
                      <span className="media-label-title mr-3">
                        <IntlMessages id="spaces.mediasize" />
                      </span>
                      <span>{blobSize}</span>
                    </li>
                  )}
                  {durationInText && durationInText !== '00:00:00.00' && (
                    <li className="mt-2">
                      <span className="media-label-title mr-3">
                        <IntlMessages id="spaces.mediaduration" />
                      </span>
                      <span>
                        {isLoading ? 'Calculating..' : durationInText}
                      </span>
                    </li>
                  )}
                </div>

                <div>
                  <InfoTag condition={optimize} label="OPTIMIZATION ENABLED" />
                  <InfoTag
                    condition={drmEnabled && DRMStatus}
                    label="DRM ENABLED"
                  />
                  <InfoTag
                    condition={aesEnabled && AESStatus}
                    label="AES ENABLED"
                  />
                </div>
              </ul>
            </div>
          </div>
        </div>
        {isLocalTab && !isImageModal && (
          <>
            <div
              className=" d-flex align-items-center justify-content-center"
              aria-disabled={!optimizationEnabled}
            >
              {!isStreamingModal && (
                <>
                  <span className="media-label-title">
                    <IntlMessages id="up.optimize" />
                  </span>
                  <Switch
                    className="custom-switch custom-switch-primary custom-switch-small ml-3"
                    checked={optimize}
                    onClick={() => setOptimize(!optimize)}
                  />
                </>
              )}
            </div>
            {!isStreamingModal && (
              <p className="dashboard-center-this-item pr-3 pl-3 mb-4 text-center text-primary">
                <strong>
                  <i className="simple-icon-info font-weight-bold"> </i>{' '}
                  {optimize ? (
                    MediaType === 1 ? (
                      <IntlMessages id="up.no-opt-vid" />
                    ) : (
                      MediaType === 2 && <IntlMessages id="up.no-opt-aud" />
                    )
                  ) : MediaType === 1 ? (
                    <IntlMessages id="up.opt-vid" />
                  ) : (
                    <IntlMessages id="up.opt-aud" />
                  )}
                  {!optimize && MediaType === 1 ? (
                    <IntlMessages id="up.opt-vid-support" />
                  ) : (
                    MediaType === 2 && <IntlMessages id="up.opt-aud-support" />
                  )}
                </strong>
              </p>
            )}
          </>
        )}
        {isRemoteTab && (
          <>
            {!isStreamingModal && MediaType === 2 && (
              <div className="d-flex flex-column">
                <div
                  className=" d-flex align-items-center justify-content-center"
                  aria-disabled={!optimizationEnabled}
                >
                  <span className="media-label-title">
                    <IntlMessages id="up.optimize" />
                  </span>
                  <Switch
                    className="custom-switch custom-switch-primary custom-switch-small ml-3"
                    checked={optimize}
                    onClick={() => setOptimize(!optimize)}
                  />
                </div>
                <p className="dashboard-center-this-item pr-3 pl-3 mt-2 text-center text-primary">
                  <strong>
                    <i className="simple-icon-info font-weight-bold"> </i>
                    {optimize ? (
                      <IntlMessages id="up.no-opt-aud" />
                    ) : (
                      <IntlMessages id="up.opt-aud" />
                    )}
                  </strong>
                </p>
              </div>
            )}
            {!isStreamingModal && MediaType === 1 && !isVimeoYTUrl && (
              <div>
                <div
                  aria-disabled={!optimizationEnabled}
                  className=" d-flex align-items-center justify-content-center mt-5"
                >
                  <span className="media-label-title">
                    <IntlMessages id="remote-optimize" />
                  </span>
                  <Switch
                    className="custom-switch custom-switch-primary custom-switch-small ml-3"
                    checked={optimize}
                    onClick={() => setOptimize(!optimize)}
                  />
                </div>
                <p className="dashboard-center-this-item pr-3 pl-3 mb-3 text-center  text-primary">
                  <strong>
                    <i className="simple-icon-info font-weight-bold"> </i>
                    {optimize ? (
                      <IntlMessages id="rem-opt" />
                    ) : (
                      <IntlMessages id="rem-notopt" />
                    )}
                    {!optimize &&
                      (MediaType === 1 ? (
                        <IntlMessages id="rem-vidsupport" />
                      ) : (
                        MediaType === 2 && <IntlMessages id="rem-audsupport" />
                      ))}
                  </strong>
                </p>
              </div>
            )}
          </>
        )}
      </>
    );
  }
  return null;
}

// eslint-disable-next-line consistent-return
const InfoTag = ({ condition, label }) => {
  if (condition) {
    return (
      <span
        style={{
          fontSize: 14,
          padding: '5px 15px',
          borderRadius: 20,
          border: '1px solid gray',
          fontWeight: 600
        }}
      >
        {label}
      </span>
    );
  }
  return null;
};

export default PreviewContent;
