/* eslint-disable no-unused-vars */
/* eslint-disable react/no-unescaped-entities */
import React, { useState } from 'react';
import { Button, Input } from 'reactstrap';
import { unlockImage } from 'functions/api/publicApi';
import IntlMessages from 'helpers/IntlMessages';

function LockedContent({ contentId, setPreview }) {
  const [isLoading, setIsLoading] = useState(false);
  const [accessCode, setAccessCode] = useState('');
  const [incorrectPassword, setIncorrectPassword] = useState(false);

  const fetchImage = async () => {
    setIsLoading(true);
    setIncorrectPassword(false);
    const { data, isError } = await unlockImage(accessCode, contentId);
    if (data && !isError) {
      const isImageExists = data.strmUrl;
      if (isImageExists) {
        setPreview(data.strmUrl);
        setIncorrectPassword(false);
      } else {
        setAccessCode('');
        setIncorrectPassword(true);
      }
    }
    setIsLoading(false);
  };

  return (
    <div
      className="p-4 d-flex flex-column text-center p-2 justify-content-center "
      style={{ height: '98%' }}
    >
      <div style={{ textAlign: 'left', fontSize: '50px' }}>
        <svg
          width="70"
          height="70"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M17 9V7c0-2.8-2.2-5-5-5S7 4.2 7 7v2c-1.7 0-3 1.3-3 3v7c0 1.7 1.3 3 3 3h10c1.7 0 3-1.3 3-3v-7c0-1.7-1.3-3-3-3zM9 7c0-1.7 1.3-3 3-3s3 1.3 3 3v2H9V7z"
            fill="#992288"
          />
        </svg>
      </div>
      <p
        className="text-align-left mb-0 mt-4"
        style={{ fontSize: '18px', fontWeight: '900' }}
      >
        Access Code:
      </p>
      <p
        style={{ fontSize: '14px', fontWeight: '600', textAlign: 'left' }}
        className="mt-2"
      >
        The content you've requested requires an access code, please enter your
        access code.
      </p>
      <Input
        type="password"
        value={accessCode}
        onChange={(e) => {
          setAccessCode(e.target.value);
          setIncorrectPassword(false);
        }}
        placeholder="Enter your access code"
      />
      {incorrectPassword && (
        <p style={{ color: 'red', textAlign: 'left', fontSize: '13px' }}>
          Invalid password,please enter a correct password{' '}
        </p>
      )}
      <Button
        type="button"
        placeholder="Enter your access code"
        color="primary"
        size="md"
        style={{ borderRadius: 4 }}
        onClick={fetchImage}
        className={`btn-shadow my-4 auth-btn ${
          isLoading ? 'btn-multiple-state show-spinner' : ''
        }`}
      >
        <span className="spinner d-inline-block">
          <span className="bounce1" />
          <span className="bounce2" />
          <span className="bounce3" />
        </span>
        <span className="label">
          <IntlMessages id="Submit" />
        </span>
      </Button>
    </div>
  );
}

export default LockedContent;
