/* eslint-disable no-unreachable */
/* eslint-disable no-lonely-if */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-expressions */
/* eslint-disable consistent-return */
/* eslint-disable array-callback-return */
/* eslint-disable no-unused-vars */
/* eslint-disable no-else-return */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import {
  getSingleShowcase,
  handleCreateShowcase,
  handleDeleteShowcase,
  handleUpdateShowcase
} from 'functions/api/showcaseApi';
import React, { useEffect, useState } from 'react';
import {
  Button,
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  FormGroup,
  Input,
  Label,
  Modal,
  ModalBody,
  UncontrolledDropdown
} from 'reactstrap';
import InfiniteScroll from 'react-infinite-scroll-component';
import { NotificationManager } from 'components/common/react-notifications';
import { nanoid } from 'nanoid';
import IntlMessages from 'helpers/IntlMessages';
import {
  validateEmail,
  validateUrl,
  getCurrentUserDetails
} from 'helpers/Utils';
import { SliderTooltip } from '../../SliderTooltip';
import InfoIcon from 'components/icon/InfoIcon';
import DraftToPublishedShowcase from '../DraftToPublishedShowcase';
import ShowcasedInfo from './ShowcasedInfo';
import RadioRow from './RadioRow';
import Decider from './Decider';
import SectionHeader from '../SectionHeader';
import PaymentInfo from './PaymentInfo';
import FormInput from 'components/input/FormInput';
import { useSelector } from 'react-redux';

const ONE_DAY = 24 * 60 * 60 * 1000;
const TWO_DAY = 48 * 60 * 60 * 1000;
const THREE_DAY = 72 * 60 * 60 * 1000;
const SEVEN_DAY = 168 * 60 * 60 * 1000;
const THIRTY_DAY = 720 * 60 * 60 * 1000;

const PURPOSE_DATA = [
  { id: 1, name: 'Know More', desc: 'opens a website to know more about' },
  {
    id: 2,
    name: 'Contact Us',
    desc: 'opens a website with contact information'
  },
  { id: 3, name: 'Sign Up', desc: 'opens a website for Sign-up' },
  { id: 4, name: 'Buy Now', desc: 'opens a website for buying products' },
  {
    id: 5,
    name: 'Watch',
    desc: 'opens a website with video / audio / images for a fully immersive experience'
  },
  { id: 6, name: 'Send Email', desc: 'send an email' },
  { id: 7, name: 'Book', desc: 'opens a website for booking appointments' },
  { id: 8, name: 'Pay Now', desc: 'Opens a website for Payment' },
  { id: 9, name: 'Donate', desc: 'opens a website for donation or charity' },
  { id: 10, name: 'Send Gift', desc: 'opens a website for e-gift, presents' }
];

const MODES = {
  DRAFT: 1,
  PUBLISH: 2
};

// NOTE -> Do not change this value. Confirm it with bipin
// before changing this value.
const SHOWCASE_NON_EXPIRY_TIME = 1;

function ShowcaseModal({
  isOpenShowCaseModal,
  toggleShowCaseModal,
  // tenantDetails,
  spaceId,
  product,
  isContent,
  setIsShowcased = () => {},
  isShowcased,
  setItems,
  items,
  setProduct,
  setRefreshAfterShowcaseUpdate,
  refreshAfterShowcaseUpdate,

  fromShowcases,
  showcases,
  setShowcases
}) {
  const [step, setStep] = useState(20);
  const [shareAlways, setShareAlways] = useState(false);
  const [selectPurpose, setSelectPurpose] = useState('Select Purpose');
  const [txtBoxType, setTxtBoxType] = useState(null);
  const [duration, setDuration] = useState(ONE_DAY);
  const [textBoxValue, setTextBoxValue] = useState('');
  const [buttonDisabled, setButtonDisabled] = useState(false);
  const [txtBoxError, setTxtBoxError] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [showcaseData, setShowcaseData] = useState(null);
  const [isShowcaseLoading, setIsShowcaseLoading] = useState(false);
  const [sliderChanged, setSliderChanged] = useState(false);
  const [showcaseAlways, setShowcaseAlways] = useState(false);
  const [mode, setMode] = useState(MODES.DRAFT);
  const [confirmationOpen, setConfirmationOpen] = useState(false);
  const toggleConfirmation = () => setConfirmationOpen(!confirmationOpen);
  const { ownerName, countryCode, selectedTenantName, selectedTenantId } =
    getCurrentUserDetails();
  const [originalPrice, setOriginalPrice] = useState(0);
  const [sellingPrice, setSellingPrice] = useState(0);
  const [currency, setCurrency] = useState('');
  const [buttonText, setButtonText] = useState('Buy Now');

  const isShowcasePublished = showcaseData?.status === MODES.PUBLISH;
  const isShowcaseExpires = showcaseData?.showcaseExpires;
  const { showcases: cachedShowcases } = useSelector(
    (state) => state.showcases
  );

  const showPayment = selectPurpose?.id === 4 || selectPurpose === 'Buy Now';

  const getSanitizedPaymentInfo = () => {
    if (showPayment) {
      return {
        originalPrice: +originalPrice,
        sellingPrice: +sellingPrice,
        priceSymbol: currency?.symbol
      };
    } else {
      return {
        originalPrice: 0,
        sellingPrice: 0,
        priceSymbol: ''
      };
    }
  };

  const getSingleShowcaseData = async () => {
    setIsShowcaseLoading(true);
    const { data, isError } = await getSingleShowcase({
      showcaseId: product.showcaseId,
      spaceId
    });
    if (!isError && data) {
      if (fromShowcases) {
        setIsShowcased(data.id);
      }
      setShowcaseData(data);
      setTxtBoxType(data?.purpose?.valueType);
      setTextBoxValue(data?.purpose?.value);
      setSelectPurpose(data?.purpose?.name ?? selectPurpose);
      setOriginalPrice(data?.purpose?.originalPrice);
      setSellingPrice(data?.purpose?.sellingPrice);
      setCurrency(data?.purpose?.priceSymbol);
      setButtonText(data?.purpose?.buttonText);
      setDuration(data?.endDateTime);
      setMode(data?.status);
      setShareAlways(data?.isPublic);
      setShowcaseAlways(data?.showcaseExpires);
      const getDate = new Date(data.endDateTime) - Date.now();
      if (getDate <= ONE_DAY) {
        setStep(20);
      } else if (getDate <= TWO_DAY) {
        setStep(40);
      } else if (getDate <= THREE_DAY) {
        setStep(60);
      } else if (getDate <= SEVEN_DAY) {
        setStep(80);
      } else if (getDate <= THIRTY_DAY) {
        setStep(100);
      }
    }
    setIsShowcaseLoading(false);
  };

  const handleResetValue = (reset) => {
    setShowcaseData(null);
    setTxtBoxType(null);
    setTextBoxValue('');
    setSelectPurpose('Select Purpose');
    setDuration(ONE_DAY);
    setShareAlways(false);
    reset && setIsShowcased(false);
    setStep(20);
    setShowcaseAlways(false);
    setButtonDisabled(false);
    toggleShowCaseModal();
  };

  const handleDeleteCachedShowcaseInfo = () => {
    const showcaseId = showcaseData.id;
    if (showcaseId) {
      const isCached = cachedShowcases?.get(showcaseId);
      if (isCached) {
        cachedShowcases?.delete(showcaseId);
      }
    }
  };

  const handleRemoveShowcase = async () => {
    setIsDeleting(true);
    const res = await handleDeleteShowcase(product?.showcaseId, spaceId);
    if (res.isError) {
      NotificationManager.error(
        <IntlMessages id="sm-rm-error" />,
        <IntlMessages id="req.failed" />,
        3000,
        null,
        null,
        ''
      );
      setIsShowcased(true);
    } else {
      if (fromShowcases && showcases && setShowcases) {
        const newShowcases = showcases.filter((f) => f.id !== showcaseData.id);
        setShowcases(newShowcases);
      }
      items?.map((m) => {
        if (m.id === product.id) {
          return {
            ...m,
            showcaseId: null
          };
        } else {
          return m;
        }
      });
      if (setProduct) {
        setProduct({
          ...product,
          showcaseId: null
        });
      }
      handleDeleteCachedShowcaseInfo();
      NotificationManager.success(
        <IntlMessages id="sm-rm-sucess" />,
        <IntlMessages id="req.success" />,
        3000,
        null,
        null,
        ''
      );
      handleResetValue(true);
    }
    setIsDeleting(false);
  };

  const handleStep = (value) => {
    setSliderChanged(true);
    if (value === 20) {
      setStep(20);
      setDuration(ONE_DAY);
    }
    if (value === 40) {
      setStep(40);
      setDuration(TWO_DAY);
    }
    if (value === 60) {
      setStep(60);
      setDuration(THREE_DAY);
    }
    if (value === 80) {
      setStep(80);
      setDuration(SEVEN_DAY);
    }
    if (value === 100) {
      setStep(100);
      setDuration(THIRTY_DAY);
    }
  };

  const allPossibleDuration = [
    ONE_DAY,
    TWO_DAY,
    THREE_DAY,
    SEVEN_DAY,
    THIRTY_DAY
  ];

  const conmputedDuration = (() => {
    const accurateDuration = allPossibleDuration.some((s) => s === duration)
      ? duration
      : 0;

    if (!showcaseData) {
      return !showcaseAlways ? SHOWCASE_NON_EXPIRY_TIME : duration + Date.now();
    }

    if (showcaseData) {
      console.log('showcase exoists', showcaseData);
      console.log('duration', duration);
      const wasDraft = +showcaseData.status === MODES.DRAFT;
      const wasShowcaseExpiring = showcaseData.showcaseExpires;

      if (showcaseAlways && !wasShowcaseExpiring) {
        return Date.now() + accurateDuration;
      }

      const newDuration = sliderChanged
        ? Date.now() + accurateDuration
        : showcaseData?.endDateTime;
      return newDuration;
    }
  })();

  console.log('conmputedDuration', conmputedDuration);
  console.log('showcaseAlways', showcaseAlways);

  const updateShowcase = async (trys = false) => {
    if (
      // showcaseData.status === MODES.DRAFT &&
      mode === MODES.PUBLISH &&
      showcaseAlways &&
      !trys
    ) {
      toggleConfirmation();
      return;
    }
    setIsCreating(true);

    const values = (() => {
      const showcaseExpires = showcaseAlways;

      const wasDraftMadePublished =
        showcaseData.status === MODES.DRAFT && mode === MODES.PUBLISH;
      const wasPublishedMadeDraft =
        showcaseData.status === MODES.PUBLISH && mode === MODES.DRAFT;

      console.log({
        wasDraftMadePublished,
        wasPublishedMadeDraft,
        isShowcaseExpires,
        conmputedDuration
      });

      if (wasDraftMadePublished) {
        if (!isShowcaseExpires) {
          return {
            endDateTime: conmputedDuration,
            showcaseExpires
          };
        } else {
          return {
            endDateTime: 1,
            showcaseExpires: false
          };
        }
      } else if (wasPublishedMadeDraft) {
        return {
          endDateTime: 1,
          showcaseExpires: false
        };
      } else {
        return {
          endDateTime: conmputedDuration,
          showcaseExpires
        };
      }
    })();

    const payload = {
      ...values,
      isPublic: shareAlways,
      id: showcaseData.id,
      status: mode,
      purpose: {
        name: selectPurpose?.name ?? selectPurpose,
        value: textBoxValue?.trim(),
        valueType: txtBoxType,
        buttonText,
        ...getSanitizedPaymentInfo()
      }
    };

    const { data, isError } = await handleUpdateShowcase(payload, spaceId);
    if (data && !isError) {
      handleDeleteCachedShowcaseInfo();
      if (fromShowcases && showcases && setShowcases) {
        if (mode === MODES.PUBLISH) {
          const newShowvcases = showcases.map((s) => {
            if (s.id === showcaseData.id) {
              return {
                ...s,
                status: mode,
                endDateTime: payload.endDateTime,
                showcaseExpires: payload.showcaseExpires,
                purpose: payload.purpose
              };
            } else {
              return s;
            }
          });
          setShowcases(newShowvcases);
        }
      }
      // eslint-disable-next-line no-unreachable
      NotificationManager.success(
        <IntlMessages id="sm-up.sucess" />,
        '',
        3000,
        null,
        null,
        ''
      );
      if (
        setRefreshAfterShowcaseUpdate &&
        typeof refreshAfterShowcaseUpdate === 'boolean'
      ) {
        setRefreshAfterShowcaseUpdate(!refreshAfterShowcaseUpdate);
      }
      handleResetValue();
    }
    if (isError) {
      NotificationManager.error(
        <IntlMessages id="sm.error" />,
        '',
        3000,
        null,
        null,
        ''
      );
    }
    setIsCreating(false);
  };

  const createShowCase = async (trys = false) => {
    if (!trys && showcaseAlways && mode !== MODES.DRAFT) {
      // if (!trys && mode === MODES.PUBLISH) {
      toggleConfirmation();
      return;
    }
    const payload = {
      id: nanoid(),
      entityId: product.id,
      entityKind: isContent ? 1 : 2,
      title: isContent ? product.title : product.name,
      description: product.description,
      tags: product.tags,
      category: product.category,
      country: countryCode,
      tenantName: selectedTenantName,
      contentsCount: isContent ? 1 : product?.items?.length,
      duration: isContent ? product.duration : null,
      mediaType: isContent ? product.mediaType : null,

      endDateTime: !showcaseAlways
        ? SHOWCASE_NON_EXPIRY_TIME
        : duration + Date.now(),
      showcaseExpires: showcaseAlways,
      // endDateTime: Date.now() + 1000 * 60 * 5,
      // using above code for testing ... showcase will expire after 10 min of creation
      status: mode,
      isPublic: shareAlways,
      metadata: product.metadata,
      purpose: {
        name: selectPurpose?.name,
        value: textBoxValue?.trim(),
        valueType: txtBoxType,
        buttonText,
        ...getSanitizedPaymentInfo()
      }
    };
    if (mode === MODES.DRAFT) {
      payload.showcaseExpires = false;
      payload.endDateTime = 1;
      delete payload.isPublic;
    }
    setIsCreating(true);
    const { data, isError } = await handleCreateShowcase(payload, spaceId);
    if (data && !isError) {
      NotificationManager.success(
        <IntlMessages id="sm.sucess" />,
        '',
        3000,
        null,
        null,
        ''
      );
      if (items) {
        const newItems = items?.map((m) => {
          if (m.id === product.id) {
            return {
              ...m,
              showcaseId: data.id
            };
          } else {
            return m;
          }
        });
        setItems(newItems);
      }
      if (setProduct) {
        setProduct({
          ...product,
          showcaseId: data.id
        });
      }
      setIsShowcased(true);
      // handleResetValue();
    }
    if (isError) {
      setIsShowcased(false);
    }
    setIsCreating(false);
  };

  useEffect(() => {
    if (step === 20) {
      setDuration(ONE_DAY);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const isEmail = selectPurpose?.id === 6 || txtBoxType === 1;

  useEffect(() => {
    if (selectPurpose?.id || txtBoxType) {
      if (isEmail) {
        setTxtBoxError(validateEmail(textBoxValue, true));
      } else {
        setTxtBoxError(validateUrl(textBoxValue, false));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [textBoxValue, selectPurpose?.id, txtBoxType]);

  useEffect(() => {
    if (product.showcaseId && isOpenShowCaseModal) {
      getSingleShowcaseData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [product.showcaseId, isOpenShowCaseModal]);

  useEffect(() => {
    const isCommonError = !!(
      txtBoxError ||
      duration === 0 ||
      selectPurpose === 'Select Purpose' ||
      txtBoxType === null ||
      !textBoxValue ||
      buttonText?.trim()?.length === 0 ||
      (showPayment &&
        (+sellingPrice === 0 ||
          !currency ||
          +originalPrice === 0 ||
          +originalPrice <= +sellingPrice))
    );

    if (!showcaseData) {
      // handling button state when showcase isnt created
      setButtonDisabled(isCommonError);
    } else {
      // handling button state when showcase is created
      // SAS= Same As Server
      const modeSAS = +mode === +showcaseData.status;
      const isShareAlwaysSAS = shareAlways === showcaseData?.isPublic;
      const isShowcaseAlwaysExpiresSAS =
        showcaseAlways === showcaseData?.showcaseExpires;
      const durationSAS = duration === showcaseData?.endDateTime;
      const isPurposeSAS = selectPurpose === showcaseData?.purpose?.name;
      const isTextBoxTypeSAS = txtBoxType === showcaseData?.purpose?.valueType;
      const isTextBoxValueSAS = textBoxValue === showcaseData?.purpose?.value;

      const isButtonTextValueSame =
        buttonText === showcaseData?.purpose?.buttonText;
      const isOrgPriceSame =
        originalPrice === showcaseData?.purpose?.originalPrice;
      const isSellingPriceSame =
        sellingPrice === showcaseData?.purpose?.sellingPrice;
      const isCurrencySame = currency === showcaseData?.purpose?.priceSymbol;

      const notSAS =
        !modeSAS ||
        !isShareAlwaysSAS ||
        !isShowcaseAlwaysExpiresSAS ||
        !durationSAS ||
        !isPurposeSAS ||
        !isTextBoxTypeSAS ||
        !isTextBoxValueSAS ||
        !isOrgPriceSame ||
        !isCurrencySame ||
        !isSellingPriceSame ||
        !isButtonTextValueSame;

      // console.log({
      //   modeSAS,
      //   isShareAlwaysSAS,
      //   isShowcaseAlwaysExpiresSAS,
      //   durationSAS,
      //   isPurposeSAS,
      //   isTextBoxTypeSAS,
      //   isTextBoxValueSAS,
      //   notSAS,
      //   isOrgPriceSame,
      //   isCurrencySame,
      //   isSellingPriceSame,
      //   isButtonTextValueSame,
      //   isCommonError,
      //   buttonDisabled: notSAS && !isCommonError
      // });

      if (notSAS && !isCommonError) {
        setButtonDisabled(false);
      } else {
        setButtonDisabled(true);
      }
    }
  }, [
    showcaseData,
    duration,
    selectPurpose,
    txtBoxType,
    txtBoxError,
    textBoxValue,
    mode,
    shareAlways,
    showcaseAlways,
    showPayment,
    buttonText,
    originalPrice,
    sellingPrice,
    currency
  ]);

  const closeModal = () => {
    handleResetValue();
  };

  const createUpdateDisabled = isCreating || buttonDisabled || isDeleting;

  const showcaseExpireModeDisable = (() => {
    const IfShowcaseDontExpireThanUserCanChangeExpiryMode = !(
      !isShowcaseExpires && isShowcasePublished
    );
    const IfShowcaseExpiresAndIsPublishedWeCannotMakeItNonExpiry =
      isShowcaseExpires && isShowcasePublished;
    if (showcaseData && showcaseData?.status !== MODES.DRAFT) {
      return (
        IfShowcaseDontExpireThanUserCanChangeExpiryMode ||
        IfShowcaseExpiresAndIsPublishedWeCannotMakeItNonExpiry
      );
    } else {
      return false;
    }
  })();

  const draftModeDisable = (() => {
    //  When showcase is published and showcase expires draft
    //  mode radio button should be disabled
    if (isShowcaseExpires && isShowcasePublished) {
      return true;
    }
    // When showcase is published and showcase doesnt gets showcaseExpires
    // draft radio button should be enabled
    if (isShowcasePublished && !isShowcaseExpires) {
      return false;
    }
  })();

  const showcasedUrl = `${window.location.origin}/app/showcase/detail/${showcaseData?.id}`;

  return (
    <>
      {isOpenShowCaseModal && confirmationOpen && (
        <DraftToPublishedShowcase
          isCreating={!isShowcased}
          expiryAt={conmputedDuration}
          showcaseAlways={showcaseAlways}
          modalOpen={confirmationOpen}
          toggleModal={toggleConfirmation}
          handleContinue={() => {
            if (isShowcased) {
              updateShowcase(true);
            } else {
              createShowCase(true);
            }
          }}
        />
      )}
      <Modal
        isOpen={isOpenShowCaseModal}
        toggle={closeModal}
        centered
        contentClassName="border-radius-15"
        style={{
          minWidth: '40%'
        }}
        backdrop="static"
      >
        {isShowcaseLoading ? (
          <div>
            <div className="loading"> </div>
          </div>
        ) : (
          <ModalBody
            className="d-flex flex-column pt-0 m-3 p-3 border-radius-10"
            style={{ border: '1px solid #992288' }}
          >
            <div className="d-flex flex-row w-full align-align-items-center justify-content-between">
              <div
                className="tab cursor-pointer d-flex flex-row"
                style={{
                  borderBottom: '2px solid #992288'
                }}
              >
                <IntlMessages id="sm.name" />
                {showcaseData?.title && (
                  <a
                    target="_blank"
                    rel="noreferrer"
                    href={showcasedUrl}
                    className="ml-1 font-weight-lightbold"
                    style={{
                      marginTop: '0px',
                      fontWeight: 'bold',
                      fontSize: '18px',
                      color: '#992288'
                    }}
                  >
                    - {showcaseData?.title}
                  </a>
                )}
              </div>
              <Button
                color="white"
                type="button"
                className="d-flex justify-content-end p-2"
                onClick={closeModal}
              >
                <i className="simple-icon-close close-btn" />
              </Button>
            </div>
            <div className="showcaseModal p-2 text-center ">
              <div>
                {/* CREATION AND EXPIRY DATE & TIME  */}
                {isShowcased && (
                  <ShowcasedInfo
                    showcaseData={showcaseData}
                    mode={mode}
                    conmputedDuration={conmputedDuration}
                    showcasedUrl={showcasedUrl}
                    showcaseAlways={showcaseAlways}
                  />
                )}

                {/* CALL TO ACTION  */}
                <>
                  <SectionHeader number={1} className="mt-3">
                    <p className="text-align-left mb-0">
                      <strong className="mr-2">
                        <IntlMessages id="sm.call-to-action" />
                      </strong>
                      <InfoIcon
                        name="call-to-action"
                        message="sm.call-to-action-info"
                      />
                    </p>
                  </SectionHeader>
                  <UncontrolledDropdown>
                    <DropdownToggle
                      caret
                      color="primary"
                      className="btn-sm w-full"
                      outline={!selectPurpose?.name}
                    >
                      {selectPurpose.name ?? selectPurpose}
                    </DropdownToggle>
                    <DropdownMenu center>
                      <InfiniteScroll
                        dataLength={7}
                        hasMore={false}
                        height={170}
                      >
                        {PURPOSE_DATA.map((p) => (
                          <DropdownItem
                            key={p.id}
                            onClick={() => {
                              setTxtBoxType(p.id === 6 ? 1 : 2);
                              setSelectPurpose(p);
                              if (p.name === showcaseData?.purpose?.name) {
                                console.log('setting old value again', {
                                  value: showcaseData.purpose.value
                                });
                                setTextBoxValue(showcaseData.purpose.value);
                                setButtonText(showcaseData?.purpose?.name);
                                return;
                              }
                              setButtonText(p.name);
                              setTextBoxValue('');
                            }}
                          >
                            <p
                              style={{
                                marginBottom: '-1px',
                                whiteSpace: 'normal'
                              }}
                            >
                              <b>{p.name}</b>
                            </p>
                            <span style={{ width: '275px' }}>{p.desc}</span>
                          </DropdownItem>
                        ))}
                      </InfiniteScroll>
                    </DropdownMenu>
                  </UncontrolledDropdown>
                  {selectPurpose.desc && (
                    <p className="text-align-left text-primary mb-5 mt-1">
                      <i className="simple-icon-info "> </i>
                      {selectPurpose.desc}
                    </p>
                  )}
                  {selectPurpose !== 'Select Purpose' && (
                    <FormInput
                      value={textBoxValue}
                      placeholder={
                        isEmail
                          ? 'Add your EmailId :'
                          : showPayment
                          ? 'Add your payment link'
                          : 'Add Your Website Link :'
                      }
                      type={isEmail ? 'email' : 'url'}
                      isImportant
                      label={
                        isEmail
                          ? 'Add your EmailId :'
                          : showPayment
                          ? 'Add your payment link'
                          : 'Add Your Website Link :'
                      }
                      onChangeText={(v) => {
                        setTextBoxValue(v?.trim());
                      }}
                      errorMessage={txtBoxError}
                    />
                  )}
                </>

                {selectPurpose !== 'Select Purpose' && (
                  <FormInput
                    value={buttonText}
                    placeholder="Enter button text"
                    type="text"
                    isImportant
                    label="Button Text"
                    style={{ width: '100%' }}
                    onChangeText={(v) => {
                      const value = v?.trim();
                      if (value.length > 30) {
                        NotificationManager.warning('Button text is too long!');
                      } else {
                        setButtonText(v?.trim());
                      }
                    }}
                    errorMessage=""
                  />
                )}

                {showPayment && (
                  <PaymentInfo
                    originalPrice={originalPrice}
                    setOriginalPrice={setOriginalPrice}
                    sellingPrice={sellingPrice}
                    setSellingPrice={setSellingPrice}
                    currency={currency}
                    setCurrency={setCurrency}
                  />
                )}

                {/* MODE OF SHOWCASE  */}
                <>
                  <SectionHeader number={2} className="mt-4">
                    <p className="text-align-left mb-0">
                      <strong>
                        <IntlMessages id="sm.showcase-mode" />
                      </strong>
                    </p>
                  </SectionHeader>
                  <div className="px-4">
                    <RadioRow
                      mode={mode}
                      setMode={setMode}
                      title="Internal"
                      value={MODES.DRAFT}
                      disabled={draftModeDisable}
                      isShowcased={isShowcased}
                      setButtonDisabled={setButtonDisabled}
                      info={
                        <p>
                          <IntlMessages id="sm.homepage-info" />
                          <b>
                            <a
                              className="text-decoration-none text-primary text-primary-hover font-weight-bold"
                              href={`/app/showcase/${selectedTenantId}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              style={{ wordWrap: 'break-word' }}
                            >
                              &nbsp;{selectedTenantName}
                            </a>
                          </b>
                          <IntlMessages id="sm.homepage-info-2" />
                        </p>
                      }
                    />
                    <RadioRow
                      mode={mode}
                      setMode={setMode}
                      isShowcased={isShowcased}
                      setButtonDisabled={setButtonDisabled}
                      title="Publish"
                      value={MODES.PUBLISH}
                      info={<></>}
                    />

                    {/* SHOWCASE EXPIRES OR NOT  */}
                    {mode === MODES.PUBLISH && (
                      <>
                        {/* PRIVATE & PUBLIC TOGGLE  */}
                        <>
                          <Decider
                            firstActive={!shareAlways}
                            onFirstClick={() => {
                              setShareAlways(false);
                            }}
                            firstLabel="sm.private-label"
                            secondLabel="sm.public-label"
                            onSecondClick={() => {
                              setShareAlways(true);
                            }}
                          />
                          <p className="text-align-left text-primary  mt-1 mb-2">
                            <i className="simple-icon-info font-weight-bold ">
                              {' '}
                            </i>
                            <IntlMessages
                              id={
                                shareAlways
                                  ? 'sm.public-info'
                                  : 'sm.private-info'
                              }
                            />{' '}
                            <a
                              className="text-decoration-none text-primary text-primary-hover font-weight-bold"
                              href={
                                shareAlways
                                  ? '/app/showcase'
                                  : `/app/showcase/${selectedTenantId}`
                              }
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <IntlMessages
                                id={
                                  shareAlways ? 'sm.showcase' : 'sm.acc-users'
                                }
                              />
                            </a>
                          </p>
                        </>
                        <Decider
                          disabled={showcaseExpireModeDisable}
                          firstActive={showcaseAlways}
                          onFirstClick={() => {
                            setDuration(ONE_DAY);
                            setShowcaseAlways(true);
                          }}
                          firstLabel="Promotions"
                          secondLabel="Portfolio"
                          onSecondClick={() => {
                            setShowcaseAlways(false);
                          }}
                        />
                        <p className="text-align-left text-primary  mt-1">
                          <i className="simple-icon-info font-weight-bold ">
                            {' '}
                          </i>
                          <IntlMessages
                            id={
                              showcaseAlways
                                ? 'Run experiments or promotion with your content'
                                : 'Create Interactive Portfolio, for always'
                            }
                          />
                        </p>
                        {showcaseAlways && (
                          <div className="d-flex flex-row w-full py-2 mt-1 mb-5">
                            <div style={{ width: '18%', fontWeight: 'bolder' }}>
                              <p className="text-align-left">
                                <IntlMessages id="sm.duration" />
                              </p>
                            </div>
                            <div
                              style={{ width: '82%' }}
                              className="d-flex flex-column w-full"
                            >
                              <SliderTooltip
                                min={20}
                                max={100}
                                defaultValue={step}
                                step={20}
                                onChange={(e) => {
                                  handleStep(e);
                                }}
                              />
                              <div className="d-flex flex-row align-items-center justify-content-between">
                                <span data="1 Day" className="span-data">
                                  |
                                </span>
                                <span data="2 Days" className="span-data">
                                  |
                                </span>
                                <span data="3 Days" className="span-data">
                                  |
                                </span>
                                <span data="7 Days" className="span-data">
                                  |
                                </span>
                                <span data="30 Days" className="span-data">
                                  |
                                </span>
                              </div>
                            </div>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </>
              </div>

              {/* BUTTONS  */}
              <div className="d-flex flex-row align-items-center justify-content-center mb-2 mt-4 gap-1">
                <Button
                  onClick={() => {
                    if (isShowcased) {
                      updateShowcase();
                    } else {
                      createShowCase();
                    }
                  }}
                  color="primary"
                  size="md"
                  className={`btn-shadow auth-btn ${
                    isCreating ? 'btn-multiple-state show-spinner' : ''
                  }`}
                  disabled={createUpdateDisabled}
                >
                  <span className="spinner d-inline-block">
                    <span className="bounce1" />
                    <span className="bounce2" />
                    <span className="bounce3" />
                  </span>
                  <span className="label">
                    {!isShowcased ? <IntlMessages id="sm.save" /> : 'Update'}
                  </span>
                </Button>
                {isShowcased && (
                  <Button
                    color="primary"
                    size="md"
                    outline
                    className={`btn-shadow auth-btn ${
                      isDeleting ? 'btn-multiple-state show-spinner' : ''
                    }`}
                    onClick={handleRemoveShowcase}
                    disabled={isDeleting}
                  >
                    <span className="spinner d-inline-block">
                      <span className="bounce1" />
                      <span className="bounce2" />
                      <span className="bounce3" />
                    </span>
                    <span className="label">
                      <i className="simple-icon-trash mr-2" />
                      <IntlMessages id="mediaPlayer.delete" />
                    </span>
                  </Button>
                )}
              </div>
            </div>
          </ModalBody>
        )}
      </Modal>
    </>
  );
}

export default ShowcaseModal;
