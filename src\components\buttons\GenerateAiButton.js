/* eslint-disable jsx-a11y/no-static-element-interactions */
import AiIcon from 'constants/AiIcon';
import IntlMessages from 'helpers/IntlMessages';
import React, { useRef, useState } from 'react';
import { Popover, PopoverBody } from 'reactstrap';
import useOnClickOutside from 'hooks/useOnClickOutSide';

function GenerateAiButton({
  text,
  onClick,
  isLoading,
  disabled,
  infoMessage = '',
  id = null,
  aiIconLeft = true
}) {
  const ref = useRef(null);
  const [toggled, setToggled] = useState(false);
  useOnClickOutside(ref, () => setToggled(false));
  const key = `Popover-${id}`;
  return (
    <span
      aria-disabled={isLoading || disabled}
      onClick={onClick}
      onMouseEnter={() => setToggled(true)}
      onMouseLeave={() => setToggled(false)}
      className="c-pointer"
      style={{
        borderRadius: '20px',
        backgroundColor: '#922c88',
        padding: '10px 35px',
        fontSize: '16px',
        width: 'fit-content'
      }}
      id={key ?? 'DEFAULT'}
    >
      {aiIconLeft && <AiIcon active height={18} />}
      <span className="mx-2 white ">
        <IntlMessages id={text} />
        {isLoading && (
          <span className="show-spinner btn-multiple-state">
            <span className="spinner d-inline-block">
              <span
                style={{ backgroundColor: '#992288' }}
                className="bounce1"
              />
              <span
                style={{ backgroundColor: '#992288' }}
                className="bounce2"
              />
              <span
                style={{ backgroundColor: '#992288' }}
                className="bounce3"
              />
            </span>
          </span>
        )}
      </span>
      {id && infoMessage && (
        <span ref={ref}>
          <Popover
            placement="top"
            isOpen={toggled}
            target={key}
            toggle={() => setToggled(!toggled)}
          >
            <PopoverBody>
              <IntlMessages id={infoMessage} />
            </PopoverBody>
          </Popover>
        </span>
      )}
      {!aiIconLeft && <AiIcon active height={18} />}
    </span>
  );
}

export default GenerateAiButton;
