/* eslint-disable no-unused-vars */
import Like from 'components/ShowcaseThumbnail/Like';
import React from 'react';
import ShowcaseTitle from './ShowcaseTitle';
import ShowcaseDropdown from '../ShowcaseDropdown';
import Avtar from 'components/common/Avtar';
import { Link } from 'react-router-dom';
import { convertNumberToShort } from 'helpers/Utils';
import Dot from './Dot';
import ShowcaseExpiryPopup from './ShowcaseExpiryPopup';
import VerifedIcon from 'constants/VerifedIcon';

const ShowcaseCardFooter = ({
  data,
  time,
  isLoggedIn,
  showcaseLikes,
  selectedTenantId,
  isDarkMode,
  isSubscribed,
  subscribing,
  myShowcases,
  roleType,
  setOpen,
  open,
  isPinned,
  highlightShowcase,
  isHighlighting,
  expandFor,
  isShowcaseBgLoading,
  showcasedBg,
  makeShowcaseContentBg,
  toggle,
  MediaType,
  handleSubscription,
  userIdentifier,
  handleShowcaseShare,
  footerClassName = ''
}) => {
  return (
    <div className={`d-flex flex-column w-full ${footerClassName}`}>
      <div className="d-flex flex-row w-full align-items-center justify-content-between mt-3 position-relative">
        <Like
          className="position-absolute c-pointer"
          isPublic={!isLoggedIn}
          showcaseId={data.id}
          showcaseLikes={showcaseLikes}
          selectedTenantId={selectedTenantId}
          style={{ left: 0, top: -40, zIndex: 100 }}
        />
        <ShowcaseTitle title={data.title} id={data.id} />
        <div>
          <ShowcaseDropdown
            isDarkMode={isDarkMode}
            isSubscribed={isSubscribed}
            subscribing={subscribing}
            isLoggedIn={isLoggedIn}
            myShowcases={myShowcases}
            roleType={roleType}
            setOpen={setOpen}
            open={open}
            isPinned={isPinned}
            highlightShowcase={highlightShowcase}
            isHighlighting={isHighlighting}
            showSubscribeButton={!expandFor}
            expandFor={expandFor}
            isShowcaseBgLoading={isShowcaseBgLoading}
            showcasedBg={showcasedBg}
            makeShowcaseContentBg={makeShowcaseContentBg}
            toggleEditShowcase={toggle}
            mediaType={MediaType}
            // using this so that we wont show add to bg option in dropdown
            isDuration={data?.duration > 0}
            showcaseExpires={data.showcaseExpires}
            handleSubscription={() =>
              handleSubscription({
                tenantId: data.tenantId,
                showcaseId: data.id,
                userIdentifier
              })
            }
            handleShare={() =>
              handleShowcaseShare(
                `${window.location.origin}/app/showcase/detail/${data.id}`,
                data.title,
                data.tenantName
              )
            }
          />
        </div>
      </div>
      <div className="d-flex flex-row w-full align-items-center justify-content-start acc">
        <span className="mr-2">
          <Avtar size={20} fName={data.tenantName} />
        </span>
        <span className="name">
          <Link to={`/app/showcase/${userIdentifier}`}>{data.tenantName}</Link>
        </span>
        <VerifedIcon isVerfied={data?.isVerified} />
      </div>
      <div className="d-flex flex-row align-items-center justify-content-between date mt-1">
        <span className="d-flex align-items-center">
          {data.totalImpressionsCount
            ? convertNumberToShort(data.totalImpressionsCount)
            : 0}
          <i className="simple-icon-eye ml-1"> </i>
        </span>
        &nbsp; <Dot />
        <span>{time}</span>
        {data.endDateTime !== 1 && (
          <>
            &nbsp;
            <Dot />
            <ShowcaseExpiryPopup date={data.endDateTime} id={data.id} />
          </>
        )}
      </div>
    </div>
  );
};

export default ShowcaseCardFooter;
