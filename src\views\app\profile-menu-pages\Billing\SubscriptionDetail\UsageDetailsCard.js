/* eslint-disable react/no-array-index-key */
import InfoIcon from 'components/icon/InfoIcon';
import AiIcon from 'constants/AiIcon';
import IntlMessages from 'helpers/IntlMessages';
import React from 'react';

const UsageDetailsCard = ({ data, showBarGraph = true }) => {
  const isTrue = data.isActive ? '#228B22' : '#FF0000';
  const values = data.values ?? [];

  return (
    <div className="mb-3">
      {data.title && (
        <div className="d-flex flex-row align-items-center justify-content-between mb-1">
          <div>
            <b>
              <IntlMessages id={data.title} />
              {data.aiEnabled && (
                <span className="ml-1">
                  <AiIcon primary active height={17} />
                </span>
              )}
            </b>
            {data.infoMessage && (
              <InfoIcon
                className="ml-2"
                key={data?.id}
                message={data.infoMessage}
                name={`info-${data.id}`}
              />
            )}
          </div>
          <span
            style={{
              border: `1px solid ${isTrue}`,
              color: isTrue,
              borderRadius: 20,
              padding: '3px 15px',
              fontSize: '14px'
            }}
          >
            {data.isActive ? 'True' : 'False'}
          </span>
        </div>
      )}
      {showBarGraph && values.length === 0 && (
        <Graph
          leftText={data.left}
          rightText={data.right}
          percentage={data?.percent}
        />
      )}
      {values.length > 0 && (
        <>
          {values?.map((m, i) => (
            <Graph
              key={i}
              marginBottom={values.length >= 1 ? 10 : 0}
              leftText={m.left}
              rightText={m.right}
              percentage={m?.percent}
            />
          ))}
        </>
      )}
    </div>
  );
};

const Graph = ({ leftText, rightText, percentage, marginBottom = 0 }) => {
  return (
    <div
      className="p-3"
      style={{ border: '1px solid silver', borderRadius: 10, marginBottom }}
    >
      <div className="d-flex flex-row w-full align-items-center justify-content-between">
        <p className="m-0">
          <IntlMessages id={leftText} />
        </p>
        <p className="m-0">{rightText}</p>
      </div>
      <div className="w-full my-1">
        <div
          style={{
            position: 'relative',
            background: '#D9D9D9',
            height: 10,
            borderRadius: 10
          }}
        >
          <div
            style={{
              position: 'absolute',
              background: '#992288',
              height: 10,
              borderRadius: 10,
              width: `${percentage}%`,
              maxWidth: '100%'
            }}
          />
        </div>
      </div>
      <p className="text-primary font-bolder mb-0">{percentage ?? 0} %</p>
    </div>
  );
};

export default UsageDetailsCard;
