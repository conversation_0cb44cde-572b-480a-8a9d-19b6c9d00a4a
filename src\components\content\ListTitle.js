/* eslint-disable consistent-return */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-vars */
import { NotificationManager } from 'components/common/react-notifications';
import { handleUpdatePlayer } from 'functions/api';
import { updateContent, updateStory } from 'functions/api/spacesApi';
import IntlMessages from 'helpers/IntlMessages';
import {
  isDarkModeActive,
  RoleTypes,
  validatePlayerTitle,
  validateStoryTitle
} from 'helpers/Utils';
import React, { useState } from 'react';
import Loader from 'react-loader-spinner';
import { Card, FormGroup, Input, Label, NavLink, Tooltip } from 'reactstrap';

const ListTitle = ({
  roleType,
  product,
  handleEdit,
  isPlayer = false,
  isStory = false,
  className = ''
}) => {
  const titleOrName = isPlayer || isStory ? product.name : product?.title;

  const [isEdit, setIsEdit] = useState(false);
  const [tempTitle, setTempTitle] = useState(titleOrName);
  const [isDisabled, setIsDisabled] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  const isTitleSame = tempTitle === titleOrName;

  const contributerOrAbove = roleType <= RoleTypes.CONTRIBUTOR;

  const handleStoryTitle = async () => {
    const errorMsgId = validateStoryTitle(tempTitle);
    if (errorMsgId) {
      NotificationManager.warning(<IntlMessages id={errorMsgId} />, '');
      return;
    }
    const handleError = () => {
      NotificationManager.error(<IntlMessages id="story-failed-name" />);
      setIsUpdating(false);
    };
    try {
      setIsUpdating(true);
      const resp = await updateStory({
        id: product.id,
        spaceId: product.spaceId,
        name: tempTitle
      });
      if (resp.isError) {
        handleError();
      } else {
        NotificationManager.success(<IntlMessages id="story-success-name" />);
        product.name = tempTitle;
        setIsUpdating(false);
        setIsEdit(false);
      }
    } catch (error) {
      console.error('Something went wrong in handleStoryTitle due to ', error);
      handleError();
    }
  };

  const handlePlayerTitle = async () => {
    const errorMsgId = validatePlayerTitle(tempTitle);
    if (errorMsgId) {
      NotificationManager.warning(<IntlMessages id={errorMsgId} />);
      return;
    }
    const handleError = () => {
      NotificationManager.error(<IntlMessages id="media-player-title-error" />);
      setIsUpdating(false);
    };
    try {
      setIsUpdating(true);
      const { data } = await handleUpdatePlayer({
        id: product.id,
        spaceId: product.spaceId,
        name: tempTitle
      });
      if (data?.data?.id) {
        NotificationManager.success(
          <IntlMessages id="media-player-title-success" />
        );
        product.name = tempTitle;
        setIsEdit(false);
        setIsUpdating(false);
      } else {
        handleError();
      }
    } catch (error) {
      console.error('Something went wrong in handlePlayerTitle due to ', error);
      handleError();
    }
  };

  const handleTitleUpdate = async () => {
    if (isPlayer) {
      handlePlayerTitle();
      return;
    }

    if (isStory) {
      handleStoryTitle();
      return;
    }

    if (tempTitle.trim().length === 0) {
      return NotificationManager.warning(
        <IntlMessages id="title.enter" />,
        '',
        3000,
        null,
        null,
        ''
      );
    }
    if (tempTitle.trim().length < 2) {
      return NotificationManager.warning(
        <IntlMessages id="title.atleast2" />,
        '',
        3000,
        null,
        null,
        ''
      );
    }
    if (tempTitle.length > 100) {
      return NotificationManager.warning(
        <IntlMessages id="story.title-less-100" />,
        '',
        3000,
        null,
        null,
        ''
      );
    }

    setIsUpdating(true);
    const resp = await updateContent(
      { Id: product.id, title: tempTitle },
      product.spaceId
    );
    if (resp.isError) {
      setIsUpdating(false);
      NotificationManager.error(<IntlMessages id="content-title-error" />);
    }
    if (!resp.isError) {
      product.title = tempTitle;
      setIsUpdating(false);
      setIsEdit(false);
      NotificationManager.success(
        <IntlMessages id="space-title-update-sucess" />,
        <IntlMessages id="req.success" />,
        3000,
        null,
        null,
        ''
      );
    }
    return null;
  };

  const handleKeyPress = (e) => {
    if (e.keyCode === 13 && !isUpdating) {
      handleTitleUpdate();
    }
  };

  const editAllowed = (() => {
    if (isPlayer) {
      return product.isEnabled;
    }
    if (isStory) {
      return product.isEnabled;
    }
    if (!isPlayer && !isStory) {
      return product?.status === 2 || product?.status === 3;
    }
    return true;
  })();

  if (!isEdit || !contributerOrAbove) {
    return (
      <DefaultTitle
        handleEdit={handleEdit}
        setIsEdit={setIsEdit}
        product={product}
        titleOrName={titleOrName}
        editAllowed={editAllowed}
      />
    );
  }

  return (
    <Card
      className={`space-media-title-editor p-1 my-1 d-flex flex-row align-items-center justify-content-center gap-2 ${className}`}
      aria-disabled={isUpdating}
    >
      <FormGroup
        className="form-group has-float-label mb-0"
        style={{ flex: 1 }}
      >
        <Input
          type="text"
          readOnly={isUpdating}
          disabled={isDisabled || !contributerOrAbove}
          onChange={(e) => {
            const inputValue = e.target.value;
            if (inputValue.length > 100) {
              NotificationManager.warning(
                <IntlMessages id="story.title-less-100" />
              );
            }
            const strippedText = inputValue.substring(0, 100);
            setTempTitle(strippedText);
          }}
          defaultValue={tempTitle}
          value={tempTitle}
          onKeyDown={handleKeyPress}
          style={{ padding: '2px 4px', fontSize: '0.75rem' }}
          className={`w-full ${
            isDisabled ? 'InputStyleActive' : 'InputStyleInActive'
          }`}
        />
      </FormGroup>
      {contributerOrAbove && (
        <span
          className="c-pointer text-primary centered"
          style={{
            fontSize: '0.8rem'
          }}
          onClick={() => {
            setIsEdit(false);
          }}
        >
          <i className="simple-icon-close" />
        </span>
      )}
      {contributerOrAbove && !isTitleSame && (
        <NavLink
          to="#"
          style={{
            fontSize: '0.8rem'
          }}
          className="p-0 c-pointer text-primary centered"
        >
          {isUpdating ? (
            <Loader type="TailSpin" color="#922c88" height={12} width={12} />
          ) : (
            <i
              onClick={() => {
                handleTitleUpdate();
              }}
              className={`${
                isDisabled ? 'simple-icon-pencil' : 'simple-icon-check'
              } `}
            />
          )}
        </NavLink>
      )}
    </Card>
  );
};

const DefaultTitle = ({
  setIsEdit,
  product,
  handleEdit,
  titleOrName,
  editAllowed
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const target = `tooltip_${product?.id}_list`;
  const [isTitleHovered, setIsTitleHovered] = useState(false);

  return (
    <div className="defaultTitle">
      <p
        onClick={handleEdit}
        id={target}
        className="text-ellipsis defaultTitleText"
        style={{
          cursor: editAllowed && 'pointer',
          color: isTitleHovered ? '#922c88' : isDarkModeActive() ? 'silver' : ''
        }}
        onMouseEnter={() => {
          if (editAllowed) setIsTitleHovered(true);
        }}
        onMouseLeave={() => setIsTitleHovered(false)}
      >
        {titleOrName}
      </p>
      <span
        className="editDefault"
        onClick={() => {
          setIsEdit(true);
        }}
      >
        <i className="simple-icon-pencil" />
      </span>
      <Tooltip
        placement="bottom"
        isOpen={isOpen}
        target={target}
        toggle={() => setIsOpen(!isOpen)}
      >
        {titleOrName}
      </Tooltip>
    </div>
  );
};

export default ListTitle;
