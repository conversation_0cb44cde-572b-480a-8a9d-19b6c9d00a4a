import React from 'react';
import { NavLink } from 'react-router-dom';
import { Button, Modal, ModalBody } from 'reactstrap';

const NotAbleToDraft = ({ modalOpen, toggleModal }) => {
  return (
    <Modal isOpen={modalOpen} toggle={toggleModal} centered>
      <NavLink
        className="btn-link text-decoration-none d-flex align-items-center justify-content-end mt-2 mr-2"
        to="#"
        onClick={toggleModal}
      >
        <i className="simple-icon-close close-btn" />
      </NavLink>
      <ModalBody>
        <p>Please Remove the Share to make this content draft.</p>
        <div className="d-flex justify-content-center">
          <Button color="primary" onClick={toggleModal}>
            ok
          </Button>
        </div>
      </ModalBody>
    </Modal>
  );
};

export default NotAbleToDraft;
