/* eslint-disable array-callback-return */
/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable no-unused-vars */
import WarningCard from 'components/cards/WarningCard';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
  Button,
  Card,
  Input,
  Modal,
  ModalBody,
  ModalHeader,
  Popover,
  PopoverBody,
  Spinner
} from 'reactstrap';
import SubmitButton from '../components/SubmitButton';
import CopyIcon from 'components/svg/CopyIcon';
import { formatDateNTime, getSessionId, isYoutube } from 'helpers/Utils';
import { AnswerIcon, DetailIcon, MidyaAiIcon, QuestionIcon } from './Icons';
import AiIcon from 'constants/AiIcon';
import ReactQuill from 'react-quill';
import {
  getAiChatHistory,
  handleChat,
  handleStartChat
} from 'functions/api/analyze';
import { NotificationManager } from 'components/common/react-notifications';
import LoadingIndicator from './LoadingIndicator';
import Switch from 'rc-switch';
import { updateContent } from 'functions/api/spacesApi';
import IntlMessages from 'helpers/IntlMessages';
import Messages from '../components/Messages';
import InfoMessage from 'components/infoMessage';
import ChatAnalytics from '../../../ChatAnalytics';
import InfoPopover from 'components/InfoPopover';
import { nanoid } from 'nanoid';
import useCounter from 'hooks/useCounter';
import ViewAnalytics from '../ViewAnalytics';

const AiChat = ({
  content,
  textTranscribe,
  isMedyaAiEnabled,
  setIsMedyaAiEnabled,
  isStory,
  getContent,
  handlePlayerChange,
  isAiEnabled,
  setIsAiEnabled,
  showEnableAiOption
}) => {
  const {
    id: contentId,
    spaceId,
    mediaType,
    contentSource,
    metadata
  } = content;
  const description = metadata?.SummaryAuto;
  const isImage = mediaType === 3;
  const isYoutubeContent = isYoutube(contentSource);
  const [isOpen, setIsOpen] = useState(false);
  const [question, setQuestion] = useState('');
  const [chat, setChat] = useState([]);
  const [isWaiting, setIsWaiting] = useState(false);
  const [newMsg, setNewMsg] = useState('');
  const [isStartChatError, setIsStartChatError] = useState(false);
  const [sessionId, setSessionId] = useState(null);
  const scrollRef = useRef(null);
  const [isloadingChatHistory, setIsLoadingChatHistory] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const { count, reset, start, stop } = useCounter();

  const scrollToBottom = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  };

  useEffect(() => {
    if (newMsg) {
      setChat((pr) => {
        const last = pr[pr.length - 1];
        last.answer = newMsg;
        const removed = pr.slice(0, pr.length - 1);
        return [...removed, last];
      });
      setTimeout(() => {
        scrollToBottom();
      }, 50);
    }
  }, [newMsg]);

  useEffect(() => {
    const startChat = async () => {
      const key = `${contentId}-chat`;
      const futureDateFromLocal = localStorage.getItem(key);
      let makeCallToStartChat = false;
      let localSessionId = null;
      if (!futureDateFromLocal) {
        localSessionId = nanoid();
        setSessionId(localSessionId);
        makeCallToStartChat = true;
      }
      if (futureDateFromLocal) {
        const parsedData = JSON.parse(futureDateFromLocal);
        const currentDate = Date.now();
        if (+currentDate > +parsedData.futureDate && parsedData?.sessionId) {
          localSessionId = parsedData?.sessionId;
          setSessionId(localSessionId);
          makeCallToStartChat = true;
        } else {
          localSessionId = parsedData?.sessionId;
          setSessionId(localSessionId);
        }
      }
      if (makeCallToStartChat && localSessionId) {
        const { data, isError } = await handleStartChat({
          scope: textTranscribe,
          sessionId: localSessionId,
          contentId
        });
        if (!isError) {
          const currentDate = new Date();
          const futureDate = currentDate.getTime() + 24 * 60 * 60 * 1000;
          console.log('futureDate', futureDate);
          localStorage.setItem(
            key,
            JSON.stringify({ futureDate, sessionId: localSessionId })
          );
          setIsStartChatError(isError);
        }
      }
    };
    if (textTranscribe) {
      startChat();
    }
  }, [textTranscribe, contentId]);

  const handleSendMessage = async () => {
    if (!question) {
      return;
    }
    reset();
    start();
    setIsWaiting(true);
    const currentDateTime = new Date();
    setChat((prev) => {
      const newMessage = {
        createdAt: currentDateTime,
        question,
        isQuestion: true,
        answer: ''
      };
      return [...prev, newMessage];
    });
    setTimeout(() => {
      scrollToBottom();
    }, 50);
    const chatResponse = await handleChat({
      query: question,
      sessionId,
      contentId,
      isStory,
      mediaType,
      spaceId,
      isYoutubeContent
    });
    if (typeof chatResponse === 'string') {
      setIsWaiting(false);
      setQuestion('');
      setChat((prev) => {
        const previous = prev;
        const newArr = previous.slice(0, -1);
        return newArr;
      });
      return;
    }
    const { data, isError } = chatResponse;
    console.log({
      data,
      isError,
      chat
    });
    if (!isError && data) {
      setNewMsg(data);
      // eslint-disable-next-line no-empty
    } else {
    }
    stop();
    setIsWaiting(false);
    setQuestion('');
  };

  useEffect(() => {
    const fetchAiChatHistory = async () => {
      if (sessionId) {
        setIsLoadingChatHistory(true);
        const { data, isError } = await getAiChatHistory(sessionId);
        console.log({
          data,
          isError,
          sessionId
        });
        if (isError) {
          console.error('Something went wrong');
        } else {
          const old = [];
          const chatHistory = data.hist ?? [];
          const answer = chatHistory.filter((f) => f.type === 'ai');
          const questions = chatHistory.filter((f) => f.type === 'human');
          questions.map((m, i) => {
            old.push({
              question: m.data.content,
              answer: answer[i].data.content,
              createdAt: null
            });
          });
          console.log('old', old);
          setChat((prev) => {
            return [...old.reverse(), ...prev];
          });
        }
        setIsLoadingChatHistory(false);
      }
    };
    if (sessionId) {
      fetchAiChatHistory();
    }
  }, [sessionId]);

  const handleMedyaAiUpdate = async () => {
    try {
      setIsUpdating(true);
      const payload = {
        Id: contentId,
        medyaAI: !isMedyaAiEnabled,
        spaceId
      };
      const { isError } = await updateContent(payload, spaceId);
      if (isError) {
        NotificationManager.error(
          <IntlMessages id="something-wrong" />,
          <IntlMessages id="req.error" />
        );
      } else {
        await getContent(true);
        handlePlayerChange();
        const newState = !isMedyaAiEnabled;
        setIsMedyaAiEnabled(newState);
        NotificationManager.success(
          <IntlMessages
            id={newState ? 'medya-ai-enabled' : 'medya-ai-disabled'}
          />
        );
      }
      setIsUpdating(false);
    } catch (error) {
      console.error(
        `Something went in handleMedyaAiUpdate due to `,
        error.message
      );
      setIsUpdating(false);
    }
  };

  if (!textTranscribe) {
    return (
      <WarningCard text="To enable the Innerloop Midya AI feature, you need to first create Detailed Insights for the content " />
    );
  }

  return (
    <>
      <div className="aichat">
        {!isStory && !isImage && !isYoutubeContent && textTranscribe && (
          <div className="aichat-header mb-3">
            <div className="headLeft">
              <div className="d-flex flex-row gap-1 align-items-center h-full">
                <span className="text-nowrap">
                  Show <b>‘Midya AI bot’</b> in Player :
                </span>
                {isUpdating ? (
                  <Spinner size="sm" color="info">
                    Loading...
                  </Spinner>
                ) : (
                  <span>
                    <Switch
                      aria-disabled={isUpdating}
                      className="custom-switch custom-switch-primary custom-switch-small ml-1"
                      checked={isMedyaAiEnabled}
                      onClick={handleMedyaAiUpdate}
                    />
                  </span>
                )}
              </div>
            </div>
            {isMedyaAiEnabled && <ViewAnalytics contentId={contentId} />}
          </div>
        )}

        <Card className="p-4 border-10">
          <div className="d-flex w-full flex-row align-items-center justify-content-between">
            <span>
              I am Innerloop Midya AI bot <MidyaAiIcon />
            </span>
            <span
              aria-disabled={!textTranscribe}
              className="c-pointer"
              onClick={() => {
                setIsOpen(true);
              }}
            >
              <DetailIcon />
            </span>
          </div>
          {textTranscribe && (
            <div
              className="custom-input"
              aria-disabled={!textTranscribe || isWaiting}
            >
              <Input
                value={question}
                onChange={(e) => {
                  const textValue = e.target.value.trimStart();
                  if (textValue.length > 300) {
                    NotificationManager.warning('Question is too large');
                  }
                  const removeExtra = textValue.substring(0, 300);
                  setQuestion(removeExtra);
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleSendMessage();
                  }
                }}
                placeholder="I can answer your questions based on the knowledge created"
              />
              <SubmitButton
                isLoading={isWaiting}
                disabled={!question}
                count={count}
                onClick={handleSendMessage}
              />
            </div>
          )}
          <InfoMessage message="chat-24-hrs" />
        </Card>

        <div className="messages scrollbar-hidden" ref={scrollRef}>
          {isloadingChatHistory && (
            <div className="d-flex flex-row justify-content-center w-full mt-5 mb-5">
              <span className="mr-5">
                <LoadingIndicator />
              </span>
              <p className="text-primary">Loading chat history</p>
            </div>
          )}
          <Messages chat={chat} isWaiting={isWaiting} />
        </div>
      </div>
      {isOpen && (
        <DetailedInsightsModal
          open={isOpen}
          description={textTranscribe}
          toggle={() => {
            setIsOpen(!isOpen);
          }}
        />
      )}
    </>
  );
};

export default AiChat;


const DetailedInsightsModal = ({ description, toggle, open }) => {
  return (
    <Modal
      isOpen={open}
      toggle={toggle}
      centered
      contentClassName="border-radius-10"
      style={{
        minWidth: '50%',
        borderRadius: 20
      }}
      backdrop="static"
    >
      <ModalBody
        className="m-3 border-radius-10 d-flex flex-column align-items-center justify-content-center"
        style={{ border: '1px solid #992288' }}
      >
        <div className="w-full d-flex flex-row align-items-center justify-content-between mb-2">
          <b>
            <p className="m-0" style={{ fontSize: '18px' }}>
              Detailed Insights
            </p>
          </b>
          <Button
            color="white"
            type="button"
            className="d-flex justify-content-end p-2"
            onClick={toggle}
          >
            <i className="simple-icon-close close-btn" />
          </Button>
        </div>
        <div
          style={{ fontSize: 13 }}
          className="d-flex w-full flex-row align-items-center justify-content-start mb-1"
        >
          <AiIcon primary active height={15} />
          &nbsp;&nbsp;
          <b className="text-primary">Insights</b>
        </div>

        <div
          className="w-100 d-flex flex-column position-relative"
          style={{ height: 250 }}
        >
          <CopyIcon
            className="ml-2 position-absolute"
            copyToClipboardOnClick
            style={{ top: 10, right: 10, zIndex: 10 }}
            dataToCopy={description}
          />
          <ReactQuill
            onChange={(e) => {
              console.log('e', e);
            }}
            style={{
              overflow: 'hidden',
              width: '100%',
              height: 400
            }}
            className="react-quill ai-editor
              disabled-ql aiInputBgQuill SpaceDetailDescription DescriptionStyleInActive "
            readOnly
            theme="bubble"
            value={description}
          />
        </div>
      </ModalBody>
    </Modal>
  );
};
