/* eslint-disable jsx-a11y/no-static-element-interactions */
import IntlMessages from 'helpers/IntlMessages';
import React from 'react';
import { Card } from 'reactstrap';

function SwitchButton({ name, showIcon = true, ...props }) {
  return (
    <Card
      className="SwitchButton d-flex flex-row align-items-center c-pointer"
      {...props}
    >
      {showIcon && (
        <svg
          width="14"
          height="13"
          className="mr-2"
          viewBox="0 0 14 13"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M0.875 4.9375H12.125L8.375 1.1875M12.3747 8.0625H1.12469L4.87469 11.8125"
            stroke="#992288"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      )}
      <span style={{ fontSize: 14 }}>
        <IntlMessages id={name} />
      </span>
    </Card>
  );
}

export default SwitchButton;
