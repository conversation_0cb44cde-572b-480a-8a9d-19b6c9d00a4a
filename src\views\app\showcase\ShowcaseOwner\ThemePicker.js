import { ColorInput } from 'components/input';
import { EditButton } from './Buttons';
import React from 'react';

const ThemePicker = ({
  themeColors,
  setThemeColors,
  isOpen,
  setIsOpen,
  orgThemeColors,
  setOrgThemeColors,
  disabled = false
}) => {
  return (
    <span className="position-relative" style={{ height: 40 }}>
      <EditButton
        onClick={() => {
          setIsOpen(!isOpen);
        }}
        label="Theme"
        isThemePicker
        bgColor={isOpen ? '#ff04e221' : '#fff'}
        iconClassName="iconsminds-palette"
        disabled={disabled}
      />
      <ColorInput
        open={isOpen}
        setOpen={setIsOpen}
        themeColors={themeColors}
        setThemeColors={setThemeColors}
        orgThemeColors={orgThemeColors}
        setOrgThemeColors={setOrgThemeColors}
        color={themeColors.subscribeButtonColor}
        setColor={(c) => {
          setThemeColors({
            ...themeColors,
            subscribeButtonColor: c,
            tenantTagsColor: c,
            contentBorderColor: c,
            shareButtonColor: c,
            reloadButtonColor: c
          });
        }}
        iconStyle={{
          display: 'none'
        }}
      />
    </span>
  );
};

export default ThemePicker;
