import { ColorInput } from 'components/input';
import { getTextColorFromBgColor } from 'helpers/Utils';
import React, { useState } from 'react';

const Tag = ({
  text,
  themeColors,
  setThemeColors,
  showColorChooser,
  orgThemeColors,
  setOrgThemeColors
}) => {
  const [isOpen, setIsOpen] = useState(false);
  return (
    <span
      className="position-relative owner-tag mb-1"
      style={{
        background: themeColors.tenantTagsColor,
        color: getTextColorFromBgColor(themeColors.tenantTagsColor)
      }}
    >
      {text}
      {showColorChooser && (
        <ColorInput
          open={isOpen}
          setOpen={setIsOpen}
          themeColors={themeColors}
          setThemeColors={setThemeColors}
          orgThemeColors={orgThemeColors}
          setOrgThemeColors={setOrgThemeColors}
          color={themeColors.tenantTagsColor}
          setColor={(c) => {
            setThemeColors({
              ...themeColors,
              tenantTagsColor: c
            });
          }}
          style={{ top: -15, right: 0, position: 'absolute' }}
        />
      )}
    </span>
  );
};

export default Tag;
