/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable react/no-children-prop */
/* eslint-disable react/no-danger */
/* eslint-disable no-unused-vars */
import CopyIcon from 'components/svg/CopyIcon';
import { convertToHtml, formatDateNTime } from 'helpers/Utils';
import React from 'react';
import { AnswerIcon, QuestionIcon } from '../AiChat/Icons';
import LoadingIndicator from '../AiChat/LoadingIndicator';
import { marked } from 'marked';

const Messages = ({ chat, isWaiting, isQna = false }) => {
  return (
    <>
      {chat?.map((m, i) => (
        <>
          <Question question={m?.question ?? ''} createdAt={m?.createdAt} />
          <Answer
            isQna={isQna}
            isWaiting={isWaiting && i === chat.length - 1}
            answer={m?.answer ?? ''}
            createdAt={m?.createdAt}
          />
        </>
      ))}
    </>
  );
};

const Question = ({ question, createdAt, isQna = false }) => {
  return (
    <div className="qns">
      <div className="left">
        <QuestionIcon />
      </div>
      <div className="right">
        {!isQna && <span className="head">You</span>}
        <div className="question cont">{question}</div>
        {createdAt && (
          <div className="d-flex flex-row align-items-center justify-content-end mt-2">
            <span style={{ fontSize: 12 }}>
              {formatDateNTime(new Date(createdAt))}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

const Answer = ({ isWaiting, answer, createdAt, isQna = false }) => {
  return (
    <div className="qns">
      <div className="left">{!isQna && <AnswerIcon />}</div>
      <div className="right">
        {!isQna && <span className="head">Innerloop Chat bot</span>}
        {isWaiting ? (
          <LoadingIndicator />
        ) : (
          <>
            <div className="response cont">
              <span
                dangerouslySetInnerHTML={{ __html: marked.parse(answer) }}
              />
            </div>
            <div className="d-flex flex-row align-items-center justify-content-between mt-2">
              <CopyIcon
                dataToCopy={answer}
                height={15}
                copyToClipboardOnClick
                color="#ABABAB"
                width={15}
              />
              {createdAt && (
                <span style={{ fontSize: 12 }}>
                  {formatDateNTime(new Date(createdAt))}
                </span>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default Messages;
