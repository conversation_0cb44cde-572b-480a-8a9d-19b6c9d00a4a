import Copy from 'components/copy';
import IntlMessages from 'helpers/IntlMessages';
import React from 'react';

function KeyValue({ name, value, isUrl }) {
  if (value) {
    return (
      <div className="d-flex flex-row w-full mb-2" style={{ fontSize: 14 }}>
        <div className="font-bolder" style={{ width: '40%' }}>
          <IntlMessages id={name} />
        </div>
        <div style={{ width: '60%' }}>
          {isUrl ? (
            <div className="w-full d-flex flex-row">
              <div style={{ width: '85%' }} className="text-ellipsis">
                {value}
              </div>
              <div style={{ width: '15%' }}>
                <Copy height={14} width={14} copyText={value} />
              </div>
            </div>
          ) : (
            <p style={{ overflowWrap: 'break-word' }}>
              <IntlMessages id={value} />
            </p>
          )}
        </div>
      </div>
    );
  }
  return null;
}

export default KeyValue;
