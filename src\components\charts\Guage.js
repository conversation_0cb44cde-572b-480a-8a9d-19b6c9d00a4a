import React from 'react';
// import { Card } from 'reactstrap';
import ReactSpeedometer from 'react-d3-speedometer';
import { ThemeColors } from 'helpers/ThemeColors';

const GuageChart = ({ value = 20 }) => {
  const colors = ThemeColors();

  return (
    <div className="pt-5 impression-scale-container">
      <ReactSpeedometer
        needleHeightRatio={0.8}
        maxValue={100}
        value={value}
        currentValueText="Total Content Impressions Till Now"
        customSegmentLabels={[
          {
            text: 'Poor',
            position: 'INSIDE',
            color: colors.primaryColor,
          },
          {
            text: 'Fair',
            position: 'INSIDE',
            color: colors.primaryColor,
          },
          {
            text: 'Good',
            position: 'INSIDE',
            color: colors.primaryColor,
          },
          {
            text: 'Great',
            position: 'INSIDE',
            color: colors.primaryColor,
          },
          {
            text: 'Excellent',
            position: 'INSIDE',
            color: colors.primaryColor,
          },
        ]}
        ringWidth={45}
        needleTransitionDuration={3333}
        needleTransition="easeElastic"
        needleColor={colors.primaryColor}
        textColor={colors.primaryColor}
      />
    </div>
  );
};

export default GuageChart;
