/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-vars */
import React, { useEffect, useMemo, useState } from 'react';
import SessionList from './SessionList';
import ChartSection from './ChartSection';
import { Modal, ModalBody, Row } from 'reactstrap';
import BackButton from 'components/buttons/BackButton';
import IntlMessages from 'helpers/IntlMessages';
import InfoMessage from 'components/infoMessage';
import { getChatAnalytics } from 'functions/api/analyticsApi';
import { CHAT_ANALYTICS_TYPE } from 'constants/defaultValues';
import { ChatAnalyticsLoading } from './loading';
import SessionDetail from './SessionList/SessionDetail';
import InfoPopover from 'components/InfoPopover';

const initialState = {
  date: {
    sessions: [],
    counts: [],
    lastUpdated: '',
    isFetched: false
  },
  hour: {
    sessions: [],
    counts: [],
    lastUpdated: '',
    isFetched: false
  },
  month: {
    sessions: [],
    counts: [],
    lastUpdated: '',
    isFetched: false
  }
};

const ChatAnalytics = ({ open, setOpen, contentId, isQna }) => {
  const [openedSession, setOpenedSession] = useState(null);
  const [activeGraph, setActiveGraph] = useState(CHAT_ANALYTICS_TYPE.DATE);
  const [isLoading, setIsLoading] = useState(false);
  const [allData, setAllData] = useState(initialState);

  const toggle = () => setOpen((prev) => !prev);

  useEffect(() => {
    const isDateTabSelected = CHAT_ANALYTICS_TYPE.DATE === activeGraph;
    const isMonthTabSelected = CHAT_ANALYTICS_TYPE.MONTH === activeGraph;
    const isHourTabSelected = CHAT_ANALYTICS_TYPE.HOUR === activeGraph;

    const getFlattenedSessions = (sessions) => {
      if (!isQna) {
        return sessions;
      }
      if (!sessions) {
        return [];
      }
      const list = [];
      // eslint-disable-next-line array-callback-return
      sessions?.map((m) => {
        // eslint-disable-next-line array-callback-return
        m?.event_details.map((j, i) => {
          list.push({
            ...m,
            event_details: [j]
          });
        });
      });
      return list;
    };

    const fetchData = async () => {
      try {
        setIsLoading(true);
        const { data, isError } = await getChatAnalytics({
          type: activeGraph,
          contentId,
          isQna
        });
        if (isError) {
          setAllData(initialState);
        } else {
          const storeData = () => {
            return {
              sessions: getFlattenedSessions(data?.sessions) ?? [],
              counts: data?.analytics ?? [],
              lastUpdated: data?.lastUpdated,
              isFetched: true
            };
          };
          const newData = (() => {
            if (CHAT_ANALYTICS_TYPE.DATE === activeGraph) {
              return {
                ...allData,
                date: storeData()
              };
            }
            if (CHAT_ANALYTICS_TYPE.HOUR === activeGraph) {
              return {
                ...allData,
                hour: storeData()
              };
            }
            return {
              ...allData,
              month: storeData()
            };
          })();
          setAllData(newData);
        }
        setIsLoading(false);
      } catch (error) {
        setIsLoading(false);
        setAllData(null);
      }
    };

    const fetchIt = (() => {
      if (isDateTabSelected) {
        return !allData.date.isFetched;
      }
      if (isMonthTabSelected) {
        return !allData.month.isFetched;
      }
      return !allData.hour.isFetched;
    })();

    if (contentId && fetchIt) {
      fetchData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeGraph, contentId, allData, isQna]);

  useEffect(() => {
    if (open) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [open]);

  return (
    <Modal
      isOpen={open}
      toggle={toggle}
      contentClassName="border-radius-10"
      centered
      style={{
        minWidth: '70%'
      }}
      backdrop="static"
    >
      <ModalBody
        className="m-3 border-radius-10 d-flex align-items-center flex-column p-3"
        style={{ border: '1px solid #992288', height: '90vh' }}
      >
        <Row className="d-flex m-0 align-items-start justify-content-between w-full">
          <Row className="d-flex m-0 align-items-center justify-content-start w-95">
            {openedSession && (
              <BackButton onClick={() => setOpenedSession(null)} />
            )}
            <h2 className="mb-0 font-bolder">
              No. of sessions ({isQna ? 'Questions' : 'Chats'} )
            </h2>
          </Row>
          <span
            onClick={toggle}
            className="w-5 d-flex flex-row align-items-center justify-content-end"
          >
            <i className="simple-icon-close close-btn" />
          </span>
        </Row>

        {!openedSession && (
          <GraphChanger active={activeGraph} setActive={setActiveGraph} />
        )}

        {!openedSession ? (
          <>
            {isLoading ? (
              <ChatAnalyticsLoading />
            ) : (
              <div className="ChatAnalytics">
                <ChartSection active={activeGraph} data={allData} />
                <SessionList
                  active={activeGraph}
                  data={allData}
                  setOpenedSession={setOpenedSession}
                  isQna={isQna}
                />
              </div>
            )}
          </>
        ) : (
          <SessionDetail data={openedSession} isQna={isQna} />
        )}
      </ModalBody>
    </Modal>
  );
};

const GraphChanger = ({ active, setActive }) => {
  const message = useMemo(() => {
    if (active === CHAT_ANALYTICS_TYPE.DATE) {
      return 'Analytics for last 30 days';
    }
    if (active === CHAT_ANALYTICS_TYPE.HOUR) {
      return 'Analytics for last 7 days';
    }
    return 'Analytics for last 12 month';
  }, [active]);

  const id = 'top-info-schat-ana';

  return (
    <>
      <div
        className="d-flex flex-row w-full align-items-center justify-content-center"
        style={{ fontSize: '14px' }}
      >
        <Tab
          isLeft
          text="By Date"
          isActive={active === CHAT_ANALYTICS_TYPE.DATE}
          onClick={() => setActive(CHAT_ANALYTICS_TYPE.DATE)}
        />
        <Tab
          text="By Hour"
          isActive={active === CHAT_ANALYTICS_TYPE.HOUR}
          onClick={() => setActive(CHAT_ANALYTICS_TYPE.HOUR)}
        />
        <Tab
          isRight
          text="By Months"
          isActive={active === CHAT_ANALYTICS_TYPE.MONTH}
          onClick={() => setActive(CHAT_ANALYTICS_TYPE.MONTH)}
        />
      </div>
      <div className="d-flex flex-row align-items-center justify-content-center">
        <InfoPopover
          infoId={id}
          style={{
            fontSize: 14
          }}
          iconClassName="text-primary"
          message="Analytics are generated based on interactions from the shared and showcase views only"
          className="mr-2"
        />
        <InfoMessage
          showInfoIcon={false}
          className="mt-1"
          style={{ fontSize: 14 }}
          message={message}
          textAlign="center"
        />
      </div>
    </>
  );
};

const Tab = ({ onClick, isActive, text, isLeft = false, isRight = false }) => {
  return (
    <div
      className="text-center c-pointer p-2"
      style={{
        width: '100px',
        borderTopRightRadius: isRight ? '50px' : '',
        borderBottomRightRadius: isRight ? '50px' : '',
        borderTopLeftRadius: isLeft ? '50px' : '',
        borderBottomLeftRadius: isLeft ? '50px' : '',
        border: '1px solid #992288',
        borderLeft: 'none',
        background: isActive && '#99228847'
      }}
      onClick={onClick}
    >
      <IntlMessages id={text} />
    </div>
  );
};

export default ChatAnalytics;
