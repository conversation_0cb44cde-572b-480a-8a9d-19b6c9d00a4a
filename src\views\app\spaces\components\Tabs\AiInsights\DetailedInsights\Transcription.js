/* eslint-disable react/no-array-index-key */
/* eslint-disable no-unused-vars */
/* eslint-disable no-use-before-define */
import { Colxx } from 'components/common/CustomBootstrap';
import IntlMessages from 'helpers/IntlMessages';
import React, { useEffect, useMemo, useState } from 'react';
import { Button, FormGroup, Modal, ModalBody, NavLink, Row } from 'reactstrap';
import { Formik, Form, Field } from 'formik';
import {
  arrayToWebVTT,
  isDarkModeActive,
  parseDurationToSeconds,
  returnFileDuration
} from 'helpers/Utils';
import { updateAutoGeneratedMetaDeta } from 'functions/api/spacesApi';
import { NotificationManager } from 'components/common/react-notifications';
import AiButton from 'components/buttons/AiButton';
import {
  handleSieveOperations,
  refreshTranscribe
} from 'functions/api/analyze';
import RefreshButton from 'components/buttons/RefreshButton';
import Switch from 'rc-switch';
import InfoIcon from 'components/icon/InfoIcon';
import InfoMessage from 'components/infoMessage';
import Loader from 'react-loader-spinner';

const Transcription = ({
  makePlayingExperienceTabActive,
  handlePlayerChange,
  content,
  selectedTenantId,
  getContent,
  setContent,
  setIsTranscribing,
  isTranscribing,
  roleType,
  isRefreshingTranscribing,
  setIsRefreshingTranscribing,
  isTranscriptionChecked,
  setIsTranscriptionCheked
}) => {
  const [showWarning, setShowWarning] = useState(false);
  const [showAnalyzeModal, setShowAnalyzeModal] = useState(false);
  const [deNoise, setDeNoise] = useState(true);
  const [showRefreshButton, setShowRefreshButton] = useState(false);

  const handleRefresh = async (isEffect = false) => {
    try {
      if (isEffect) {
        setIsTranscribing(true);
      }
      setIsRefreshingTranscribing(true);
      const { isError, data } = await refreshTranscribe(content.id);
      console.log('handleRefresh', data, isError);
      if (data === 'UPDATED_VALUES') {
        await getContent(true);
        setTimeout(() => {
          setIsTranscribing(false);
        }, 100);
      }
      if (data === 'INPROCESSING') {
        NotificationManager.error(<IntlMessages id="transcribing-process" />);
        setContent({
          ...content,
          autoTranscribeStatus: 2,
          autoTranscribeError: ''
        });
      }
      if (data === 'ERROR_WHILE_PROCESSING') {
        NotificationManager.error(<IntlMessages id="refresh-error" />);
        setIsTranscribing(false);
        setContent({ ...content, autoTranscribeStatus: 4 });
      }
      if (data === 'NEVER_TRIGGERED') {
        setIsTranscribing(false);
      }
      if (isError) {
        NotificationManager.error(<IntlMessages id="refresh-error" />);
        setIsTranscribing(false);
        setContent({ ...content, autoTranscribeStatus: 4 });
      }
      setIsRefreshingTranscribing(false);
    } catch (error) {
      NotificationManager.error(<IntlMessages id="refresh-catch" />);
      setIsRefreshingTranscribing(false);
      if (isEffect) {
        setIsTranscribing(false);
      }
    }
  };

  const handleTriggerTranscribe = async () => {
    try {
      setShowRefreshButton(false);
      setIsTranscribing(true);
      const res = await handleSieveOperations(
        {
          contentId: content.id,
          aiOperationType: 1,
          removeNoise: deNoise,
          mediaDuration: content.duration
        },
        content.spaceId
      );
      console.log('res', res);

      if (typeof res === 'string') {
        setIsTranscribing(false);
        setContent({
          ...content,
          autoTranscribeStatus: 0,
          autoTranscribeError: ''
        });
        return;
      }

      if (
        res?.data?.data === 'UPDATED_VALUES' ||
        res.data === 'UPDATED_VALUES'
      ) {
        await getContent(true);
        setTimeout(() => {
          setIsTranscribing(false);
        }, 100);
        return;
      }
      if (res.data === 'INPROCESSING') {
        NotificationManager.error(<IntlMessages id="transcribing-process" />);
        setShowRefreshButton(true);
        return;
      }
      if (res?.isError) {
        NotificationManager.error(
          <IntlMessages id="content-media-analyze" />,
          <IntlMessages id="req.failed" />
        );
        setIsTranscribing(false);
      } else {
        setContent({
          ...content,
          autoTranscribeStatus: 2,
          autoTranscribeError: ''
        });
      }
      setTimeout(() => {
        setShowRefreshButton(true);
      }, 10000);
    } catch (error) {
      NotificationManager.error(
        <IntlMessages id="content-media-analyze" />,
        <IntlMessages id="req.failed" />
      );
      setIsTranscribing(false);
    }
  };

  useEffect(() => {
    if (!isTranscriptionChecked) {
      handleRefresh(true);
      setIsTranscriptionCheked(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isTranscriptionChecked]);

  return (
    <>
      <div className="p-4 cardborder" style={{ borderRadius: 20 }}>
        <Row className="m-0 d-flex flex-row w-full align-items-center justify-content-between">
          <p className="font-bolder mb-0 w-20" style={{ fontSize: 16 }}>
            AI Transcription
          </p>
          <Row className="m-0 p-0 gap-1 w-80 justify-content-end">
            {isTranscribing && showRefreshButton && (
              <RefreshButton
                isText
                isLoading={isRefreshingTranscribing}
                text={isRefreshingTranscribing ? 'Checking' : 'Check Status'}
                handleRefresh={handleRefresh}
              />
            )}
            <AiButton
              text="Transcribe"
              isLoading={isTranscribing}
              disabled={isTranscribing}
              onClick={() => {
                if (content?.chapters?.length > 0) {
                  setShowWarning(true);
                }
                setShowAnalyzeModal(true);
              }}
            />
          </Row>
        </Row>

        {isTranscribing && (
          <div
            className="w-full centered d-flex flex-row font-bolder"
            color="#787878"
          >
            <IntlMessages
              id={
                content?.chapters?.length > 0
                  ? 'analyzing-text-update'
                  : 'analyzing-text'
              }
            />
            <Loader
              className="ml-3"
              type="TailSpin"
              color="#787878"
              height={15}
              width={15}
            />
          </div>
        )}

        {content?.chapters?.length > 0 && !isTranscribing && (
          <Colxx className="m-0 p-0">
            <NavLink
              to="tabIndex=3"
              className=" text-primary p-0 py-4 c-pointer"
              onClick={(event) => {
                event.preventDefault();
                makePlayingExperienceTabActive();
                const focusingTab = document.getElementById('playing-exp-tab');
                if (focusingTab) {
                  focusingTab.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'nearest'
                  });
                  focusingTab.style.outline = 'none';
                  focusingTab.style.border = '2px solid #992288';
                  setTimeout(() => {
                    focusingTab.style.border = 'none';
                  }, 2000);
                }
              }}
            >
              <IntlMessages id="sub-closed" />
              <i className="simple-icon-link ml-2" />
            </NavLink>

            <Chapters
              handlePlayerChange={handlePlayerChange}
              content={content}
              selectedTenantId={selectedTenantId}
            />
          </Colxx>
        )}

        {content?.autoTranscribeError === 'VIDEO_DOESNT_HAVE_AUDIO' &&
          !isTranscribing && (
            <div className="w-full centered">
              <div className="mt-2 novtt">
                <i className="simple-icon-info mr-2" />
                <IntlMessages id="vtt-empty" />
              </div>
            </div>
          )}
      </div>
      {showAnalyzeModal && (
        <TranscribeModal
          open={showAnalyzeModal}
          showWarning={showWarning}
          setDeNoise={setDeNoise}
          deNoise={deNoise}
          handleConfirm={() => {
            handleTriggerTranscribe();
            setShowAnalyzeModal(false);
            setShowWarning(false);
          }}
          toggle={() => {
            setShowAnalyzeModal(false);
            setShowWarning(false);
          }}
        />
      )}
    </>
  );
};

const Chapters = ({ content, selectedTenantId, handlePlayerChange }) => {
  const [isEdit, setIsEdit] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [chaptersData, setChaptersData] = useState(
    content?.chapters?.map((m) => {
      return {
        ...m,
        timecode: returnFileDuration(m.startTime).durationInText
      };
    }) ?? []
  );
  const [isButtonDisabled, setIsButtonDisabled] = useState(false);
  const contentDuration = content.duration;
  const isDarkMode = isDarkModeActive();

  const handleSave = async () => {
    try {
      setIsUpdating(true);
      const { data, isError } = await updateAutoGeneratedMetaDeta({
        tenantId: selectedTenantId,
        contentId: content.id,
        chapters_vtt: arrayToWebVTT(chaptersData, contentDuration),
        chapters: chaptersData,
        tags_auto: content?.metadata?.tags_auto,
        title_auto: content?.metadata?.title_auto,
        summary_auto: content?.metadata?.summary_auto,
        aiOperationType: 1
      });
      if (!isError) {
        content.chapters = chaptersData;
        NotificationManager.success(
          <IntlMessages id="metadata-update-success" />
        );
        handlePlayerChange();
        setIsEdit(false);
      } else {
        NotificationManager.error(<IntlMessages id="metadata-update-error" />);
      }
      setIsUpdating(false);
    } catch (error) {
      setIsUpdating(false);
      console.log('error', error);
    }
  };

  useEffect(() => {
    const isTitleError = chaptersData.some(
      (f) => validateChapterTitle(f.title) !== ''
    );
    const isTimeCodeError = chaptersData.some(
      (f) =>
        validateTimeCode(
          parseDurationToSeconds(f.timecode),
          f.timecode,
          contentDuration
        ) !== ''
    );
    console.log({
      isTitleError,
      isTimeCodeError
    });
    setIsButtonDisabled(isTitleError || isTimeCodeError);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [chaptersData]);

  useEffect(() => {
    setChaptersData(
      content?.chapters?.map((m) => {
        return {
          ...m,
          timecode: returnFileDuration(m.startTime).durationInText
        };
      }) ?? []
    );
  }, [content?.chapters]);

  return (
    <div className="w-full" aria-disabled={isUpdating}>
      <div className="d-flex flex-row align-items-center justify-content-between mb-3">
        <span style={{ fontSize: 16 }}>Chapters</span>
        <>
          {isEdit ? (
            <Button
              outline
              className="p-2 px-3"
              color="primary"
              style={{ fontSize: 14, textDecoration: 'none' }}
              aria-disabled={isButtonDisabled}
              onClick={() => {
                setChaptersData([
                  ...chaptersData,
                  {
                    title: '',
                    startTime: contentDuration - 20,
                    timecode: returnFileDuration(contentDuration).durationInText
                  }
                ]);
              }}
            >
              <i className="simple-icon-plus mr-2" />
              Add New Chapters
            </Button>
          ) : (
            <Button
              outline
              color="primary p-1 px-3"
              style={{ fontSize: 14 }}
              onClick={() => setIsEdit(!isEdit)}
            >
              <i className="simple-icon-pencil mr-2" />
              Edit
            </Button>
          )}
        </>
      </div>
      {chaptersData?.map((m, i) => (
        <ChapterCard
          chapter={m}
          index={i}
          key={i}
          isDarkMode={isDarkMode}
          isEdit={isEdit}
          chaptersData={chaptersData}
          setChaptersData={setChaptersData}
          isButtonDisabled={isButtonDisabled}
          setIsButtonDisabled={setIsButtonDisabled}
          contentDuration={contentDuration}
        />
      ))}
      {isEdit && (
        <div className="centered flex-row gap-1 mt-4">
          <Button
            outline
            color="primary"
            style={{ fontSize: 14, width: 130 }}
            onClick={() => {
              setIsEdit(!isEdit);
              setChaptersData(content?.chapters);
            }}
          >
            Cancel
          </Button>
          <Button
            disabled={isButtonDisabled || chaptersData?.length === 0}
            color="primary"
            style={{ fontSize: 14, width: 130 }}
            onClick={handleSave}
          >
            Save
          </Button>
        </div>
      )}
    </div>
  );
};

const ChapterCard = ({
  index,
  isEdit,
  chapter,
  chaptersData,
  setChaptersData,
  setIsButtonDisabled,
  isButtonDisabled,
  contentDuration,
  isDarkMode
}) => {
  const timeCodeError = useMemo(() => {
    const duplicateTitle =
      chaptersData.filter((f) => f.timecode === chapter.timecode).length > 1;
    if (duplicateTitle) {
      return 'Timecode cannot be same';
    }
    return validateTimeCode(
      parseDurationToSeconds(chapter.timecode),
      chapter.timecode,
      contentDuration
    );
  }, [chapter.timecode, chaptersData, contentDuration]);

  const titleError = useMemo(() => {
    if (!chapter.title) {
      return 'Chapter title cannot be empty';
    }
    const duplicateTitle =
      chaptersData.filter((f) => f.title === chapter.title).length > 1;
    if (duplicateTitle) {
      return 'Duplicate title';
    }
    if (chapter.title.length < 3) {
      return 'Chapter title is too small';
    }
    return '';
  }, [chapter.title, chaptersData]);

  // useEffect(() => {
  //   if (!!timeCodeError || !!titleError) {
  //     setIsButtonDisabled(true);
  //   } else {
  //     setIsButtonDisabled(false);
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [timeCodeError, titleError]);

  const inputBgColor = (() => {
    if (isDarkMode) {
      return !isEdit ? '#242224' : '#1d1a1d';
    }
    return isEdit ? '#FFF5FE' : '#FFF5FE';
  })();

  return (
    <Formik initialValues={{ time: '', title: '' }}>
      <Form
        className="av-tooltip tooltip-label-bottom chapterCard mb-2 cardborder"
        style={{ borderRadius: 2 }}
      >
        <div className="w-7 centered text-center" style={{ height: 50 }}>
          {index + 1}
        </div>
        <div className="w-85 mid-cont">
          <div className="top centered" style={{ height: 50 }}>
            <div className="d-flex flex-row p-2 h-full w-full">
              <FormGroup className="form-group has-float-label w-full">
                <Field
                  className="form-control h-full px-2"
                  type="text"
                  name="time"
                  disabled={!isEdit}
                  style={{
                    background: inputBgColor,
                    border: 'none',
                    fontSize: 13
                  }}
                  value={chapter.timecode}
                  onChange={(e) => {
                    const val = e.target?.value;
                    console.log('val', val);
                    const updatedValue = chaptersData.map((m, i) => {
                      if (i === index) {
                        return {
                          ...m,
                          timecode: val,
                          startTime:
                            val?.length > 4 ? parseDurationToSeconds(val) : 0
                        };
                      }
                      return m;
                    });
                    setChaptersData(updatedValue);
                  }}
                />
                {timeCodeError && isEdit && (
                  <div className="invalid-feedback d-block">
                    {timeCodeError}
                  </div>
                )}
              </FormGroup>
              <span
                className="centered text-primary h-full c-pointer"
                style={{
                  width: 30,
                  border: isEdit ? '1px solid #992288' : '',
                  fontSize: 13
                }}
              >
                <i className="simple-icon-clock" />
              </span>
            </div>
          </div>
          <div
            className="bottom centered text-align-start p-1"
            style={{ height: 50 }}
          >
            <FormGroup className="form-group has-float-label w-full mb-0">
              <Field
                className="form-control px-2 h-full w-full"
                type="text"
                name="time"
                disabled={!isEdit}
                style={{
                  fontSize: 13,
                  background: inputBgColor,
                  border: 'none'
                }}
                value={chapter.title}
                onChange={(e) => {
                  const val = e.target.value;
                  const updatedValue = chaptersData.map((m, i) => {
                    if (i === index) {
                      return {
                        ...m,
                        title: val
                      };
                    }
                    return m;
                  });
                  setChaptersData(updatedValue);
                }}
              />
              {titleError && isEdit && (
                <div className="invalid-feedback d-block">{titleError}</div>
              )}
            </FormGroup>
          </div>
        </div>
        <div
          className="w-8 centered text-primary"
          style={{ height: 50, fontSize: 14 }}
        >
          {isEdit && (
            <span
              className="c-pointer"
              onClick={() => {
                setChaptersData(
                  chaptersData.filter((f) => f.title !== chapter?.title)
                );
              }}
            >
              <i className="simple-icon-trash" />
            </span>
          )}
        </div>
      </Form>
    </Formik>
  );
};

const validateTimeCode = (seconds, timeCode, contentTimeInSeconds) => {
  const regex = /^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(\.\d{1,4})?$/;
  if (!timeCode) {
    return 'Timecode cannot be empty';
  }
  console.log({
    seconds,
    timeCode,
    contentTimeInSeconds,
    condition: +seconds >= +contentTimeInSeconds
  });
  if (+seconds >= +contentTimeInSeconds) {
    return 'Timecode cannot be greater than contents length';
  }
  if (regex.test(timeCode)) {
    return '';
  }
  return "Time format is invalid. Please use the format '00:00:00.00'.";
};

const validateChapterTitle = (title) => {
  if (!title) {
    return 'Chapter title cannot be empty';
  }
  if (title.length < 3) {
    return 'Chapter title is too small';
  }
  return '';
};

const TranscribeModal = ({
  open,
  toggle,
  handleConfirm,
  deNoise,
  setDeNoise,
  showWarning
}) => {
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && open) {
        toggle();
      }
    };
    window.addEventListener('keydown', handleEscape);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  return (
    <Modal
      isOpen
      toggle={toggle}
      centered
      style={{
        minWidth: '40%'
      }}
    >
      <ModalBody>
        <div className="w-full d-flex flex-row align-items-center justify-content-end">
          <i
            className="simple-icon-close close-btn cursor-pointer"
            onClick={toggle}
          />
        </div>
        <InfoMessage
          style={{ fontSize: 14 }}
          className="mb-2"
          textAlign="center"
          message="ad-media-enrichment-info-voice"
        />
        {showWarning && (
          <InfoMessage
            style={{ fontSize: 14 }}
            textAlign="center"
            className="mb-2"
            message="ai.retrigger"
          />
        )}

        <div className="d-flex flex-row align-items-center justify-content-center gap-2 mb-2">
          <span style={{ fontSize: 14 }}>
            <InfoIcon
              name="RemoveNoise"
              message="ad-rm-noise-info"
              iconStyle={{ fontSize: 14 }}
            />
            <IntlMessages id="ad-rm-noise" />
          </span>
          <Switch
            className="custom-switch custom-switch-primary custom-switch-small ml-3"
            checked={deNoise}
            onClick={() => {
              setDeNoise(!deNoise);
            }}
          />
        </div>
        <div className="w-full d-flex flex-row align-items-center justify-content-center gap-2 mt-3">
          <Button
            color="primary"
            onClick={() => {
              toggle();
              handleConfirm();
            }}
          >
            Yes
          </Button>
          <Button color className="badge-outline-primary" onClick={toggle}>
            No
          </Button>
        </div>
      </ModalBody>
    </Modal>
  );
};

export default Transcription;
