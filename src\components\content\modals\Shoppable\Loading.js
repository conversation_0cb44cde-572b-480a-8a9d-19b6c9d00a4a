import LoadingSkeleton from 'components/LoadingSkeleton';
import React from 'react';

const Loading = () => {
  return (
    <div className="p-4 w-full h-full">
      <div className="d-flex flex-row w-full mb-2">
        <div className="d-flex flex-column w-40">
          <LoadingSkeleton
            style={{ height: '200px', width: '100%' }}
            containerClassName="d-flex align-items-center"
          />
        </div>
        <div className="d-flex flex-column w-60 gap-2 px-2">
          <LoadingSkeleton
            style={{
              height: '50px',
              width: '100%'
            }}
          />
          <LoadingSkeleton
            style={{
              height: '50px',
              width: '100%'
            }}
          />
          <LoadingSkeleton
            style={{
              height: '50px',
              width: '100%'
            }}
          />
        </div>
      </div>
      <LoadingSkeleton
        style={{ width: '100%', height: '20px' }}
        containerClassName="d-flex align-items-center justify-content-center mb-2"
      />
      <LoadingSkeleton
        style={{ width: '100%', height: '100px' }}
        containerClassName="d-flex align-items-center justify-content-center h-85 mb-2"
      />
      <LoadingSkeleton
        style={{ width: '100%', height: '20px' }}
        containerClassName="d-flex align-items-center justify-content-center mb-2"
      />
      <LoadingSkeleton
        style={{ width: '100%', height: '100px' }}
        containerClassName="d-flex align-items-center justify-content-center h-85 mb-2"
      />
      <LoadingSkeleton
        style={{ width: '100%', height: '20px' }}
        containerClassName="d-flex align-items-center justify-content-center mb-2"
      />
      <LoadingSkeleton
        style={{ width: '100%', height: '100px' }}
        containerClassName="d-flex align-items-center justify-content-center h-85 mb-2"
      />

      <div className="d-flex flex-row w-full align-items-center gap-1 justify-content-center">
        <LoadingSkeleton
          style={{ height: '50px', borderRadius: 40 }}
          containerClassName="d-flex align-items-center w-20"
        />
        <LoadingSkeleton
          style={{ height: '50px', borderRadius: 40 }}
          containerClassName="d-flex align-items-center w-20"
        />
      </div>
    </div>
  );
};

export default Loading;
