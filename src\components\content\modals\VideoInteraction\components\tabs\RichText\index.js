/* eslint-disable no-unused-vars */
/* eslint-disable jsx-a11y/label-has-associated-control */
import React, { useEffect } from 'react';
import PlayerPreview from '../../PlayerPreview';
import {
  INTERACTION_TYPE,
  PLAYER_PREVIEW_TYPE
} from 'constants/VideoInteraction';
import TimeControl from '../../TimeControl';
import ReactQuill from 'react-quill';
import SectionHeader from '../../SectionHeader';
import InfoMessage from 'components/infoMessage';
import 'react-quill/dist/quill.snow.css';
import { getPlainText } from 'helpers/Utils';

const RichTextTab = ({
  isParticular,
  text,
  setText,
  position,
  setPosition,
  startTime,
  setStartTime,
  endTime,
  setEndTime,
  pauseVideo,
  setPauseVideo,
  interactiondetails,

  setBgColor,
  bgColor,
  isTransparent,
  setIsTransparent
}) => {
  const modules = {
    toolbar: [
      [{ header: [1, 2, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [
        { list: 'ordered' },
        { list: 'bullet' },
        { indent: '-1' },
        { indent: '+1' }
      ],
      ['link'],
      ['clean'],
      [{ color: [] }, { background: [] }]
    ]
  };

  const formats = [
    'header',
    'bold',
    'italic',
    'underline',
    'strike',
    'list',
    'bullet',
    'link',
    'color',
    'background'
  ];

  useEffect(() => {
    if (
      interactiondetails &&
      interactiondetails?.type === INTERACTION_TYPE.RICH_TEXT
    ) {
      setPosition(+interactiondetails.data.position);
      setPauseVideo(interactiondetails?.pauseOnShow);
      setEndTime(interactiondetails?.endAtTimecode);
      setStartTime(interactiondetails?.showAtTimecode);
      setText(interactiondetails.data.title);
      setIsTransparent(interactiondetails.isTransparent);
      setBgColor(interactiondetails.bgColor);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [interactiondetails]);

  return (
    <>
      <div className="richText">
        {isParticular && (
          <TimeControl
            style={{ marginTop: '2rem' }}
            startTime={startTime}
            setStartTime={setStartTime}
            endTime={endTime}
            setEndTime={setEndTime}
            pauseVideo={pauseVideo}
            setPauseVideo={setPauseVideo}
          />
        )}
        <div className="overlay-txt">
          <SectionHeader>
            Overlay text <span style={{ color: 'red' }}>*</span>
          </SectionHeader>
          <ReactQuill
            formats={formats}
            modules={modules}
            theme="snow"
            value={text}
            onChange={(e) => {
              const plainText = getPlainText(e);
              if (plainText.length > 100) {
                const truncatedText = plainText.substring(0, 100);
                setText(truncatedText);
              } else {
                setText(e);
              }
            }}
          />
          <InfoMessage message="Max character Limit: 100" />
        </div>
        <div className="d-flex flex-row align-items-center justify-content-start">
          <input
            style={{
              borderWidth: 2,
              accentColor: '#992288',
              transform: 'scale(1.25)',
              marginRight: '8px'
            }}
            size={20}
            aria-label="Checkbox for following text input"
            type="checkbox"
            id="pause-duration"
            checked={isTransparent}
            onChange={(e) => {
              setIsTransparent(e.target.checked);
            }}
          />
          <label
            name="pause-duration"
            htmlFor="pause-duration"
            className="mb-0 font-bolder"
          >
            Make background transparent
          </label>
        </div>
        {!isTransparent && (
          <div className="d-flex flex-row align-items-center justify-content-start mt-4">
            <input
              style={{
                borderWidth: 2,
                accentColor: '#992288',
                transform: 'scale(1.25)',
                marginRight: '8px'
              }}
              size={20}
              type="color"
              id="bg-color"
              value={bgColor}
              onChange={(e) => {
                setBgColor(e.target.value);
              }}
            />
            <label
              name="bg-color"
              htmlFor="bg-color"
              className="mb-0 font-bolder ml-2"
            >
              Background color
            </label>
          </div>
        )}
        <PlayerPreview
          type={PLAYER_PREVIEW_TYPE.RICH_TEXT}
          setPosition={setPosition}
          position={position}
          text={text}
          bgColor={bgColor}
          isTransparent={isTransparent}
        />
      </div>
    </>
  );
};

export default RichTextTab;
