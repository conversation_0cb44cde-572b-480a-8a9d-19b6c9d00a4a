import React from 'react';
import { FormGroup, Input, Label } from 'reactstrap';

const FormInput = ({
  label,
  isImportant = false,
  type,
  placeholder = '',
  onChangeText,
  errorMessage,
  value,
  className = '',
  style = {}
}) => {
  return (
    <FormGroup
      className={`form-group has-float-label my-4 ${className}`}
      style={{ flex: 1, width: '100%', ...style }}
    >
      <Label for="Industry">
        {label}
        {isImportant && <span style={{ color: '#922c88' }}> *</span>}
      </Label>
      <Input
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={(e) => {
          const textValue = e?.target?.value;
          onChangeText(textValue);
        }}
      />
      {errorMessage && (
        <div className="invalid-feedback d-block">{errorMessage}</div>
      )}
    </FormGroup>
  );
};

export default FormInput;
