import React from 'react';

const SectionHeader = ({ children, number, className = '' }) => {
  return (
    <div
      className={`d-flex flex-row w-full align-items-center gap-2 mb-2 ${className}`}
    >
      <span
        className="centered"
        style={{
          height: 30,
          width: 30,
          borderRadius: 15,
          border: '1px solid #992288',
          textAlign: 'center',
          fontSize: 14,
          color: '#992288'
        }}
      >
        {number}
      </span>
      {children}
    </div>
  );
};

export default SectionHeader;
