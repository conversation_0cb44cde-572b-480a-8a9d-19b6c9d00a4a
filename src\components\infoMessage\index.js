import IntlMessages from 'helpers/IntlMessages';
import React from 'react';

const InfoMessage = ({
  color = '#992288',
  className,
  message,
  textAlign = 'right',
  style = {},
  showInfoIcon = true
}) => {
  return (
    <p
      style={{ fontSize: 12, color, ...style }}
      className={`text-align-${textAlign} mb-0 ${className}`}
    >
      {showInfoIcon && <i className="simple-icon-info mr-1" />}
      <IntlMessages id={message} />
    </p>
  );
};

export default InfoMessage;
