/* eslint-disable consistent-return */
/* eslint-disable no-else-return */
/* eslint-disable jsx-a11y/no-static-element-interactions */
// import { handleSearchPlayers } from 'functions/api';
import React, { useEffect, useState } from 'react';
import ListPageHeading from './ListPageHeading';
import ListPageListing from './ListPageListing';
import FlagModal from './modals/FlagModal';
import ReviewModal from './modals/ReviewModal';
import ShareModal from './modals/Share';
import UploadModal from './upload/uploadModal';
import { RoleTypes, isYoutubeORVimeoSource } from 'helpers/Utils';
import { useSelector } from 'react-redux';
import useWindowDimensions from 'hooks/useWindowDimensions';

const ContentManager = ({
  display,
  content,
  setContent,
  showMediaProcessing,
  isImageModal,
  title,
  setContentId,
  spaceId,
  acceptedMediaType,
  MediaType,
  icon,
  fetchContents,
  reload,
  contentsLoading,
  setContentsLoading,
  query,
  setQuery,
  hasMoreContents,
  filter,
  setFilter,
  isStreamingModal,
  setIsStreamingModal,
  isContentManagement,
  modalOpen,
  setModalOpen,
  uploading,
  setUploading,
  roleType,
  activeMediaTab,
  handleRefresh,
  subscriptionDetails,
  previewCache,
  allContent,
  setAllContent,

  selectedStatus,
  setSelectedStatus,
  isShowcaseSelected,
  setIsShowcaseSelected,
  isSharedSelected,
  setIsSharedSelected,
  isFlaggedSelected,
  setIsFlaggedSelected,
  selectedReview,
  setSelectedReview,
  isStreamingSelected,
  setIsStreamingSelected
}) => {
  const [displayMode, setDisplayMode] = useState(
    sessionStorage.getItem('displayMode') || 'thumblist'
  );
  const [watermarkModalOpen, setWatermarkModalOpen] = useState(false);
  const [contentIdForWatermark, setContentIdForWatermark] = useState('');
  const [shareModalOpen, setShareModalOpen] = useState(false);
  const [entity, setEntity] = useState(null);
  const [shareChanged, setShareChanged] = useState(null);
  const [reviewModalOpen, setReviewModalOpen] = useState(false);
  const [isUpload, setIsUpload] = useState(false);
  const [isStream, setIsStream] = useState(false);
  const [makeActiveModal, setMakeActiveModal] = useState(1);
  const { width } = useWindowDimensions();

  // const maxResultCount = 5;
  const [flagModalOpen, setFlagModalOpen] = useState(false);
  const [flaggedContent, setFlaggedContent] = useState(null);
  const [workFlowStatus, setWorkFlowStatus] = useState(null);
  const [singleContentLoading, setSingleContentLoading] = useState(null);
  const { workFlowEnabled } = useSelector((state) => state.subscription);
  const [isProcessing, setIsProcessing] = useState([]);

  const ModalType = window.location.href.split('&type=')[1]?.split('&')[0];

  useEffect(() => {
    if (width < 750) {
      setDisplayMode('imagelist');
      sessionStorage.setItem('displayMode', 'imagelist');
    }
  }, [width]);

  useEffect(() => {
    if (shareChanged) {
      content.find((arr) => arr.id === shareChanged.id).shareId =
        shareChanged.shareId;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shareChanged]);

  const reloadContent = async (product) => {
    setSingleContentLoading(product);
    await reload(product, 'update');
    setSingleContentLoading(null);
  };

  useEffect(() => {
    if (workFlowStatus !== null) reloadContent(workFlowStatus.id);
    return () => {
      setWorkFlowStatus(null);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [workFlowStatus]);

  useEffect(() => {
    if (flaggedContent) {
      content.find((arr) => arr.id === flaggedContent.id).isFlagged =
        flaggedContent.isFlagged;
      content.find((arr) => arr.id === flaggedContent.id).flagMessage =
        flaggedContent.flagMessage;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [flaggedContent]);

  const handleUpload = () => {
    setUploading(false);
    setModalOpen(!modalOpen);
  };

  const handleWatermarkChange = (watermarkId) => {
    setUploading(false);
    setContentIdForWatermark(watermarkId);
    setWatermarkModalOpen(!watermarkModalOpen);
  };

  const StreamingVideo = [
    {
      id: 11,
      name: 'Local Upload',
      info: '',
      onClick: () => {
        setIsStreamingModal(true);
        setMakeActiveModal(1);
        handleUpload();
      }
    },
    {
      id: 12,
      name: 'Remote Upload',
      info: '',
      onClick: () => {
        setIsStreamingModal(true);
        setMakeActiveModal(2);
        handleUpload();
      }
    },
    {
      id: 13,
      info: '',
      name: 'Data Transfer Service',
      onClick: () => {
        setIsStreamingModal(true);
        setMakeActiveModal(3);
        handleUpload();
      }
    }
  ];

  const StreamingAudio = [
    {
      id: 11,
      name: 'Local Upload',
      info: '',
      onClick: () => {
        setIsStreamingModal(true);
        setMakeActiveModal(1);
        handleUpload();
      }
    },
    {
      id: 12,
      name: 'Remote Upload',
      info: '',
      onClick: () => {
        setIsStreamingModal(true);
        setMakeActiveModal(2);
        handleUpload();
      }
    },
    {
      id: 13,
      info: '',
      name: 'Data Transfer Service',
      onClick: () => {
        setIsStreamingModal(true);
        setMakeActiveModal(3);
        handleUpload();
      }
    }
  ];

  const UploadingVideo = [
    {
      id: 21,
      info: '',
      name: 'Local Upload',
      onClick: () => {
        setMakeActiveModal(1);
        setIsStreamingModal(false);
        handleUpload();
      }
    },
    {
      id: 22,
      info: '',
      name: 'Remote Upload',
      onClick: () => {
        setMakeActiveModal(2);
        setIsStreamingModal(false);
        handleUpload();
      }
    },
    {
      id: 24,
      info: '',
      name: 'Data Transfer Service',
      onClick: () => {
        setMakeActiveModal(3);
        setIsStreamingModal(false);
        handleUpload();
      }
    }
  ];

  const UpploadAudio = [
    {
      id: 21,
      info: '',
      name: 'Local Upload',
      onClick: () => {
        setMakeActiveModal(1);
        setIsStreamingModal(false);
        handleUpload();
      }
    },
    {
      id: 22,
      info: '',
      name: 'Remote Upload',
      onClick: () => {
        setMakeActiveModal(2);
        setIsStreamingModal(false);
        handleUpload();
      }
    },
    {
      id: 23,
      info: '',
      name: 'Data Transfer Service',
      onClick: () => {
        setMakeActiveModal(3);
        setIsStreamingModal(false);
        handleUpload();
      }
    }
  ];

  const getStreaming = () => {
    if (MediaType === 1) {
      return StreamingVideo;
    } else if (MediaType === 2) {
      return StreamingAudio;
    }
  };

  const getUploading = () => {
    if (MediaType === 1) {
      return UploadingVideo;
    } else if (MediaType === 2) {
      return UpploadAudio;
    }
  };
  useEffect(() => {
    if (+ModalType === 1 || (isImageModal && +activeMediaTab === 3)) {
      setModalOpen(true);
    }
    if (+ModalType === 2) {
      setIsStreamingModal(true);
      handleUpload();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [ModalType, isImageModal]);

  return (
    <>
      <ListPageHeading
        displayMode={displayMode}
        changeDisplayMode={setDisplayMode}
        search={query}
        setSearch={setQuery}
        createFun={handleUpload}
        createText="spaces.upload"
        createIcon="simple-icon-cloud-upload"
        loading={contentsLoading}
        isContentManagement={isContentManagement}
        filter={filter}
        setFilter={setFilter}
        roleType={roleType}
        handleRefresh={handleRefresh}
        MediaType={MediaType}
        handleUpload={handleUpload}
        StreamingTypes={getStreaming()}
        UploadTypes={getUploading()}
        setIsStream={setIsStream}
        setIsUpload={setIsUpload}
        isStream={isStream}
        isUpload={isUpload}
        subscriptionDetails={subscriptionDetails}
      />
      <FlagModal
        modalOpen={flagModalOpen}
        toggleModal={() => setFlagModalOpen(!flagModalOpen)}
        entity={entity}
        setEntity={setEntity}
        spaceId={spaceId}
        setFlaggedContent={setFlaggedContent}
        list={allContent}
        setList={setAllContent}
      />
      {shareModalOpen && entity?.id && (
        <ShareModal
          modalOpen={shareModalOpen}
          toggleModal={() => setShareModalOpen(!shareModalOpen)}
          setShareChanged={setShareChanged}
          baseUrl={`${process.env.REACT_APP_EMBED_URL}/playersvc/ins-share`}
          EntityKind={1}
          entity={entity}
          spaceId={spaceId}
          key={entity?.id}
          isYtVimeoVideo={isYoutubeORVimeoSource(entity?.contentSource)}
          MediaType={MediaType}
          handleDelete={(entityId) => {
            content.find((f) => f.id === entityId).shareId = null;
          }}
          handleCreate={(entityId, shareId) => {
            content.find((f) => f.id === entityId).shareId = shareId;
          }}
        />
      )}
      {reviewModalOpen &&
        roleType <= RoleTypes.CONTRIBUTOR &&
        workFlowEnabled && (
          <ReviewModal
            modalOpen={reviewModalOpen}
            toggleModal={() => setReviewModalOpen(!reviewModalOpen)}
            content={entity}
            reload={reload}
            role={roleType}
            setWorkFlowStatus={setWorkFlowStatus}
          />
        )}

      <div className="disable-text-selection">
        {/* content modal  */}
        <UploadModal
          uploading={uploading}
          setUploading={setUploading}
          modalOpen={modalOpen}
          toggleModal={() => setModalOpen(!modalOpen)}
          showMediaProcessing={showMediaProcessing}
          isImageModal={isImageModal}
          title={title}
          spaceId={spaceId}
          isModal
          roleType={roleType}
          makeActiveModal={makeActiveModal}
          acceptedMediaType={acceptedMediaType}
          MediaType={MediaType}
          reload={reload}
          setContentsLoading={setContentsLoading}
          isStreamingModal={isStreamingModal}
          setIsStreamingModal={setIsStreamingModal}
          subscriptionDetails={subscriptionDetails}
        />
        {/* watermark modal  */}
        <UploadModal
          uploading={uploading}
          setUploading={setUploading}
          modalOpen={watermarkModalOpen}
          toggleModal={() => setWatermarkModalOpen(!watermarkModalOpen)}
          spaceId={spaceId}
          contentIdForWatermark={contentIdForWatermark}
          isModal
          isWatermarkModal
          acceptedMediaType={acceptedMediaType}
          MediaType={MediaType}
          reload={reload}
          subscriptionDetails={subscriptionDetails}
          setContentsLoading={setContentsLoading}
        />
        {!contentsLoading && content && content?.length === 0 ? (
          <p className="mt-4">No content</p>
        ) : (
          <ListPageListing
            items={content}
            setItems={setContent}
            displayMode={displayMode}
            setContentId={setContentId}
            icon={icon}
            hasMore={hasMoreContents}
            fetchContents={fetchContents}
            reload={reload}
            MediaType={MediaType}
            handleWatermarkChange={handleWatermarkChange}
            contentsLoading={contentsLoading || !display}
            isImageModal={isImageModal}
            setShareModalOpen={setShareModalOpen}
            shareModalOpen={shareModalOpen}
            setEntity={setEntity}
            setFlagModalOpen={setFlagModalOpen}
            setReviewModalOpen={setReviewModalOpen}
            roleType={roleType}
            subscriptionDetails={subscriptionDetails}
            previewCache={previewCache}
            selectedStatus={selectedStatus}
            setSelectedStatus={setSelectedStatus}
            isShowcaseSelected={isShowcaseSelected}
            setIsShowcaseSelected={setIsShowcaseSelected}
            isSharedSelected={isSharedSelected}
            setIsSharedSelected={setIsSharedSelected}
            isFlaggedSelected={isFlaggedSelected}
            setIsFlaggedSelected={setIsFlaggedSelected}
            selectedReview={selectedReview}
            setSelectedReview={setSelectedReview}
            isStreamingSelected={isStreamingSelected}
            setIsStreamingSelected={setIsStreamingSelected}
            singleContentLoading={singleContentLoading}
            allContent={allContent}
            setAllContent={setAllContent}
            setIsProcessing={setIsProcessing}
            isProcessing={isProcessing}
          />
        )}
      </div>
    </>
  );
};

export default ContentManager;
