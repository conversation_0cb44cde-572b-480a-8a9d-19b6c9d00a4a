/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable no-nested-ternary */
import Avtar from 'components/common/Avtar';
import ListPageHeading from 'components/content/ListPageHeading';
import ReviewModal from 'components/content/modals/ReviewModal';
import ReviewIcon from 'containers/navs/ReviewIcon';
import { getUserAssignedWorkflow } from 'functions/api/mgmtApi';
import React, { useState, useEffect } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import { Button, Card, CardText, Row } from 'reactstrap';
import { NavLink, useHistory } from 'react-router-dom';
import usePreview from 'hooks/usePreview';
import IntlMessages from 'helpers/IntlMessages';
import WorkflowPill from 'components/WorkflowPill';
import { formatDate, getType } from 'helpers/Utils';
import { Colxx } from 'components/common/CustomBootstrap';
import RefreshButton from '../../../components/buttons/RefreshButton';
import { diableEnableStory } from 'functions/api/spacesApi';
import { NotificationManager } from 'components/common/react-notifications';

const Review = ({ previewCache }) => {
  const [query, setQuery] = useState('');
  const [filter, setFilter] = useState(1);
  const [maxResultCount] = useState(10);
  const [skipCount, setSkipCount] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [actionReqData, setActionReqData] = useState([]);
  const [loading, setLoading] = useState('');
  const [reviewModalOpen, setReviewModalOpen] = useState(false);
  const [content, setContent] = useState();
  const [reloadRequire, setReloadRequire] = useState(false);
  const [workFlowStatus, setWorkFlowStatus] = useState(null);

  const getActionReqData = async (option, newData) => {
    const { data } = await getUserAssignedWorkflow(
      option?.maxResultCount,
      option?.skipCount
    );
    setSkipCount(option.skipCount + maxResultCount);
    if (newData) {
      setActionReqData(data?.items);
    } else {
      setActionReqData([...actionReqData, ...data?.items]);
    }
    if (data?.items?.length === 0) {
      setHasMore(false);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (filter === 1) {
      setLoading(true);
      getActionReqData(
        {
          maxResultCount,
          skipCount: 0
        },
        true
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filter]);

  const handleRefresh = () => {
    setLoading(true);
    setActionReqData([]);
    if (!loading)
      getActionReqData(
        {
          maxResultCount,
          skipCount: 0
        },
        true
      );
  };

  useEffect(() => {
    if (reloadRequire) {
      handleRefresh();
      setReloadRequire(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [reloadRequire]);

  useEffect(() => {
    if (workFlowStatus) {
      actionReqData?.map((a) => {
        if (a.entityId === workFlowStatus.id) {
          return {
            ...a,
            status: workFlowStatus.status
          };
        }
        return a;
      });
    }

    return () => {
      setWorkFlowStatus(null);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [workFlowStatus]);

  const handleEnableDisableStory = async () => {
    const payload = {
      accessControl: content?.accessControl,
      creationDateTime: content?.creationDateTime,
      isDefault: content?.isDefault,
      isEnabled: !content?.isEnabled,
      description: content?.description,
      items: content?.items ?? [],
      lastUpdatedDateTime: content?.lastUpdatedDateTime,
      id: content?.id,
      name: content?.name,
      spaceId: content?.spaceId
    };
    const { isError } = await diableEnableStory(payload);
    if (isError) {
      NotificationManager.error('Failed to disable Story', 'Error');
    }
  };

  return (
    <div>
      {content && (
        <ReviewModal
          content={content}
          role={content?.roleType}
          modalOpen={reviewModalOpen}
          toggleModal={() => setReviewModalOpen(false)}
          setWorkFlowStatus={setWorkFlowStatus}
          isStory={content?.isStory}
          key={content?.id}
          setReloadRequire={setReloadRequire}
          disableEnableSpaceStory={handleEnableDisableStory}
          inReview
        />
      )}
      <ListPageHeading
        search={query}
        setSearch={setQuery}
        inReview
        setFilter={setFilter}
        // searchDisabled
      />
      <RefreshButton handleRefresh={handleRefresh} />
      {filter === 1 && (
        <div>
          <div>
            <ReviewTitle />

            {loading ? (
              <div className="loading" />
            ) : (
              <InfiniteScroll
                dataLength={actionReqData?.length}
                next={() =>
                  getActionReqData(
                    {
                      maxResultCount,
                      skipCount
                    },
                    false
                  )
                }
                hasMore={hasMore}
                loader={
                  <p
                    style={{
                      textAlign: 'center',
                      fontSize: '14px',
                      padding: '10px 0px',
                      margin: '0',
                      fontWeight: 'bold',
                      color: '#94308A'
                    }}
                  >
                    Loading...
                  </p>
                }
                endMessage={
                  <p
                    style={{
                      textAlign: 'center',
                      fontSize: '14px',
                      padding: '10px 0px',
                      margin: '0',
                      fontWeight: 'bold',
                      color: '#94308A'
                    }}
                  >
                    {actionReqData?.length === 0 ? (
                      <IntlMessages id="no-review-left" />
                    ) : (
                      <IntlMessages id="seen-all" />
                    )}
                  </p>
                }
                style={{
                  overflow: 'hidden',
                  paddingBottom: '4rem',
                  minHeight: 300,
                  width: '100%'
                }}
              >
                {actionReqData?.map((item) => {
                  if (
                    !item?.assignedUser?.roleType ||
                    item?.assignedUser?.roleType > 999 ||
                    item?.assignedUser?.roleType < 1
                  ) {
                    return null;
                  }
                  return (
                    <ReviewRowItem
                      key={item?.id}
                      requestedTime={item?.lastUpdatedDateTime}
                      type={item?.mediaType}
                      requestorName={item?.actor?.fullName}
                      title={item?.title}
                      setReviewModalOpen={setReviewModalOpen}
                      id={item?.entityId}
                      setContent={setContent}
                      spaceId={item?.spaceId}
                      workflowId={item?.id}
                      status={item?.status}
                      item={item}
                      previewCache={previewCache}
                      roleType={item?.assignedUser?.roleType}
                    />
                  );
                })}
              </InfiniteScroll>
            )}
          </div>
        </div>
      )}
      {filter === 2 && (
        <ShowCaseRequests
          name="Arohi Khanna"
          tanentName="Sam's Innerloop"
          time="02:51 pm 11th  November 2022"
          key="1"
        />
      )}
    </div>
  );
};

const ReviewTitle = () => {
  return (
    <Colxx xxs="12">
      <Row className="RL text-small mb-2">
        <div className="centered RLThumbnail" />
        <div className="centered RLTitle">Title</div>
        <div className="centered RLWorkflow">Workflow Status</div>
        <div className="centered RLRequestedTime">Requested Time</div>
        <div className="centered RLType">Type</div>
        <div className="centered RLRequestorsName">Requestor&apos;s Name</div>
        <div className="centered RLReview" />
      </Row>
    </Colxx>
  );
};

const ReviewRowItem = ({
  title,
  requestedTime,
  type,
  requestorName,
  setReviewModalOpen,
  id,
  workflowId,
  spaceId,
  setContent,
  status,
  previewCache,
  roleType,
  item
}) => {
  console.log('item', item);
  const isStory = type !== 1 && type !== 2 && type !== 3;
  const { previewSrc, previewValid } = usePreview({
    spaceId,
    contentId: id,
    isMulti: false,
    MediaType: type,
    waitFor: !isStory,
    previewCache,
    roleType,
    roleReqd: true
  });

  const history = useHistory();
  const redirectToCM = () => {
    if (!isStory) {
      history.push(`/app/spaces/space/${spaceId}/manage-content/${id}/edit`);
    } else {
      history.push(`/app/spaces/space/${spaceId}/stories/${id}/detail`);
    }
  };

  const handleReview = () => {
    setReviewModalOpen(true);
    setContent({
      ...item,
      title,
      name: title,
      spaceId,
      id,
      lastUpdatedDateTime: item.lastUpdatedDateTime,
      items: item.items,
      description: item.description,
      isDefault: item.isDefault,
      isEnabled: !item.isEnabled,
      creationDateTime: item.creationDateTime,
      accessControl: item.accessControl,
      mediaType: type,
      workflowStatus: workflowId,
      roleType,
      isStory,
      status: item?.status
    });
  };

  return (
    <Colxx xxs="12" className="mb-3">
      <Card className="RL d-flex flex-row">
        <div className="RLThumbnail centered c-pointer " onClick={redirectToCM}>
          <NavLink to="#" style={{ height: '90px' }} className="centered">
            {previewValid && previewSrc && (
              <img src={previewSrc} height={80} width={100} alt="random" />
            )}
            {!previewValid && type === 1 ? (
              <i
                className="simple-icon-camrecorder list-default-icon-style list-thumbnail
                border-0 card-img-left"
              >
                {' '}
              </i>
            ) : type === 2 && !previewValid ? (
              <i
                className="simple-icon-earphones list-default-icon-style list-thumbnail
                border-0 card-img-left"
              >
                {' '}
              </i>
            ) : (
              type === 3 &&
              !previewValid && (
                <i
                  className="simple-icon-picture list-default-icon-style list-thumbnail
                border-0 card-img-left"
                >
                  {' '}
                </i>
              )
            )}
          </NavLink>
        </div>
        <div className="RLTitle centered">
          <span className="w-full">
            <p
              onClick={redirectToCM}
              className="text-ellipsis mb-0 font-weight-bolder cursor-pointer text-primary-hover"
            >
              {title}
            </p>
          </span>
        </div>
        <div className="RLWorkflow centered ">
          <CardText className="text-small font-weight-light mb-0">
            {status && <WorkflowPill status={status} />}
          </CardText>
        </div>
        <div className="RLRequestedTime centered">
          <CardText className="text-small font-weight-light mb-0">
            {formatDate(requestedTime)}{' '}
            {new Date(requestedTime).toLocaleTimeString()}
          </CardText>
        </div>
        <div className="RLType centered">
          <CardText className="text-small font-weight-light mb-0">
            {getType(type)}
          </CardText>
        </div>
        <div className="RLRequestorsName centered">
          <CardText className="text-small font-weight-light mb-0">
            {requestorName}
          </CardText>
        </div>
        <div className="RLReview centered">
          <Card
            className="text-center pointer display-flex justify-content-center align-items-center p-2"
            onClick={handleReview}
          >
            <ReviewIcon height={16} width={16} active />
            <CardText className="text-small font-weight-light mb-0">
              Review
            </CardText>
          </Card>
        </div>
      </Card>
    </Colxx>
  );
};

const ShowCaseRequests = ({ name, tanentName, time }) => {
  return (
    <Card className="pl-2 pb-2 pt-2">
      <div className="d-flex align-items-center">
        <div
          style={{
            maxWidth: '5rem',
            flex: 0.2
          }}
        >
          <Avtar fName={name} newColor size={40} />
        </div>
        <div
          className="d-flex justify-content-around align-items-center"
          style={{
            flex: 0.8
          }}
        >
          <div>
            <div className="mb-2">{name}</div>
            <div>{`A Request has been sent by “${name}” to join “${tanentName}”.`}</div>
            <div>{time}</div>
          </div>
          <div className="d-flex justify-content-between">
            <Button>Ignore</Button>
            <Button color="primary ml-4">Approve</Button>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default Review;
