import React from 'react';
import ConfirmationModal from 'components/common/ConfirmationModal';

const DeletePlayerModal = ({
  openModal,
  toggleModal,
  deletePlayer,
  mediaTitle
}) => {
  const handleConfirm = async () => {
    toggleModal();
    deletePlayer();
  };

  const handleClose = () => {
    toggleModal();
  };

  return (
    <ConfirmationModal
      openConfirmationModal={openModal}
      toggleConfirmationModal={toggleModal}
      handleConfirm={handleConfirm}
      handleClose={handleClose}
      type="deletePlayer"
      mediaTitle={mediaTitle}
    />
  );
};

export default DeletePlayerModal;
