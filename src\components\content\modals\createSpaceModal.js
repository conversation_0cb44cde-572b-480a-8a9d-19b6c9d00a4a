import LoadingButton from 'components/buttons/LoadingButton';
import { NotificationManager } from 'components/common/react-notifications';

import IntlMessages from 'helpers/IntlMessages';
import useWindowDimensions from 'hooks/useWindowDimensions';
import React from 'react';
import { Form, FormGroup, Input, Label, Modal, ModalBody } from 'reactstrap';

const CreateSpaceModal = ({
  modalOpen,
  toggleModal,
  id,
  handleClick = () => {},
  setName,
  name
}) => {
  // const [loading, setLoading] = useState(false);
  const { width } = useWindowDimensions();

  const createSpace = () => {
    if (!name) {
      NotificationManager.warning(<IntlMessages id="space-not-empty" />);
      return;
    }
    if (name.trim().length < 5) {
      NotificationManager.warning(<IntlMessages id="space-notless-empty" />);
      return;
    }
    handleClick(id);
    toggleModal();
  };
  const CloseButton = () => {
    setName(null);
    toggleModal();
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    createSpace();
  };
  return (
    <Modal
      isOpen={modalOpen}
      toggle={toggleModal}
      centered
      style={{ minWidth: '40%' }}
    >
      <ModalBody>
        <div className="d-flex justify-content-between  mb-xs-4">
          <p
            className="font-weight-lightbold "
            style={{ fontSize: '17px', color: 'black' }}
          >
            New Space
          </p>
          <span
            role="button"
            tabIndex={0}
            onClick={() => CloseButton()}
            className="w-5"
          >
            <i
              style={{ color: 'black' }}
              className="simple-icon-close close-btn"
            />
          </span>
        </div>
        <Form onSubmit={handleSubmit}>
          <FormGroup className={`form-group has-float-label  `}>
            <Label for="name" style={{ cursor: 'default' }}>
              <IntlMessages id="space.name" />
              &nbsp;
              <span
                style={{
                  color: '#922c88'
                }}
              >
                {' '}
                *
              </span>
            </Label>
            <Input
              className="w-300 my-4"
              id="spaceName"
              name="Space Name"
              placeholder="Enter Space Name"
              type="text"
              value={name}
              onChange={(e) => {
                const text = e.target.value;
                const values = text.slice(0, 100);
                setName(values);
                if (text.length > 100) {
                  NotificationManager.warning(
                    <IntlMessages id="no-more-100" />
                  );
                }
              }}
            />
          </FormGroup>
        </Form>

        <div className="d-flex justify-content-center my-5">
          <LoadingButton
            style={{ minWidth: width < '600' ? '60%' : '30%' }}
            text="Create Space"
            // loading={loading}
            // disabled={loading}
            onClick={handleSubmit}
          />
        </div>
      </ModalBody>
    </Modal>
  );
};

export default CreateSpaceModal;
