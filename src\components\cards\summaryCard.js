/* eslint-disable jsx-a11y/no-static-element-interactions */
import React, { useState, useRef } from 'react';
import { Card, CardBody, Button, Popover, PopoverBody } from 'reactstrap';
import IntlMessages from 'helpers/IntlMessages';
import useOnClickOutside from 'hooks/useOnClickOutSide';
import StoryIcon from 'constants/StoryIcon';
import NextUpdateTime from 'components/nextUpdateTime';

const SummaryCard = ({
  className = 'mb-4',
  icon,
  title,
  value,
  tooltip,
  lastUpdated
}) => {
  const cardRef = useRef(null);
  const [popoverOpen, setPopoverOpen] = useState(false);
  // eslint-disable-next-line no-plusplus
  const id = title.split(' ').join('');

  useOnClickOutside(cardRef, () => setPopoverOpen(false));

  return (
    <div className={`icon-row-item ${className}`} ref={cardRef}>
      <span className="info" onClick={() => setPopoverOpen(true)}>
        <Button className="mr-1 mb-2" id={`popover_${id}`}>
          <i className="simple-icon-info font-weight-bold" />
        </Button>
        <Popover
          placement="top"
          isOpen={popoverOpen}
          target={`popover_${id}`}
          toggle={() => setPopoverOpen(!popoverOpen)}
        >
          <PopoverBody>{tooltip}</PopoverBody>
        </Popover>
      </span>
      <div style={{ width: '100%', height: '100%' }}>
        <Card>
          <CardBody className="text-center">
            {icon === 'iconsminds-photo-album-2' ? (
              <div className="my-2" style={{ height: ' fit-content' }}>
                <StoryIcon active width={36} />
              </div>
            ) : (
              <i className={[icon, 'fa-8x'].join(' ')} />
            )}
            {/* <i className={[icon, 'fa-8x'].join(' ')} /> */}
            <p className="card-text font-weight-semibold mb-0">
              <IntlMessages id={title} />
            </p>
            <p className="lead text-center mb-0">{value}</p>
          </CardBody>
        </Card>
        {lastUpdated && <NextUpdateTime time={lastUpdated} />}
      </div>
    </div>
  );
};

export default React.memo(SummaryCard);
