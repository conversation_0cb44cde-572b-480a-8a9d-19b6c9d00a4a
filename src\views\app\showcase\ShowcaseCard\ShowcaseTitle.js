import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Tooltip } from 'reactstrap';

const ShowcaseTitle = ({ title, id }) => {
  const [isOpen, setIsOpen] = useState(false);
  const target = `tooltip_${id}_showcase_content`;
  return (
    <>
      <span className="title" id={target}>
        <Link
          target="_blank"
          rel="noreferrer"
          to={`/app/showcase/detail/${id}`}
        >
          {title}
        </Link>
      </span>
      <Tooltip
        placement="bottom"
        isOpen={isOpen}
        target={target}
        toggle={() => setIsOpen(!isOpen)}
      >
        {title}
      </Tooltip>
    </>
  );
};

export default ShowcaseTitle;
