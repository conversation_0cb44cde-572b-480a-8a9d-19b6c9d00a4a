import { positionData } from 'constants/VideoInteraction';
import { removeHtmlTags } from 'helpers/Utils';
import React from 'react';
import { Card } from 'reactstrap';

const RichTextPreview = ({ position, text, bgColor, isTransparent }) => {
  const positionClass = positionData.find((f) => f.id === +position)?.title;

  const isTextExists = removeHtmlTags(text);

  return (
    <div className={`preview-cont-${positionClass} position-absolute p-2`}>
      {isTextExists && (
        <Card
          className="d-flex centered RichTextPreviewCard shadow-none"
          style={{
            borderRadius: 10,
            overflow: 'hidden',
            height: 'fit-content',
            padding: '4px 10px',
            background: isTransparent ? 'transparent' : bgColor
          }}
          dangerouslySetInnerHTML={{ __html: text }}
        />
      )}
    </div>
  );
};

export default RichTextPreview;
