/* eslint-disable no-unreachable */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-unused-vars */
/* eslint-disable react/no-array-index-key */
import React, { useEffect, useMemo, useState } from 'react';
import InteractionCard from './InteractionCard';
import { getInteractions } from 'functions/api/interactionApi';
import ToggleInteraction from './ToggleInteraction';
import { getParticularList, getPlayerPauseList } from 'helpers/Utils';
import IntlMessages from 'helpers/IntlMessages';
import InfiniteScroll from 'react-infinite-scroll-component';
import { Card } from 'reactstrap';
import LoadingSkeleton from 'components/LoadingSkeleton';

const LIMIT = 10;

const InteractionList = ({
  content,
  getContent,
  handlePlayerChange,
  isParticular,
  setUpdateInteractionId,
  list,
  setList,
  skip,
  setSkip,
  hasMore,
  setHasMore,
  isLoading,
  setIsLoading,
  setSelectedInteractions,
  setSelectedPollId
}) => {
  const filteredList = useMemo(() => {
    if (isParticular) {
      return getParticularList(list);
    }
    return getPlayerPauseList(list);
  }, [list, isParticular]);

  const fetchList = async ({ skip = 0 }) => {
    try {
      if (skip === 0) {
        setList([]);
        setIsLoading(true);
      }
      const { data, isError } = await getInteractions({
        spaceId: content.spaceId,
        contentId: content.id,
        params: {
          skipCount: skip,
          maxResultCount: LIMIT
        }
      });
      if (isError) {
        setList([]);
      } else {
        const lengthOfServerData = data.items.length;
        setHasMore(!(lengthOfServerData < LIMIT));
        setList((prev) => [...prev, ...data.items]);
        setSkip((prev) => prev + LIMIT);
      }
      setIsLoading(false);
    } catch (error) {
      console.error('Something went wrong in fetchList due to ', error);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchList({ skip: 0 });
  }, []);

  if (isLoading) {
    return (
      <div className="InteractionList mt-4">
        {new Array(5).fill('').map((m, i) => (
          <InteractionLoadingCard key={i} />
        ))}
      </div>
    );
  }

  if (filteredList.length === 0) {
    return (
      <p style={{ textAlign: 'center', color: '#94308A' }}>
        No Interactions found. Please create a new interaction
      </p>
    );
  }

  return (
    <div className="InteractionList">
      <InfiniteScroll
        dataLength={list?.length}
        next={() => {
          if (!isLoading) {
            fetchList({ skip });
          }
        }}
        hasMore={hasMore}
        loader={
          <p style={{ textAlign: 'center', color: '#94308A' }}>
            <b>Loading...</b>
          </p>
        }
        endMessage={
          <p style={{ textAlign: 'center', color: '#94308A' }}>
            <IntlMessages id="seen-all" />
          </p>
        }
        className={`overflow-inherit`}
      >
        {filteredList.map((m, i) => (
          <InteractionCard
            setList={setList}
            list={list}
            key={m.id}
            data={m}
            content={content}
            setSelectedInteractions={setSelectedInteractions}
            isParticular={isParticular}
            setSelectedPollId={setSelectedPollId}
            handleEdit={() => {
              setUpdateInteractionId(m.id);
            }}
          />
        ))}
      </InfiniteScroll>

      {isParticular && (
        <ToggleInteraction
          content={content}
          getContent={getContent}
          handlePlayerChange={handlePlayerChange}
        />
      )}
    </div>
  );
};

const InteractionLoadingCard = () => {
  return (
    <Card className="d-flex w-full  p-2 flex-row align-items-center justify-content-between mb-2">
      <div style={{ width: '15%' }}>
        <LoadingSkeleton
          style={{
            height: '80px',
            width: '100%'
          }}
        />
      </div>
      <div className="d-flex flex-column" style={{ width: '82%' }}>
        <LoadingSkeleton style={{ height: '20px', width: '100%' }} />
        <LoadingSkeleton style={{ height: '20px', width: '100%' }} />
      </div>
    </Card>
  );
};

export default InteractionList;
