/* eslint-disable no-unused-vars */
import { Colxx } from 'components/common/CustomBootstrap';
import React, { useEffect, useState } from 'react';
import AiInputTitle from '../../components/AiInputTitle';
import { Row } from 'reactstrap';
import CopyIcon from 'components/svg/CopyIcon';
import ReactQuill from 'react-quill';
import { DescriptionInput } from 'components/input';
import ImageInsightsModal from 'views/app/spaces/components/ImageInsightsModal';
import { NotificationManager } from 'components/common/react-notifications';
import IntlMessages from 'helpers/IntlMessages';
import { updateAutoGeneratedMetaDeta } from 'functions/api/spacesApi';

const BodyInputs = ({ isStory, content, roleType, selectedTenantId }) => {
  const [isOpen, setIsOpen] = useState(false);
  const toggle = () => setIsOpen(!isOpen);
  const [disabledDesc, setDisableDesc] = useState(true);
  const [updatingTextTranscribe, setUpdatingTextTranscribe] = useState(false);
  const [textTranscribe, setTextTranscribe] = useState(content?.textTranscribe);
  const [values, setValues] = useState({
    title: '',
    description: '',
    tags: []
  });

  const handleSave = async () => {
    if (updatingTextTranscribe) {
      return;
    }
    if (!disabledDesc && content.textTranscribe !== textTranscribe) {
      try {
        setUpdatingTextTranscribe(true);
        const { data, isError } = await updateAutoGeneratedMetaDeta({
          tenantId: selectedTenantId,
          contentId: content.id,
          // chapters_vtt: arrayToWebVTT(content.chapters),
          // chapters: content.chapters,
          // tags_auto: content?.metadata?.tags_auto,
          // title_auto: content?.metadata?.title_auto,
          // summary_auto: content?.metadata?.summary_auto,
          textTranscribe,
          aiOperationType: 1
        });
        if (!isError) {
          content.textTranscribe = textTranscribe;
          NotificationManager.success(
            <IntlMessages id="textTranscribe-success" />
          );
        } else {
          NotificationManager.error(<IntlMessages id="textTranscribe-error" />);
        }
        setUpdatingTextTranscribe(false);
      } catch (error) {
        setUpdatingTextTranscribe(false);
        console.log('error', error);
      }
    }
    setDisableDesc(!disabledDesc);
  };

  useEffect(() => {
    if (content) {
      setValues({
        title: content?.metadata?.TitleAuto,
        description: content?.metadata?.SummaryAuto,
        tags: content?.metadata?.TagsAuto
      });
      setTextTranscribe(content?.textTranscribe);
    }
  }, [content]);

  const { title, tags, description } = values;

  return (
    <>
      <Colxx className="p-0 mb-2">
        {title && (
          <Colxx className="m-0 mt-4 p-0">
            <AiInputTitle title="Auto generated Title" />
            <Row className="d-flex justify-content-start flex-row w-full m-0 gap-1">
              <p
                style={{
                  fontSize: 14,
                  overflow: 'hidden',
                  borderRadius: 10
                }}
                className="mb-0 p-2 aiInputBg"
              >
                {title}
              </p>
              <CopyIcon copyToClipboardOnClick dataToCopy={title} />
            </Row>
          </Colxx>
        )}

        {tags.length > 0 && (
          <Colxx className="m-0 mt-4 p-0">
            <AiInputTitle title="Auto generated Tags" />
            <div className="d-flex flex-row flex-wrap">
              {tags?.map((m) => (
                <span
                  className="mr-1"
                  style={{
                    padding: '2px 14px',
                    borderRadius: 20,
                    color: '#fff',
                    background: '#992288',
                    fontSize: 13,
                    marginTop: 5
                  }}
                  key={m}
                >
                  {m}
                </span>
              ))}
              <CopyIcon
                className="ml-2 mt-1 centered"
                copyToClipboardOnClick
                dataToCopy={tags?.toString()}
              />
            </div>
          </Colxx>
        )}

        <Colxx className="mt-4 p-0">
          <AiInputTitle title="Auto generated Description" />
          <div className="w-100 position-relative" style={{ height: 100 }}>
            <CopyIcon
              className="ml-2 position-absolute"
              copyToClipboardOnClick
              style={{ top: 10, right: 10, zIndex: 10 }}
              dataToCopy={description}
            />
            <ReactQuill
              style={{
                overflow: 'hidden',
                width: '100%'
              }}
              className="react-quill ai-editor 
      disabled-ql aiInputBgQuill  DescriptionStyleInActive overflow-hidden-quill "
              readOnly
              theme="bubble"
              value={description}
            />
            <span
              onClick={toggle}
              className="position-absolute c-pointer px-2 "
              style={{
                color: '#992288',
                fontSize: '12px',
                right: 0,
                bottom: 5,
                background: '#FFF5FE'
              }}
            >
              Read More...
            </span>
          </div>
        </Colxx>

        {!isStory && (
          <Colxx className="my-3 mb-4 p-0">
            <DescriptionInput
              tempDesc={textTranscribe}
              setTempDesc={setTextTranscribe}
              clickToEditDesc={handleSave}
              updatingDesc={updatingTextTranscribe}
              mandatory
              contributerOrAbove
              roleType={roleType}
              title="ai-text-transcribe"
              limit={content.textTranscribe?.length * 2}
              showLimit={false}
              disabledDesc={disabledDesc}
            />
          </Colxx>
        )}
      </Colxx>
      {isOpen && (
        <ImageInsightsModal
          isImgAi={false}
          isOpen={isOpen}
          toggle={toggle}
          description={description}
        />
      )}
    </>
  );
};

export default BodyInputs;
