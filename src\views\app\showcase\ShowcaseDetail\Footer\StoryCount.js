import { isDarkModeActive } from 'helpers/Utils';
import React from 'react';

const StoryCount = ({ count, className = '' }) => {
  return (
    <div
      className={`total-views ${className}`}
      style={{
        background: isDarkModeActive() ? '#9922872d' : '#F0F0F0',
        textWrap: 'nowrap',
        fontSize: 12
      }}
    >
      Items: {count}
    </div>
  );
};

export default StoryCount;
