/* eslint-disable no-unused-vars */
/* eslint-disable no-unused-expressions */
/* eslint-disable consistent-return */
/* eslint-disable array-callback-return */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-unneeded-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable react/no-array-index-key */
import React, { useState, useEffect } from 'react';
import { Row, Card, Button } from 'reactstrap';
import { injectIntl } from 'react-intl';
import { getShowcases } from 'functions/api/showcaseApi';
import IntlMessages from 'helpers/IntlMessages';
import InfiniteScroll from 'react-infinite-scroll-component';
import { NotificationManager } from 'components/common/react-notifications';
import {
  getPublicShowcases,
  getPublicSingleShowcase
} from 'functions/api/publicApi';
import { useHistory, useParams } from 'react-router-dom';
import ContentContainer from './ContentContainer';
import 'rc-switch/assets/index.css';
import { useDispatch, useSelector } from 'react-redux';
import ShowcaseOwner from './ShowcaseOwner';
import { adminRoot } from 'constants/defaultValues';
import {
  checkifLoggedIn,
  getLastVisitedSpaceId,
  isDarkModeActive,
  RoleTypes
} from 'helpers/Utils';
import RefreshButton from 'components/buttons/RefreshButton';
import BackButton from 'components/buttons/BackButton';
import {
  DEFAULT_SUBSCRIBE_BUTTON_COLOR,
  DEFUALT_COLOR,
  INITIAL_COLORS
} from 'constants/showcase';
import LoadingSkeleton from 'components/LoadingSkeleton';
import { SearchInput } from 'components/input';
import { storeShowcases } from 'redux/showcase/action';
import ScrollableTags from 'components/ScrollableTags';
import SelectCategory from './SelectCategory';
import SEO from 'components/SEO';
import ShowcaseDetail from './ShowcaseDetail';
import TopList from './ShowcaseAccounts/TopList';
import ShowcaseAccounts from './ShowcaseAccounts';

function Showcase({ intl, previewCache, roleType }) {
  const { messages } = intl;
  const isLoggedIn = checkifLoggedIn();
  const params = useParams();
  const { name, id, category } = params;
  console.log({
    name,
    id,
    category,
    params
  });
  const history = useHistory();
  const [expandFor, setExpandFor] = useState(name !== 'detail' ? name : null);
  const { subscriptionExpired: expiredSubscription, isLoading } = useSelector(
    (state) => state.subscription
  );
  const [tenantDetails, setTenantDetails] = useState(
    JSON.parse(localStorage.getItem('tenantDetails'))?.data
  );
  const selectedTenantId = tenantDetails?.selectedTenantId;
  const selectedTenantUserName = tenantDetails?.selectedTenant?.username;
  const selectedTenantName = tenantDetails?.selectedTenant?.name;
  const isMyShowcases =
    name === `@${selectedTenantUserName}` || name === selectedTenantId;
  const [expand, setExpand] = useState(false);
  const [showcaseId, setShowcaseId] = useState(null);
  const [search, setSearch] = useState('');
  const [showcases, setShowcases] = useState([]);
  const [isShowcasesLoading, setIsShowcasesLoading] = useState(false);
  const [maxResultCount] = useState(15);
  const [tags, setTags] = useState([]);
  const [skipCount, setSkipCount] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [tempSearch, setTempSearch] = useState('');
  const [activeTab, setActiveTab] = useState(1);
  const [myShowcases, setMyShowcases] = useState(false);
  const [selectedContent, setSelectedContent] = useState({});
  const [ownerLoading, setOwnerLoading] = useState(false);
  const [isMyHomePageActive, setIsMyHomePageActive] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [themeColors, setThemeColors] = useState(INITIAL_COLORS);
  const [orgThemeColors, setOrgThemeColors] = useState(INITIAL_COLORS);
  const [expandForUserName, setExpandForUserName] = useState({
    id: null,
    username: ''
  });
  const [ownerData, setOwnerData] = useState(null);
  const { likes: showcaseLikes } = useSelector((s) => s.showcaseLikes);
  const isDarkMode = isDarkModeActive();
  const dispatch = useDispatch();
  const showEditOptions = roleType <= RoleTypes.CONTENT_MANAGER;
  const userDetails = JSON.parse(
    localStorage.getItem('innerloop_current_user')
  );
  const [isVideoFile, setIsVideoFile] = useState(false);
  const [selectedCategories, setSelectedCategories] = useState('');
  const [selectedTag, setSelectedTag] = useState('');
  const optionsForCategory = useSelector((state) => state.categories.data);
  const [localBgImg, setLocalBgImg] = useState(null);
  const [isShowcaseBgLoading, setIsShowcaseBgLoading] = useState(false);
  const [showAccountList, setShowAccountList] = useState(false);
  const [topList, setTopList] = useState([]);

  console.log({
    expandForUserName,
    expandFor,
    name,
    id,
    params,
    ownerData
  });

  const showcasedBgId = ownerData?.contentId;

  useEffect(() => {
    if (id) {
      setShowcaseId(id);
      setExpand(true);
    } else {
      setShowcaseId(null);
      setExpand(false);
    }
  }, [id]);

  useEffect(() => {
    if (category) {
      setSelectedCategories(category);
    } else {
      setSelectedCategories('');
    }
  }, [category]);

  useEffect(() => {
    if (name && name !== 'detail' && name !== 'explore') {
      setMyShowcases(isMyShowcases);
      // setSelectedTag('');
      // setSelectedCategories('');
      // setSearch('');
      // setTempSearch('');
      setIsMyHomePageActive(isMyShowcases);
      setSkipCount(0);
      if (name.includes('@') && isMyShowcases) {
        setExpandFor(selectedTenantId);
      } else {
        setExpandFor(name);
      }
    }
    // else {
    //   setIsMyHomePageActive(false);
    //   setExpandFor(null);
    //   setMyShowcases(false);
    // }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [name, isMyShowcases]);

  useEffect(() => {
    if (!name && !id) {
      setOwnerData(null);
      setExpandFor(null);
    }
  }, [name, id]);

  const toggleExpanded = () => {
    setExpand(false);
    if (expandFor) {
      if (expandFor === expandForUserName?.id && expandForUserName?.username) {
        history.push(`/app/showcase/${expandForUserName?.username}`);
      } else {
        history.push(`/app/showcase/${expandFor}`);
      }
    } else {
      history.push(`/app/showcase`);
    }
  };

  const allShowcases =
    isLoggedIn && !expiredSubscription ? getShowcases : getPublicShowcases;

  const handleChangeTab = (number) => {
    setHasMore(true);
    setSkipCount(0);
    setActiveTab(number);
    setShowcases([]);
  };

  const fetchIndividualShowcasesAndStoreToRedux = async (showcases) => {
    try {
      showcases?.map(async (m) => {
        const { data: publicData, isError } = await getPublicSingleShowcase({
          showcaseId: m.id,
          tenantId: m?.tenantId,
          userId: userDetails?.uid
        });
        if (publicData && !isError) {
          dispatch(storeShowcases(publicData));
        }
      });
    } catch (error) {
      console.error(
        `Something went wrong in fetchIndividualShowcasesAndStoreToRedux due to `,
        error
      );
    }
  };

  const fetchShowCases = async (hardrefresh, stop = false, islist = false) => {
    !islist && !stop && setIsShowcasesLoading(true);
    if (stop) {
      return;
    }

    const newSkipCount = (() => {
      if (hardrefresh) {
        return 0;
      }
      return skipCount;
    })();

    const SubscribedOnly = !myShowcases ? activeTab === 2 : false;

    let payload = {
      query: search,
      maxResultCount,
      skipCount: newSkipCount,
      isLoggedIn,
      SubscribedOnly,
      tag: selectedTag,
      category: selectedCategories
    };
    if (expandFor) {
      if (isMyShowcases) {
        payload = {
          ...payload,
          AccountShowcaseOnly: true
        };
      } else {
        payload = {
          ...payload,
          tenantId: expandFor
        };
      }
    } else {
      setThemeColors(INITIAL_COLORS);
      setOrgThemeColors(INITIAL_COLORS);
    }
    if (!isLoggedIn) {
      delete payload.SubscribedOnly;
    }
    const { data, isError } = await allShowcases(payload);

    if (!ownerLoading && data) {
      const newshowcases = data?.items ?? [];
      const newTags = data?.tags?.filter((f) => !!f) ?? [];
      if (islist) {
        setShowcases([...showcases, ...newshowcases]);
      } else {
        setShowcases(newshowcases ?? []);
      }
      fetchIndividualShowcasesAndStoreToRedux(newshowcases);
      setTags([...new Set([...tags, ...newTags])]);
      setHasMore(true);
      if (newshowcases?.length < maxResultCount) {
        setHasMore(false);
      }
      setSkipCount(skipCount + maxResultCount);
    }
    if (isError) {
      NotificationManager.error(
        <IntlMessages id="something-wrong" />,
        <IntlMessages id="req.failed" />,
        3000,
        null,
        null,
        ''
      );
    }
    (!islist || isShowcasesLoading) && setIsShowcasesLoading(false);
  };

  useEffect(() => {
    if (!ownerLoading) {
      fetchShowCases(true, expandFor?.includes('@'));
    }
    console.log({
      search,
      activeTab,
      expandFor,
      ownerLoading
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    search,
    activeTab,
    expandFor,
    ownerLoading,
    selectedTag,
    selectedCategories
  ]);

  const handleSearch = () => {
    setSkipCount(0);
    setSearch(tempSearch);
  };

  const handleClearClick = () => {
    setTempSearch('');
    setSearch('');
  };

  const handleRefresh = () => {
    setSkipCount(0);
    fetchShowCases(true);
    setSelectedTag('');
    setSelectedCategories('');
    setSearch('');
    setTempSearch('');
  };

  const handleBack = () => {
    history.push(`${adminRoot}/showcase`);
    setExpandFor(null);
    setShowcases([]);
    setSkipCount(0);
    setMyShowcases(false);
    setSelectedTag('');
    setSelectedCategories('');
    setSearch('');
    setTempSearch('');
    setIsShowcasesLoading(true);
  };

  const handleAccountSwitch = () => {
    const e = !myShowcases;
    setShowcases([]);
    setMyShowcases(e);
    setSkipCount(0);
    setActiveTab(1);
    setShowAccountList(false);
    setSelectedTag('');
    setSelectedCategories('');
    setSearch('');
    setTempSearch('');
    const userIdentifier = selectedTenantUserName
      ? `@${selectedTenantUserName}`
      : selectedTenantId;
    if (e) {
      setExpandFor(selectedTenantId ?? null);
      history.push(`/app/showcase/${userIdentifier}`);
    } else {
      setMyShowcases(false);
      setExpandFor(null);
      history.push(`/app/showcase`);
    }
  };

  useEffect(() => {
    if (expiredSubscription && isLoggedIn && !isLoading) {
      NotificationManager.error(
        <IntlMessages id="subscription-expired" />,
        <IntlMessages id="req.failed" />,
        3000,
        null,
        null,
        ''
      );
      history.push('/app/home');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoading, expiredSubscription, isLoggedIn]);

  const handleSubscription = () => {
    setExpandFor(null);
    setShowAccountList(false);
    setMyShowcases(false);
    if (activeTab === 1) {
      history.push(`/app/showcase`);
      handleChangeTab(2);
    } else {
      handleChangeTab(1);
    }
  };

  const handleShowcaseURContent = () => {
    const lastVisisted = getLastVisitedSpaceId();
    history.push(`/app/spaces/space/${lastVisisted}/manage-content`);
    NotificationManager.success(<IntlMessages id="u-can-showcase" />, '', 5000);
  };

  useEffect(() => {
    if (expand) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [expand]);

  const isSubscribedTab = activeTab === 2;

  useEffect(() => {
    // eslint-disable-next-line react-hooks/exhaustive-deps
    if (window?.Tawk_API?.hideWidget) {
      window.Tawk_API.hideWidget();
    }
    return () => {
      if (window?.Tawk_API?.showWidget) {
        window.Tawk_API.showWidget();
      }
    };
  }, []);

  const showColorPicker = isMyHomePageActive && isEdit;

  return (
    <>
      {category ? (
        <SEO
          title={`Explore ${category}`}
          description={`Explore ${category} with Innerloop Showcase`}
          url={window.location.href}
        />
      ) : (
        <SEO
          title="Innerloop Showcase - leading media network for your business marketing and growth"
          description="Start Storytelling with your videos, images and audio media assets and showcase globally"
          url={window.location.href}
        />
      )}
      {expand && (
        <ShowcaseDetail
          hash={showcaseId}
          showcases={showcases}
          selectedContent={selectedContent}
          setShowcases={setShowcases}
          toggleExpanded={toggleExpanded}
          isSubscribedTab={isSubscribedTab}
          history={history}
          showcaseLikes={showcaseLikes}
          usePlayerId={!!expandFor}
          selectedTenantId={selectedTenantId}
          previewCache={previewCache}
        />
      )}
      <div
        className={`d-flex flex-column showcase${
          isLoggedIn ? '' : '-anon'
        } w-full h-full`}
      >
        {!expandFor && (
          <h1 className="showcase-headeing mb-3">
            <IntlMessages id="sh-all-title-1" />
            <img
              alt="welcomeimg"
              src="/assets/icons/showcase-welcome.gif"
              className="mx-2 mb-1"
            />
            <IntlMessages id="sh-all-title-2" />
          </h1>
        )}
        {!showAccountList && activeTab !== 2 && !name && (
          <TopList
            isLoggedIn={isLoggedIn}
            data={topList}
            setData={setTopList}
            setShowAccountList={setShowAccountList}
          />
        )}
        <Header
          showAccountList={showAccountList}
          setShowAccountList={setShowAccountList}
          expandFor={expandFor}
          handleSearch={handleSearch}
          messages={messages}
          tempSearch={tempSearch}
          setTempSearch={setTempSearch}
          handleClearClick={handleClearClick}
          isLoggedIn={isLoggedIn}
          myShowcases={myShowcases}
          activeTab={activeTab}
          history={history}
          setActiveTab={setActiveTab}
          handleChangeTab={handleChangeTab}
          selectedTenantUserName={selectedTenantUserName}
          selectedTenantName={selectedTenantName}
          handleBack={handleBack}
          setExpandFor={setExpandFor}
          isSubscribedTab={isSubscribedTab}
          handleAccountSwitch={handleAccountSwitch}
          handleSubscription={handleSubscription}
          optionsForCategory={optionsForCategory}
          selectedCategories={selectedCategories}
          setSelectedCategories={setSelectedCategories}
        />

        {showAccountList ? (
          <ShowcaseAccounts
            setShowAccountList={setShowAccountList}
            isLoggedIn={isLoggedIn}
          />
        ) : (
          <>
            {expandFor && (
              <ShowcaseOwner
                key={expandFor}
                expandFor={expandFor}
                selectedTenantId={selectedTenantId}
                showcases={showcases}
                setShowcases={setShowcases}
                isLoggedIn={isLoggedIn}
                setExpandFor={setExpandFor}
                setOwnerLoading={setOwnerLoading}
                history={history}
                selectedTenantUserName={selectedTenantUserName}
                themeColors={themeColors}
                setThemeColors={setThemeColors}
                setIsEdit={setIsEdit}
                isEdit={isEdit}
                myShowcases={myShowcases}
                orgThemeColors={orgThemeColors}
                setOrgThemeColors={setOrgThemeColors}
                roleType={roleType}
                setTenantDetails={setTenantDetails}
                expandForUserName={expandForUserName}
                setExpandForUserName={setExpandForUserName}
                ownerData={ownerData}
                setOwnerData={setOwnerData}
                isVideoFile={isVideoFile}
                setIsVideoFile={setIsVideoFile}
                localBgImg={localBgImg}
                setLocalBgImg={setLocalBgImg}
                isShowcaseBgLoading={isShowcaseBgLoading}
                showcaseCardProps={{
                  isSubscribedTab,
                  previewCache,
                  setSelectedContent,
                  showColorPicker,
                  isDarkMode,
                  showcaseLikes,
                  showcasedBgId,
                  setIsShowcaseBgLoading
                }}
              />
            )}

            {!isShowcasesLoading && !ownerLoading && expandFor && (
              <>
                <div className="home-bottom-search mt-3">
                  <SearchInput
                    needClearIcon
                    value={tempSearch}
                    onChange={(e) => setTempSearch(e.target.value)}
                    onPressEnter={handleSearch}
                    handleClick={handleSearch}
                    placeholder={messages['menu.showcase-searchmore']}
                    handleClear={handleClearClick}
                    className="mr-0 hide-bottom-search"
                  />
                </div>
                <div className="home-bottom-refresh">
                  <div className="d-flex justify-content-end">
                    <RefreshButton
                      handleRefresh={handleRefresh}
                      color={themeColors.reloadButtonColor}
                      themeColors={themeColors}
                      showColorPicker={isEdit && isMyHomePageActive}
                      setThemeColors={setThemeColors}
                      orgThemeColors={orgThemeColors}
                      setOrgThemeColors={setOrgThemeColors}
                    />
                    {!isShowcasesLoading &&
                      isMyShowcases &&
                      showEditOptions &&
                      showcases?.length > 0 && (
                        <Button
                          color="primary"
                          className="showcase-ur-btn"
                          onClick={handleShowcaseURContent}
                        >
                          <IntlMessages id="showcase-ur-content" />
                        </Button>
                      )}
                  </div>
                </div>
              </>
            )}

            {!expandFor && (
              <div className="d-flex flex-row w-full justify-content-between catCont">
                <div className="categories">
                  {tags?.length > 0 && (
                    <ShowcaseTags
                      tags={tags}
                      search={selectedTag}
                      setSearch={setSelectedTag}
                    />
                  )}
                </div>
                <div className="reloadbtn">
                  <RefreshButton handleRefresh={handleRefresh} />
                </div>
              </div>
            )}

            {!isShowcasesLoading &&
              !ownerLoading &&
              showEditOptions &&
              isMyShowcases &&
              showcases?.length === 0 && (
                <div className="d-flex flex-column gap-2 align-items-center justify-content-center">
                  <p className="text-primary mb-1">
                    <IntlMessages id="no-content-showcased" />
                  </p>
                  <Button color="primary" onClick={handleShowcaseURContent}>
                    <IntlMessages id="showcase-ur-content" />
                  </Button>
                </div>
              )}

            {isShowcasesLoading || ownerLoading ? (
              <div className="ContentContainer mt-4 mb-3 w-full d-flex flex-column position-relative">
                <Row
                  className={`ContentCardParent${
                    isLoggedIn ? '' : 'Anonymous'
                  } mb-3`}
                >
                  {new Array(10).fill('').map((m, i) => (
                    <ShowcaseSkeletonCard key={i} />
                  ))}
                </Row>
              </div>
            ) : showcases?.length < 1 ? (
              <div className="mt-2 text-primary text-center">
                {activeTab === 2
                  ? 'Please subscribe to see content'
                  : isMyShowcases
                  ? ''
                  : 'No showcased content'}
              </div>
            ) : (
              <>
                <InfiniteScroll
                  dataLength={showcases?.length}
                  next={() => fetchShowCases(false, false, true)}
                  hasMore={hasMore}
                  loader={
                    <p style={{ textAlign: 'center', color: '#94308A' }}>
                      <b>Loading...</b>
                    </p>
                  }
                  endMessage={
                    <p style={{ textAlign: 'center', color: '#94308A' }}>
                      <IntlMessages id="seen-all" />
                    </p>
                  }
                  className={`overflow-inherit`}
                >
                  <ContentContainer
                    isLoggedIn={isLoggedIn}
                    showcases={showcases}
                    selectedTenantId={selectedTenantId}
                    setShowcases={setShowcases}
                    myShowcases={myShowcases}
                    isSubscribedTab={activeTab === 2}
                    setSelectedContent={setSelectedContent}
                    previewCache={previewCache}
                    history={history}
                    themeColors={themeColors}
                    setThemeColors={setThemeColors}
                    orgThemeColors={orgThemeColors}
                    setOrgThemeColors={setOrgThemeColors}
                    showColorPicker={showColorPicker}
                    isDarkMode={isDarkMode}
                    expandFor={expandFor}
                    roleType={roleType}
                    showcaseLikes={showcaseLikes}
                    showcasedBgId={showcasedBgId}
                    setOwnerData={setOwnerData}
                    localBgImg={localBgImg}
                    setLocalBgImg={setLocalBgImg}
                    isShowcaseBgLoading={isShowcaseBgLoading}
                    setIsShowcaseBgLoading={setIsShowcaseBgLoading}
                  />
                </InfiniteScroll>
              </>
            )}
          </>
        )}
      </div>
    </>
  );
}

export default injectIntl(Showcase);

const Header = ({
  expandFor,
  handleSearch,
  messages,
  tempSearch,
  setTempSearch,
  handleClearClick,
  isLoggedIn,
  myShowcases,
  handleBack,
  handleAccountSwitch,
  selectedTenantUserName,
  selectedTenantName,
  activeTab,
  handleChangeTab,
  isSubscribedTab,
  setActiveTab,
  setExpandFor,
  history,
  handleSubscription,
  optionsForCategory,
  selectedCategories,
  setSelectedCategories,
  showAccountList,
  setShowAccountList
}) => {
  if (showAccountList) {
    return (
      <div className="SAAHeader">
        <BackButton
          onClick={() => {
            setShowAccountList(false);
          }}
        />
        <Row className="othertwo">
          <MysubcriptionButton
            condition={isLoggedIn}
            active={activeTab === 2}
            onClick={handleSubscription}
          />
          <MyTenantButton
            active={myShowcases}
            condition={isLoggedIn}
            onClick={handleAccountSwitch}
            selectedTenantName={selectedTenantName}
            selectedTenantUserName={selectedTenantUserName}
          />
        </Row>
      </div>
    );
  }

  return (
    <>
      {/* header for owner  */}
      <div className="d-flex flex-row align-items-center w-full justify-content-between mb-2">
        {expandFor && (
          <div className="owner-header">
            <div className="d-flex flex-row gap-1 owner-header-left">
              <BackButton onClick={handleBack} />
              <SearchInput
                needClearIcon
                value={tempSearch}
                onChange={(e) => setTempSearch(e.target.value)}
                onPressEnter={handleSearch}
                handleClick={handleSearch}
                placeholder={messages['menu.showcase-searchmore']}
                handleClear={handleClearClick}
                className="mr-0 hide-owner-top-input"
              />
            </div>
            <div className="w-full d-flex flex-row justify-content-end owner-header-right">
              <MysubcriptionButton
                condition={isLoggedIn}
                active={activeTab === 2}
                onClick={handleSubscription}
              />
              <MyTenantButton
                active={myShowcases}
                condition={isLoggedIn}
                onClick={handleAccountSwitch}
                selectedTenantName={selectedTenantName}
                selectedTenantUserName={selectedTenantUserName}
              />
            </div>
          </div>
        )}
      </div>
      {/* header for showcase common  */}
      {!expandFor && (
        <div>
          <div className="mb-2 showHeadCont">
            <div className="search-cont">
              <span className="back-n-search">
                {isSubscribedTab && (
                  <BackButton onClick={() => setActiveTab(1)} />
                )}
                <SearchInput
                  id="search-sh-custom"
                  needClearIcon
                  minWidth=""
                  value={tempSearch}
                  onChange={(e) => setTempSearch(e.target.value)}
                  onPressEnter={handleSearch}
                  handleClick={handleSearch}
                  placeholder={messages['menu.showcase-searchmore']}
                  handleClear={handleClearClick}
                  className="mr-0 home-search"
                />
              </span>
              <span>
                <SelectCategory
                  history={history}
                  selectedCategories={selectedCategories}
                  optionsForCategory={optionsForCategory}
                  setSelectedCategories={setSelectedCategories}
                  className="home-search-category"
                />
              </span>
            </div>
            <Row className="btnCont">
              <MysubcriptionButton
                condition={isLoggedIn}
                active={activeTab === 2}
                onClick={handleSubscription}
              />
              <MyTenantButton
                active={myShowcases}
                condition={isLoggedIn}
                onClick={handleAccountSwitch}
                selectedTenantName={selectedTenantName}
                selectedTenantUserName={selectedTenantUserName}
              />
            </Row>
          </div>
        </div>
      )}
    </>
  );
};

const MyTenantButton = ({
  onClick,
  active,
  selectedTenantUserName,
  selectedTenantName,
  condition
}) => {
  if (condition) {
    return (
      <Card
        onClick={onClick}
        className={`btnCard ${active && 'btnCardactive'}`}
      >
        <div className="left">
          <i className="simple-icon-home mr-2" />
        </div>
        <div className="d-flex flex-column right">
          <span className="text-ellipsis mb-0 font-bolder w-full">
            <strong>{selectedTenantName}</strong>
          </span>
          {selectedTenantUserName && (
            <span className="my-0 text-ellipsis" style={{ fontSize: '10px' }}>
              @{selectedTenantUserName}
            </span>
          )}
        </div>
      </Card>
    );
  }
  return null;
};

const MysubcriptionButton = ({ onClick, active, condition }) => {
  if (condition) {
    return (
      <Card
        onClick={onClick}
        className={`btnCard ${active && 'btnCardactive'}`}
      >
        <i className="simple-icon-user-following mx-2" />
        <span>My Subscriptions</span>
      </Card>
    );
  }
  return null;
};

const ShowcaseSkeletonCard = () => {
  return (
    <div className="ContentCard">
      <div className="d-flex w-full flex-row align-items-center justify-content-center mb-2">
        <LoadingSkeleton
          circle
          style={{
            height: '150px',
            width: '150px'
          }}
        />
      </div>
      <LoadingSkeleton
        style={{ height: '20px', width: '100%' }}
        containerClassName="d-flex align-items-center justify-content-center mb-0"
      />
      <LoadingSkeleton
        style={{ height: '10px', width: '100%' }}
        containerClassName="d-flex align-items-center justify-content-center mb-0"
      />
      <LoadingSkeleton
        style={{ height: '10px', width: '100%' }}
        containerClassName="d-flex align-items-center justify-content-center mb-0"
      />
    </div>
  );
};

const Category = ({ name, backgroundColor, onClick }) => {
  return (
    <span
      className="tagglide c-pointer"
      style={{
        backgroundColor
      }}
      onClick={onClick}
    >
      {name}
    </span>
  );
};

const ShowcaseTags = ({ tags, search, setSearch }) => {
  return (
    <ScrollableTags>
      {tags.map((a) => {
        return (
          <Category
            key={a}
            name={a}
            backgroundColor={search === a ? '#ff83ef' : ''}
            onClick={() => {
              if (search === a) {
                setSearch('');
              } else {
                setSearch(a);
              }
            }}
          />
        );
      })}
    </ScrollableTags>
  );
};
