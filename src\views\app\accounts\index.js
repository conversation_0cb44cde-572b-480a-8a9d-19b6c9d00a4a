/* eslint-disable react/no-array-index-key */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable react/no-unescaped-entities */
/* eslint-disable no-unused-vars */
import { Colxx } from 'components/common/CustomBootstrap';
import { Row, Card } from 'reactstrap';
import React, { useEffect, useMemo, useState } from 'react';
import BackButton from 'components/buttons/BackButton';
import InfoIcon from 'components/icon/InfoIcon';
import { fetchUser, getTenants, setDefaultTenantApi } from 'functions/api';
import IntlMessages from 'helpers/IntlMessages';
import { getTeamUserRoleType } from 'helpers/Utils';
import SwitchButton from 'components/buttons/SwitchButton';
import InfiniteScroll from 'react-infinite-scroll-component';

const COUNT = 10;

function Accounts({ history }) {
  const [maxResultCount] = useState(COUNT);
  const [hasMore, setHasMore] = useState(false);
  const [skip, setSkip] = useState(COUNT);
  const [accounts, setAccounts] = useState([]);
  const [showLoading, setShowLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const currentUserId = JSON.parse(
    localStorage.getItem('innerloop_current_user')
  )?.uid;
  const selectedTenantId = useMemo(
    () => localStorage.getItem('selectedTenantId'),
    []
  );

  const getAccounts = async (skip = 0) => {
    try {
      if (skip === 0) {
        setIsLoading(true);
      }
      const { data } = await getTenants({
        maxResultCount,
        skipCount: skip
      });
      const accountUsers = data?.items;
      setAccounts((prev) => [...prev, ...accountUsers]);
      setSkip(skip + maxResultCount);
      if (accountUsers.length < maxResultCount) {
        setHasMore(false);
      } else {
        setHasMore(true);
      }
      if (skip === 0) {
        setIsLoading(false);
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    getAccounts();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const setDefaultTenant = async (tenantId) => {
    const isSame = tenantId === selectedTenantId;
    if (isSame) {
      return;
    }
    setShowLoading(true);
    const data = await setDefaultTenantApi(tenantId);
    const res = await fetchUser();
    if (!data?.isError && res?.data) {
      if (history.location.pathname === '/app/home') {
        history.go(0);
      } else {
        sessionStorage.setItem('reloadRequired', true);
        history.push('/app/home');
      }
    }
    setShowLoading(false);
  };

  if (isLoading) {
    return <div className="loading"> </div>;
  }

  if (showLoading) {
    return (
      <div className="screenloading">
        <div className="loading"> </div>
      </div>
    );
  }

  return (
    <Colxx>
      <Row className="justify-content-start align-items-center mb-4">
        <BackButton onClick={() => history.goBack()} />
        <h2 className="ml-3">
          <IntlMessages id="acc-directory" />
        </h2>
      </Row>
      <Colxx className="p-4">
        <Card
          className="d-flex flex-row"
          style={{
            border: '1px solid #5b5b5b40',
            borderTopRightRadius: 20,
            borderTopLeftRadius: 20,
            padding: 15,
            fontWeight: 'bolder'
          }}
        >
          <div className="centered" style={{ width: '25%' }}>
            <IntlMessages id="acc-acname" />
          </div>
          <div className="centered" style={{ width: '25%' }}>
            <IntlMessages id="acc-acemail" />
          </div>
          <div
            className="d-flex align-items-center justify-content-center"
            style={{ width: '25%' }}
          >
            <IntlMessages id="acc-my-role" />
            <InfoIcon
              message="Current logged in user’s highest role for that account"
              name="role"
              className="ml-2"
            />
          </div>
          <div
            className="d-flex align-items-center justify-content-center"
            style={{ width: '25%' }}
          >
            <IntlMessages id="acc-switch" />
            <InfoIcon
              message="Using this user can switch between the accounts"
              className="ml-2"
              name="switch"
            />
          </div>
        </Card>
        <InfiniteScroll
          dataLength={accounts.length}
          next={() => getAccounts(skip)}
          hasMore={hasMore}
          style={{ minHeight: 300 }}
          loader={
            <p
              style={{
                textAlign: 'center',
                fontSize: '14px',
                padding: '10px 0px',
                margin: '0',
                fontWeight: 'bold',
                color: '#94308A'
              }}
            >
              Loading...
            </p>
          }
          endMessage={
            <p
              style={{
                textAlign: 'center',
                fontSize: '14px',
                padding: '10px 0px',
                margin: '0',
                fontWeight: 'bold',
                color: '#94308A'
              }}
            >
              <IntlMessages id="seen-all" />
            </p>
          }
        >
          {accounts.map((j, i) => (
            <DataRow
              key={i.toString()}
              item={j}
              index={i}
              isLast={i + 1 === accounts.length}
              currentUserId={currentUserId}
              setDefaultTenant={setDefaultTenant}
              selectedTenantId={selectedTenantId}
            />
          ))}
        </InfiniteScroll>
      </Colxx>
    </Colxx>
  );
}

const DataRow = ({
  item,
  currentUserId,
  setDefaultTenant,
  isLast,
  selectedTenantId
}) => {
  const amIOwner = item.ownerId === currentUserId;
  console.log({
    selectedTenantId,
    ternantId: item.id,
    condition: selectedTenantId !== item.id
  });
  return (
    <Card
      className="d-flex flex-row"
      style={{
        border: '1px solid #5b5b5b40',
        borderBottomRightRadius: isLast ? 20 : 0,
        borderBottomLeftRadius: isLast ? 20 : 0,
        borderTop: 0,
        padding: 15
      }}
    >
      <div className="centered text-ellipsis" style={{ width: '25%' }}>
        {item.name}
      </div>
      <div className="centered" style={{ width: '25%' }}>
        {item.email ?? '-'}
      </div>
      <div className="centered" style={{ width: '25%' }}>
        {getTeamUserRoleType(item?.roleType)}
      </div>
      <div className="centered" style={{ width: '25%' }}>
        {selectedTenantId !== item.id && (
          <SwitchButton
            name="Switch"
            onClick={() => setDefaultTenant(item.id)}
          />
        )}
      </div>
    </Card>
  );
};

export default Accounts;
