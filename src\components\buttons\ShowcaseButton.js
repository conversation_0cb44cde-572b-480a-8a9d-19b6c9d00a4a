import React, { useState } from 'react';
import { Button } from 'reactstrap';

function ShowcaseButton({ isShowcased = false, handleShowcase }) {
  const [hovered, setHovered] = useState(false);

  return (
    <span
      style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center'
      }}
    >
      <Button
        color={isShowcased && 'primary'}
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
        style={{
          height: '3rem',
          width: '3rem',
          marginRight: '1rem'
        }}
        onClick={handleShowcase}
        className={
          !isShowcased && 'badge-outline-primary btn btn-outline-primary'
        }
      >
        <span style={{ position: 'relative', left: '-5px' }}>
          <ShowcaseIcon active={isShowcased} h={16} w={16} hovered={hovered} />
        </span>
      </Button>
      <span style={{ left: '-10px', position: 'relative', color: '#992288' }}>
        Showcase{isShowcased && 'd'}
      </span>
    </span>
  );
}

export default ShowcaseButton;

function ShowcaseIcon({ active, h = 30, w = 30, hovered }) {
  const fill = active || hovered ? '#fff' : '#992288';

  return (
    <svg
      width={w}
      height={h}
      viewBox="0 0 50 50"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_179_3)">
        <path
          d="M44.6107 13.8889H5.38845C4.69595 13.8889 4.0318 14.164 3.54212 14.6537C3.05244 15.1434 2.77734 15.8075 2.77734 16.5V41.8334C2.77734 42.5259 3.05244 43.19 3.54212 43.6797C4.0318 44.1694 4.69595 44.4445 5.38845 44.4445H44.6107C45.3032 44.4445 45.9673 44.1694 46.457 43.6797C46.9467 43.19 47.2218 42.5259 47.2218 41.8334V16.5C47.2218 15.8075 46.9467 15.1434 46.457 14.6537C45.9673 14.164 45.3032 13.8889 44.6107 13.8889ZM44.444 41.6667H5.55512V16.6667H44.444V41.6667Z"
          fill={fill}
        />
        <path
          d="M11.8893 27.0139C12.7134 27.0139 13.519 26.7695 14.2042 26.3117C14.8894 25.8538 15.4235 25.2031 15.7388 24.4417C16.0542 23.6804 16.1367 22.8426 15.9759 22.0343C15.8152 21.2261 15.4183 20.4837 14.8356 19.9009C14.2529 19.3182 13.5105 18.9214 12.7022 18.7606C11.8939 18.5998 11.0562 18.6823 10.2948 18.9977C9.53345 19.3131 8.88271 19.8471 8.42487 20.5323C7.96703 21.2175 7.72266 22.0231 7.72266 22.8472C7.72266 23.9523 8.16164 25.0121 8.94304 25.7935C9.72445 26.5749 10.7843 27.0139 11.8893 27.0139ZM11.8893 20.625C12.331 20.6139 12.766 20.7348 13.1387 20.9722C13.5114 21.2096 13.8048 21.5526 13.9816 21.9576C14.1583 22.3626 14.2103 22.811 14.131 23.2457C14.0516 23.6803 13.8444 24.0815 13.536 24.3978C13.2275 24.7142 12.8318 24.9314 12.3993 25.0218C11.9667 25.1121 11.5171 25.0715 11.1078 24.9051C10.6985 24.7386 10.3481 24.4539 10.1014 24.0874C9.85465 23.7208 9.72279 23.2891 9.72266 22.8472C9.72247 22.2673 9.94896 21.7104 10.3538 21.2952C10.7586 20.88 11.3096 20.6395 11.8893 20.625Z"
          fill={fill}
        />
        <path
          d="M10.9727 38.8889L19.306 30.5556L23.7227 34.9722L19.806 38.8889H22.5838L32.9449 28.5278L41.6671 37.1806V34.4028L33.6115 26.3889C33.4268 26.2057 33.1772 26.1029 32.9171 26.1029C32.657 26.1029 32.4074 26.2057 32.2227 26.3889L25.056 33.5556L19.9588 28.4722C19.7741 28.289 19.5245 28.1862 19.2643 28.1862C19.0042 28.1862 18.7546 28.289 18.5699 28.4722L8.22266 38.8889H10.9727Z"
          fill={fill}
        />
        <path
          d="M41.8607 4.16672C41.8607 3.79836 41.7143 3.4451 41.4539 3.18463C41.1934 2.92416 40.8401 2.77783 40.4718 2.77783H9.91623C9.54788 2.77783 9.19461 2.92416 8.93414 3.18463C8.67367 3.4451 8.52734 3.79836 8.52734 4.16672V5.55561H41.8607V4.16672Z"
          fill={fill}
        />
        <path
          d="M44.6115 9.72226C44.6115 9.35391 44.4652 9.00064 44.2047 8.74017C43.9443 8.4797 43.591 8.33337 43.2227 8.33337H7.11154C6.74319 8.33337 6.38992 8.4797 6.12945 8.74017C5.86899 9.00064 5.72266 9.35391 5.72266 9.72226V11.1112H44.6115V9.72226Z"
          fill={fill}
        />
      </g>
      <defs>
        <clipPath id="clip0_179_3">
          <rect width="50" height="50" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
