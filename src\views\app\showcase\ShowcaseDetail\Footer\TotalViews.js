import { convertNumberToShort, isDarkModeActive } from 'helpers/Utils';
import React from 'react';

const TotalViews = ({ count, className = '' }) => {
  return (
    <div
      style={{ background: isDarkModeActive() ? '#9922872d' : '#F0F0F0' }}
      className={`total-views ${className}`}
    >
      <h6 className=" mb-0">{count ? convertNumberToShort(count) : 0}</h6>
      <i className="simple-icon-eye ml-1" />
    </div>
  );
};

export default TotalViews;
