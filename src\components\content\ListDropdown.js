import { adminRoot } from 'constants/defaultValues';
import IntlMessages from 'helpers/IntlMessages';
import React, { useState } from 'react';
// eslint-disable-next-line import/no-extraneous-dependencies, import/no-unresolved
import * as DropdownMenu from '@radix-ui/react-dropdown-menu';
import { isYoutubeORVimeoSource, RoleTypes } from 'helpers/Utils';
import Review from '../../assets/img/content/Review.svg';
import { getPreviewByContentId } from 'functions/api/spacesApi';
import { NotificationManager } from 'components/common/react-notifications';

export default function ListDropdown({
  // isImageModal,
  // isRemoteFile,
  getStatusText,
  product,
  handleDelete,
  handleEdit,
  handleWatermarkChange,
  setDraftWarning,
  handleReady,
  setEntity,
  handleRestart,
  history,
  toggleShowCaseModal,
  setReviewModalOpen,
  roleType,
  setFlagModalOpen,
  isShowcased,
  handleShare,
  workFlowEnabled,
  optimizationEnabled
}) {
  const [isDownloading, setIsDownloading] = useState(false);
  const isVideo = +product.mediaType === 1;
  const statusText = getStatusText({
    status: product?.status,
    isStreaming: product?.streaming
  });

  const handleReview = () => {
    setReviewModalOpen(true);
    setEntity(product);
  };

  const handleFlag = () => {
    setFlagModalOpen(true);
    setEntity(product);
  };

  const handleDownloadGIF = async () => {
    try {
      setIsDownloading(true);
      const { data, isError } = await getPreviewByContentId(
        product.spaceId,
        product.id,
        {
          mode: 1,
          type: 2,
          index: 1
        },
        false
      );
      console.log('data', data);
      if (!isError && data) {
        const newUrl = `${data}?download=1`;
        window.open(newUrl);
      } else {
        NotificationManager.error('Something went wrong while downloading GIF');
      }
      setIsDownloading(false);
    } catch (error) {
      setIsDownloading(false);
      console.error(error);
    }
  };

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger asChild>
        <i
          className="simple-icon-options-vertical cursor-pointer"
          style={{ fontSize: '1rem' }}
        />
      </DropdownMenu.Trigger>
      <DropdownMenu.Portal>
        <DropdownMenu.Content
          className="DropdownMenuContent dropdown-radix"
          sideOffset={5}
        >
          {(statusText === 'PROCESSING' || statusText === 'OPTIMIZING') && (
            <FlagItem roleType={roleType} handleFlag={handleFlag} />
          )}
          {statusText === 'READY' && (
            <>
              <DropdownMenu.Item
                onClick={handleEdit}
                className="dot-dropitem-radix"
              >
                <i className="simple-icon-drawer mr-2" />
                <span>Detail</span>
              </DropdownMenu.Item>
              <ReviewItem
                workFlowEnabled={workFlowEnabled}
                roleType={roleType}
                handleReview={handleReview}
              />
              {roleType <= RoleTypes.CONTENT_MANAGER && (
                <DropdownMenu.Item
                  onClick={toggleShowCaseModal}
                  className="dot-dropitem-radix"
                >
                  <img
                    height={16}
                    width={16}
                    alt="showcase-icon"
                    src="/assets/icons/showcase.svg"
                    className="mr-2"
                  />
                  <span>Showcase{isShowcased && 'd'}</span>
                </DropdownMenu.Item>
              )}
              <WatermarkItem
                isVideo={isVideo}
                product={product}
                optimizationEnabled={optimizationEnabled}
                handleWatermarkChange={handleWatermarkChange}
              />
              {roleType <= RoleTypes.CONTENT_MANAGER && (
                <DropdownMenu.Item
                  className="dot-dropitem-radix"
                  onClick={() => setDraftWarning(true)}
                >
                  <i className="simple-icon-note mr-2" />
                  <span>Draft</span>
                </DropdownMenu.Item>
              )}
              {roleType <= RoleTypes.CONTENT_MANAGER && (
                <DropdownMenu.Item
                  className="dot-dropitem-radix"
                  onClick={handleShare}
                >
                  <i className="simple-icon-share mr-2" />
                  <span>{product?.shareId ? 'Shared' : 'Share'}</span>
                </DropdownMenu.Item>
              )}

              <FlagItem roleType={roleType} handleFlag={handleFlag} />
              <DeleteItem roleType={roleType} handleDelete={handleDelete} />

              {isVideo &&
                !isYoutubeORVimeoSource(product?.contentSource) &&
                roleType <= RoleTypes.CONTENT_MANAGER && (
                  <DownloadGIF
                    onClick={handleDownloadGIF}
                    disabled={isDownloading}
                  />
                )}
            </>
          )}
          {statusText === 'DRAFT' && (
            <>
              <DropdownMenu.Item
                onClick={handleEdit}
                className="dot-dropitem-radix"
              >
                <i className="simple-icon-pencil mr-2" />
                <span>Edit</span>
              </DropdownMenu.Item>
              <ReviewItem
                workFlowEnabled={workFlowEnabled}
                roleType={roleType}
                handleReview={handleReview}
              />
              <WatermarkItem
                isVideo={isVideo}
                product={product}
                optimizationEnabled={optimizationEnabled}
                handleWatermarkChange={handleWatermarkChange}
              />
              {roleType <= RoleTypes.CONTENT_MANAGER && (
                <DropdownMenu.Item
                  className="dot-dropitem-radix"
                  onClick={handleReady}
                >
                  <i className="simple-icon-check mr-2" />
                  <span>Ready</span>
                </DropdownMenu.Item>
              )}
              <FlagItem roleType={roleType} handleFlag={handleFlag} />
              <DeleteItem roleType={roleType} handleDelete={handleDelete} />
            </>
          )}
          {statusText === 'FAILED' && (
            <>
              <DropdownMenu.Item
                onClick={handleRestart}
                className="dot-dropitem-radix"
              >
                <i className="iconsminds-repeat-6 mr-1" />
                <span>Restart</span>
              </DropdownMenu.Item>
              <FlagItem roleType={roleType} handleFlag={handleFlag} />
              <DeleteItem roleType={roleType} handleDelete={handleDelete} />
            </>
          )}
          <HelpItem product={product} history={history} />
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
}

const HelpItem = ({ product, history }) => {
  return (
    <DropdownMenu.Item
      className="dot-dropitem-radix"
      onClick={() =>
        history.push(
          `${adminRoot}/help-support?id=${product.id}&title=${
            product?.name ?? product.title
          }`
        )
      }
    >
      <i style={{ fontSize: '1em' }} className="iconsminds-support mr-1" />
      <span>
        <IntlMessages id="menu.support" />
      </span>
    </DropdownMenu.Item>
  );
};

const DeleteItem = ({ handleDelete, roleType }) => {
  if (roleType <= RoleTypes.CONTENT_MANAGER) {
    return (
      <DropdownMenu.Item onClick={handleDelete} className="dot-dropitem-radix">
        <i className="simple-icon-trash mr-2" />
        <span>Delete</span>
      </DropdownMenu.Item>
    );
  }
  return null;
};

const FlagItem = ({ roleType, handleFlag }) => {
  if (roleType <= RoleTypes.CONTRIBUTOR) {
    return (
      <DropdownMenu.Item onClick={handleFlag} className="dot-dropitem-radix">
        <i className="simple-icon-flag mr-2" />
        <span>Flag</span>
      </DropdownMenu.Item>
    );
  }
  return null;
};

const WatermarkItem = ({
  isVideo,
  product,
  handleWatermarkChange,
  optimizationEnabled
}) => {
  if (
    isVideo &&
    product.optimization &&
    optimizationEnabled &&
    process.env.REACT_APP_ENV === 'DEV'
  ) {
    return (
      <DropdownMenu.Item
        className="dot-dropitem-radix"
        onClick={() => handleWatermarkChange(product.id)}
      >
        <i className="iconsminds-id-card mr-1" />
        <span>Watermark</span>
      </DropdownMenu.Item>
    );
  }
  return null;
};

const ReviewItem = ({ workFlowEnabled, roleType, handleReview }) => {
  if (workFlowEnabled && roleType <= RoleTypes.CONTRIBUTOR) {
    return (
      <DropdownMenu.Item onClick={handleReview} className="dot-dropitem-radix">
        <img
          height={15}
          width={15}
          alt="review-icon"
          src={Review}
          className="mr-2"
        />
        <span>Review</span>
      </DropdownMenu.Item>
    );
  }
  return null;
};

const DownloadGIF = ({ onClick, disabled }) => {
  return (
    <DropdownMenu.Item
      aria-disabled={disabled}
      className="dot-dropitem-radix"
      onClick={(e) => {
        e.stopPropagation();
        e.preventDefault();
        onClick();
      }}
    >
      <svg
        width="18"
        height="18"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M15.4 3C16.786 3 17.92 4.187 17.996 5.682L18 5.844V14.156C18 15.673 16.915 16.912 15.547 16.996L15.4 17H4.6C3.214 17 2.08 15.813 2.004 14.318L2 14.156V5.844C2 4.327 3.085 3.088 4.452 3.004L4.6 3H15.4ZM15.25 4H4.75C3.824 4 3.066 4.793 3.004 5.797L3 5.929V14.071C3 15.092 3.72 15.928 4.63 15.996L4.75 16H15.25C16.176 16 16.934 15.207 16.996 14.204L17 14.07V5.93C17 4.909 16.28 4.073 15.37 4.005L15.25 4ZM6.851 7.002C7.471 7.00067 7.998 7.10033 8.432 7.301C8.699 7.425 8.806 7.725 8.672 7.972C8.60099 8.09342 8.48688 8.18367 8.35236 8.2248C8.21785 8.26594 8.07278 8.25494 7.946 8.194C7.678 8.069 7.314 8.002 6.851 8.002C5.825 8.002 5.081 8.834 5.081 10.002C5.081 11.121 5.894 12 6.851 12C7.448 12 7.853 11.649 7.911 11.327L7.919 11.247V10.501L7.54 10.5C7.242 10.5 7 10.276 7 10C7 9.755 7.191 9.55 7.443 9.508L7.541 9.5L8.459 9.501C8.58139 9.49737 8.70126 9.53623 8.79824 9.61097C8.89522 9.68571 8.96333 9.79173 8.991 9.911L9 10.001V11.247C9 12.124 8.114 13 6.851 13C5.256 13 4 11.642 4 10.001C4 8.32 5.177 7.002 6.851 7.002ZM11 7C11.117 6.99996 11.2304 7.04097 11.3203 7.11589C11.4102 7.19081 11.4709 7.29489 11.492 7.41L11.5 7.5V12.5C11.5002 12.6249 11.4537 12.7455 11.3695 12.8378C11.2853 12.9301 11.1696 12.9876 11.0452 12.9989C10.9207 13.0102 10.7966 12.9745 10.6972 12.8988C10.5977 12.8231 10.5303 12.7129 10.508 12.59L10.5 12.5V7.5C10.5 7.36739 10.5527 7.24021 10.6464 7.14645C10.7402 7.05268 10.8674 7 11 7ZM15.5 7C15.6249 6.99977 15.7455 7.04633 15.8378 7.13051C15.9301 7.21469 15.9876 7.33039 15.9989 7.45482C16.0102 7.57926 15.9745 7.70341 15.8988 7.80283C15.8231 7.90225 15.7129 7.96974 15.59 7.992L15.5 8H14V10H15.5C15.6249 9.99977 15.7455 10.0463 15.8378 10.1305C15.9301 10.2147 15.9876 10.3304 15.9989 10.4548C16.0102 10.5793 15.9745 10.7034 15.8988 10.8028C15.8231 10.9023 15.7129 10.9697 15.59 10.992L15.5 11H14V12.5C14.0002 12.6249 13.9537 12.7455 13.8695 12.8378C13.7853 12.9301 13.6696 12.9876 13.5452 12.9989C13.4207 13.0102 13.2966 12.9745 13.1972 12.8988C13.0977 12.8231 13.0303 12.7129 13.008 12.59L13 12.5V7.5C13 7.36739 13.0527 7.24021 13.1464 7.14645C13.2402 7.05268 13.3674 7 13.5 7H15.5Z"
          fill="#62677C"
        />
      </svg>
      <span>Download GIF</span>
    </DropdownMenu.Item>
  );
};
