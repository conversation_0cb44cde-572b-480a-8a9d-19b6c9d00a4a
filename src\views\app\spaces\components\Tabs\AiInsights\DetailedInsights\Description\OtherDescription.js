/* eslint-disable no-unused-vars */
import AiButton from 'components/buttons/AiButton';
import { Colxx } from 'components/common/CustomBootstrap';
import { NotificationManager } from 'components/common/react-notifications';
import { getYoutubeInsights, getImageInsights } from 'functions/api/analyze';
import IntlMessages from 'helpers/IntlMessages';
import useCounter from 'hooks/useCounter';
import React, { useState } from 'react';
import { Row } from 'reactstrap';
import BodyInputs from './BodyInputs';
import ConfirmationModal from 'components/common/ConfirmationModal';
import RefreshButton from 'components/buttons/RefreshButton';

const OtherDescription = ({
  content,
  contentIds,
  isStory,
  getContent,
  isYoutubeContent,
  selectedTenantId,
  roleType,
  isImage
}) => {
  const { id: contentId, spaceId, mediaType } = content;
  const { count, reset, start, stop } = useCounter();

  const [isGeneratingImgInsights, setIsgeneratingImgInsights] = useState(false);
  const [isGeneratingYtInsights, setIsgeneratingYtInsights] = useState(false);
  const [isCheckingYtInsights, setIsCheckingYtInsights] = useState(false);
  const [isCheckingImageInsights, setIsCheckingImageInsights] = useState(false);

  const title = content?.metadata?.TitleAuto;
  const description = content?.metadata?.SummaryAuto;
  const tags = content?.metadata?.TagsAuto;

  const [openconfirm, setOpenConfirm] = useState(false);
  const toggle = () => setOpenConfirm(!openconfirm);
  const [showCheckStatus, setShowCheckStatus] = useState(false);

  const handleImageInsights = async (isChecking = false) => {
    const check = (val) => {
      if (isChecking) {
        setIsCheckingImageInsights(val);
      } else {
        setIsgeneratingImgInsights(val);
      }
    };

    try {
      if (!isChecking) {
        reset();
        start();
      }
      check(true);
      const { data, isError } = await getImageInsights({
        contentId: isStory ? contentIds : contentId,
        spaceId,
        storyId: isStory ? contentId : null,
        wasLoadingBecauseItWasProcessing: showCheckStatus
      });
      if (isError) {
        NotificationManager.error(
          'Something went wrong while generating insights'
        );
      } else {
        if (data === 'SUCCESS' || data === 'RELOAD') {
          await getContent(true);
          reset();
          console.log('success');
          setShowCheckStatus(false);
          setIsgeneratingImgInsights(false);
          setIsCheckingImageInsights(false);
          stop();
          return;
        }
        if (data === 'STARTED' || data === 'OTHER_PROCESSING') {
          setShowCheckStatus(true);
          setIsgeneratingImgInsights(true);
          setIsCheckingImageInsights(false);
          NotificationManager.info('Generating Insights.Please wait');
          return;
        }
        if (data === 'ERROR') {
          NotificationManager.error(
            'Something went wrong while generating insights'
          );
        }
      }
      if (!isChecking) {
        reset();
      }
      check(false);
    } catch (error) {
      if (!isChecking) {
        reset();
      }
      console.error(error);
      check(false);
    }
  };

  const handleYtImageInsights = async (isChecking = false) => {
    const check = (val) => {
      if (isChecking) {
        setIsCheckingYtInsights(val);
      } else {
        setIsgeneratingYtInsights(val);
      }
    };
    try {
      if (!isChecking) {
        start();
      }
      check(true);
      const { isError, data } = await getYoutubeInsights({
        contentId,
        spaceId,
        wasLoadingBecauseItWasProcessing: showCheckStatus
      });
      if (data === 'SUCCESS' || data === 'RELOAD') {
        await getContent(true);
        setShowCheckStatus(false);
        setIsCheckingYtInsights(false);
        setIsgeneratingYtInsights(false);
        stop();
        reset();
        return;
      }
      if (isError || data === 'ERROR') {
        NotificationManager.error(
          'Something went wrong while generating insights'
        );
      }
      if (data === 'STARTED' || data === 'OTHER_PROCESSING') {
        setShowCheckStatus(true);
        NotificationManager.info('Generating Insights.Please wait');
        setIsCheckingYtInsights(false);
        setIsgeneratingYtInsights(true);
        return;
      }
      if (!isChecking) {
        reset();
      }
      check(false);
    } catch (error) {
      if (!isChecking) {
        reset();
      }
      check(false);
      console.error('error', error);
    }
  };

  const ytCondition = isYoutubeContent && !isStory;
  const imgStoryCondition = isImage || isStory;

  const disabled = (() => {
    return (
      (ytCondition && description && isGeneratingYtInsights) ||
      (imgStoryCondition && description && isGeneratingImgInsights)
    );
  })();

  const isRefreshing = isCheckingYtInsights || isCheckingImageInsights;

  return (
    <>
      <Row
        // aria-disabled={disabled}
        className="m-0 d-flex flex-row w-full align-items-center justify-content-between"
      >
        <p className="font-bolder mb-0" style={{ fontSize: 16 }}>
          AI Description
        </p>
        <div className="d-flex flex-row align-items-center justify-content-end">
          {showCheckStatus && (
            <RefreshButton
              text={isRefreshing ? 'Checking Status' : 'Check Status'}
              isLoading={isRefreshing}
              disabled={isRefreshing}
              handleRefresh={() => {
                if (ytCondition) {
                  handleYtImageInsights(true);
                }
                if (imgStoryCondition) {
                  handleImageInsights(true);
                }
              }}
            />
          )}
          {ytCondition && (
            <AiButton
              showCount
              count={isGeneratingYtInsights ? count : 0}
              isLoading={isGeneratingYtInsights}
              disabled={isGeneratingYtInsights}
              text="Generate Insights"
              onClick={() => {
                if (description) {
                  toggle();
                } else {
                  handleYtImageInsights();
                }
              }}
            />
          )}
          {imgStoryCondition && (
            <AiButton
              showCount
              count={isGeneratingImgInsights ? count : 0}
              isLoading={isGeneratingImgInsights}
              disabled={isGeneratingImgInsights}
              text="Generate Insights"
              onClick={() => {
                if (description) {
                  toggle();
                } else {
                  handleImageInsights();
                }
              }}
            />
          )}
        </div>
      </Row>
      {isGeneratingYtInsights && (
        <Colxx className="mt-2">
          <p className="text-primary text-align-center mb-0">
            <IntlMessages id="hang-tight" />
          </p>
        </Colxx>
      )}
      {description && !disabled && (
        <BodyInputs
          disabled={disabled}
          isStory={isStory}
          content={content}
          roleType={roleType}
          selectedTenantId={selectedTenantId}
        />
      )}
      <ConfirmationModal
        openConfirmationModal={openconfirm}
        toggleConfirmationModal={toggle}
        handleConfirm={() => {
          if (ytCondition) {
            handleYtImageInsights();
          }
          if (imgStoryCondition) {
            handleImageInsights();
          }
          toggle();
        }}
        handleClose={toggle}
        type="confirmDetailedInsights"
      />
    </>
  );
};

export default OtherDescription;
