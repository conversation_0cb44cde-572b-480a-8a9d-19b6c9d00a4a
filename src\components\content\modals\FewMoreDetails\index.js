/* eslint-disable no-extra-boolean-cast */
/* eslint-disable no-unused-vars */
import LoadingButton from 'components/buttons/LoadingButton';
import { Formik, Form, Field } from 'formik';
import IntlMessages from 'helpers/IntlMessages';
import { isInvitedUser, validateInput } from 'helpers/Utils';
import React, { useState } from 'react';
import { FormGroup, Label, Modal, ModalBody } from 'reactstrap';
import IndustryList from 'data/IndustryList';
import HearAboutOptions from './HearAboutOptions';
import { handelProfileUpdate } from 'functions/api';
import { NotificationManager } from 'components/common/react-notifications';

const FewMoreDetails = ({ open, toggle }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [industry] = useState();
  const [others] = useState();
  const [channel] = useState();
  const isInvited = isInvitedUser();

  const handleClose = () => {
    toggle();
  };

  const handleNext = async (values) => {
    try {
      setIsSubmitting(true);
      const industryValue =
        values.industry === 'Others' ? values.others : values.industry;
      const hearAboutUsValue = isInvited ? 'Invitation' : values.channel;

      const { data } = await handelProfileUpdate({
        Industry: industryValue,
        heardAboutUs: hearAboutUsValue
      });
      if (data.isError) {
        NotificationManager.error(<IntlMessages id="few-more-error" />);
      } else {
        NotificationManager.success(<IntlMessages id="few-more-success" />);
        localStorage.removeItem('invitationId');
        const localTenantDetails = JSON.parse(
          localStorage.getItem('tenantDetails')
        );
        const newTenantDetails = {
          ...localTenantDetails.data,
          selectedTenant: {
            ...localTenantDetails.data.selectedTenant,
            industry: industryValue
          }
        };
        localStorage.setItem(
          'tenantDetails',
          JSON.stringify({
            data: newTenantDetails,
            isError: false
          })
        );
        toggle();
      }
      setIsSubmitting(false);
    } catch (error) {
      setIsSubmitting(false);
    }
  };

  const initialValues = {
    others: '',
    industry: '',
    channel: isInvited ? 'Invitation' : ''
  };

  return (
    <Modal
      isOpen={open}
      toggle={handleClose}
      contentClassName="border-radius-10"
      centered
      style={{
        minWidth: '50%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        borderRadius: 20
      }}
      backdrop="static"
    >
      <ModalBody
        className="m-3 border-radius-10 d-flex align-items-center flex-column"
        style={{ border: '1px solid #992288' }}
      >
        <h3 className="mb-0 font-bolder text-center mb-4">
          We just need few more details then it should be all good.
        </h3>
        <Formik
          onSubmit={(values) => {
            console.log('values', values);
            handleNext(values);
          }}
          initialValues={initialValues}
        >
          {({ errors, touched, values }) => {
            return (
              <Form className="av-tooltip tooltip-label-bottom w-90 mt-4">
                {/* INDUSTRY  */}
                <div>
                  <p className="mb-0 font-bolder mb-3">
                    Which Industry do you belong to?
                  </p>
                  <FormGroup className="form-group has-float-label">
                    <Label>
                      <IntlMessages id="user.industry" />
                      <span style={{ color: '#922c88' }}> *</span>
                    </Label>
                    <Field
                      className="form-control"
                      as="select"
                      name="industry"
                      value={industry}
                      defaultValue="Select Industry"
                      validate={(val) => {
                        if (!val) {
                          return 'Industry cannot be empty';
                        }
                        return '';
                      }}
                    >
                      <IndustryList />
                    </Field>
                    {errors.industry && touched.industry && (
                      <div className="invalid-feedback d-block">
                        {errors.industry}
                      </div>
                    )}
                  </FormGroup>

                  {/* OTHERS  */}
                  {values.industry === 'Others' && (
                    <FormGroup className="form-group has-float-label">
                      <Label>
                        <IntlMessages id="Industry Name" />
                        <span style={{ color: '#922c88' }}> *</span>
                      </Label>
                      <Field
                        className="form-control"
                        type="text"
                        name="others"
                        value={others}
                        validate={(val) => {
                          if (!val) {
                            return 'Industry name cannot be empty';
                          }
                          if (val.length > 20) {
                            return 'Industry name cannot be this Large';
                          }
                          return '';
                        }}
                      />
                      {errors.others && touched.others && (
                        <div className="invalid-feedback d-block">
                          {errors.others}
                        </div>
                      )}
                    </FormGroup>
                  )}
                </div>

                {/* HEAR ABOUT US  */}
                <div className="mt-5 mb-5">
                  <p className="mb-0 font-bolder mb-3">
                    Where did you hear about us?
                  </p>
                  <FormGroup
                    className="form-group has-float-label"
                    aria-disabled={isInvited}
                  >
                    <Field
                      className="form-control"
                      as="select"
                      name="channel"
                      value={isInvited ? 'Invitation' : channel}
                      defaultValue="Select Your Choice"
                      validate={(val) => {
                        if (!val) {
                          return 'Please select where do you hear about us';
                        }
                        return '';
                      }}
                    >
                      <HearAboutOptions />
                    </Field>
                    {errors.channel && touched.channel && (
                      <div className="invalid-feedback d-block">
                        {errors.channel}
                      </div>
                    )}
                  </FormGroup>
                </div>

                {/* NEXT BUTTON  */}
                <div className="w-full centered mt-4">
                  <LoadingButton
                    type="submit"
                    disabled={isSubmitting}
                    loading={isSubmitting}
                    text="Next"
                  />
                </div>
              </Form>
            );
          }}
        </Formik>
      </ModalBody>
    </Modal>
  );
};

export default FewMoreDetails;
