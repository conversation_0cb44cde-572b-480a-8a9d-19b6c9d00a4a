/* eslint-disable jsx-a11y/label-has-associated-control */
import React from 'react';

const TimeInput = ({
  label,
  required,
  onChange,
  value,
  className = '',
  labelStyle = {}
}) => {
  return (
    <div className={`d-flex flex-column h-full TimeInput w-full ${className}`}>
      <label style={{ ...labelStyle }}>
        {label}
        {required && <span style={{ color: 'red' }}>*</span>}
      </label>
      <div className="d-flex flex-row">
        <input
          className="form-control h-full px-2 tiemInputStyle"
          type="text"
          name="time"
          value={value}
          onChange={(e) => {
            const val = e.target.value;
            onChange(val);
          }}
        />
        <span
          className="centered text-primary c-pointer"
          style={{
            width: 30,
            border: '1px solid #992288',
            fontSize: 13
          }}
        >
          <i className="simple-icon-clock" />
        </span>
      </div>
    </div>
  );
};

export default TimeInput;
