import React from 'react';
import GlideComponent from 'components/carousel/GlideComponent';

export default function RegistrationSlider() {
  const timeObj = {
    timeOut: 500,
    className: 'fade',
  };
  const slideSettings = {
    type: 'carousel',
    gap: 30,
    perView: 1,
    hideNav: true,
    peek: { before: 10, after: 10 },
    // focusAt: 'center',
    breakpoints: {
      600: { perView: 1 },
      992: { perView: 1 },
      1200: { perView: 1 },
    },
    customSlideAnimation: { timeObj },
  };

  const slideItems = [
    {
      icon: 'iconsminds-mouse-3',
      title: 'some title',
      id: 0,
    },
    {
      icon: 'iconsminds-electric-guitar',
      title: 'Video Player',
      id: 1,
    },
    {
      icon: 'iconsminds-keyboard',
      title: 'Keyboard Shortcuts',
      id: 2,
    },
  ];
  return (
    <GlideComponent settings={slideSettings}>
      {slideItems.map((item) => (
        <div
          key={item.id}
          style={{
            width: '110px',
            height: '350px',
            background: '#C4C4C4',
            borderRadius: '15px',
          }}
        />
      ))}
    </GlideComponent>
  );
}
