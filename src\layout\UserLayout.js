import React, { useEffect } from 'react';
import { withRouter } from 'react-router-dom';

const UserLayout = ({ children, onInvitePage }) => {
  useEffect(() => {
    document.body.classList.add('background');
    document.body.classList.add('no-footer');
    localStorage.removeItem('tokenExpired');
    return () => {
      document.body.classList.remove('background');
      document.body.classList.remove('no-footer');
    };
  }, []);

  return (
    <>
      {!onInvitePage && <div className="fixed-background" />}
      {onInvitePage && <div className="inviteBackground" />}

      <main>
        <div className="container">{children}</div>
      </main>
    </>
  );
};

export default withRouter(UserLayout);
