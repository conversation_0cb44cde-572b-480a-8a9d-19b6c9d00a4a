/* eslint-disable no-unused-vars */
/* eslint-disable no-nested-ternary */

import { ColorInput } from 'components/input';
import IntlMessages from 'helpers/IntlMessages';
import { Button, Card } from 'reactstrap';
import React, { useState } from 'react';
import ColorChooserIcon from 'constants/ColorChooserIcon';
import { getTextColorFromBgColor } from 'helpers/Utils';
import e from 'cors';

const ShareButton = ({
  onClick,
  themeColors,
  expandFor,
  isMyShowcase,
  setThemeColors,
  orgThemeColors,
  setOrgThemeColors
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const color = themeColors.shareButtonColor;

  return (
    <span
      className="c-pointer d-flex align-items-center justify-content-center position-relative"
      style={{
        border: `1px solid ${color}`,
        color,
        background: isHovered ? 'transparent' : color,
        height: 40,
        width: 42,
        borderRadius: 20
      }}
      onClick={() => {
        if (!isOpen) {
          onClick();
        }
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {expandFor && isMyShowcase && (
        <ColorInput
          open={isOpen}
          setOpen={setIsOpen}
          themeColors={themeColors}
          setOrgThemeColors={setOrgThemeColors}
          orgThemeColors={orgThemeColors}
          color={themeColors.shareButtonColor}
          setThemeColors={setThemeColors}
          setColor={(c) => {
            setThemeColors({
              ...themeColors,
              shareButtonColor: c
            });
          }}
          style={{ top: -15, right: 0, position: 'absolute' }}
        />
      )}
      <svg
        width="16"
        height="16"
        viewBox="0 0 20 20"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fill={isHovered ? color : '#fff'}
          d="M15.815 0C17.575 0 19 1.45 19 3.235s-1.424 3.234-3.185 3.234a3.155 3.155 0 0 1-2.378-1.084l-6.284 3.44c.14.364.216.76.216 1.175 0 .542-.13 1.052-.363 1.501l6.008 3.725a3.177 3.177 0 0 1 2.801-1.695c1.76 0 3.185 1.45 3.185 3.234C19 18.55 17.576 20 15.815 20c-1.76 0-3.184-1.45-3.184-3.235l.003-.138-6.53-4.046a3.138 3.138 0 0 1-1.92.654C2.425 13.235 1 11.785 1 10s1.424-3.235 3.185-3.235c.852 0 1.626.34 2.197.893l6.382-3.493a3.282 3.282 0 0 1-.133-.93C12.63 1.45 14.055 0 15.815 0Zm0 14.926c-.992 0-1.8.822-1.8 1.84 0 1.017.808 1.839 1.8 1.839.993 0 1.8-.822 1.8-1.84 0-1.017-.807-1.839-1.8-1.839ZM4.185 8.161c-.993 0-1.8.822-1.8 1.839s.807 1.84 1.8 1.84c.992 0 1.8-.823 1.8-1.84 0-1.017-.808-1.84-1.8-1.84Zm11.63-6.766c-.992 0-1.8.822-1.8 1.84 0 1.017.808 1.839 1.8 1.839.993 0 1.8-.822 1.8-1.84 0-1.017-.807-1.839-1.8-1.839Z"
        />
      </svg>
    </span>
  );
};

const EditButton = ({
  onClick,
  label,
  iconClassName,
  children,
  isThemePicker,
  bgColor,
  disabled = false
}) => {
  return (
    <Card
      aria-disabled={disabled}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
        onClick();
      }}
      className="d-flex flex-row box-shadow c-pointer px-3"
      style={{
        background: isThemePicker ? bgColor : '#fff',
        border: 'none',
        padding: '8px 10px',
        borderRadius: '50px',
        width: 'fit-content',
        alignSelf: 'end'
      }}
    >
      <i className={`${iconClassName} mr-2`} />
      {label}
      {children}
    </Card>
  );
};

const EditImageButton = ({
  onClick,
  disabled = false,
  isShowcaseBgLoading
}) => {
  return (
    <span
      aria-disabled={disabled}
      onClick={() => {
        if (!isShowcaseBgLoading) {
          onClick();
        }
      }}
      className="box-shadow c-pointer position-absolute d-flex align-items-center"
      style={{
        background: '#949494',
        color: '#fff',
        border: 'none',
        padding: '8px 15px',
        borderRadius: '50px',
        right: 10,
        top: 10
      }}
    >
      {isShowcaseBgLoading ? (
        <div
          className="d-flex flex-row align-items-center justify-content-center"
          style={{ height: 30, width: 70 }}
        >
          <span
            style={{ height: '100%', width: '100%' }}
            className="btn-multiple-state show-spinner"
          >
            <span className="spinner d-inline-block">
              <span style={{ backgroundColor: '#fff' }} className="bounce1" />
              <span style={{ backgroundColor: '#fff' }} className="bounce2" />
              <span style={{ backgroundColor: '#fff' }} className="bounce3" />
            </span>
          </span>
        </div>
      ) : (
        <>
          <i className="simple-icon-pencil mr-2" />
          Edit
        </>
      )}
    </span>
  );
};

const ColorPickerButton = ({ isEdit, setIsEdit, isThemePicker }) => {
  return (
    <Card
      className="c-pointer d-flex align-items-center justify-content-center color-picker-btn"
      style={{
        background: isEdit ? '#ff04e221' : '#fff'
      }}
      onClick={() => setIsEdit(!isEdit)}
      aria-disabled={isThemePicker}
    >
      <ColorChooserIcon
        color="#fff"
        isActive={isEdit}
        bgColor={isEdit ? '#ff04e221' : '#fff'}
      />
    </Card>
  );
};

const SubcribeButton = ({
  subscribing,
  themeColors,
  handleSubscription,
  ownerData,
  setThemeColors,
  isLoggedIn,
  isSubscribed,
  showColorPicker,
  orgThemeColors,
  setOrgThemeColors,
  userIdentifier,
  fullWidth
}) => {
  const [isOpen, setIsOpen] = useState(false);
  return (
    <span className={`position-relative ${fullWidth && 'w-full'}`}>
      <Button
        type="submit"
        size="md"
        className={`btn-multiple-state noHoverStyles auth-btn ${
          subscribing ? 'show-spinner' : ''
        }`}
        style={{
          width: fullWidth ? '70%' : '135px',
          background: themeColors.subscribeButtonColor,
          color: getTextColorFromBgColor(themeColors.subscribeButtonColor),
          border: 'none'
        }}
        disabled={subscribing}
        onClick={() =>
          handleSubscription({
            tenantId: ownerData?.id,
            userIdentifier
          })
        }
      >
        <span className="spinner d-inline-block">
          <span className="bounce1" />
          <span className="bounce2" />
          <span className="bounce3" />
        </span>
        <span className="label">
          <IntlMessages
            id={`showcase-${
              !isLoggedIn
                ? 'subscribe'
                : isSubscribed
                ? 'unsubscribe'
                : 'subscribe'
            }`}
          />
        </span>
      </Button>
      {showColorPicker && (
        <ColorInput
          open={isOpen}
          setOpen={setIsOpen}
          setThemeColors={setThemeColors}
          themeColors={themeColors}
          orgThemeColors={orgThemeColors}
          setOrgThemeColors={setOrgThemeColors}
          color={themeColors.subscribeButtonColor}
          setColor={(c) => {
            setThemeColors({
              ...themeColors,
              subscribeButtonColor: c
            });
          }}
          style={{ position: 'absolute', top: -15, left: 0 }}
        />
      )}
    </span>
  );
};

export {
  ShareButton,
  EditButton,
  EditImageButton,
  ColorPickerButton,
  SubcribeButton
};
