/* eslint-disable jsx-a11y/no-autofocus */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable no-unused-vars */
import ColorChooserIcon from 'constants/ColorChooserIcon';
import React, { useState, memo, useRef, useEffect } from 'react';
import * as DropdownMenu from '@radix-ui/react-dropdown-menu';
import { RgbaColorPicker } from 'react-colorful';
import { colord, extend } from 'colord';
import namesPlugin from 'colord/plugins/names';
import { Card, Input } from 'reactstrap';
import { updateTenantProfile } from 'functions/api';
import LoadingButton from 'components/buttons/LoadingButton';
import { NotificationManager } from 'components/common/react-notifications';
import IntlMessages from 'helpers/IntlMessages';

extend([namesPlugin]);

function ColorInput({
  color,
  setColor,
  style = {},
  iconStyle = {},
  themeColors,
  open,
  setOpen,
  setThemeColors = () => {},
  orgThemeColors = {},
  setOrgThemeColors = () => {}
}) {
  const [isUpdating, setIsUpdating] = useState(false);
  const tenantId = localStorage.getItem('selectedTenantId');
  const isColorValid = colord(color).isValid();

  const handleColorChange = (c) => {
    if (colord(c).isValid()) {
      const changedColor = colord(c).toHex();
      setColor(changedColor);
    } else {
      setColor(c);
    }
  };

  const handleSave = async () => {
    try {
      setIsUpdating(true);
      const payload = {
        id: tenantId,
        ...themeColors
      };
      const { data, isError } = await updateTenantProfile(payload);
      if (data && !isError) {
        setOpen(false);
        setThemeColors(themeColors);
        setOrgThemeColors(themeColors);
        NotificationManager.success(<IntlMessages id="picker-success" />);
      } else {
        NotificationManager.error(<IntlMessages id="picker-error" />);
      }
      setIsUpdating(false);
    } catch (error) {
      setIsUpdating(false);
      NotificationManager.error(<IntlMessages id="picker-error" />);
      console.log('error', error);
    }
  };

  return (
    <span style={style}>
      <DropdownMenu.Root
        modal
        open={open}
        onOpenChange={(o) => {
          if (!isUpdating) {
            setOpen(o);
            if (!o) {
              setThemeColors(orgThemeColors);
            }
          }
        }}
      >
        <DropdownMenu.Trigger asChild>
          <span>
            <ColorChooserIcon
              style={iconStyle}
              color={color}
              className="c-pointer"
            />
          </span>
        </DropdownMenu.Trigger>
        <DropdownMenu.Portal>
          <DropdownMenu.Content
            style={{ zIndex: 1006, width: '210px' }}
            className="DropdownMenuContent dropdown-radix p-0"
            sideOffset={5}
          >
            <Card className="m-1 d-flex align-items-center justify-content-center">
              <RgbaColorPicker
                color={colord(color).toRgb()}
                onChange={(e) => {
                  handleColorChange(e);
                }}
              />
              <Input
                style={{
                  // outline: 'none',
                  borderRadius: 10,
                  border: !isColorValid && '1px solid red'
                  // fontSize: 12,
                  // background: "transparent"
                }}
                autoFocus
                value={color}
                onChange={(e) => setColor(e.target.value)}
                className="mt-2 mb-0"
              />
              {!isColorValid && (
                <p
                  className="mt-1 text-center mb-0"
                  style={{ fontSize: '12px', color: 'red', lineHeight: '12px' }}
                >
                  <IntlMessages id="picker-warning" />
                </p>
              )}
              <LoadingButton
                disabled={isUpdating}
                loading={isUpdating}
                onClick={handleSave}
                style={{
                  marginTop: '5px',
                  marginBottom: '5px'
                }}
                text="picker-save"
              />
            </Card>
          </DropdownMenu.Content>
        </DropdownMenu.Portal>
      </DropdownMenu.Root>
    </span>
  );
}

export default memo(ColorInput);
