import React, { useEffect } from 'react';
import Loader from 'react-loader-spinner';
// import { Modal, ModalBody, ModalHeader } from 'reactstrap';
import './MainLoader.css';

const MainLoader = ({
  spinner = false,
  height = 15,
  width = 15,
  noBackdrop = false,
  text,
  dataLoad,
}) => {
  const list = [
    'simple-icon-cup',
    'simple-icon-camrecorder',
    'simple-icon-playlist',
    'simple-icon-chart',
  ];
  const [icon, setIcon] = React.useState(list[0]);
  const imageChanger = () => {
    let i = 0;
    const changeImage = () => {
      setTimeout(() => {
        setIcon(list[(i += 1) % 4]);
        changeImage();
      }, 3000);
    };
    changeImage();
  };

  const [count, setCount] = React.useState(0);
  const counterFun = () => {
    let j = 0;
    const changeCount = () => {
      setTimeout(() => {
        setCount((j += 1) % 100);
        changeCount();
      }, 100);
    };
    changeCount();
  };
  useEffect(() => {
    imageChanger();
    counterFun();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      {!noBackdrop && (
        <div className="position-fixed MainLoader">
          {!spinner && !dataLoad && (
            <div className="loader">
              <div className="image loader-image">
                <i className={icon} />
              </div>
              <div
                style={{
                  marginTop: '-5rem ',
                  display: 'inlineBlock',
                  width: 200,
                  marginLeft: `${text ? '-2rem' : '2rem'}`,
                  fontSize: 14,
                }}
              >
                {text || `${count}%`}
              </div>
              <span />
            </div>
          )}
          {spinner && !dataLoad && (
            <Loader
              type="TailSpin"
              color="#922c88"
              height={height}
              width={width}
            />
          )}
          {dataLoad && <div className="dataloading" />}
        </div>
      )}
      {noBackdrop && !spinner && !dataLoad && (
        <div className="loader">
          <div className="image loader-image">
            <i className={icon} />
          </div>
          <div
            style={{
              marginTop: '-5rem',
              display: 'inlineBlock',
              width: 200,
              marginLeft: `${text ? '-2rem' : '2rem'}`,
              fontSize: 15,
            }}
          >
            {text || `${count}%`}
          </div>
          <span />
        </div>
      )}
      {noBackdrop && spinner && (
        <Loader type="TailSpin" color="#922c88" height={height} width={width} />
      )}
      {noBackdrop && dataLoad && <div className="dataloading" />}
    </>
  );
};

export default MainLoader;
