import VerifedIcon from 'constants/VerifedIcon';
import React from 'react';
import UserAvatar from './UserAvatar';
import {
  ColorPickerButton,
  EditButton,
  EditImageButton,
  ShareButton,
  SubcribeButton
} from './Buttons';
import ThemePicker from './ThemePicker';
import { handleOwnerShare } from 'helpers/Utils';

const ImageOwner = ({
  isVideoFile,
  bgImg,
  showEditOptions,
  isMyShowcase,
  setEditImageModalOpen,
  isEdit,
  profileImg,
  setIsEdit,
  isThemePicker,
  setIsThemePicker,
  setThemeColors,
  themeColors,
  orgThemeColors,
  setOrgThemeColors,
  toggleEditOwnerModal,
  handleSubscription,
  ownerData,
  expandFor,
  userIdentifier,
  isSubscribed,
  isLoggedIn,
  accountName,
  currentColor,
  subscribing,
  handleEditBg,
  isShowcaseBgLoading
}) => {
  return (
    <>
      <div className="position-relative">
        <div
          className={`position-relative showcase-banner${
            isVideoFile ? '-video' : ''
          }`}
        >
          {bgImg && (
            <>
              {isVideoFile ? (
                <video
                  alt="background"
                  height="100%"
                  width="100%"
                  src={bgImg}
                  muted
                  autoPlay
                  loop
                  style={{ overflow: 'hidden', objectFit: 'cover' }}
                />
              ) : (
                <img
                  alt="background"
                  height="100%"
                  width="100%"
                  style={{ overflow: 'hidden', objectFit: 'cover' }}
                  src={bgImg}
                />
              )}
            </>
          )}
          {isMyShowcase && showEditOptions && (
            <EditImageButton
              disabled={isEdit}
              onClick={handleEditBg}
              isShowcaseBgLoading={isShowcaseBgLoading}
            />
          )}
        </div>
        <div
          className="position-absolute w-full align-items-center justify-content-center d-flex"
          style={{ bottom: '-75px' }}
        >
          <UserAvatar
            isEdit={isEdit}
            setEditImageModalOpen={setEditImageModalOpen}
            profileImageUrl={profileImg}
            isMyShowcase={isMyShowcase && showEditOptions}
          />
        </div>
      </div>
      <div className="d-flex flex-column w-full mt-3 text-center">
        <div
          className="position-absolute d-flex flex-column"
          style={{
            rowGap: '10px',
            alignSelf: 'end'
          }}
        >
          {isMyShowcase && showEditOptions && (
            <div className="owner-button-cont">
              <div className="d-flex flex-row gap-2 card-and-theme">
                <ColorPickerButton
                  isEdit={isEdit}
                  setIsEdit={setIsEdit}
                  isThemePicker={isThemePicker}
                />
                <ThemePicker
                  themeColors={themeColors}
                  setThemeColors={setThemeColors}
                  isOpen={isThemePicker}
                  setIsOpen={setIsThemePicker}
                  orgThemeColors={orgThemeColors}
                  setOrgThemeColors={setOrgThemeColors}
                  disabled={isEdit}
                />
              </div>
              <EditButton
                onClick={toggleEditOwnerModal}
                label="Edit"
                iconClassName="simple-icon-pencil"
                disabled={isEdit || isThemePicker}
              />
            </div>
          )}
          <div className="subscribe-share">
            <SubcribeButton
              showColorPicker={isEdit && isMyShowcase}
              subscribing={subscribing}
              themeColors={themeColors}
              handleSubscription={handleSubscription}
              ownerData={ownerData}
              setThemeColors={setThemeColors}
              isLoggedIn={isLoggedIn}
              isSubscribed={isSubscribed}
              orgThemeColors={orgThemeColors}
              setOrgThemeColors={setOrgThemeColors}
              userIdentifier={userIdentifier}
            />
            <ShareButton
              isMyShowcase={isMyShowcase && isEdit}
              expandFor={expandFor}
              themeColors={themeColors}
              orgThemeColors={orgThemeColors}
              setOrgThemeColors={setOrgThemeColors}
              setThemeColors={setThemeColors}
              onClick={() =>
                handleOwnerShare(window.location.href, ownerData?.profileTitle)
              }
            />
          </div>
        </div>

        <div className="showcase-owner-title">
          <h1
            style={{
              color: currentColor ? '#8f8f8f' : '#000',
              wordWrap: 'break-word'
            }}
          >
            {accountName}
            <VerifedIcon isVerfied={ownerData?.isVerified} />
          </h1>
        </div>
      </div>
    </>
  );
};

export default ImageOwner;
