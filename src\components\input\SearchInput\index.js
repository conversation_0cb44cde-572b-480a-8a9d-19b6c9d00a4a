/* eslint-disable no-unused-vars */
import React from 'react';

function SearchInput({
  handleClick,
  handleClear,
  needClearIcon = false,
  value,
  placeholder,
  onChange,
  onPressEnter,
  disabled,
  className = 'mr-4',
  width,
  minWidth = '99%',
  id
}) {
  return (
    <span
      id={id}
      style={{ width, minWidth }}
      className={`search-sm mb-1 align-top${className} `}
    >
      <button type="button" className="search-sm-btn" onClick={handleClick}>
        <i className="simple-icon-magnifier search-sm-icon" />
      </button>
      <input
        type="text"
        name="keyword"
        id="search"
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            onPressEnter();
          }
        }}
        disabled={disabled}
      />
      {needClearIcon && value && (
        <button type="button" className="search-sm-btn-2" onClick={handleClear}>
          <span style={{ color: '#131313' }} aria-hidden="true">
            ×
          </span>
        </button>
      )}
    </span>
  );
}

export default SearchInput;
