import InfoIcon from 'components/icon/InfoIcon';
import React from 'react';
import { Card } from 'reactstrap';
import Switch from 'rc-switch';
import IntlMessages from 'helpers/IntlMessages';

function AutoApprove({ autoApprove, setAutoApprove }) {
  return (
    <div className="d-flex align-items-center justify-content-center mt-3">
      <Card
        className="d-flex flex-row p-3"
        style={{ width: 'max-content', borderRadius: '50px' }}
      >
        <InfoIcon message="auto-approve-info" />
        <span>
          <IntlMessages id="auto-approve" />
        </span>
        <Switch
          className="custom-switch custom-switch-primary custom-switch-small ml-3"
          checked={autoApprove}
          onClick={(e) => {
            setAutoApprove(e);
          }}
        />
      </Card>
    </div>
  );
}

export default AutoApprove;
