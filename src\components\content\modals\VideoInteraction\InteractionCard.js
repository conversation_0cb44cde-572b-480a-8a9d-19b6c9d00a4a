/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable react/no-danger */
/* eslint-disable react/no-array-index-key */
/* eslint-disable no-unused-vars */
/* eslint-disable no-nested-ternary */
import { INTERACTION_TYPE } from 'constants/VideoInteraction';
import React, { useState } from 'react';
import { Card } from 'reactstrap';
import useInteraction from './components/useInteraction';
import {
  formatDate,
  formatInteraction,
  formatTime,
  getInteractionTypes
} from 'helpers/Utils';
import * as DropdownMenu from '@radix-ui/react-dropdown-menu';
import Pill from './components/Pill';
import URL from './components/URL';
import Question from './components/Question';
import Text from './components/Text';
import xlsx from 'json-as-xlsx';
import TimeRow from './components/TimeRow';
import { getInteractionAnalyticsById } from 'functions/api/analyticsApi';
import { NotificationManager } from 'components/common/react-notifications';
import LoadingIndicator from 'views/app/spaces/components/Tabs/AiInsights/AiChat/LoadingIndicator';
import DeleteWarningModal from './DeleteWarningModal';

const InteractionCard = ({
  data,
  isParticular,
  setList,
  content,
  handleEdit,
  setSelectedInteractions,
  setSelectedPollId
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const { isImage, isPoll, isRichText, isSurvey } = getInteractionTypes(data);
  const {
    isDeleting,
    handleDelete,
    handleDisable,
    isDisabling,
    isEnabling,
    handleEnable
  } = useInteraction({
    id: data.id,
    spaceId: content.spaceId,
    contentId: content.id,
    onDelete: (id) => {
      setList((prev) => prev.filter((f) => f.id !== data.id));
    },
    onDisable: (id) => {
      setList((prev) =>
        prev.map((f) => {
          if (f.id === data.id) {
            return {
              ...f,
              isEnabled: false
            };
          }
          return f;
        })
      );
    },
    onEnable: (id) => {
      setList((prev) =>
        prev.map((f) => {
          if (f.id === data.id) {
            return {
              ...f,
              isEnabled: true
            };
          }
          return f;
        })
      );
    }
  });
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const questions = (() => {
    if (isPoll) {
      return [data?.data?.title];
    }
    if (isSurvey) {
      return data?.data?.surveyQuestions?.map((m) => m.question);
    }
    return [];
  })();

  const additionalQuestions = questions?.length - 2;

  const isDisabled = isDeleting || isDisabling || isEnabling;

  const createdAt = new Date(data?.creationDateTime);
  const updatedAt = new Date(data?.lastUpdatedDateTime);

  return (
    <>
      <Card className="ICard" aria-disabled={isDisabled}>
        <div className="ICardHeader">
          <div className="d-flex flex-row gap-2 align-items-center">
            <Pill type={data.type} />
            {isParticular && (
              <TimeRow
                startTime={data?.showAtTimecode}
                endTime={!data?.pauseOnShow && data?.endAtTimecode}
              />
            )}
          </div>
          <div>
            {!data.isEnabled && (
              <span
                style={{
                  border: '1px solid red',
                  padding: '2px 10px',
                  borderRadius: 20,
                  fontSize: '0.75rem',
                  color: 'red'
                }}
              >
                Disabled
              </span>
            )}
            <CardDropdown
              handleDelete={() => {
                setShowDeleteModal(true);
              }}
              handleDisable={handleDisable}
              handleEnable={handleEnable}
              open={isOpen}
              showDisableOption={data?.isEnabled}
              setOpen={setIsOpen}
              handleEdit={handleEdit}
            />
          </div>
        </div>
        <div className="ICardTop">
          {isImage && (
            <img
              height={100}
              width={100}
              src={data?.data?.imageUrl}
              alt="interaction_image"
            />
          )}
          <div className="d-flex flex-column" style={{ width: 'inherit' }}>
            {isImage && data?.data?.title && <URL url={data?.data?.title} />}
            {isRichText && data?.data?.title && (
              <Text text={data?.data?.title} />
            )}
            <div style={{ position: 'relative' }}>
              {questions?.slice(0, 2).map((m, i) => (
                <Question key={i} question={m} />
              ))}
              {additionalQuestions > 0 && (
                <span
                  style={{
                    position: 'absolute',
                    right: 0,
                    bottom: 0,
                    fontSize: '0.75rem'
                  }}
                >
                  +{additionalQuestions} more
                </span>
              )}
            </div>
          </div>
        </div>
        <div className="ICardBottom">
          <div className="d-flex flex-row" style={{ gap: '10%' }}>
            <DateCol text="Created on:" date={createdAt} />
            {!data?.isEnabled && (
              <DateCol text="Modified on:" date={new Date(updatedAt)} />
            )}
          </div>
          <div className="d-flex flex-row align-items-center gap-2">
            {isPoll && (
              <PollResult
                onClick={() => {
                  setSelectedPollId(data);
                }}
              />
            )}
            {(isPoll || isImage || isSurvey) && (
              <DownloadButton
                isImage={isImage}
                isPoll={isPoll}
                isSurvey={isSurvey}
                data={data}
                contentId={content?.id}
              />
            )}
            {!isRichText && (
              <AnalyticsButton
                onClick={() => {
                  setSelectedInteractions(data);
                }}
              />
            )}
          </div>
        </div>
      </Card>
      {showDeleteModal && (
        <DeleteWarningModal
          show={showDeleteModal}
          setIsOpen={setShowDeleteModal}
        />
      )}
    </>
  );
};

const CardDropdown = ({
  setOpen,
  open,
  handleDelete,
  handleDisable,
  handleEdit,
  showDisableOption,
  handleEnable
}) => {
  return (
    <DropdownMenu.Root
      open={open}
      onOpenChange={(o) => {
        setOpen(o);
      }}
    >
      <DropdownMenu.Trigger asChild>
        <i
          className="simple-icon-options-vertical cursor-pointer"
          style={{ fontSize: '1rem' }}
        />
      </DropdownMenu.Trigger>
      <DropdownMenu.Portal>
        <DropdownMenu.Content
          style={{ zIndex: 10000 }}
          className="DropdownMenuContent dropdown-radix p-2 "
          sideOffset={5}
        >
          {showDisableOption && (
            <DropdownMenu.Item
              onClick={handleEdit}
              className="cursor-pointer outline-none"
            >
              <i
                style={{ fontSize: '1em' }}
                className="simple-icon-pencil mr-3"
              />
              Edit
            </DropdownMenu.Item>
          )}
          <DropdownMenu.Item
            onClick={handleDelete}
            className="cursor-pointer outline-none"
          >
            <i style={{ fontSize: '1em' }} className="simple-icon-trash mr-3" />
            Delete
          </DropdownMenu.Item>
          {showDisableOption ? (
            <DropdownMenu.Item
              onClick={handleDisable}
              className="cursor-pointer outline-none"
            >
              <i style={{ fontSize: '1em' }} className="simple-icon-ban mr-3" />
              Disable
            </DropdownMenu.Item>
          ) : (
            <DropdownMenu.Item
              onClick={handleEnable}
              className="cursor-pointer outline-none"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-3"
              >
                <path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z" />
                <path d="m9 12 2 2 4-4" />
              </svg>
              Enable
            </DropdownMenu.Item>
          )}
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
};

const DateCol = ({ date, text }) => {
  return (
    <div className="DateCol">
      <p className="DateColText">{text}</p>
      <p className="DateColDate">
        {formatTime(date)} {formatDate(date)}
      </p>
    </div>
  );
};

const ICON_SIZE = 18;

const PollResult = (props) => {
  return (
    <button type="button" className="analyticsButton" {...props}>
      <svg
        width={ICON_SIZE}
        height={ICON_SIZE}
        viewBox="0 0 34 35"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M14.875 24.9375H23.375V27.0625H14.875V24.9375ZM10.625 24.9375H12.75V27.0625H10.625V24.9375ZM14.875 19.625H23.375V21.75H14.875V19.625ZM10.625 19.625H12.75V21.75H10.625V19.625ZM14.875 14.3125H23.375V16.4375H14.875V14.3125ZM10.625 14.3125H12.75V16.4375H10.625V14.3125Z"
          fill="#922C88"
        />
        <path
          d="M26.5625 5.8125H23.375V4.75C23.375 4.18641 23.1511 3.64591 22.7526 3.2474C22.3541 2.84888 21.8136 2.625 21.25 2.625H12.75C12.1864 2.625 11.6459 2.84888 11.2474 3.2474C10.8489 3.64591 10.625 4.18641 10.625 4.75V5.8125H7.4375C6.87391 5.8125 6.33341 6.03638 5.9349 6.4349C5.53638 6.83341 5.3125 7.37391 5.3125 7.9375V30.25C5.3125 30.8136 5.53638 31.3541 5.9349 31.7526C6.33341 32.1511 6.87391 32.375 7.4375 32.375H26.5625C27.1261 32.375 27.6666 32.1511 28.0651 31.7526C28.4636 31.3541 28.6875 30.8136 28.6875 30.25V7.9375C28.6875 7.37391 28.4636 6.83341 28.0651 6.4349C27.6666 6.03638 27.1261 5.8125 26.5625 5.8125ZM12.75 4.75H21.25V9H12.75V4.75ZM26.5625 30.25H7.4375V7.9375H10.625V11.125H23.375V7.9375H26.5625V30.25Z"
          fill="#922C88"
        />
      </svg>
    </button>
  );
};

const AnalyticsButton = (props) => {
  return (
    <button type="button" className="analyticsButton" {...props}>
      <svg
        width={ICON_SIZE}
        height={ICON_SIZE}
        viewBox="0 0 22 29"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M21.5 17.7998V28H17.6113V17.7998H21.5ZM12.9443 1V28H9.05566V1H12.9443ZM4.38867 9.40039V28H0.5V9.40039H4.38867Z"
          stroke="#922C88"
        />
      </svg>
    </button>
  );
};

const DownloadButton = ({ data, contentId, isImage, isPoll, isSurvey }) => {
  const [isDownloading, setIsDownloading] = useState(false);

  const downloadFile = (respData) => {
    const settings = {
      fileName: `player_interactions_content_${contentId}_interaction_${data.id}`
    };
    const info = formatInteraction({
      data: respData,
      isImage,
      isPoll,
      isSurvey
    });
    xlsx(info, settings);
    NotificationManager.success('Downloaded the analytics successfully');
  };

  const handleDownload = async () => {
    const handleError = () => {
      setIsDownloading(false);
      NotificationManager.error('Failed to download the analytics');
    };

    try {
      setIsDownloading(true);
      const { data: respData, isError } = await getInteractionAnalyticsById({
        contentId,
        interactionId: data.id
      });
      if (respData) {
        downloadFile(respData);
      } else {
        handleError();
      }
      setIsDownloading(false);
    } catch (error) {
      console.error('Something went wrong in handleDownload due to ', error);
      handleError();
    }
  };

  if (isDownloading) {
    return (
      <span
        style={{
          position: 'relative',
          width: 50,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        <LoadingIndicator />
      </span>
    );
  }

  return (
    <button type="button" className="downloadExcel" onClick={handleDownload}>
      <svg width={ICON_SIZE} height={ICON_SIZE} viewBox="0 0 25 25" fill="none">
        <path
          d="M12.5001 18.6107L6.45429 12.5666L7.66379 11.3366L11.6459 15.3187V0.541626H13.3542V15.3187L17.3347 11.3383L18.5459 12.5666L12.5001 18.6107ZM3.30241 24.4583C2.51544 24.4583 1.85887 24.1952 1.33271 23.669C0.806539 23.1429 0.542887 22.4857 0.541748 21.6976V17.5583H2.25008V21.6976C2.25008 21.9607 2.35941 22.2022 2.57808 22.422C2.79675 22.6418 3.03762 22.7511 3.30071 22.75H21.6995C21.9614 22.75 22.2023 22.6406 22.4221 22.422C22.6419 22.2033 22.7512 21.9618 22.7501 21.6976V17.5583H24.4584V21.6976C24.4584 22.4846 24.1953 23.1412 23.6692 23.6673C23.143 24.1935 22.4859 24.4572 21.6977 24.4583H3.30241Z"
          fill="#922C88"
        />
      </svg>
    </button>
  );
};

export default InteractionCard;
