/* eslint-disable no-unused-vars */
/* eslint-disable no-unreachable */
/* eslint-disable react/self-closing-comp */
/* eslint-disable react/no-array-index-key */
import React, { useEffect, useState } from 'react';
import SACard from './SACard';
import { getTenantProfiles } from 'functions/api/tenantApi';
import { TopListLoading } from './loading';

const LIMIT = 5;

const TopList = ({ isLoggedIn, setShowAccountList, data, setData }) => {
  const [isLoading, setIsLoading] = useState(false);

  const fetchList = async ({ skip = 0 }) => {
    try {
      if (skip === 0) {
        setIsLoading(true);
      }
      const { data, isError } = await getTenantProfiles({
        isPublic: !isLoggedIn,
        skip,
        limit: LIMIT
      });
      const totalItems = data?.items ?? [];
      if (!isError) {
        setData(totalItems);
      }
      setIsLoading(false);
    } catch (error) {
      console.error('Sopmething went wrong in the fetchList due to ', error);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (data.length === 0) {
      fetchList({ skip: 0 });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  if (isLoading) {
    return <TopListLoading />;
  }

  return (
    <div className="SATopList">
      {data.map((m, i) => (
        <SACard
          key={i}
          data={m}
          handleShowcaseAccountClick={() => {
            setShowAccountList(false);
          }}
        />
      ))}

      <div className="allAccounts">
        <p
          className="font-bolder c-pointer"
          onClick={() => {
            setShowAccountList(true);
          }}
        >
          See All Accounts
        </p>
      </div>
    </div>
  );
};

export default TopList;
