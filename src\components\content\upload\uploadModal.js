import React, { useEffect, useState } from 'react';
import { Modal, ModalBody } from 'reactstrap';
import UploadWizard from 'components/content/upload/uploadWizard';
import { useSelector } from 'react-redux';

const UploadModal = ({
  modalOpen,
  toggleModal,
  showMediaProcessing,
  isModal,
  isImageModal,
  isWatermarkModal,
  title,
  spaceId,
  contentIdForWatermark,
  acceptedMediaType,
  MediaType,
  reload,
  setContentsLoading,
  uploading,
  setUploading,
  isStreamingModal,
  setIsStreamingModal,
  makeActiveModal,
  subscriptionDetails,
  roleType
}) => {
  const [openConfirmationModal, setOpenConfirmationModal] = useState(false);
  const ref = React.useRef(null);
  const [isImageUploading, setIsImageUploading] = useState(false);
  const { drmEnabled, aesEnabled, workFlowEnabled, optimizationEnabled } =
    useSelector((state) => state.subscription);
  const [isPreview, setIsPreview] = useState(false);

  const toggleConfirmationModal = () => {
    setOpenConfirmationModal(!openConfirmationModal);
  };

  React.useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        toggleConfirmationModal();
      }
    };
    window.addEventListener('keydown', handleEscape);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!modalOpen && isStreamingModal) {
      // eslint-disable-next-line no-unused-expressions
      setIsStreamingModal && setIsStreamingModal(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [modalOpen]);

  return (
    <Modal
      isOpen={modalOpen}
      ref={ref}
      toggle={toggleModal}
      centered
      keyboard={false}
      wrapClassName="modal-space"
      modalClassName={uploading}
      style={{ maxWidth: MediaType === 2 && isPreview && '60%' }}
      backdrop="static"
    >
      <ModalBody
        style={{
          minHeight: '300px',

          backgroundColor: isImageUploading && 'transparent !important'
        }}
      >
        <UploadWizard
          uploading={uploading}
          setUploading={setUploading}
          showMediaProcessing={showMediaProcessing}
          title={title}
          isModal={isModal}
          isImageModal={isImageModal}
          modalOpen={modalOpen}
          isWatermarkModal={isWatermarkModal}
          toggleModal={toggleModal}
          openConfirmationModal={openConfirmationModal}
          spaceId={spaceId}
          contentIdForWatermark={contentIdForWatermark}
          toggleConfirmationModal={toggleConfirmationModal}
          acceptedMediaType={acceptedMediaType}
          MediaType={MediaType}
          reload={reload}
          makeActiveModal={makeActiveModal}
          setContentsLoading={setContentsLoading}
          isStreamingModal={isStreamingModal}
          setIsStreamingModal={setIsStreamingModal}
          setIsImageUploading={setIsImageUploading}
          subscriptionDetails={subscriptionDetails}
          isPreview={isPreview}
          setIsPreview={setIsPreview}
          drmEnabled={drmEnabled}
          aesEnabled={aesEnabled}
          roleType={roleType}
          workFlowEnabled={workFlowEnabled}
          optimizationEnabled={optimizationEnabled}
        />
      </ModalBody>
    </Modal>
  );
};

export default UploadModal;
