import AiIcon from 'constants/AiIcon';
import IntlMessages from 'helpers/IntlMessages';
import React from 'react';
import { Button } from 'reactstrap';

const AiButton = ({
  disabled,
  onClick,
  text,
  count,
  isLoading,
  showCount = false,
  ...props
}) => {
  return (
    <Button
      {...props}
      disabled={disabled}
      color="primary"
      className="d-flex flex-row centered"
      onClick={onClick}
    >
      {showCount && count > 0 ? (
        <>{count}</>
      ) : (
        <>
          <AiIcon primary={false} active />
          <span className="ml-2" style={{ textWrap: 'nowrap' }}>
            <IntlMessages id={text} />
          </span>
        </>
      )}
      {isLoading && (
        <span className="ml-2 text-primary pr-4">
          <span className="show-spinner btn-multiple-state">
            <span className="spinner d-inline-block">
              <span style={{ backgroundColor: '#fff' }} className="bounce1" />
              <span style={{ backgroundColor: '#fff' }} className="bounce2" />
              <span style={{ backgroundColor: '#fff' }} className="bounce3" />
            </span>
          </span>
        </span>
      )}
    </Button>
  );
};

export default AiButton;
