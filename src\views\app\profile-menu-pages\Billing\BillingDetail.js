/* eslint-disable jsx-a11y/anchor-is-valid */
import {
  getBillingDetails,
  updateBillingDetails
} from 'functions/api/billingApi';
import React, { useEffect, useState } from 'react';
import { NotificationManager } from 'components/common/react-notifications';
import IntlMessages from 'helpers/IntlMessages';
import { Button, Form, FormGroup, Input, Label } from 'reactstrap';
import MainLoader from 'components/loaders/MainLoader';
import {
  validateEmail,
  validatePhone,
  validateAddress
} from '../../../../helpers/Utils';
import { Colxx } from 'components/common/CustomBootstrap';

const BillingDetail = () => {
  const [billingDetails, setBillingDetails] = useState();
  const [tempBilling, setTempBilling] = useState();
  const [loading, setLoading] = useState(true);
  const [formChanged, setFormChanged] = useState(false);
  const [error, setError] = useState({});

  const isProduction = process.env.REACT_APP_ENV === 'PROD';

  const fetchBillingDetais = async () => {
    setLoading(true);
    const response = await getBillingDetails();
    setBillingDetails(response);
    setTempBilling(response);
    setLoading(false);
  };

  const updateBilling = async () => {
    if (
      !formChanged ||
      error.name ||
      error.email ||
      error.phone ||
      error.address ||
      error.taxId
    ) {
      return;
    }
    setLoading(true);
    const response = await updateBillingDetails({
      name: tempBilling.name,
      email: tempBilling.email,
      phone: tempBilling.phone,
      taxId: tempBilling.taxId,
      address: tempBilling.address
    });
    if (response === 200 || (response > 200 && response < 300)) {
      setBillingDetails(tempBilling);
      setFormChanged(false);
      NotificationManager.success(
        <IntlMessages id="billing-update" />,
        <IntlMessages id="req.success" />,
        3000,
        null,
        null,
        ''
      );
    } else {
      NotificationManager.error(
        <IntlMessages id="something-wrong" />,
        <IntlMessages id="req.failed" />,
        3000,
        null,
        null,
        ''
      );
    }
    setLoading(false);
  };
  useEffect(() => {
    fetchBillingDetais();
  }, []);
  return (
    <div
      style={{
        display: 'block',
        position: 'relative'
      }}
    >
      {!loading ? (
        <Colxx>
          {!isProduction && (
            <div className="w-full d-flex align-items-center justify-content-end mb-4">
              <Button color="primary">
                <IntlMessages id="sub-paid" />
              </Button>
            </div>
          )}
          <Form
            className="av-tooltip tooltip-label-bottom"
            noValidate
            onChange={() => setFormChanged(true)}
            onSubmit={(e) => {
              e.preventDefault();
              updateBilling();
            }}
            onReset={() => {
              setTempBilling(billingDetails);
              setFormChanged(false);
            }}
          >
            <FormGroup className="form-group has-float-label">
              <Label for="name">
                <b>
                  Name<span style={{ color: '#922c88' }}> *</span>
                </b>
              </Label>
              <Input
                id="name"
                name="Name"
                placeholder="Your Name"
                type="text"
                value={tempBilling?.name}
                onChange={(e) => {
                  setError({
                    ...error,
                    name: e.target.value?.length < 3 ? 'Name is required' : ''
                  });
                  setTempBilling({
                    ...tempBilling,
                    name: e.target.value
                  });
                }}
              />
              {error?.name && (
                <div className="invalid-feedback d-block">{error.name}</div>
              )}
            </FormGroup>
            <FormGroup className="form-group has-float-label">
              <Label for="email">
                <b>
                  Email<span style={{ color: '#922c88' }}> *</span>
                </b>
              </Label>

              <Input
                id="email"
                name="Email"
                placeholder="Billing Email"
                type="email"
                value={tempBilling?.email}
                onChange={(e) => {
                  setError({
                    ...error,
                    email: validateEmail(e.target.value)
                  });
                  setTempBilling({
                    ...tempBilling,
                    email: e.target.value
                  });
                }}
              />
              {error?.email && (
                <div className="invalid-feedback d-block">{error.email}</div>
              )}
            </FormGroup>
            <FormGroup className="form-group has-float-label">
              <Label for="phone">
                <b>
                  Phone<span style={{ color: '#922c88' }}> *</span>
                </b>
              </Label>

              <Input
                id="phone"
                name="phone"
                placeholder="Phone Number"
                type="number"
                value={tempBilling?.phone}
                onChange={(e) => {
                  setError({
                    ...error,
                    phone: validatePhone(e.target.value)
                  });
                  setTempBilling({
                    ...tempBilling,
                    phone: e.target.value
                  });
                }}
              />
              {error?.phone && (
                <div className="invalid-feedback d-block">{error.phone}</div>
              )}
            </FormGroup>
            <FormGroup className="form-group has-float-label">
              <Label for="billingAddress">
                <b>
                  Billing Address<span style={{ color: '#922c88' }}> *</span>
                </b>
              </Label>

              <Input
                id="billingAddress"
                type="textarea"
                name="Billing Address"
                placeholder="billing address"
                value={
                  tempBilling?.address === null ? '' : tempBilling?.address
                }
                onChange={(e) => {
                  setError({
                    ...error,
                    address: validateAddress(e.target.value)
                  });
                  setTempBilling({
                    ...tempBilling,
                    address: e.target.value
                  });
                }}
              />
              {error?.address && (
                <div className="invalid-feedback d-block">{error.address}</div>
              )}
            </FormGroup>
            <FormGroup className="form-group has-float-label">
              <Label for="taxID">
                <b>Tax ID (EIN / VAT / UID)</b>
              </Label>

              <Input
                id="taxID"
                name="Tax ID (EIN / VAT / UID)"
                placeholder="Tax ID (EIN / VAT / UID)"
                type="text"
                value={tempBilling?.taxId}
                onChange={(e) => {
                  setTempBilling({ ...tempBilling, taxId: e.target.value });
                }}
              />
              {error?.taxId && (
                <div className="invalid-feedback d-block">{error.taxId}</div>
              )}
            </FormGroup>
            <FormGroup className="d-flex justify-content-center">
              <Button type="reset" disabled={!formChanged} className="mr-2">
                Reset
              </Button>
              <Button color="primary" disabled={!formChanged} type="submit">
                Submit
              </Button>
            </FormGroup>
          </Form>
        </Colxx>
      ) : (
        <div className="d-flex justify-content-center">
          <MainLoader dataLoad noBackdrop />
        </div>
      )}
    </div>
  );
};

export default BillingDetail;
