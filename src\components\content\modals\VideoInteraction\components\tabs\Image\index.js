/* eslint-disable no-unused-vars */
/* eslint-disable consistent-return */
import SelectedImg from 'components/SelectedImg';
import React, { useEffect, useRef } from 'react';
import { Form, FormGroup, Input, Label } from 'reactstrap';
import PlayerPreview from '../../PlayerPreview';
import {
  INTERACTION_TYPE,
  PLAYER_PREVIEW_TYPE
} from 'constants/VideoInteraction';
import TimeControl from '../../TimeControl';
import SectionHeader from '../../SectionHeader';
import { getImage } from 'helpers/Utils';
import { Field, Formik } from 'formik';
import IntlMessages from 'helpers/IntlMessages';

const ImageTab = ({
  isParticular,
  position,
  setPosition,
  img,
  setImg,
  url,
  setUrl,
  interactiondetails,
  startTime,
  setStartTime,
  endTime,
  setEndTime,
  pauseVideo,
  setPauseVideo,
  height,
  width,
  setHeight,
  setWidth
}) => {
  const imageRef = useRef(null);

  const handleSelectImage = (e) => {
    console.log('e', e);
    const imageFile = e.target.files[0];
    setImg(imageFile);
  };

  useEffect(() => {
    if (
      interactiondetails &&
      interactiondetails?.type === INTERACTION_TYPE.IMAGE
    ) {
      setUrl(interactiondetails?.data?.title);
      setImg(interactiondetails?.data?.imageUrl);
      setPosition(+interactiondetails.data.position);
      setPauseVideo(interactiondetails?.pauseOnShow);
      setEndTime(interactiondetails?.endAtTimecode);
      setStartTime(interactiondetails?.showAtTimecode);
      setHeight(+interactiondetails.data.height);
      setWidth(+interactiondetails.data.width);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [interactiondetails]);

  const validateMarkWidth = (value) => {
    setWidth(value);
    let error = '';
    if (!value) {
      error = 'Please enter a interaction image width';
    } else if (value < 5 || value > 100) {
      error = 'Please enter a width in between 5% and 100%';
    }
    return error;
  };

  const validateMarkHeight = (value) => {
    setHeight(value);
    let error = '';
    if (!value) {
      error = 'Please enter a interaction image height';
    } else if (value < 5 || value > 100) {
      error = 'Please enter a height in between 5% and 100%';
    }
    return error;
  };

  return (
    <>
      <div className="ImageTab">
        {isParticular && (
          <TimeControl
            startTime={startTime}
            setStartTime={setStartTime}
            endTime={endTime}
            setEndTime={setEndTime}
            pauseVideo={pauseVideo}
            setPauseVideo={setPauseVideo}
          />
        )}
        <SectionHeader style={{ marginTop: '2rem', marginBottom: '0.1rem' }}>
          Add Image <span style={{ color: 'red' }}>*</span>
        </SectionHeader>
        <>
          {!img ? (
            <>
              <button
                type="button"
                color="primary"
                onClick={() => {
                  imageRef.current.click();
                }}
                style={{
                  background: 'transparent',
                  borderRadius: 10,
                  color: '#992288',
                  fontSize: 14,
                  width: 'auto',
                  padding: '4px 10px'
                }}
                className="dotted-border-primary-box mb-4 mt-3"
              >
                Add Image
              </button>
            </>
          ) : (
            <SelectedImg
              src={getImage(img)}
              showCloseIcon
              active={false}
              onCloseClick={(e) => {
                setImg(null);
              }}
            />
          )}
        </>

        <input
          type="file"
          className="dashboard-file-input"
          accept="image/jpeg, image/jpg, image/png, image/gif"
          ref={imageRef}
          onChange={handleSelectImage}
        />

        <FormGroup
          className="form-group has-float-label mr-2 mt-2 mb-4"
          style={{ flex: 1 }}
        >
          <Label for="Industry">URL</Label>
          <Input
            type="url"
            onChange={(e) => {
              const value = e.target?.value;
              setUrl(value);
            }}
            value={url}
            placeholder="Enter redirect link"
            className="tiemInputStyle"
          />
        </FormGroup>
        <Formik
          initialValues={{
            height,
            width,
            url
          }}
        >
          {({ errors, touched }) => (
            <Form className="av-tooltip tooltip-label-bottom">
              <div className="d-flex justify-content-center align-items-center mt-3">
                <IntlMessages id="menu.markHeight" />
                <FormGroup className="form-group has-float-label mb-0">
                  <Field
                    className="form-control markSize"
                    type="number"
                    name="height"
                    value={height}
                    validate={validateMarkHeight}
                  />
                  {errors.height && touched.height && (
                    <div className="invalid-feedback d-block">
                      {errors.height}
                    </div>
                  )}
                </FormGroup>
                <span>%</span>

                <span className="ml-3 mr-3">X</span>

                <IntlMessages id="menu.markWidth" />
                <FormGroup className="form-group has-float-label mb-0">
                  <Field
                    className="form-control markSize"
                    type="number"
                    name="width"
                    value={width}
                    validate={validateMarkWidth}
                  />
                  {errors.width && touched.width && (
                    <div className="invalid-feedback d-block">
                      {errors.width}
                    </div>
                  )}
                </FormGroup>
                <span>%</span>
              </div>
            </Form>
          )}
        </Formik>
        <PlayerPreview
          type={PLAYER_PREVIEW_TYPE.IMAGE}
          img={img}
          position={position}
          setPosition={setPosition}
          width={width}
          height={height}
        />
      </div>
    </>
  );
};

export default ImageTab;
